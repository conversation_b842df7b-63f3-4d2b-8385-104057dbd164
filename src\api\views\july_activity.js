import { request } from '../index';

/**
 * 活动页7月夏日宾果大作战活动
 */

/**
 * 七月活动 - 首页
 */
export function ApiActivityJulyIndex(params = {}) {
  return request('/activity/july_two/index', params);
}

/**
 * 七月活动 - 领取任务奖励
 */
export function ApiActivityJulyTask(params = {}) {
  return request('/activity/july_two/task', params);
}

/**
 * 七月活动 - 奖励记录日志
 */
export function ApiActivityJulyRewardLog(params = {}) {
  return request('/activity/july_two/rewardLog', params);
}

/**
 * 七月活动 - 奖励轮次
 */
export function ApiActivityJulyActivityList(params = {}) {
  return request('/activity/july_two/activityList', params);
}

/**
 * 七月活动 - 敲冰块
 */
export function ApiActivityJulyTakeIce(params = {}) {
  return request('/activity/july_two/takeIce', params);
}

/**
 * 七月活动 - 领取连冰奖励
 */
export function ApiActivityJulyTakeReward(params = {}) {
  return request('/activity/july_two/takeReward', params);
}

/**
 * 七月活动 - 消暑大礼包检测
 */
export function ApiActivityJulyCheckFinish(params = {}) {
  return request('/activity/july_two/checkFinish', params);
}

/**
 * 七月活动 - 消暑大礼包-领取
 */
export function ApiActivityJulyTakeFinish(params = {}) {
  return request('/activity/july_two/takeFinish', params);
}
