import { ApiUserInfo } from '@/api/views/users.js';

export default {
  state: {
    uuid: 0,
    from: 0,
    userInfo: {},
    userInfoEx: {},
    areaCode: 86, // 手机区号
  },
  mutations: {
    setUuid(state, uuid) {
      state.uuid = uuid || 0;
    },
    setFrom(state, from) {
      state.from = from || 0;
    },
    setUserInfo(state, userInfo) {
      if (!userInfo) {
        state.userInfo = {};
      } else {
        state.userInfo = userInfo;
      }
    },
    setUserInfoEx(state, userInfoEx) {
      if (!userInfoEx) {
        state.userInfoEx = {};
      } else {
        state.userInfoEx = userInfoEx;
      }
    },

    setAreaCode(state, areaCode) {
      state.areaCode = areaCode || 86;
    },
  },
  getters: {
    uuid(state) {
      return state.uuid;
    },
    from(state) {
      return state.from;
    },
    userInfo(state) {
      return state.userInfo;
    },
    userInfoEx(state) {
      return state.userInfoEx;
    },

    areaCode(state) {
      return state.areaCode;
    },
  },
  actions: {
    // returnErr 默认不发出未登录提醒
    async SET_USER_INFO({ commit, rootState }, returnErr = false) {
      try {
        const res = await ApiUserInfo({ returnErr });
        commit('setUserInfo', res.data);
      } finally {
      }
    },
  },
};
