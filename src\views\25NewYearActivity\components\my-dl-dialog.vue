<template>
  <div>
    <van-dialog
      v-model="popup"
      :show-confirm-button="false"
      :lock-scroll="false"
      class="lucky-bag-dialog"
    >
      <div class="dialog-content">
        <div class="title-container">
          <div class="top-title">
            <div class="title">我的春联</div>
          </div>
        </div>
        <div class="close-btn" @click="closePopup"></div>
        <div class="dl-cover">
          <div class="capture" id="capture">
            <div class="dl-img"></div>
            <div class="dl-tips">我在3733为您送上新春祝福</div>
            <div class="dl-item">
              <div>
                <span>{{ myDlInfo.sl }}</span>
              </div>
              <div>
                <span>{{ myDlInfo.xl }} </span>
              </div>
            </div>
          </div>
          <div class="dl-dow" @click="handleDlCover"></div>
        </div>
        <div class="share-btn" @click="handleShare">
          {{
            isExistHbCover || hbCoverQuantity >= 200
              ? '分享'
              : '分享得定制微信红包封面'
          }}
        </div>
      </div>
    </van-dialog>
  </div>
</template>

<script>
import html2canvas from 'html2canvas';
import { remNumberLess } from '@/common/styles/_variable.less';
export default {
  name: 'luckyBagDialog',
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    isExistHbCover: {
      type: Boolean,
      default: false,
    },
    hbCoverQuantity: {
      type: Number,
      default: 0,
    },
    myDlInfo: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {};
  },
  computed: {
    popup: {
      get() {
        return this.show;
      },
      set(value) {
        this.$emit('update:show', value);
      },
    },
  },

  methods: {
    closePopup() {
      this.$emit('update:show', false);
    },
    handleShare() {
      this.closePopup();
      this.$emit('handleShare', true);
    },
    handleDlCover() {
      const dom = document.querySelector('#capture');
      const box = window.getComputedStyle(dom);
      const scaleBy = window.devicePixelRatio > 2 ? 4 : 2;
      const width = parseInt(box.width, 10);
      const height = parseInt(box.height, 10);
      const canvas = document.createElement('canvas');
      canvas.width = width * scaleBy;
      canvas.height = height * scaleBy;
      canvas.style.width = `${width}*${remNumberLess}`;
      canvas.style.height = `${height}*${remNumberLess}`;
      // const context = canvas.getContext("2d");
      // context.scale(scaleBy, scaleBy);

      html2canvas(dom, {
        canvas,
        logging: false,
        useCORS: true, // 启用 CORS，防止图片跨域问题
        scale: scaleBy,
      }).then(canvas => {
        let pageData = canvas.toDataURL('image/png');
        let eleLink = document.createElement('a');
        eleLink.href = pageData;
        eleLink.download = '3733对联送祝福';
        // 触发点击
        document.body.appendChild(eleLink);
        eleLink.click();
        // 然后移除
        document.body.removeChild(eleLink);
      });
    },
  },
};
</script>

<style lang="less" scoped>
.lucky-bag-dialog {
  width: 300 * @rem;
  background: transparent;
  overflow: visible;
  .logo-icon {
    background: url(~@/assets/images/25newyear/25newyear-logo1.png) top center
      no-repeat;
    background-size: 176 * @rem 196 * @rem;
    width: 176 * @rem;
    height: 196 * @rem;
    margin: 0 auto;
    position: relative;
    z-index: 3;
  }
  .dialog-content {
    box-sizing: border-box;
    position: relative;
    border-radius: 16 * @rem;
    z-index: 2;
    // padding: 84 * @rem 27 * @rem 0;
    width: 300 * @rem;
    height: 464 * @rem;
    background-color: #fff;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    .title-container {
      position: relative;
      box-sizing: border-box;
      height: 24 * @rem;
      margin: 20 * @rem auto 0;
      .top-title {
        display: flex;
        align-items: center;
        justify-content: center;
        .title {
          position: relative;
          height: 23 * @rem;
          font-weight: 600;
          font-size: 18 * @rem;
          color: #5a2a2a;
          line-height: 23 * @rem;
          text-align: center;
          &::before {
            content: '';
            position: absolute;
            left: -38 * @rem;
            top: 50%;
            transform: translateY(-50%);
            width: 32 * @rem;
            height: 32 * @rem;
            background: url(~@/assets/images/25newyear/bt_left.png) no-repeat;
            background-size: 32 * @rem 32 * @rem;
          }
          &::after {
            content: '';
            position: absolute;
            right: -38 * @rem;
            top: 50%;
            transform: translateY(-50%);
            width: 32 * @rem;
            height: 32 * @rem;
            background: url(~@/assets/images/25newyear/bt_right.png) no-repeat;
            background-size: 32 * @rem 32 * @rem;
          }
        }
      }
    }
    .close-btn {
      width: 13 * @rem;
      height: 13 * @rem;
      background: url('~@/assets/images/25newyear/btn-close.png') center top
        no-repeat;
      background-size: 13 * @rem 13 * @rem;
      position: absolute;
      top: 22 * @rem;
      right: 15 * @rem;
    }
    .dl-cover {
      position: relative;
      margin-top: 21 * @rem;
      .capture {
        width: 238 * @rem;
        height: 326 * @rem;
        .dl-img {
          width: 238 * @rem;
          height: 326 * @rem;
          background: url('~@/assets/images/25newyear/25newyear-bg7.png') center
            top no-repeat;
          background-size: 238 * @rem 326 * @rem;
        }
        .dl-tips {
          position: absolute;
          top: 19 * @rem;
          left: 12 * @rem;
          height: 18 * @rem;
          font-weight: 400;
          font-size: 12 * @rem;
          color: #fffdf1;
          line-height: 18 * @rem;
        }
        .dl-item {
          position: absolute;
          top: 49 * @rem;
          left: 50%;
          transform: translateX(-50%);
          display: flex;
          align-items: center;
          div {
            width: 68 * @rem;
            height: 260 * @rem;
            background: url('~@/assets/images/25newyear/25newyear-icon12.png')
              center top no-repeat;
            background-size: 68 * @rem 260 * @rem;
            display: flex;
            align-items: center;
            justify-content: center;
            &:last-child {
              margin-left: 16 * @rem;
              background: url('~@/assets/images/25newyear/25newyear-icon13.png')
                center top no-repeat;
              background-size: 68 * @rem 260 * @rem;
            }
            span {
              display: flex;
              flex-direction: column;
              width: 20 * @rem;
              font-weight: 600;
              font-size: 20 * @rem;
              color: #fff8e5;
              line-height: 25 * @rem;
              text-align: center;
            }
          }
        }
      }

      .dl-dow {
        position: absolute;
        top: 13 * @rem;
        right: 9 * @rem;
        width: 24 * @rem;
        height: 24 * @rem;
        background: url('~@/assets/images/25newyear/btn-down.png') center top
          no-repeat;
        background-size: 24 * @rem 24 * @rem;
      }
    }
    .share-btn {
      margin-top: 16 * @rem;
      width: 238 * @rem;
      height: 40 * @rem;
      line-height: 40 * @rem;
      background: linear-gradient(90deg, #f64b4b 0%, #ffb07c 100%), #d9d9d9;
      border-radius: 20 * @rem;
      font-weight: 500;
      font-size: 15 * @rem;
      color: #fffdf1;
      text-align: center;
    }
  }
}
</style>
