import { request } from '../index';
/**
 * 开始博饼
 */
export function ApiAutumnDiceBySelf(params = {}) {
  return request('/activity/autumn/diceBySelf', params);
}

/**
 * 兑换博饼
 */
export function ApiAutumnExchangeCount(params = {}) {
  return request('/activity/autumn/exchangeCount', params);
}

/**
 * 获取博饼次数
 */
export function ApiAutumnGetDiceCount(params = {}) {
  return request('/activity/autumn/getDiceCount', params);
}

/**
 * 博饼首页内容
 */
export function ApiAutumnIndex(params = {}) {
  return request('/activity/autumn/index', params);
}

/**
 * 博饼-积分排行榜
 */
export function ApiAutumnRankingList(params = {}) {
  return request('/activity/autumn/rankingList', params);
}

/**
 * 博饼-个人积分记录
 */
export function ApiAutumnUserScore(params = {}) {
  return request('/activity/autumn/userScore', params);
}

/**
 * 博饼-检测是否能兑换
 */
export function ApiAutumnCheckExchange(params = {}) {
  return request('/activity/autumn/checkExchange', params);
}

/**
 * 博饼-领取宝箱积分
 */
export function ApiAutumnGetBoxScore(params = {}) {
  return request('/activity/autumn/getBoxScore', params);
}

/**
 * 博饼-用户信息-积分加排行
 */
export function ApiAutumnGetUserScore(params = {}) {
  return request('/activity/autumn/getUserScore', params);
}
