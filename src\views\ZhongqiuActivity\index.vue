<template>
  <div class="zhongqiu-activity">
    <div class="main-page">
      <!-- back -->
      <div @click="back" class="back"></div>
      <!-- active-axplain -->
      <div @click="active_explain_show = true" class="active-explain">
        活动说明
      </div>
      <!-- active-explain2 -->
      <div class="active-explain2">
        {{ title_text || '状元即可得3733金币，积分冲榜更有金币等你拿' }}
      </div>
      <!-- game -->
      <div class="game">
        <div
          v-for="(item, index) in dice_list"
          :key="index"
          :class="[
            `dice${index}`,
            `res${item}`,
            animation ? `animation${index}` : '',
          ]"
          class="dice"
        ></div>
        <img class="bowl" src="@/assets/images/zhongqiu/bowl.png" />
        <audio
          class="audio"
          ref="audio"
          src="@/assets/images/zhongqiu/bobing.mp3"
        ></audio>
      </div>
      <!-- button-list -->
      <div class="button-list">
        <div
          @click="startBoBing"
          :class="{
            'more': dice_count === 0,
            'no-more': free_exchange_count === 0 && dice_count === 0,
          }"
          class="start"
        ></div>
        <div class="small-text">
          {{
            dice_count === 0 && free_exchange_count !== 0
              ? '用户每天可使用50金币兑换一次博饼机会'
              : ''
          }}
        </div>
        <div class="text">
          今日还剩<span class="count">{{ dice_count }}</span
          >次博饼机会
        </div>
        <div class="bottom">
          <div @click="setShowPtbRechargePopup(true)" class="button left"></div>
          <div @click="openRank" class="button center"></div>
          <div @click="openRankRecord" class="button right"></div>
        </div>
      </div>
      <div class="bg2">
        <div class="award">
          <div class="title"></div>
          <div class="text">累计博饼达到限定次数可获得额外积分奖励</div>
          <div class="progress-container">
            <div class="icon-list">
              <div class="item"></div>
              <div
                v-for="(item, index) in box_list"
                :key="index"
                @click="getBoxScore(item.score, item.status)"
                class="item"
              >
                <div class="award-item">{{ item.desc }}</div>
                <div :class="{ on: item.status }" class="icon"></div>
              </div>
            </div>
            <div class="progress">
              <div
                :style="{ width: `${progress_bar}%` }"
                class="current-progress"
              ></div>
            </div>
            <div class="bottom-list">
              <div class="bottom-item">0</div>
              <div
                v-for="(item, index) in box_list"
                :key="index"
                class="bottom-item"
              >
                {{ item.title }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 文章 -->
      <div class="bg2">
        <div class="article">
          <div class="title title1"></div>
          <p class="small-title">一．活动时间：</p>
          <p class="text last">2022年9月9日00:00:00——2022年9月13日23:59:59</p>
          <p class="small-title">二．活动规则：</p>
          <p class="text">
            1.
            活动期间每位用户每天有1次免费博饼机会，SVIP用户可额外获得1次免费机会。
          </p>
          <p class="text">
            2.
            活动期间每位用户每天可使用50金币兑换一次博饼机会，SVIP用户可额外使用50金币兑换一次机会。
          </p>
          <p class="text">
            3.
            每天首次完成现金充值（包括充值平台币、开通SVIP、游戏内微信、支付宝充值）可额外多得3次机会。一笔订单获得一次机会。
          </p>
          <p class="text">
            温馨提醒：以上首次充值平台币/游戏首次现金充值/首次开通SVIP三种形式，每天各分别只有一次机会。总共三次。
          </p>
          <p class="text">
            4.活动期间，参与博饼可额外获得积分奖励。博饼积分榜实时更新，活动结束后以排行榜成绩进行统一发放奖励。
          </p>
          <p class="text last">
            5.
            参与活动需绑定手机号，同一平台账号，同一手机号，同一身份证号，同一设备均视为同一用户。
          </p>
          <p class="small-title">三．活动奖励：</p>
          <p class="text red">
            博中状元即可获得3733个金币，积分排行榜冲榜更能赢取更多金币
          </p>
          <p class="text">第1名：8888金币</p>
          <p class="text">第2名：6888金币</p>
          <p class="text">第3名：3888金币</p>
          <p class="text">第4-10名：2888金币</p>
          <p class="text">第11-30名：1888金币</p>
          <p class="text">第31-50名：888金币</p>
          <p class="text">参与奖：100金币</p>
          <p class="text last">注：积分相同的用户，则按时间先后排列。</p>
          <p class="small-title">四、奖励发放：</p>
          <p class="text last">
            奖励将在活动结束后3个工作日内统一发放至相应账号中，届时可在金币明细记录中查看，如有异议，请联系平台客服进行咨询。
          </p>
        </div>
      </div>
    </div>
    <!-- 兑换机会 -->
    <div v-if="popup_show" class="popup">
      <div class="container">
        <div class="text">
          确定花费<span class="color">50金币</span>，换取一次博饼机会？
        </div>
        <div class="button-container">
          <div @click="popup_show = false" class="left"></div>
          <div @click="exchangeCount" class="right"></div>
        </div>
      </div>
    </div>
    <!-- svip兑换 -->
    <div v-if="svip_popup_show" class="popup popup-svip">
      <div class="container">
        <div class="text">
          SVIP用户可额外使用<span class="color">50金币</span
          >兑换一次机会,是否要充值SVIP？
        </div>
        <div class="button-container">
          <div @click="svip_popup_show = false" class="left"></div>
          <div @click="setShowSvipRechargePopup(true)" class="right"></div>
        </div>
      </div>
    </div>
    <!-- 宝箱积分弹窗 -->
    <!-- <div v-if="box_popup_show" class="popup popup-box">
      <div class="container">
        <div class="text">
          SVIP用户可额外使用<span class="color">50金币</span
          >兑换一次机会,是否要充值SVIP？
        </div>
        <div class="button-container">
          <div @click="svip_popup_show = false" class="right"></div>
        </div>
      </div>
    </div> -->
    <!-- 排行榜 -->
    <div v-if="rank_show" class="rank-page">
      <div class="page">
        <div class="wrapper bg1">
          <i @click="rank_show = false" class="close"></i>
          <div class="header"></div>
          <ul class="title">
            <li class="rank">排名</li>
            <li class="user">用户</li>
            <li class="point">积分</li>
          </ul>
          <ul class="rank-list" id="ranking_list">
            <li v-for="(item, index) in rank_list" class="rank-item">
              <div class="rank">
                <div class="circle">{{ index + 1 }}</div>
              </div>
              <div class="user">{{ item.nickname }}</div>
              <div class="point">{{ item.score_total }}</div>
            </li>
          </ul>
        </div>
        <div class="footer">
          <UserAvatar :src="userInfo.avatar" class="avatar self_avatar" />
          <div class="content">
            <div class="point">
              我的积分:<span class="red self_total_score">{{
                user_rank.score
              }}</span>
            </div>
          </div>
          <div class="right-wrapper">
            我的排名:<span class="red self_ranking_top">{{
              user_rank.rank
            }}</span>
          </div>
        </div>
      </div>
    </div>
    <!-- 活动说明 -->
    <div v-if="active_explain_show" class="active-explain-page">
      <div class="main bg1">
        <i @click="active_explain_show = false" class="close"></i>
        <div class="article">
          <div class="title title1"></div>
          <p class="small-title">一．活动时间：</p>
          <p class="text last">2022年9月9日00:00:00——2022年9月13日23:59:59</p>
          <p class="small-title">二．活动规则：</p>
          <p class="text">
            1.
            活动期间每位用户每天有1次免费博饼机会，SVIP用户可额外获得1次免费机会。
          </p>
          <p class="text">
            2.
            活动期间每位用户每天可使用50金币兑换一次博饼机会，SVIP用户可额外使用50金币兑换一次机会。
          </p>
          <p class="text">
            3.
            每天首次完成现金充值（包括充值平台币、开通SVIP、游戏内微信、支付宝充值）可额外多得3次机会。一笔订单获得一次机会。
          </p>
          <p class="text">
            温馨提醒：以上首次充值平台币/游戏首次现金充值/首次开通SVIP三种形式，每天各分别只有一次机会。总共三次。
          </p>
          <p class="text">
            4.活动期间，参与博饼可额外获得积分奖励。博饼积分榜实时更新，活动结束后以排行榜成绩进行统一发放奖励。
          </p>
          <p class="text last">
            5.
            参与活动需绑定手机号，同一平台账号，同一手机号，同一身份证号，同一设备均视为同一用户。
          </p>
          <p class="small-title">三．活动奖励：</p>
          <p class="text red">
            博中状元即可获得3733个金币，积分排行榜冲榜更能赢取更多金币
          </p>
          <p class="text">第1名：8888金币</p>
          <p class="text">第2名：6888金币</p>
          <p class="text">第3名：3888金币</p>
          <p class="text">第4-10名：2888金币</p>
          <p class="text">第11-30名：1888金币</p>
          <p class="text">第31-50名：888金币</p>
          <p class="text">参与奖：100金币</p>
          <p class="text last">注：积分相同的用户，则按时间先后排列。</p>
          <p class="small-title">四、奖励发放：</p>
          <p class="text last">
            奖励将在活动结束后3个工作日内统一发放至相应账号中，届时可在金币明细记录中查看，如有异议，请联系平台客服进行咨询。
          </p>
          <div class="title title2"></div>
          <img
            src="https://static.pic3733.com/activity/midautumn/point.png"
            alt=""
            class="explain"
          />
        </div>
      </div>
    </div>
    <!-- 积分记录 -->
    <div v-if="rank_record_show" class="rank-record-page">
      <div class="page">
        <div class="wrapper bg1">
          <i @click="rank_record_show = false" class="close"></i>
          <div class="header"></div>
          <div class="rank-record">
            <div v-if="rank_record.length === 0" class="empty">
              暂无积分获取记录
            </div>
            <div
              v-else
              v-for="(item, index) in rank_record"
              :key="index"
              class="rank-item"
            >
              <div class="left">{{ item.type }}</div>
              <div class="right">
                <span class="red">+{{ item.score }}</span>
                积分
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <PtbRechargePopup @success="initData" />
    <SvipRechargePopup @success="initData" />
  </div>
</template>

<script>
import PtbRechargePopup from '@/components/ptb-recharge-popup';
import SvipRechargePopup from '@/components/svip-recharge-popup';
import UserAvatar from '@/components/user-avatar';
import {
  ApiAutumnDiceBySelf,
  ApiAutumnExchangeCount,
  ApiAutumnGetDiceCount,
  ApiAutumnCheckExchange,
  ApiAutumnGetBoxScore,
  ApiAutumnGetUserScore,
  ApiAutumnIndex,
  ApiAutumnRankingList,
  ApiAutumnUserScore,
} from '@/api/views/autumn.js';
import { mapGetters, mapMutations } from 'vuex';

export default {
  name: 'Zhongqiu',
  data() {
    return {
      rank_show: false, //排行榜显示
      active_explain_show: false, //活动说明显示
      rank_record_show: false, //积分记录显示
      popup_show: false, //消息弹窗显示
      svip_popup_show: false, //svip弹窗显示
      animation: false, //是否开启动画
      dice_list: [2, 5, 4, 1, 3, 6], // 骰子数组
      title_text: '', //标题文案
      dice_count: 0, //今日剩余次数
      rank_list: [], //排行榜列表
      rank_record: [], //积分记录列表
      user_rank: {}, //用户积分排名
      box_list: [
        {
          title: '10',
          score: 50,
          status: 0,
        },
        {
          title: '20',
          score: 100,
          status: 0,
        },
        {
          title: '30',
          score: 200,
          status: 0,
        },
      ], //宝箱数据
      user_dice_count: 0, //该用户总博饼次数
      free_exchange_count: 0, //可兑换次数
      ajaxing: false, //防抖
      progress_bar: 0, //进度条
    };
  },
  computed: {
    ...mapGetters({
      userInfo: 'user/userInfo',
    }),
  },
  async created() {
    await this.initData();
  },
  methods: {
    // init
    async initData() {
      const res = await ApiAutumnIndex();
      this.title_text = res.data.text;
      this.dice_count = parseInt(res.data.dice_count);
      this.user_dice_count = parseInt(res.data.user_dice_count);
      this.free_exchange_count = parseInt(res.data.free_exchange_count);
      this.progress_bar = parseFloat(res.data.progress_bar);
      this.box_list = [];
      this.box_list.push(...res.data.box_list);
    },
    // 兑换博饼次数
    async exchangeCount() {
      const res = await ApiAutumnExchangeCount();
      this.$toast(res.msg);
      this.popup_show = false;
      this.startBoBing();
    },
    // 打开排行榜
    async openRank() {
      const toast1 = this.$toast.loading({
        duration: 0,
      });
      try {
        const res = await ApiAutumnRankingList();
        const res2 = await ApiAutumnGetUserScore();
        this.user_rank = res2.data;
        this.rank_list = res.data.list;
        this.rank_show = true;
      } finally {
        toast1.clear();
      }
    },
    // 打开积分记录
    async openRankRecord() {
      const toast1 = this.$toast.loading({
        duration: 0,
      });
      try {
        const res = await ApiAutumnUserScore();
        this.rank_record = res.data.info;
        this.rank_record_show = true;
      } finally {
        toast1.clear();
      }
    },
    // 获取宝箱积分
    async getBoxScore(score, status) {
      if (!parseInt(status)) {
        return false;
      }
      const res = await ApiAutumnGetBoxScore({ score: score });
      this.$toast(res.msg);
      await this.initData();
    },
    // 博饼动画
    boBingAnimation() {
      this.animation = true;
    },
    // 重置动画、状态等
    reset() {
      this.animation = false;
      this.dice_list.length = 0;
      this.dice_list.push(...[2, 5, 4, 1, 3, 6]);
    },
    // 处理博饼
    async startBoBing() {
      await this.initData();
      // 判断次数是否使用完
      if (this.dice_count) {
        if (this.ajaxing) return false;
        this.ajaxing = true;
        const toast1 = this.$toast.loading({
          duration: 0,
        });
        try {
          const res = await ApiAutumnDiceBySelf();
          await this.initData();
          this.dice_list.length = 0;
          this.animation = true;
          setTimeout(() => {
            this.$refs['audio'].play();
          }, 100);
          this.dice_list.push(...res.data.arr);
        } finally {
          setTimeout(() => {
            this.animation = false;
            this.ajaxing = false;
          }, 2000);
        }
      }
      // 使用完的话
      else {
        // 是否能继续兑换
        if (this.free_exchange_count >= 1) {
          this.popup_show = true;
        } else {
          // 如果不是svip用户
          if (!this.userInfo.is_svip) {
            this.svip_popup_show = true;
          } else {
            this.$toast('今日兑换次数已经用完，请明日再来');
          }
        }
      }
    },
    ...mapMutations({
      setShowPtbRechargePopup: 'recharge/setShowPtbRechargePopup',
      setShowSvipRechargePopup: 'recharge/setShowSvipRechargePopup',
    }),
  },
  components: {
    PtbRechargePopup,
    SvipRechargePopup,
    UserAvatar,
  },
};
</script>

<style lang="less" scoped>
.zhongqiu-activity {
  position: relative;
  min-height: 100vh;
  background-image: url('~@/assets/images/zhongqiu/<EMAIL>');
  background-size: 100%;
  background-color: #3d6646;
  background-repeat: no-repeat;
}
.main-page {
  position: relative;
  display: block;
  font-size: 0.3204rem;
  overflow: hidden;
}
.back {
  position: fixed;
  top: @safeAreaTop;
  top: @safeAreaTopEnv;
  z-index: 999;
  left: 0;
  width: 30 * @rem;
  height: 50 * @rem;
  background: url(~@/assets/images/nav-bar-back-black.png) center center
    no-repeat;
  background-size: 10 * @rem 18 * @rem;
}
.main-page .active-explain {
  position: absolute;
  top: calc(0.267rem + @safeAreaTop);
  top: calc(0.267rem + @safeAreaTopEnv);
  right: 0.267rem;
  width: 2.0025rem;
  height: 0.801rem;
  text-align: center;
  line-height: 0.801rem;
  border-radius: 12.5px;
  background: #fff;
  font-size: 0.3738rem;
  color: #9e3448;
  font-weight: bold;
}
.main-page .active-explain2 {
  width: 330 * @rem;
  height: 28 * @rem;
  margin: 208 * @rem auto 55 * @rem;
  text-align: center;
  line-height: 0.801rem;
  border-radius: 3px;
  background: url(~@/assets/images/zhongqiu/bg1.png);
  background-size: 100%;
  font-size: 0.3204rem;
  color: #f7eb06;
}
.main-page {
  .bg2 {
    position: relative;
    box-sizing: border-box;
    width: 355 * @rem;
    margin: 52 * @rem auto;
    padding: 0 20 * @rem;
    background-size: 355 * @rem 20 * @rem;
    background-image: url(~@/assets/images/zhongqiu/popup-bg2.png);
    &::before {
      content: '';
      position: absolute;
      top: -34 * @rem;
      left: 0 * @rem;
      width: 355 * @rem;
      height: 35 * @rem;
      background-size: 100%;
      background-image: url(~@/assets/images/zhongqiu/popup-radius-top2.png);
      background-repeat: no-repeat;
    }
    &::after {
      content: '';
      position: absolute;
      bottom: -36 * @rem;
      left: 0 * @rem;
      width: 355 * @rem;
      height: 36 * @rem;
      background-size: 100%;
      background-image: url(~@/assets/images/zhongqiu/popup-radius-bottom2.png);
      background-repeat: no-repeat;
    }
  }
}
.main-page .game {
  position: relative;
  width: 100%;
  margin: 0 auto;
}
.main-page .game .bowl {
  width: 100%;
  height: auto;
}
.main-page .game .dice {
  position: absolute;
  width: 52 * @rem;
  height: 52 * @rem;
  background-image: url(~@/assets/images/zhongqiu/dice.png);
  background-repeat: no-repeat;
  background-size: 50 * @rem;
  overflow: hidden;
}
.main-page .game .dice.dice0 {
  top: 91 * @rem;
  left: 112 * @rem;
  background-position: 0 0;
}
.main-page .game .dice.dice1 {
  top: 83 * @rem;
  left: 168 * @rem;
  background-position: 0 -52 * @rem;
}
.main-page .game .dice.dice2 {
  top: 91 * @rem;
  left: 222 * @rem;
  background-position: 0 -104 * @rem;
}
.main-page .game .dice.dice3 {
  top: 133 * @rem;
  left: 104 * @rem;
  background-position: 0 -156 * @rem;
}
.main-page .game .dice.dice4 {
  top: 134 * @rem;
  left: 165 * @rem;
  background-position: 0 -208 * @rem;
}
.main-page .game .dice.dice5 {
  top: 130 * @rem;
  left: 222 * @rem;
  background-position: 0 -260 * @rem;
}
.main-page .game .dice.res1 {
  background-position: 0 0;
}
.main-page .game .dice.res2 {
  background-position: 0 -52 * @rem;
}
.main-page .game .dice.res3 {
  background-position: 0 -104 * @rem;
}
.main-page .game .dice.res4 {
  background-position: 0 -156 * @rem;
}
.main-page .game .dice.res5 {
  background-position: 0 -210 * @rem;
}
.main-page .game .dice.res6 {
  background-position: 0 -262 * @rem;
}
.main-page .game .dice.animation0 {
  animation: rotateAnimation1 0.5s linear forwards;
  animation-iteration-count: 1;
}
.main-page .game .dice.animation1 {
  animation: rotateAnimation2 0.5s linear forwards;
}
.main-page .game .dice.animation2 {
  animation: rotateAnimation3 0.5s linear forwards;
}
.main-page .game .dice.animation3 {
  animation: rotateAnimation4 0.5s linear forwards;
}
.main-page .game .dice.animation4 {
  animation: rotateAnimation5 0.5s linear forwards;
}
.main-page .game .dice.animation5 {
  animation: rotateAnimation6 0.5s linear forwards;
}
.main-page .button-list {
  overflow: hidden;
}
.main-page .button-list .start {
  width: 222 * @rem;
  height: 67.5 * @rem;
  margin: 0 auto;
  background-image: url(~@/assets/images/zhongqiu/button1.png);
  background-size: 100%;
  &.more {
    background-image: url(~@/assets/images/zhongqiu/button2.png);
  }
  &.no-more {
    background-image: url(~@/assets/images/zhongqiu/button3.png);
  }
  &.no-change {
    background-image: url(~@/assets/images/zhongqiu/button4.png);
  }
}
.main-page .button-list .text {
  width: 216 * @rem;
  height: 30 * @rem;
  line-height: 30 * @rem;
  background-color: rgba(0, 0, 0, 0.4);
  border-radius: 30 * @rem;
  margin: 0 auto 21 * @rem;
  text-align: center;
  font-size: 16 * @rem;
  color: #ffffff;
  font-family: PingFang SC-Semibold, PingFang SC;
}
.main-page .button-list .text span {
  margin: 0 3 * @rem;
  color: #f94f57;
}
.main-page .button-list .small-text {
  height: 15 * @rem;
  margin: 6 * @rem 0;
  text-align: center;
  font-size: 12 * @rem;
  color: #ffb701;
}
.main-page .button-list .bottom {
  display: flex;
  justify-content: space-between;
  padding: 0 25 * @rem;
}
.main-page .button-list .bottom .button {
  float: left;
  width: 100 * @rem;
  height: 38.5 * @rem;
  background-size: 100%;
}
.main-page .button-list .bottom .button.left {
  background-image: url(~@/assets/images/zhongqiu/button6.png);
}
.main-page .button-list .bottom .button.center {
  background-image: url(~@/assets/images/zhongqiu/button7.png);
}
.main-page .button-list .bottom .button.right {
  background-image: url(~@/assets/images/zhongqiu/button5.png);
}
.award {
  margin-bottom: 90 * @rem;
  .title {
    width: 242 * @rem;
    height: 22.5 * @rem;
    margin: 0 auto;
    background-image: url(~@/assets/images/zhongqiu/title1.png);
    background-size: 100%;
  }
  .text {
    margin: 20 * @rem 0 30 * @rem;
    font-size: 12 * @rem;
    font-family: PingFang SC-Semibold, PingFang SC;
    font-weight: 600;
    color: #663f3d;
  }
  .icon-list {
    display: flex;
    justify-content: space-between;
    width: 278 * @rem;
    margin: 0 auto;
    .item {
      width: 55 * @rem;
    }
    .award-item {
      margin-bottom: 5 * @rem;
      text-align: center;
      font-size: 10 * @rem;
      color: #ff7c03;
    }
    .icon {
      display: block;
      width: 30 * @rem;
      height: 30 * @rem;
      margin: 0 auto;
      background-size: 100%;
      background-image: url(~@/assets/images/zhongqiu/icon1.png);
      &.on {
        background-image: url(~@/assets/images/zhongqiu/icon2.png);
      }
      &.icon0 {
        background: none;
      }
    }
  }
  .progress {
    margin: 5 * @rem auto 10 * @rem;
    width: 278 * @rem;
    height: 8 * @rem;
    background: #e7d199;
    border-radius: 16 * @rem 16 * @rem 16 * @rem 16 * @rem;
    .current-progress {
      height: 100%;
      background: #ff7c03;
      border-radius: 16 * @rem 16 * @rem 16 * @rem 16 * @rem;
      opacity: 1;
    }
  }
  .bottom-list {
    display: flex;
    justify-content: space-between;
    width: 278 * @rem;
    margin: 0 auto;
    .bottom-item {
      width: 55 * @rem;
      text-align: center;
    }
  }
}

.rank-page {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  .page {
    position: absolute;
    top: 50%;
    left: 50%;
    height: auto;
    transform: translate(-50%, -50%);
    .wrapper {
      position: relative;
      background-size: 100%;
      background-image: url(~@/assets/images/zhongqiu/popup-bg.png);
      .header {
        width: 207 * @rem;
        height: 22.5 * @rem;
        margin: 0 auto 10 * @rem;
        background-image: url(~@/assets/images/zhongqiu/title4.png);
        background-size: 100%;
      }
      .title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 313 * @rem;
        height: 28.5 * @rem;
        box-sizing: border-box;
        margin: 0 auto;
        padding: 0 10 * @rem;
        background: #ffe0b4;
        border-radius: 6px;
        font-size: 14 * @rem;

        color: #66493d;
        .rank,
        .point {
          flex: 0 0 30 * @rem;
        }
        .rank {
          margin-right: 12 * @rem;
        }
        .user {
          flex: 1;
          text-align: left;
        }
        .point {
          text-align: right;
        }
      }
      .rank-list {
        height: 346 * @rem;
        overflow-y: scroll;
        .rank-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          box-sizing: border-box;
          width: 313 * @rem;
          height: 35 * @rem;
          margin: 0 auto;
          padding: 0 10 * @rem;
          .rank {
            .circle {
              width: 16 * @rem;
              height: 16 * @rem;
              background: #ffdba8;
              margin-right: 12 * @rem;
              text-align: center;
              border-radius: 50%;
              font-size: 12 * @rem;
              font-family: PingFang SC-Semibold, PingFang SC;
              font-weight: 600;
              color: #66493d;
            }
          }
          .user {
            flex: 1;
            text-align: left;
            font-size: 14 * @rem;
            font-family: PingFang SC-Semibold, PingFang SC;
            font-weight: 600;
            color: #66493d;
          }
          .rank,
          .point {
            flex: 0 0 30 * @rem;
          }
          .point {
            text-align: right;
            font-size: 14 * @rem;
            font-family: PingFang SC-Semibold, PingFang SC;
            font-weight: 600;
            color: #66493d;
          }
        }
      }
    }
  }
  .page {
    .footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 345 * @rem;
      height: 62 * @rem;
      box-sizing: border-box;
      margin-top: 40 * @rem;
      padding: 0 22 * @rem;
      font-size: 14 * @rem;

      color: #66493d;
      background-image: url(~@/assets/images/zhongqiu/popup-bg3.png);
      background-size: 100%;
      .avatar {
        display: block;
        width: 32 * @rem;
        height: 32 * @rem;
        margin-right: 0.267rem;
        border: 1px solid #eee;
        border-radius: 50%;
        overflow: hidden;
      }
      .content {
        flex: 1;
      }
      .red {
        color: #f00;
      }
    }
  }
}
// 活动说明相关
.active-explain-page {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  .main {
    position: absolute;
    top: 175 * @rem;
    left: 50%;
    z-index: 1000;
    transform: translate(-50%, 0);
    box-sizing: border-box;
    width: 345 * @rem;
    padding: 0 20 * @rem;
  }
}

// 背景1
.bg1 {
  position: relative;
  background-size: 345 * @rem 5.5 * @rem;
  background-image: url(~@/assets/images/zhongqiu/popup-bg.png);
  &::before {
    content: '';
    position: absolute;
    top: -24 * @rem;
    left: 0 * @rem;
    width: 345 * @rem;
    height: 25 * @rem;
    background-size: 100%;
    background-image: url(~@/assets/images/zhongqiu/popup-radius-top.png);
    background-repeat: no-repeat;
  }
  &::after {
    content: '';
    position: absolute;
    bottom: -24 * @rem;
    left: 0 * @rem;
    width: 345 * @rem;
    height: 25 * @rem;
    background-size: 100%;
    background-image: url(~@/assets/images/zhongqiu/popup-radius-bottom.png);
    background-repeat: no-repeat;
  }
  .close {
    position: absolute;
    top: -65 * @rem;
    right: 0;
    display: block;
    width: 25 * @rem;
    height: 25 * @rem;
    background-image: url('~@/assets/images/zhongqiu/<EMAIL>');
    background-size: 100%;
    background-repeat: no-repeat;
  }
}
// 说明文
.article {
  p {
    line-height: 20 * @rem;
    font-size: 16 * @rem;
    color: #66493d;
  }
  .text {
    margin: 11 * @rem 0 11 * @rem 0.3204rem;
    &.red {
      font-weight: 600;
      color: #db4b01;
    }
    &.last {
      margin-bottom: 0.2136rem;
    }
  }
  .title {
    width: 207 * @rem;
    height: 22.5 * @rem;
    margin: 2 * @rem auto 20 * @rem;
    background-size: 100%;
    background-repeat: no-repeat;
    &.title1 {
      background-image: url(~@/assets/images/zhongqiu/title2.png);
    }
    &.title2 {
      background-image: url(~@/assets/images/zhongqiu/title6.png);
    }
  }
  .small-title {
    margin-top: 20 * @rem;
    font-size: 16 * @rem;
    font-family: PingFang SC-Semibold, PingFang SC;
    font-weight: 600;
    color: #532525;
  }
  .explain {
    width: 100%;
    margin-bottom: 0.267rem;
  }
}
.rank-record-page {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  .page {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1000;
    width: 9.2115rem;
    height: auto;
    .wrapper {
      position: relative;
      background-size: 100%;
      background-image: url(~@/assets/images/zhongqiu/popup-bg.png);
    }
    .header {
      width: 207 * @rem;
      height: 22.5 * @rem;
      margin: 0 auto 10 * @rem;
      background-image: url(~@/assets/images/zhongqiu/title3.png);
      background-size: 100%;
    }
    .rank-record {
      height: 166 * @rem;
      overflow-y: scroll;
      .empty {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        width: 3.3375rem;
        height: 0.4272rem;
        margin: auto;
        font-size: 14 * @rem;
        color: #66493d;
      }
      .rank-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 35 * @rem;
        padding: 0 25 * @rem;
        .left {
          flex: 0 0 48%;
          font-size: 16 * @rem;
          color: #66493d;
        }
        .right {
          flex: 0 0 48%;
          font-size: 16 * @rem;
          color: #66493d;
          text-align: right;
          .red {
            font-weight: 600;
            color: #ff4c00;
          }
        }
      }
    }
  }
}
.popup {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  .container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 335 * @rem;
    height: 189 * @rem;
    box-sizing: border-box;
    padding: 40 * @rem 0;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    background: #ffefd8;
    border-radius: 24 * @rem;
    border: 4 * @rem solid #f6b039;
    .text {
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-wrap: wrap;
      font-size: 16 * @rem;
      color: #66493d;
      line-height: 20 * @rem;
      .color {
        color: #ff4c00;
      }
    }
    .button-container {
      display: flex;
      justify-content: center;
      margin-top: 12 * @rem;
      .left {
        width: 100 * @rem;
        height: 38.5 * @rem;
        margin-right: 7.5 * @rem;
        background-size: 100%;
        background-repeat: no-repeat;
        background-image: url(~@/assets/images/zhongqiu/button9.png);
      }
      .right {
        width: 100 * @rem;
        height: 38.5 * @rem;
        margin-left: 7.5 * @rem;
        background-size: 100%;
        background-repeat: no-repeat;
        background-image: url(~@/assets/images/zhongqiu/button10.png);
      }
    }
  }
}

@keyframes rotateAnimation1 {
  0% {
    top: -52 * @rem;
    left: 145 * @rem;
    transform: rotate(0deg);
    -ms-transform: rotate(0deg);
    -moz-transform: rotate(0deg);
    -webkit-transform: rotate(0deg);
    -o-transform: rotate(0deg);
  }
  50% {
    top: 91 * @rem;
    left: 145 * @rem;
    transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    -moz-transform: rotate(360deg);
    -webkit-transform: rotate(360deg);
    -o-transform: rotate(360deg);
  }
  100% {
    top: 91 * @rem;
    left: 112 * @rem;
    transform: rotate(720deg);
    -ms-transform: rotate(720deg);
    -moz-transform: rotate(720deg);
    -webkit-transform: rotate(720deg);
    -o-transform: rotate(720deg);
  }
}
@keyframes rotateAnimation2 {
  0% {
    top: -52 * @rem;
    left: 155 * @rem;
    transform: rotate(0deg);
    -ms-transform: rotate(0deg);
    -moz-transform: rotate(0deg);
    -webkit-transform: rotate(0deg);
    -o-transform: rotate(0deg);
  }
  50% {
    top: 91 * @rem;
    left: 155 * @rem;
    transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    -moz-transform: rotate(360deg);
    -webkit-transform: rotate(360deg);
    -o-transform: rotate(360deg);
  }
  100% {
    top: 83 * @rem;
    left: 168 * @rem;
    transform: rotate(720deg);
    -ms-transform: rotate(720deg);
    -moz-transform: rotate(720deg);
    -webkit-transform: rotate(720deg);
    -o-transform: rotate(720deg);
  }
}
@keyframes rotateAnimation3 {
  0% {
    top: -52 * @rem;
    left: 165 * @rem;
    transform: rotate(0deg);
    -ms-transform: rotate(0deg);
    -moz-transform: rotate(0deg);
    -webkit-transform: rotate(0deg);
    -o-transform: rotate(0deg);
  }
  50% {
    top: 91 * @rem;
    left: 165 * @rem;
    transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    -moz-transform: rotate(360deg);
    -webkit-transform: rotate(360deg);
    -o-transform: rotate(360deg);
  }
  100% {
    top: 91 * @rem;
    left: 222 * @rem;
    transform: rotate(720deg);
    -ms-transform: rotate(720deg);
    -moz-transform: rotate(720deg);
    -webkit-transform: rotate(720deg);
    -o-transform: rotate(720deg);
  }
}
@keyframes rotateAnimation4 {
  0% {
    top: -62 * @rem;
    left: 145 * @rem;
    transform: rotate(0deg);
    -ms-transform: rotate(0deg);
    -moz-transform: rotate(0deg);
    -webkit-transform: rotate(0deg);
    -o-transform: rotate(0deg);
  }
  50% {
    top: 91 * @rem;
    left: 145 * @rem;
    transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    -moz-transform: rotate(360deg);
    -webkit-transform: rotate(360deg);
    -o-transform: rotate(360deg);
  }
  100% {
    top: 133 * @rem;
    left: 104 * @rem;
    transform: rotate(720deg);
    -ms-transform: rotate(720deg);
    -moz-transform: rotate(720deg);
    -webkit-transform: rotate(720deg);
    -o-transform: rotate(720deg);
  }
}
@keyframes rotateAnimation5 {
  0% {
    top: -57 * @rem;
    left: 155 * @rem;
    transform: rotate(0deg);
    -ms-transform: rotate(0deg);
    -moz-transform: rotate(0deg);
    -webkit-transform: rotate(0deg);
    -o-transform: rotate(0deg);
  }
  50% {
    top: 91 * @rem;
    left: 155 * @rem;
    transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    -moz-transform: rotate(360deg);
    -webkit-transform: rotate(360deg);
    -o-transform: rotate(360deg);
  }
  100% {
    top: 134 * @rem;
    left: 165 * @rem;
    transform: rotate(720deg);
    -ms-transform: rotate(720deg);
    -moz-transform: rotate(720deg);
    -webkit-transform: rotate(720deg);
    -o-transform: rotate(720deg);
  }
}
@keyframes rotateAnimation6 {
  0% {
    top: -57 * @rem;
    left: 160 * @rem;
    transform: rotate(0deg);
    -ms-transform: rotate(0deg);
    -moz-transform: rotate(0deg);
    -webkit-transform: rotate(0deg);
    -o-transform: rotate(0deg);
  }
  50% {
    top: 91 * @rem;
    left: 160 * @rem;
    transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    -moz-transform: rotate(360deg);
    -webkit-transform: rotate(360deg);
    -o-transform: rotate(360deg);
  }
  100% {
    top: 130 * @rem;
    left: 222 * @rem;
    transform: rotate(720deg);
    -ms-transform: rotate(720deg);
    -moz-transform: rotate(720deg);
    -webkit-transform: rotate(720deg);
    -o-transform: rotate(720deg);
  }
}
</style>
