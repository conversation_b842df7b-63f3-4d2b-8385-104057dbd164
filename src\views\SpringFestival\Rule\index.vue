<template>
  <div class="sf-rule">
    <nav-bar-2
      ref="topNavBar"
      bgStyle="transparent-white"
      :azShow="true"
      :title="'活动规则'"
      :placeholder="false"
      v-if="navBgTransparent"
    ></nav-bar-2>
    <nav-bar-2
      :title="'活动规则'"
      :placeholder="false"
      :border="true"
      :azShow="true"
      v-else
    >
    </nav-bar-2>
    <main>
      <div class="text">
        爆竹声声辞旧岁，合家欢乐迎新春。春节期间，参与活动不仅可以获得各种红包奖励，还有机会触发天降福星，额外获取一份超级豪礼。让您新的一年福气满满。
      </div>
      <div class="text color">活动时间：1月20日0点0分-1月27日23点59分</div>
      <div class="title">一．辞旧迎新送红包</div>
      <div class="text">
        除夕跨年夜1月21日23点-22日1点免费领跨年红包（开启获得168、888金币其中一个）。限量500份大年初一1月22日11点-13点免费领拜年红包（开启获得268、888金币其中一个），限量500份。<br />1月27日9点-10点，免费领奋斗红包（开启获得188、888金币其中一个），限量500份。<br />数量有限，先到先得。
      </div>
      <div class="title">二．新春超值回馈</div>
      <div class="text">
        活动期间，每日任意游戏实付大于10元，且累计达到5天，可免费领取388金币+7天SVIP。（活动期间每个用户仅可参与一次）<br />
        温馨提示：<span class="color">仅限游戏内使用微信/支付宝充值</span
        >，如有使用金币/平台币/代金券抵扣的，则只计算抵扣后的现金充值金额。
      </div>
      <div class="title">三．集爆竹领红包</div>
      <div class="text">
        爆竹数量达到8个，可获得新春快乐红包（开启获得88金币）<br />
        爆竹数量达到18个，可获得大吉大利红包（开启获得188、588、888金币其中一个）<br />
        爆竹数量达到38个，可获得万事如意红包（开启获得388、888、1888金币其中一个）<br />
        爆竹数量达到68个，可获得心想事成红包（开启可获得688金币、68平台币、7天SVIP其中一个）<br />
        爆竹数量达到88个，可获得恭喜发财红包（开启可获得1888金币、188平台币、15天SVIP其中一个）<br />
        爆竹数量达到138个，可获得福气满满红包（开启可获得8888金币、888平台币、福卡其中一个）
      </div>
      <div class="text color">
        福卡奖励：凭卡可在活动期间联系客服申请一张5折代金券（仅限BT游戏）
      </div>
      <div class="text">
        开启以上红包均有机会触发祝福：天降福星。额外获得超级豪礼一份（包含18888金币+888平台币+福卡一张）。（每个用户仅能触发一次）
      </div>
      <div class="title color">爆竹获取：</div>
      <div class="text">
        每日登陆游戏可免费领取爆竹1个。（每日限一次）<br />
        每日任意实付大于等于1元可领取爆竹2个。（每日限一次）<br />
        SVIP大于30天的用户可免费领取爆竹2个。（每日限一次）<br />
        每日游戏充值实付大于等于100元可领取爆竹5个。（每日限一次）<br />
        每日游戏充值实付大于等于300元可领取爆竹10个。（每日限一次）<br />
        活动期间累计实付达到100元可领取爆竹5个。（活动期间仅一次）<br />
        活动期间累计实付达到500元可领取爆竹10个。（活动期间仅一次）<br />
        活动期间累计实付达到1000元可领取爆竹15个。（活动期间仅一次）<br />
        活动期间累计实付达到2000元可领取爆竹20个。（活动期间仅一次）<br />
        温馨提示：<span class="color">仅限游戏内使用微信/支付宝充值</span
        >，如有使用金币/平台币/代金券抵扣的，则只计算抵扣后的现金充值金额。
      </div>
      <div class="title">四．活动规则</div>
      <div class="text">
        1. 概率公示：<br />
        跨年红包：168金币（90%）、888金币（10%）<br />
        拜年红包：268金币（90%）、888金币（10%）<br />
        奋斗红包：188金币（90%）、888金币（10%）<br />
        大吉大利红包：188金币（85%）、588金币（10%）、888金币（5%）<br />
        万事如意红包：388金币（85%）、888金币（10%）、1888金币（5%）<br />
        心想事成红包：688金币（70%）、68平台币（10%）、7天SVIP（20%）<br />
        恭喜发财红包：1888金币（70%）、188平台币（15%）、15天SVIP（15%）<br />
        福气满满红包：8888金币（70%）、888平台币（20%）、福卡（10%）<br />
        天降福星概率：1%。
      </div>
      <div class="text">
        2.活动期间充值完成后请返回本活动页面点击<span class="color"
          >【刷新】</span
        >领取爆竹，并及时兑换奖励，活动结束后将清空所有爆竹和红包奖励。
      </div>
      <div class="text">
        3.温馨提示：由于游戏厂商的要求，部分游戏内充值暂不支持使用金币支付，请先确定需要用金币充值的游戏在不在可使用名单内。<span
          @click="game_dialog_show = true"
          class="color btn"
          >查看名单></span
        >
      </div>
    </main>
    <!-- 查看名单 -->
    <van-dialog
      v-model="game_dialog_show"
      :lock-scroll="false"
      :show-confirm-button="false"
      :close-on-click-overlay="true"
      class="no-gold-game-popup"
    >
      <div class="search-container">
        <div class="close-search" @click="game_dialog_show = false"></div>
        <div class="search-bar">
          <div class="input-text">
            <form @submit.prevent="searchGame">
              <input
                type="text"
                v-model.trim="input_game"
                placeholder="输入游戏名"
              />
            </form>
          </div>
          <div class="search-btn" @click="searchGame">搜索</div>
        </div>
        <yy-list
          class="yy-list game-list"
          v-model="loading_obj"
          :finished="finished"
          @refresh="onRefresh"
          @loadMore="loadMore"
          :empty="!game_list.length"
          tips="请输入您想搜索的游戏"
          :check="false"
        >
          <div
            class="game-item btn"
            v-for="item in game_list"
            :key="item.id"
            @click="toGame(item)"
          >
            <div class="game-icon">
              <img :src="item.titlepic" alt="" />
            </div>
            <div class="right">
              <div class="game-name">{{ item.title }}</div>
              <div class="use-card" :class="{ can: item.use_gold_pay == 1 }">
                {{ item.use_gold_pay == 1 ? '' : '不' }}支持使用金币支付
              </div>
            </div>
          </div>
        </yy-list>
      </div>
    </van-dialog>
  </div>
</template>
<script>
import { ApiGameCardSearchGame } from '@/api/views/recharge.js';
import { BOX_goToGame } from '@/utils/box.uni.js';

export default {
  name: 'Rule',
  data() {
    return {
      game_list: [], //查看的游戏名单
      loading_obj: {
        loading: false,
        reloading: false,
      }, //loading对象
      input_game: '', //search的input框
      page: 1, //查看名单的页码
      listRows: 20, //查看名单的大小
      empty: false, //查看名单的空
      game_dialog_show: false, //查看名单弹窗
      finished: false, //防抖
      navBgTransparent: true,
    };
  },
  mounted() {
    // 获取顶部导航栏的高度
    this.stickyOffsetTop =
      document.querySelector('.van-nav-bar').offsetHeight + 'px';
    window.addEventListener('scroll', this.handleScroll);
  },
  methods: {
    // 滚动处理
    handleScroll(e) {
      let scrollTop = e.target.scrollingElement.scrollTop;
      if (scrollTop > 50) {
        this.navBgTransparent = false;
      } else {
        this.navBgTransparent = true;
      }
    },
    // 搜索不可使用金币的游戏
    async searchGame() {
      if (!this.input_game) {
        this.$toast('请输入游戏名');
        return false;
      }
      this.$toast({
        type: 'loading',
        duration: 0,
        message: '拼命加载中...',
      });
      this.game_list = [];
      await this.getGameList();
    },
    async getGameList(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      let res = await ApiGameCardSearchGame({
        keyword: this.input_game,
        listRows: 20,
        page: this.page,
      });
      this.$toast.clear();
      let { list } = res.data;
      if (action === 1 || this.page === 1) {
        this.game_list = [];
      }
      if (!list.length) {
        this.empty = true;
      }
      this.game_list.push(...list);
      if (list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
    },
    async onRefresh() {
      try {
        await this.getGameList();
      } finally {
        this.loading_obj.reloading = false;
      }
    },
    async loadMore() {
      try {
        await this.getGameList(2);
      } finally {
        this.loading_obj.loading = false;
      }
    },
    toGame(item) {
      BOX_goToGame(
        {
          params: {
            id: item.id,
          },
        },
        { id: item.id },
      );
    },
  },
};
</script>
<style lang="less" scoped>
.sf-rule {
  background-color: rgba(255, 82, 62, 1);
  padding: 50 * @rem 22 * @rem 22 * @rem;
  overflow: hidden;
  main {
    background: rgba(254, 234, 204, 1);
    border-radius: 12 * @rem;
    padding: 17 * @rem;
    font-size: 13 * @rem;
    color: #82390e;
    margin-top: 15 * @rem;
    line-height: 22 * @rem;
    .text {
      margin-bottom: 4 * @rem;
    }
    .title {
      margin: 14 * @rem 0 6 * @rem;
      font-size: 15 * @rem;
      font-weight: 600;
      color: #763006;
      line-height: 22 * @rem;
    }
    .color {
      color: #ff3018;
    }
  }
}
.no-gold-game-popup {
  overflow: unset;
  width: 320 * @rem;
  .search-container {
    box-sizing: border-box;
    width: 320 * @rem;
    height: 450 * @rem;
    padding: 24 * @rem 19 * @rem 10 * @rem;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: unset;
    .close-search {
      width: 24 * @rem;
      height: 24 * @rem;
      background: url(~@/assets/images/close-search.png) center center no-repeat;
      background-size: 24 * @rem 24 * @rem;
      position: absolute;
      right: -10 * @rem;
      top: -10 * @rem;
    }
    .search-bar {
      display: flex;
      align-items: center;
      .input-text {
        width: 240 * @rem;
        height: 35 * @rem;
        border: 1 * @rem solid #e5e5e5;
        border-radius: 18 * @rem;
        flex: 1;
        overflow: hidden;
        form {
          display: block;
          width: 100%;
          height: 100%;
        }
        input {
          box-sizing: border-box;
          display: block;
          width: 100%;
          height: 100%;
          padding: 0 18 * @rem;
          font-size: 15 * @rem;
          color: #333333;
          background-color: #f6f6f6;
        }
      }
      .search-btn {
        font-size: 15 * @rem;
        color: #666666;
        padding-left: 13 * @rem;
        height: 35 * @rem;
        line-height: 35 * @rem;
      }
    }
    .game-list {
      flex: 1;
      overflow: auto;
      margin-top: 10 * @rem;
      .game-item {
        display: flex;
        align-items: center;
        padding: 21 * @rem 0;
        border-bottom: 1 * @rem solid #eeeeee;
        .game-icon {
          width: 50 * @rem;
          height: 50 * @rem;
          border-radius: 10 * @rem;
          background-color: #b5b5b5;
        }
        .right {
          flex: 1;
          min-width: 0;
          margin-left: 10 * @rem;
          .game-name {
            font-size: 16 * @rem;
            font-weight: bold;
            color: #000000;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
          }
          .use-card {
            font-size: 12 * @rem;
            color: #f72e2e;
            margin-top: 10 * @rem;
            &.can {
              color: #36b150;
            }
          }
        }
      }
    }
  }
}
</style>
