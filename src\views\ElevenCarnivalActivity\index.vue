<template>
  <div class="eleven-carnival-activity">
    <nav-bar-2
      ref="topNavBar"
      bgStyle="transparent-white"
      :azShow="true"
      :placeholder="false"
    >
      <template #left>
        <div class="back" @click="back"></div>
      </template>
    </nav-bar-2>

    <div class="main">
      <div class="section section-1">
        <img
          class="bg-banner"
          src="@/assets/images/eleven-carnival-activity/bg-1.png"
          alt=""
        />
        <div class="user-container">
          <!-- 用户信息 -->
          <div v-if="userInfo.token" class="user">
            <user-avatar class="avatar"></user-avatar>
            <div class="nickname">{{ userInfo.nickname }}</div>
          </div>
          <div @click="login" v-else class="user no-login">
            <user-avatar class="avatar"></user-avatar>
            <div class="nickname">未登录</div>
          </div>
        </div>
        <div
          @click="toPage('ElevenCarnivalActivityRule')"
          class="rule-btn btn"
        ></div>
        <div class="record-btn btn" @click="openRecordPopup"></div>
        <div class="activity-time">11月11日00:00 - 11月13日23:59</div>
      </div>
      <div class="section section-2">
        <img
          class="bg-banner"
          src="@/assets/images/eleven-carnival-activity/bg-2.png"
          alt=""
        />
        <div class="section-content">
          <div class="koi-list" v-if="koi_content.awards_list">
            <div
              class="koi-item"
              v-for="(item, index) in koi_content.awards_list.slice(0, 3)"
              :key="index"
            >
              <div class="koi-info">
                <user-avatar
                  class="avatar"
                  v-if="item.awards"
                  :self="false"
                  :src="item.awards.avatar"
                ></user-avatar>
                <img
                  v-if="item.awards"
                  class="koi-item-bg"
                  src="@/assets/images/eleven-carnival-activity/koi-user.png"
                  alt=""
                />
                <img
                  v-else
                  class="koi-item-bg"
                  src="@/assets/images/eleven-carnival-activity/koi-user-default.png"
                  alt=""
                />
                <div class="nickname">
                  {{ item.awards ? item.awards.username : '中奖者' }}
                </div>
              </div>
              <div class="date" :class="{ on: item.awards }">
                {{ item.date }}
              </div>
            </div>
          </div>
          <div class="koi-tips">{{ koi_content.awards_desc }}</div>
          <div class="status-bar" v-if="koi_content.task_list">
            <div
              class="status-btn"
              :class="{ no: koi_content.task_list[0].status == 0 }"
            >
              <div class="status-tip"></div>
            </div>
            <div class="status-text">{{ koi_content.task_list[0].title }}</div>
          </div>
          <div class="gold-left">
            {{ toThousands(lottery_content.remain_gold) }}
          </div>
        </div>
      </div>
      <div class="section section-3">
        <img
          class="bg-banner"
          src="@/assets/images/eleven-carnival-activity/bg-3.png"
          alt=""
        />
        <div class="section-content">
          <!-- 刮奖公告 -->
          <div class="coin-broadcast">
            <van-swipe
              class="coin-broadcast-swiper"
              vertical
              :autoplay="2000"
              :touchable="false"
              :show-indicators="false"
            >
              <van-swipe-item
                v-for="(item, index) in lottery_content.fake_list"
                :key="index"
              >
                <div class="coin-broadcast-item" v-html="item"></div>
              </van-swipe-item>
            </van-swipe>
          </div>

          <!-- 刮奖容器 -->
          <div class="scratch-container">
            <div class="title-bar">
              <div class="scratch-times">
                刮奖次数：<span>{{ lottery_content.remain_lottery_count }}</span
                >次
              </div>
              <div class="refresh-btn btn" @click="refresh"></div>
            </div>

            <div class="scratch-content">
              <div class="prize">{{ lottery_content.first_gold }}金币</div>
              <canvas
                id="canvas"
                @touchmove="touchMove"
                @touchstart="touchStart"
                @touchend="touchEnd"
              ></canvas>
            </div>
          </div>
          <div class="recharge-btn" @click="toRecharge"></div>
        </div>
      </div>
      <div class="section section-4">
        <img
          class="bg-banner"
          src="@/assets/images/eleven-carnival-activity/bg-4.png"
          alt=""
        />
        <div class="section-content">
          <div class="current-score">
            当前积分：{{ exchange_content.remain_integral }}
          </div>
          <div class="exchange-list">
            <div
              class="exchange-item"
              v-for="(item, index) in exchange_content.exchange_list"
              :key="index"
            >
              <div class="exchange-title">{{ item.title }}</div>
              <div class="exchange-btn had btn" v-if="item.status == 2"></div>
              <div
                class="exchange-btn btn"
                v-else
                @click="handleExchange(item)"
              ></div>
            </div>
          </div>
          <div class="exchange-tips">
            活动期间，玩家在游戏内实付充值可获得积分，<br />
            RMB对积分的比例为<span>1:100</span>，即充值<span>1元</span>获得<span>100</span>积分.
          </div>
          <div class="activity-tips">
            温馨提示： <br />
            1.仅限游戏内使用微信/支付宝充值，如有使用金币/平台币/代金券抵扣的，则只计算抵扣后的现金充值金额。<br />
            2.活动期间，每项奖励仅可兑换一次；
          </div>
        </div>
      </div>
    </div>
    <!-- 奖励记录 -->
    <van-popup
      v-model="recordPopup"
      :lock-scroll="false"
      :close-on-click-overlay="false"
      class="popup record-popup"
    >
      <div class="record-popup-close" @click="closeRecordPopup"></div>
      <div class="record-popup-title">奖励记录</div>
      <div class="record-list" v-if="record_list.length > 0">
        <div
          v-for="(item, index) in record_list"
          :key="index"
          class="record-item"
        >
          <div class="title">{{ item.title }}</div>
          <div class="desc">{{ item.desc }}</div>
        </div>
      </div>
      <div v-else class="record-list empty">暂无兑奖记录</div>
    </van-popup>
    <ptb-recharge-popup @success="getIndexData"></ptb-recharge-popup>
  </div>
</template>

<script>
import { mapMutations, mapActions } from 'vuex';
import { platform, boxInit } from '@/utils/box.uni.js';
import {
  ApiActivityElevenCarnivalIndex,
  ApiActivityElevenCarnivalGetContent,
  ApiActivityElevenCarnivalLottery,
  ApiActivityElevenCarnivalElevenLog,
  ApiActivityElevenCarnivalExchange,
  ApiActivityElevenCarnivalReceiveLottery,
} from '@/api/views/eleven_carnival_activity.js';
import { BOX_login } from '@/utils/box.uni.js';
import canvasImg from '@/assets/images/eleven-carnival-activity/eleven-scratch-bg.png';
import ptbRechargePopup from '@/components/ptb-recharge-popup';

export default {
  components: {
    ptbRechargePopup,
  },
  data() {
    return {
      activity_status: 3, // 活动状态 1活动已开始 2 活动不存在 3活动未开始 4活动已结束

      koi_content: {}, // 锦鲤
      lottery_content: {}, // 刮奖
      exchange_content: [], // 兑换

      canvas: '', // 画布
      ctx: '', // 画笔
      scratchStatus: 0, //0=未开始 1=刮奖中 2=刮奖完
      clearCount: 0, // 刮奖区被刮开的程度 最大150就全解开
      showPrize: false, // 显示奖品
      ratio: 0,

      recordPopup: false,
      record_list: [],
    };
  },
  computed: {
    activity_status_text() {
      const arr = [
        '活动已开始！',
        '活动不存在！',
        '活动未开始！',
        '活动已结束！',
      ];
      return arr[this.activity_status - 1];
    },
  },
  async mounted() {
    // 官包触发页面刷新（将onResume挂载在window上）
    if (platform == 'android') {
      window.onResume = this.onResume;
    }
    // 初始化canvas
    this.ratio = document.body.clientWidth / 375;
    this.canvas = document.getElementById('canvas');
    this.canvas.width = this.canvas.clientWidth;
    this.canvas.height = this.canvas.clientHeight;
    this.ctx = this.canvas.getContext('2d');
    this.initCanvas();
    // 页面数据初始化
    await this.getIndexData();
  },
  methods: {
    ...mapMutations({
      setShowPtbRechargePopup: 'recharge/setShowPtbRechargePopup',
    }),
    ...mapActions({
      SET_USER_INFO: 'user/SET_USER_INFO',
    }),
    async onResume() {
      await boxInit();
      await this.SET_USER_INFO(true);
      await this.init();
    },
    openRecordPopup() {
      if (this.activity_status == 2 || this.activity_status == 3) {
        this.$toast(this.activity_status_text);
        return false;
      }
      if (!this.userInfo.token) {
        this.$toast('请登录');
        this.login();
        return false;
      }
      this.recordPopup = true;
      this.getRewardLog();
    },
    async getRewardLog() {
      const res = await ApiActivityElevenCarnivalElevenLog();
      this.record_list = res.data.list;
    },
    login() {
      BOX_login();
    },
    async getIndexData() {
      const res = await ApiActivityElevenCarnivalIndex();
      let { activity_status, koi_content, lottery_content, exchange_content } =
        res.data;
      this.activity_status = activity_status;
      this.koi_content = koi_content;
      this.lottery_content = lottery_content;
      this.exchange_content = exchange_content;
    },
    async refreshContent(type) {
      const res = await ApiActivityElevenCarnivalGetContent({ type: type });
      const { content } = res.data;
      switch (type) {
        case 1:
          this.lottery_content = content;
          break;
        case 2:
          this.koi_content = content;
          break;
        case 3:
          this.exchange_content = content;
          break;
        default:
          break;
      }
    },
    closeRecordPopup() {
      this.recordPopup = false;
    },
    toThousands(num) {
      return (num || 0).toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,');
    },

    async initCanvas() {
      this.ctx.globalCompositeOperation = 'source-over';
      let image = new Image();
      image.src = canvasImg;
      image.onload = async () => {
        await this.ctx.drawImage(
          image,
          0,
          0,
          this.canvas.width,
          this.canvas.height,
        );
      };
    },
    touchStart(e) {
      if (this.activity_status != 1) {
        this.$toast(this.activity_status_text);
        return false;
      }
      if (!this.userInfo.token) {
        this.login();
        return false;
      }
      switch (this.scratchStatus) {
        case 0: // 未开始
          if (this.lottery_content.remain_lottery_count == 0) {
            this.$toast('刮奖次数已用完');
            return;
          } else {
            // 开始刮奖
            this.scratchStatus = 1;
          }
          break;
        case 1: // 刮奖中
          break;
        case 2: // 已结束
          break;
      }
    },
    touchMove(e) {
      if (this.scratchStatus == 1) {
        let x = e.touches[0].clientX - this.canvas.getBoundingClientRect().left,
          y = e.touches[0].clientY - this.canvas.getBoundingClientRect().top;
        e.preventDefault();
        this.ctx.clearRect(x, y, 15 * this.ratio, 15 * this.ratio);
        this.clearCount++;
      }
    },
    async touchEnd(e) {
      if (this.scratchStatus == 1) {
        if (this.clearCount > 80) {
          // 当手指累计滑动超过80时触发清空矩形画布, 数值越大要刮得越久
          this.scratchStatus = 2;
          this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
          await this.handleScratch();
        }
      }
    },
    async handleScratch() {
      try {
        const res = await ApiActivityElevenCarnivalLottery();
        let {
          code,
          data: { remain_lottery_count, gold },
        } = res;
        if (code > 0) {
          this.lottery_content.remain_lottery_count = remain_lottery_count;
          this.first_gold = gold;
          this.$toast(`恭喜获得${gold}金币`);
        }
      } finally {
        setTimeout(async () => {
          this.scratchStatus = 0;
          this.clearCount = 0;
          this.initCanvas();
          await this.getIndexData();
        }, 2000);
      }
    },
    async refresh() {
      this.$toast.loading('刷新中...');
      // await this.getIndexData();
      await this.refreshContent(1);
      this.scratchStatus = 0;
      this.clearCount = 0;
      this.initCanvas();
      this.$toast('刷新成功');
    },
    toRecharge() {
      if (!this.userInfo.token) {
        this.login();
        return false;
      }
      if (this.activity_status != 1) {
        this.$toast(this.activity_status_text);
        return false;
      }
      this.setShowPtbRechargePopup(true);
    },
    async handleExchange(item) {
      if (this.activity_status != 1) {
        this.$toast(this.activity_status_text);
        return false;
      }
      this.$toast.loading('兑换中...');
      const res = await ApiActivityElevenCarnivalExchange({
        exchange_id: item.exchange_id,
      });
      await this.refreshContent(3);
    },
  },
};
</script>

<style lang="less" scoped>
.eleven-carnival-activity {
  width: 100%;
  overflow: hidden;
  .back {
    width: 28 * @rem;
    height: 28 * @rem;
    border-radius: 15 * @rem;
    background: rgba(0, 0, 0, 0.3) url(~@/assets/images/nav-bar-back-white.png)
      center center no-repeat;
    background-size: 8 * @rem 14 * @rem;
  }
  .main {
    width: 100%;
    .section {
      position: relative;
      .bg-banner {
        position: absolute;
        left: 0;
        top: 0;
      }
      .section-content {
        position: relative;
      }
    }
    .section-1 {
      width: 100%;
      height: 570 * @rem;
      .user-container {
        position: absolute;
        top: calc(13 * @rem + @safeAreaTop);
        top: calc(13 * @rem + @safeAreaTopEnv);
        left: 60 * @rem;
        z-index: 1000;
        width: 106 * @rem;
        height: 25 * @rem;
        background: url(~@/assets/images/eleven-carnival-activity/user-bg.png)
          left center no-repeat;
        background-size: 106 * @rem 25 * @rem;
        color: #fff;
        line-height: 25 * @rem;
        .user {
          padding: 0 12 * @rem 0 30 * @rem;
          .nickname {
            font-size: 12 * @rem;
            color: #313131;
            line-height: 25 * @rem;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
        .avatar {
          position: absolute;
          top: 2 * @rem;
          left: 2 * @rem;
          width: 22 * @rem;
          height: 22 * @rem;
          border-radius: 50%;
        }
      }
      .rule-btn {
        position: absolute;
        right: 0;
        top: 106 * @rem;
        width: 28 * @rem;
        height: 80 * @rem;
        .image-bg('~@/assets/images/eleven-carnival-activity/eleven-rule-icon.png');
        background-size: 28 * @rem 80 * @rem;
      }
      .record-btn {
        position: absolute;
        right: 0;
        top: 188 * @rem;
        width: 28 * @rem;
        height: 83 * @rem;
        .image-bg('~@/assets/images/eleven-carnival-activity/eleven-record-icon.png');
        background-size: 28 * @rem 83 * @rem;
      }
      .activity-time {
        position: absolute;
        top: 345 * @rem;
        left: 50%;
        transform: translateX(-50%);
        text-align: center;
        font-size: 12 * @rem;
        color: #ffffff;
        line-height: 16 * @rem;
        font-weight: bold;
        white-space: nowrap;
      }
    }
    .section-2 {
      width: 100%;
      height: 560 * @rem;
      padding-top: 0.1px;
      .koi-list {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: -42 * @rem;
        .koi-item {
          display: flex;
          flex-direction: column;
          width: 82 * @rem;
          height: 100 * @rem;
          margin: 0 13 * @rem;
          position: relative;
          .avatar {
            width: 70 * @rem;
            height: 70 * @rem;
            border-radius: 50%;
            position: absolute;
            left: 7 * @rem;
            top: 0 * @rem;
          }
          .koi-item-bg {
            position: relative;
            width: 82 * @rem;
            height: 78 * @rem;
          }
          .nickname {
            box-sizing: border-box;
            font-size: 11 * @rem;
            color: #313131;
            height: 14 * @rem;
            line-height: 14 * @rem;
            text-align: center;
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            top: 56 * @rem;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            width: 100%;
            padding: 0 10 * @rem;
          }
          .date {
            font-size: 13 * @rem;
            color: #ffffff;
            line-height: 17 * @rem;
            text-align: center;
            margin-top: 6 * @rem;
            &.on {
              color: #dbff00;
              font-weight: 600;
            }
          }
        }
      }
      .koi-tips {
        font-size: 13 * @rem;
        color: #ffffff;
        line-height: 17 * @rem;
        margin-top: 16 * @rem;
        text-align: center;
        position: relative;
      }
      .status-bar {
        position: relative;
        margin-top: 72 * @rem;
        .status-btn {
          margin: 0 auto;
          position: relative;
          width: 170 * @rem;
          height: 70 * @rem;
          background: url('~@/assets/images/eleven-carnival-activity/status-yes.png')
            no-repeat;
          background-size: 170 * @rem 70 * @rem;
          &.no {
            background-image: url('~@/assets/images/eleven-carnival-activity/status-no.png');
          }
          .status-tip {
            position: absolute;
            right: -44 * @rem;
            top: 0;
            width: 71 * @rem;
            height: 53 * @rem;
            background: url('~@/assets/images/eleven-carnival-activity/status-tip.png')
              no-repeat;
            background-size: 71 * @rem 53 * @rem;
          }
        }
        .status-text {
          font-size: 12 * @rem;
          color: #ffffff;
          line-height: 15 * @rem;
          text-align: center;
          margin-top: 0 * @rem;
        }
      }
      .gold-left {
        height: 40 * @rem;
        text-align: center;
        font-size: 40 * @rem;
        color: #000000;
        margin-top: 232 * @rem;
        font-weight: bold;
      }
    }
    .section-3 {
      width: 100%;
      height: 610 * @rem;
      padding-top: 1 * @rem;
      .lottory {
        margin-top: 42 * @rem;
      }

      .coin-broadcast {
        width: 100%;
        height: 20 * @rem;
        margin: -5 * @rem auto 0;
        .coin-broadcast-swiper {
          height: 20 * @rem;
          .coin-broadcast-item {
            height: 20 * @rem;
            white-space: nowrap;
            text-align: center;
            line-height: 20 * @rem;
            font-size: 12 * @rem;
            color: #fff;
            font-weight: 500;
          }
        }
      }
      .scratch-container {
        box-sizing: border-box;
        width: 290 * @rem;
        height: 214 * @rem;
        margin: 26 * @rem auto 0;
        .title-bar {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 10 * @rem 13 * @rem 0;
          .scratch-times {
            font-size: 15 * @rem;
            font-weight: 500;
            color: #976cc7;
            span {
              color: #f7572d;
            }
          }
          .refresh-btn {
            width: 61 * @rem;
            height: 32 * @rem;
            background: url(~@/assets/images/eleven-carnival-activity/refresh-btn.png);
            background-size: 61 * @rem 32 * @rem;
          }
        }
        .scratch-content {
          width: 272 * @rem;
          height: 96 * @rem;
          position: relative;
          background: url(~@/assets/images/eleven-carnival-activity/eleven-scratch-bg.png)
            center center no-repeat;
          background-size: 272 * @rem 96 * @rem;
          overflow: hidden;
          margin: 33 * @rem 8 * @rem 0;
          .prize {
            width: 272 * @rem;
            height: 96 * @rem;
            line-height: 96 * @rem;
            background: #ffffff;
            font-size: 30 * @rem;
            letter-spacing: 3 * @rem;
            text-align: center;
            color: #976cc7;
            font-weight: bold;
          }
          #canvas {
            width: 100%;
            height: 100%;
            position: absolute;
            top: 0;
            left: 0;
          }
        }
      }
      .recharge-btn {
        width: 171 * @rem;
        height: 61 * @rem;
        .image-bg('~@/assets/images/eleven-carnival-activity/recharge-btn.png');
        background-size: 171 * @rem 61 * @rem;
        margin: 18 * @rem auto 0;
      }
    }
    .section-4 {
      width: 100%;
      height: 600 * @rem;
      padding-top: 0.1 * @rem;
      .current-score {
        box-sizing: border-box;
        width: 168 * @rem;
        height: 38 * @rem;
        line-height: 38 * @rem;
        background: url(~@/assets/images/eleven-carnival-activity/exchange-score-bg.png)
          no-repeat;
        background-size: 168 * @rem 38 * @rem;
        margin-left: 28 * @rem;
        font-size: 14 * @rem;
        white-space: nowrap;
        color: #ffffff;
        font-weight: 600;
        padding-left: 19 * @rem;
        margin-top: -21 * @rem;
      }
      .exchange-list {
        .exchange-item {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 8 * @rem 18 * @rem;
          .exchange-title {
            font-size: 13 * @rem;
            color: #ffffff;
            font-weight: 600;
            padding: 0 0 * @rem 0 10 * @rem;
            flex: 1;
            min-width: 0;
          }
          .exchange-btn {
            width: 60 * @rem;
            height: 32 * @rem;
            background: url('~@/assets/images/eleven-carnival-activity/exchange-btn.png');
            background-size: 60 * @rem 32 * @rem;
            &.had {
              background-image: url('~@/assets/images/eleven-carnival-activity/exchange-btn-had.png');
            }
          }
        }
      }
      .exchange-tips {
        font-size: 12 * @rem;
        color: #ffffff;
        line-height: 15 * @rem;
        text-align: center;
        margin-top: 24 * @rem;
        span {
          color: #ff7041;
        }
      }
      .activity-tips {
        margin: 68 * @rem auto 0;
        padding: 0 13 * @rem 0 20 * @rem;
        font-size: 11 * @rem;
        color: rgba(255, 255, 255, 0.6);
        line-height: 14 * @rem;
      }
    }
  }
}
.record-popup {
  box-sizing: border-box;
  overflow: visible;
  width: 289 * @rem;
  height: 310 * @rem;
  background: url('~@/assets/images/eleven-carnival-activity/record-popup-bg.png');
  background-size: 289 * @rem 310 * @rem;
  padding: 15 * @rem 15 * @rem 10 * @rem 15 * @rem;
  .record-popup-close {
    width: 39 * @rem;
    height: 43 * @rem;
    background: url('~@/assets/images/eleven-carnival-activity/record-popup-close.png');
    background-size: 39 * @rem 43 * @rem;
    position: absolute;
    right: -8 * @rem;
    top: -7 * @rem;
  }
  .record-popup-title {
    font-size: 16 * @rem;
    color: #000000;
    line-height: 20 * @rem;
    font-weight: 600;
    text-align: center;
  }
  .record-list {
    margin-top: 20 * @rem;
    height: 240 * @rem;
    overflow-y: auto;
    .record-item {
      display: flex;
      align-items: flex-start;
      line-height: 16 * @rem;
      margin-top: 14 * @rem;
      &:first-of-type {
        margin-top: 0;
      }
      .title {
        flex: 1;
        min-width: 0;
        font-size: 12 * @rem;
        color: #353535;
      }
      .desc {
        min-width: 106 * @rem;
        font-size: 12 * @rem;
        color: #ff6235;
        margin-left: 10 * @rem;
        white-space: nowrap;
      }
    }
    &.empty {
      font-size: 14 * @rem;
      color: #666;
      text-align: center;
      line-height: 220 * @rem;
    }
  }
}
</style>
