<template>
  <div class="page-250101-activity">
    <nav-bar-2
      ref="topNavBar"
      bgStyle="transparent-white"
      :azShow="true"
      :placeholder="false"
    >
      <template #left>
        <div class="back" @click="back"></div>
      </template>
      <template #right>
        <div class="rule" @click="clickRule">规则</div>
      </template>
    </nav-bar-2>

    <div class="fixed-btns">
      <div class="fixed-share-btn" @click="handleShare">分享</div>
    </div>
    <div class="fixed-btns right-fixed">
      <div class="fixed-prize-btn" @click="myPrize">我的<br />奖品</div>
    </div>

    <div class="main">
      <div class="activity-top">
        <!-- 顶部内容 -->
        <div class="activity-title"></div>
        <div class="activity-sub-title">迎新年拿京东卡、红米手机等福礼</div>
        <div class="activity-time">活动时间 2025.1.27 0点 ~ 2025.2.5 24点</div>
      </div>
      <div class="activity-content">
        <div class="lucky-bag-box">
          <div class="lucky-bag-content">
            <div class="dazzle-light"></div>
            <div
              @click="clickLuckyBag"
              ref="luckyBagRef"
              class="lucky-bag-bg animate__animated"
              :class="luckyBagClasses"
            ></div>
            <div class="dazzle-shadow"></div>
            <div class="dazzle-over-bg" v-if="is_num == 0"></div>
          </div>
        </div>
        <div
          class="bag-item"
          :class="{
            'bag-item1': item.task_id == 6,
            'bag-item2': item.task_id == 7,
            'bag-item3': item.task_id == 8,
            'bag-item4': item.task_id == 9,
            'bag-item-grey': item.status == 0,
          }"
          v-for="(item, index) in initInfo"
          :key="index"
        >
          <div class="item">
            <img
              v-if="item.task_id == 6 && item.status !== 0"
              src="@/assets/images/25newyear/25newyear-icon7.png"
              alt=""
            />
            <img
              v-if="item.task_id == 6 && item.status == 0"
              src="@/assets/images/25newyear/25newyear-icon7-grey.png"
              alt=""
            />
            <span
              v-if="item.task_id == 6"
              class="title"
              :class="{ 'title-grey': item.status == 0 }"
              >20元<br />京东卡</span
            >
            <img
              v-if="item.task_id == 7 && item.status !== 0"
              src="@/assets/images/25newyear/25newyear-icon8.png"
              alt=""
            />
            <img
              v-if="item.task_id == 7 && item.status == 0"
              src="@/assets/images/25newyear/25newyear-icon8-grey.png"
              alt=""
            />
            <span
              v-if="item.task_id == 7"
              class="title"
              :class="{ 'title-grey': item.status == 0 }"
              >50元<br />京东卡</span
            >
            <img
              v-if="item.task_id == 8 && item.status !== 0"
              src="@/assets/images/25newyear/25newyear-icon9.png"
              alt=""
            />
            <img
              v-if="item.task_id == 8 && item.status == 0"
              src="@/assets/images/25newyear/25newyear-icon9-grey.png"
              alt=""
            />
            <span
              v-if="item.task_id == 8"
              class="title"
              :class="{ 'title-grey': item.status == 0 }"
              >100元<br />京东卡</span
            >
            <img
              v-if="item.task_id == 9 && item.status !== 0"
              :class="{ img4: item.task_id == 9 }"
              src="@/assets/images/25newyear/25newyear-icon10.png"
              alt=""
            />
            <img
              v-if="item.task_id == 9 && item.status == 0"
              :class="{ img4: item.task_id == 9 }"
              src="@/assets/images/25newyear/25newyear-icon10-grey.png"
              alt=""
            />
            <span
              v-if="item.task_id == 9"
              class="title"
              :class="{ 'title-grey': item.status == 0 }"
              >红米手机</span
            >
          </div>
        </div>
        <div class="progress-bar">
          <div class="progress-level">
            <div class="level">{{ currentLevel }}级</div>
            <div class="progress">
              <div class="progress-speed">
                <div class="speed" :style="{ width: percentageLevel }"></div>
              </div>
              <div class="progress-text">
                {{ points }}/{{ currentLevelPoint }}
              </div>
            </div>
          </div>
          <div class="progress-icon" @click="handleCheckProbability">
            <img src="@/assets/images/25newyear/25newyear-icon2.png" alt="" />
          </div>
        </div>
        <div class="lucky-container" v-if="maxReward.length">
          <div class="notice-icon"></div>
          <van-swipe
            class="lucky-swipe"
            vertical
            :autoplay="2000"
            :show-indicators="false"
            :touchable="false"
          >
            <van-swipe-item v-for="(item, index) in maxReward" :key="index">
              <div class="lucky-text">
                <span>{{ formatNickname(item?.nickname) }}：</span
                >{{ item.desc }}
              </div>
            </van-swipe-item>
          </van-swipe>
        </div>
      </div>
      <div class="activity-bottom">
        <div class="bottom-btn" @click="openLuckyBag">
          <img
            v-if="is_num > 0 || is_num == -1"
            src="@/assets/images/25newyear/btn.png"
            alt=""
          />
          <img
            v-if="is_num == 0"
            src="@/assets/images/25newyear/btn-over.png"
            alt=""
          />
          <div class="bottom-text">
            <img
              v-if="is_num > 0 || is_num == -1"
              class="img1"
              src="@/assets/images/25newyear/25newyear-title2.png"
              alt=""
            />
            <img
              v-if="is_num == 0"
              class="img2"
              src="@/assets/images/25newyear/25newyear-title3.png"
              alt=""
            />
            <span v-if="is_num > 0" class="num">x{{ is_num }}</span>
          </div>
        </div>
        <div
          class="bottom-icon1"
          v-if="is_num !== 0 && points == 0 && current_time < close_time"
          @click="handleInvitationBtn"
        >
          <div class="bottom-icon1-tips"></div>
        </div>
        <div
          class="bottom-icon2"
          v-if="is_num !== 0 && current_time < close_time"
          @click="handleBlessingValueBtn"
        ></div>
        <div class="bottom-icon3" @click="handleCoupletsBtn"></div>
        <div class="bottom-icon4" @click="handleSvipBtn"></div>
        <div
          class="bottom-tips"
          v-if="isLoading"
          :class="{ 'tips-vip': !is_svip }"
        >
          <div class="tips-box" v-if="!is_svip" @click="handleSvipBtn">
            <img src="@/assets/images/25newyear/btn-tips.png" alt="" />
            <span class="tips-text">
              开通SVIP可多开
              <span class="number">1次</span>
              福袋
            </span>
          </div>
          <div class="tips-box" v-else>
            <span class="tips-text1">另含SVIP专属开福袋次数 x1</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 福袋弹窗 -->
    <lucky-bag-popUp
      :show.sync="LuckyBagPopUp"
      @openUpLuckyBag="openUpLuckyBag"
    ></lucky-bag-popUp>
    <!-- 福袋开启弹窗 -->
    <lucky-bag-open-popUp
      :show.sync="LuckyBagOpenPopUp"
      :luckyBagInfo="luckyBagInfo"
    ></lucky-bag-open-popUp>
    <!-- 邀请有礼弹窗 -->
    <invitation-dialog
      :show.sync="invitationShow"
      @confirmFunc="confirmFunc"
    ></invitation-dialog>
    <!-- 查概率弹窗 -->
    <check-probability-popup
      :show.sync="checkProbabilityPopup"
      :currentlevel="currentlevel"
      :nextlevel="nextlevel"
      :probabilityList="probabilityList"
    ></check-probability-popup>
    <!-- 我的奖品弹窗 -->
    <prize-popup :show.sync="prizePopup"></prize-popup>
    <!-- 下载游戏弹窗 -->
    <download-game-popup :show.sync="downloadGamePopup"></download-game-popup>
    <!-- 体验游戏弹窗 -->
    <experience-game-popup
      :show.sync="experienceGamePopup"
    ></experience-game-popup>
  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex';
import {
  platform,
  boxInit,
  BOX_openInNewWindow,
  BOX_login,
  iframeCopy,
  BOX_close,
} from '@/utils/box.uni.js';
import { envFun } from '@/utils/function.js';
import { ApiCommonShareInfo } from '@/api/views/system.js';
import {
  ApiDoingsChunjieIndex,
  ApiDoingsChunjieTaskLuckyBag,
  ApiDoingsChunjieProbability,
} from '@/api/views/25_new_year_activity.js';
import { LOCAL_HOST } from '@/utils/constants.js';
export default {
  components: {
    invitationDialog: () => import('./components/invitation-dialog.vue'),
    luckyBagPopUp: () => import('./components/lucky-bag-popUp.vue'),
    luckyBagOpenPopUp: () => import('./components/lucky-bag-open-popUp.vue'),
    checkProbabilityPopup: () =>
      import('./components/check-probability-popup.vue'),
    prizePopup: () => import('./components/prize-popup.vue'),
    downloadGamePopup: () => import('./components/download-game-popup.vue'),
    experienceGamePopup: () => import('./components/experience-game-popup.vue'),
  },
  data() {
    return {
      activity_status: 3, // 活动状态 1活动已开始 2 活动不存在 3活动未开始 4活动已结束
      current_time: 0, //当前时间
      close_time: 1738684800, // 关闭时间 2025-02-05 00:00:00
      currentLevel: 1, //福袋等级
      is_num: -1, // 福袋次数
      is_svip: false, // 是否svip
      points: 0, // 福气值
      info: {}, // 活动信息
      shareInfo: {}, //分享信息
      invitationShow: false, //邀请有礼弹窗
      checkProbabilityPopup: false, //查概率弹窗
      prizePopup: false,
      downloadGamePopup: false,
      experienceGamePopup: false,
      LuckyBagPopUp: false,
      LuckyBagOpenPopUp: false,
      LuckyBagOpenPopUpStatus: false,
      operationLoading: false,
      luckyBagInfo: {},
      initInfo: [
        {
          task_id: 6,
          title: '20元京东卡',
          need_num: 1,
          status: 1,
        },
        {
          task_id: 7,
          title: '50元京东卡',
          need_num: 1,
          status: 1,
        },
        {
          task_id: 8,
          title: '100元京东卡',
          need_num: 1,
          status: 1,
        },
        {
          task_id: 9,
          title: '红米K80手机',
          need_num: 1,
          status: 1,
        },
      ],
      maxReward: [],
      isLoading: false,
      currentlevel: 0,
      nextlevel: 0,
      probabilityList: [],
    };
  },
  computed: {
    ...mapGetters({
      initData: 'system/initData',
      userInfo: 'user/userInfo',
    }),
    luckyBagClasses() {
      if (this.is_num == 0 && this.isLoading) {
        return 'level-over-bg';
      }
      const levelClasses = {
        1: 'level1-bg',
        2: 'level2-bg',
        3: 'level3-bg',
        4: 'level4-bg',
        5: 'level5-bg',
        6: 'level6-bg',
        7: 'level7-bg',
        8: 'level8-bg',
        9: 'level9-bg',
        10: 'level10-bg',
      };
      return [levelClasses[this.currentLevel] || ''];
    },
    activity_status_text() {
      const arr = [
        '活动已开始！',
        '活动不存在！',
        '活动未开始！',
        '活动已结束！',
      ];
      return arr[this.activity_status - 1];
    },
    currentLevelPoint() {
      switch (this.currentLevel) {
        case 1:
          return 100;
        case 2:
          return 200;
        case 3:
          return 300;
        case 4:
          return 500;
        case 5:
          return 800;
        case 6:
          return 900;
        case 7:
          return 1000;
        case 8:
          return 1200;
        case 9:
          return 1500;
        default:
          return 1500;
      }
    },
    percentageLevel() {
      let percentage = 0;
      switch (this.currentLevel) {
        case 1:
          percentage = (this.points / 100) * 100;
          break;
        case 2:
          percentage = (this.points / 200) * 100;
          break;
        case 3:
          percentage = (this.points / 300) * 100;
          break;
        case 4:
          percentage = (this.points / 500) * 100;
          break;
        case 5:
          percentage = (this.points / 800) * 100;
          break;
        case 6:
          percentage = (this.points / 900) * 100;
          break;
        case 7:
          percentage = (this.points / 1000) * 100;
          break;
        case 8:
          percentage = (this.points / 1200) * 100;
          break;
        case 9:
          percentage = (this.points / 1500) * 100;
          break;
        default:
          percentage = 100;
          break;
      }
      return `${percentage.toFixed(2)}%`;
    },
  },
  async created() {
    await this.getInitData(true);
  },
  async mounted() {
    // 官包触发页面刷新（将onResume挂载在window上）
    if (platform == 'android') {
      document.title = '春节活动';
      window.onResume = this.onResume;
    }
    document.body.addEventListener('touchstart', function () {});
  },
  beforeDestroy() {
    document.body.removeEventListener('touchstart', function () {});
    if (this.timeClock) {
      clearInterval(this.timeClock);
      this.timeClock = null;
    }
  },
  async activated() {
    await this.resetPopup();
    await this.getChunjieProbabilityInfo();
    await this.getInitData();
  },
  methods: {
    ...mapActions({
      SET_USER_INFO: 'user/SET_USER_INFO',
    }),
    async onResume() {
      await boxInit();
      await this.SET_USER_INFO(true);
      await this.resetPopup();
      await this.getChunjieProbabilityInfo();
      await this.getInitData();
    },
    async getChunjieProbabilityInfo() {
      const res = await ApiDoingsChunjieProbability();
      this.currentlevel = res.data.currentlevel;
      this.nextlevel = res.data.nextlevel;
      if (this.currentlevel < 4) {
        const filteredData = res.data.prizes.filter(
          item => item.formattedProbability !== '0%',
        );
        this.probabilityList = filteredData;
      } else {
        this.probabilityList = res.data.prizes;
      }
    },
    // 活动规则
    clickRule() {
      this.toPage('25NewYearActivityRulePage');
      return false;
      if (platform == 'android') {
        if (process.env.NODE_ENV === 'development') {
          BOX_openInNewWindow(
            {
              name: 'Activity',
              params: {
                url: `${LOCAL_HOST}/#/25_new_year_activity/rule_page`,
              },
            },
            {
              url: `${LOCAL_HOST}/#/25_new_year_activity/rule_page`,
            },
          );
        } else {
          BOX_openInNewWindow(
            {
              name: 'Activity',
              params: {
                url: `https://${envFun()}activity.3733.com/#/25_new_year_activity/rule_page`,
              },
            },
            {
              url: `https://${envFun()}activity.3733.com/#/25_new_year_activity/rule_page`,
            },
          );
        }
      } else {
        this.toPage('25NewYearActivityRulePage');
      }
    },
    login() {
      BOX_login();
    },
    // 初始化信息
    async getInitData(is_first = false) {
      try {
        const res = await ApiDoingsChunjieIndex();
        this.activity_status = res.data.activity_status;
        this.current_time = res.data.current_time;
        this.currentLevel = res.data.level;
        this.is_svip = res.data.is_svip;
        this.points = res.data.points;
        this.is_num = res.data.is_num;
        this.initInfo = res.data.list.filter(item => {
          return [6, 7, 8, 9].includes(item.task_id);
        });
        this.maxReward = res.data.extra;
      } catch (error) {
      } finally {
        if (is_first) {
          this.isLoading = true;
        }
      }
    },
    resetPopup() {
      this.prizePopup = false;
      this.LuckyBagPopUp = false;
      this.LuckyBagOpenPopUp = false;
      this.invitationShow = false;
    },
    handleCheckProbability() {
      if (!this.userInfo.token) {
        this.$toast('请登录');
        this.login();
        return false;
      }
      if (!this.isLoading) return;
      if ([2, 3, 4].includes(this.activity_status)) {
        this.$toast(this.activity_status_text);
        return false;
      }
      this.$nextTick(() => {
        this.checkProbabilityPopup = true;
      });
    },
    // 是否开启福袋
    openLuckyBag() {
      if (!this.userInfo.token) {
        this.$toast('请登录');
        this.login();
        return false;
      }
      if (!this.isLoading) return;
      if ([2, 3, 4].includes(this.activity_status)) {
        this.$toast(this.activity_status_text);
        return false;
      }

      //   福袋已开启
      if (this.is_num == 0) {
        return;
      }
      // 福袋等级大于等于10级 活动时间大于等于2025-02-05 00:00:00 直接开福袋
      if (this.currentLevel >= 10 || this.current_time >= this.close_time) {
        this.openUpLuckyBag(true);
        return;
      }
      this.LuckyBagPopUp = true;
    },
    // 打开福袋
    async openUpLuckyBag(e) {
      if (e) {
        const luckyBag = this.$refs.luckyBagRef;
        if (luckyBag) {
          Promise.all([
            this.triggerAnimation(luckyBag),
            this.getChunjieTaskLuckyBag(),
          ])
            .then(async () => {
              if (Object.keys(this.luckyBagInfo).length > 0) {
                this.LuckyBagOpenPopUp = true;
              } else {
                this.LuckyBagOpenPopUp = false;
              }
              this.$toast.clear();
              await this.getInitData();
            })
            .finally(() => {
              this.$toast.clear();
            });
        }
      }
    },
    async getChunjieTaskLuckyBag() {
      try {
        const res = await ApiDoingsChunjieTaskLuckyBag();
        this.luckyBagInfo = res.data;
      } catch (error) {}
    },
    triggerAnimation(element) {
      return new Promise((resolve, reject) => {
        if (element.classList.contains('animate__rubberBand')) return resolve();
        element.classList.add('animate__rubberBand');
        setTimeout(() => {
          element.classList.remove('animate__rubberBand');
          this.$toast.loading({
            duration: 0,
            forbidClick: true,
          });
          return resolve();
        }, 1200);
      });
    },
    clickLuckyBag() {
      if (this.is_num == 0) return;
      const luckyBag = this.$refs.luckyBagRef;
      if (luckyBag) {
        if (luckyBag.classList.contains('animate__bounce')) return;
        luckyBag.classList.add('animate__bounce');

        setTimeout(() => {
          luckyBag.classList.remove('animate__bounce');
        }, 1500);
      }
    },
    // 邀请有礼
    handleInvitationBtn() {
      if (!this.userInfo.token) {
        this.$toast('请登录');
        this.login();
        return false;
      }
      if (!this.isLoading) return;
      if ([2, 3, 4].includes(this.activity_status)) {
        this.$toast(this.activity_status_text);
        return false;
      }
      this.invitationShow = true;
    },
    // 邀请弹窗确认
    confirmFunc() {
      this.getInitData();
    },
    // 赚福气值(任务)
    handleBlessingValueBtn() {
      if (!this.userInfo.token) {
        this.$toast('请登录');
        this.login();
        return false;
      }
      if (!this.isLoading) return;
      if ([2, 3, 4].includes(this.activity_status)) {
        this.$toast(this.activity_status_text);
        return false;
      }
      if (platform == 'android') {
        this.toPage('25NewYearActivityLuckyValueTask');
        return;
      }

      if (process.env.NODE_ENV === 'development') {
        BOX_openInNewWindow(
          {
            name: 'Activity',
            params: {
              url: `${LOCAL_HOST}/#/25_new_year_activity/lucky_value_task`,
            },
          },
          {
            url: `${LOCAL_HOST}/#/25_new_year_activity/lucky_value_task`,
          },
        );
      } else {
        BOX_openInNewWindow(
          {
            name: 'Activity',
            params: {
              url: `https://${envFun()}activity.3733.com/#/25_new_year_activity/lucky_value_task`,
            },
          },
          {
            url: `https://${envFun()}activity.3733.com/#/25_new_year_activity/lucky_value_task`,
          },
        );
      }
    },
    // 妙笔贺岁(对联)
    handleCoupletsBtn() {
      if (!this.userInfo.token) {
        this.$toast('请登录');
        this.login();
        return false;
      }
      if (!this.isLoading) return;
      if ([2, 3, 4].includes(this.activity_status)) {
        this.$toast(this.activity_status_text);
        return false;
      }
      if (platform == 'android') {
        this.toPage('25NewYearActivityCouplets');
        return;
      }
      if (process.env.NODE_ENV === 'development') {
        BOX_openInNewWindow(
          {
            name: 'Activity',
            params: {
              url: `${LOCAL_HOST}/#/25_new_year_activity/couplets`,
            },
          },
          {
            url: `${LOCAL_HOST}/#/25_new_year_activity/couplets`,
          },
        );
      } else {
        BOX_openInNewWindow(
          {
            name: 'Activity',
            params: {
              url: `https://${envFun()}activity.3733.com/#/25_new_year_activity/couplets`,
            },
          },
          {
            url: `https://${envFun()}activity.3733.com/#/25_new_year_activity/couplets`,
          },
        );
      }
    },
    // 入会见喜(SVIP)
    handleSvipBtn() {
      if (!this.userInfo.token) {
        this.$toast('请登录');
        this.login();
        return false;
      }
      if (!this.isLoading) return;
      if ([2, 3, 4].includes(this.activity_status)) {
        this.$toast(this.activity_status_text);
        return false;
      }
      BOX_openInNewWindow(
        { name: 'Svip' },
        { url: `https://${envFun()}game.3733.com/#/svip` },
      );
    },
    // 获取分享信息
    // 当is_share为1时，表示上报分享成功
    async getShareInfo(is_share = 0) {
      let params = {
        type: 11,
        id: this.userInfo.user_id ? this.userInfo.user_id : 1,
      };
      if (is_share) {
        params.is_share = 1;
      }
      const res = await ApiCommonShareInfo(params);
      if (!is_share) {
        this.shareInfo = res.data;
      }
    },
    async handleShare() {
      if (!this.userInfo.token) {
        this.$toast('请登录');
        this.login();
        return false;
      }
      if (!this.isLoading) return;
      if ([2, 3, 4].includes(this.activity_status)) {
        this.$toast(this.activity_status_text);
        return false;
      }

      await this.getShareInfo();
      if (this.initData?.share_info?.length) {
        if (this.operationLoading) {
          return false;
        }
        this.operationLoading = true;
        setTimeout(() => {
          this.operationLoading = false;
        }, 1000);
        window.BOX.mobShare(11, this.userInfo.user_id);
      } else {
        // web的iframe安全策略导致无法复制，故需使用postMessage转移至父级窗口中复制
        await this.getShareInfo(1);
        iframeCopy(this.shareInfo.share_text + this.shareInfo.url);
      }
      setTimeout(async () => {
        await this.getInitData();
      }, 1000);
    },
    myPrize() {
      if (!this.userInfo.token) {
        this.$toast('请登录');
        this.login();
        return false;
      }
      if (!this.isLoading) return;
      if ([2, 3, 4].includes(this.activity_status)) {
        this.$toast(this.activity_status_text);
        return false;
      }

      this.prizePopup = true;
    },
    formatNickname(nickname) {
      if (nickname.length > 5) {
        return nickname.slice(0, 2) + '**' + nickname.slice(-2);
      }
      return nickname;
    },
    debounce(fn, delay) {
      let timer = null;
      return function (value) {
        clearTimeout(timer);
        timer = setTimeout(() => {
          fn.call(this, value);
        }, delay);
      };
    },
  },
};
</script>

<style lang="less" scoped>
.page-250101-activity {
  font-family: PingFang SC, PingFang SC;
  display: flex;
  flex-direction: column;
  width: 100%;
  overflow: hidden;
  min-height: 100vh;
  background: rgba(253, 219, 194, 1);
  .back,
  .rule {
    width: 40 * @rem;
    height: 40 * @rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(109, 24, 39, 0.3);
    border-radius: 14 * @rem;
  }
  .back {
    background: rgba(109, 24, 39, 0.3)
      url(~@/assets/images/25newyear/left-back.png) center center no-repeat;
    background-size: 9 * @rem 14 * @rem;
  }
  .rule {
    font-weight: 500;
    font-size: 12 * @rem;
    color: #ffffff;
  }
  .fixed-btns {
    position: fixed;
    top: 106 * @rem;
    padding-top: @safeAreaTop;
    padding-top: @safeAreaTopEnv;
    z-index: 999;
    padding: 0 16 * @rem;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    &.right-fixed {
      right: 0;
    }
    .fixed-share-btn,
    .fixed-prize-btn {
      box-sizing: border-box;
      padding: 0 7 * @rem;
      min-width: 40 * @rem;
      height: 40 * @rem;
      background: rgba(109, 24, 39, 0.3);
      border-radius: 14 * @rem;
      font-size: 12 * @rem;
      color: #ffffff;
      flex-wrap: wrap;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
  .main {
    width: 100%;
    position: relative;
    height: 100vh;
    flex: 1;
    min-height: 0;
    background: url(~@/assets/images/25newyear/25newyear-bg.png) center top
      no-repeat;
    background-size: 100% 596 * @rem;
    padding-bottom: 94 * @rem;
    .activity-top {
      width: 100%;
      //   height: 205 * @rem;
      overflow: hidden;
      .activity-title {
        width: 199 * @rem;
        height: 53 * @rem;
        margin: 95 * @rem auto 0;
        background: url(~@/assets/images/25newyear/25newyear-title1.png) center
          top no-repeat;
        background-size: 199 * @rem 53 * @rem;
      }
      .activity-sub-title {
        font-size: 16 * @rem;
        font-weight: 600;
        color: #fffaf0;
        text-align: center;
        margin-top: 11 * @rem;
        height: 22 * @rem;
        line-height: 19 * @rem;
      }
      .activity-time {
        font-weight: 500;
        width: fit-content;
        height: 24 * @rem;
        border-radius: 20 * @rem;
        background: rgba(255, 255, 255, 0.2);
        font-size: 13 * @rem;
        color: #fff;
        display: flex;
        padding: 0 17 * @rem;
        margin: 8 * @rem auto 0;
        line-height: 24 * @rem;
      }
    }
    .activity-content {
      position: relative;
      //   margin: 71 * @rem auto 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      .lucky-bag-box {
        position: relative;
        width: 375 * @rem;
        height: 346 * @rem;
        .lucky-bag-content {
          height: 298 * @rem;
          z-index: 0;
          position: relative;
          margin-top: 48 * @rem;
          display: flex;
          align-items: center;
          justify-content: center;
          .dazzle-light {
            position: absolute;
            z-index: 1;
            width: 375 * @rem;
            height: 375 * @rem;
            background: url(~@/assets/images/25newyear/25newyear-bg4.png) center
              top no-repeat;
            background-size: 375 * @rem 375 * @rem;
            z-index: 0;
            animation: rotate-animation 10s linear infinite;
          }
          .lucky-bag-bg {
            z-index: 2;
            width: 240 * @rem;
            height: 298 * @rem;
            &.level1-bg {
              background: url(~@/assets/images/25newyear/25newyear-level1.png)
                center top no-repeat;
              background-size: 240 * @rem 298 * @rem;
            }
            &.level2-bg {
              background: url(~@/assets/images/25newyear/25newyear-level2.png)
                center top no-repeat;
              background-size: 240 * @rem 298 * @rem;
            }
            &.level3-bg {
              background: url(~@/assets/images/25newyear/25newyear-level3.png)
                center top no-repeat;
              background-size: 240 * @rem 298 * @rem;
            }
            &.level4-bg {
              background: url(~@/assets/images/25newyear/25newyear-level4.png)
                center top no-repeat;
              background-size: 240 * @rem 298 * @rem;
            }
            &.level5-bg {
              background: url(~@/assets/images/25newyear/25newyear-level5.png)
                center top no-repeat;
              background-size: 240 * @rem 298 * @rem;
            }
            &.level6-bg {
              background: url(~@/assets/images/25newyear/25newyear-level6.png)
                center top no-repeat;
              background-size: 240 * @rem 298 * @rem;
            }
            &.level7-bg {
              background: url(~@/assets/images/25newyear/25newyear-level7.png)
                center top no-repeat;
              background-size: 240 * @rem 298 * @rem;
            }
            &.level8-bg {
              background: url(~@/assets/images/25newyear/25newyear-level8.png)
                center top no-repeat;
              background-size: 240 * @rem 298 * @rem;
            }
            &.level9-bg {
              background: url(~@/assets/images/25newyear/25newyear-level9.png)
                center top no-repeat;
              background-size: 240 * @rem 298 * @rem;
            }
            &.level10-bg {
              background: url(~@/assets/images/25newyear/25newyear-level10.png)
                center top no-repeat;
              background-size: 240 * @rem 298 * @rem;
            }
            &.level-over-bg {
              width: 342 * @rem;
              height: 298 * @rem;
              background: url(~@/assets/images/25newyear/level-over-bg.png)
                center top no-repeat;
              background-size: 342 * @rem 298 * @rem;
            }
          }
          .dazzle-shadow {
            position: absolute;
            bottom: -6 * @rem;
            width: 182 * @rem;
            height: 33 * @rem;
            background: url(~@/assets/images/25newyear/25newyear-bg9.png) center
              top no-repeat;
            background-size: 182 * @rem 33 * @rem;
          }
          .dazzle-over-bg {
            position: absolute;
            z-index: 9;
            top: 64 * @rem;
            width: 375 * @rem;
            height: 234 * @rem;
            background: url(~@/assets/images/25newyear/25newyear-bg10.png)
              center top no-repeat;
            background-size: 375 * @rem 234 * @rem;
          }
        }
      }
      .bag-item {
        position: absolute;
        width: 76 * @rem;
        height: 76 * @rem;
        background: url(~@/assets/images/25newyear/25newyear-icon1.png) center
          top no-repeat;
        background-size: 76 * @rem 76 * @rem;
        .item {
          position: relative;
          width: 100%;
          height: 100%;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          img {
            width: 34 * @rem;
            height: 20 * @rem;
            &.img4 {
              width: 28 * @rem;
              height: 35 * @rem;
            }
          }
          .title {
            margin-top: 3 * @rem;
            text-align: center;
            font-weight: 500;
            font-size: 11 * @rem;
            color: #8a322e;
            &.title4 {
              white-space: nowrap;
            }
            &.title-grey {
              color: #d3a9a7;
            }
          }
        }
        &.bag-item1 {
          left: 24 * @rem;
          top: 59 * @rem;
        }
        &.bag-item2 {
          right: 19 * @rem;
          top: 59 * @rem;
        }
        &.bag-item3 {
          left: 24 * @rem;
          top: 184 * @rem;
        }
        &.bag-item4 {
          right: 19 * @rem;
          top: 184 * @rem;
        }
        &.bag-item-grey {
          background: url(~@/assets/images/25newyear/25newyear-icon1-grey.png)
            center top no-repeat;
          width: 76 * @rem;
          height: 76 * @rem;
          background-size: 76 * @rem 76 * @rem;
        }
      }
      .progress-bar {
        display: flex;
        align-items: center;
        margin: 17 * @rem auto 0;
        height: 20 * @rem;
        .progress-level {
          padding: 0 4 * @rem 0 7 * @rem;
          height: 20 * @rem;
          background: rgba(255, 255, 255, 0.6);
          box-shadow: 0px 4px 4px 0px rgba(255, 199, 148, 0.3);
          border-radius: 20 * @rem;
          display: flex;
          align-items: center;
          .level {
            display: inline-block;
            height: 12 * @rem;
            font-weight: 500;
            font-size: 12 * @rem;
            color: #550000;
          }
          .progress {
            position: relative;
            box-sizing: border-box;
            margin-left: 2 * @rem;
            width: 146 * @rem;
            height: 14 * @rem;
            background: #fcc1b5;
            border-radius: 20 * @rem;
            padding: 0 2 * @rem;
            display: flex;
            align-items: center;
            overflow: hidden;
            .progress-speed {
              position: absolute;
              width: calc(100% - 4 * @rem);
              box-sizing: border-box;
              height: 10 * @rem;
              z-index: 1;
              .speed {
                height: 100%;
                width: 0;
                border-radius: 20 * @rem;
                background: linear-gradient(180deg, #ff8471 0%, #f53b63 100%),
                  #f53b63;
              }
            }
            .progress-text {
              height: 10 * @rem;
              font-weight: 500;
              font-size: 10 * @rem;
              color: #ffffff;
              line-height: 10 * @rem;
              text-align: center;
              margin: 0 auto;
              z-index: 2;
            }
          }
        }
        .progress-icon {
          z-index: 9;
          margin-left: 4 * @rem;
          padding: 5 * @rem;
          width: 16 * @rem;
          height: 16 * @rem;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
      .lucky-container {
        position: absolute;
        top: 21 * @rem;
        box-sizing: border-box;
        width: 200 * @rem;
        height: 20 * @rem;
        background: rgba(128, 91, 75, 0.8);
        border-radius: 20 * @rem;
        display: flex;
        align-items: center;
        justify-content: center;
        .notice-icon {
          width: 12 * @rem;
          height: 12 * @rem;
          .image-bg('~@/assets/images/25newyear/notice-icon.png');
          margin-right: 5 * @rem;
        }
        .lucky-swipe {
          height: 26 * @rem;
          .van-swipe-item {
            color: #ffe2a4;
            font-size: 11 * @rem;
            line-height: 26 * @rem;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            span {
              color: #ffe2a4;
            }
          }
        }
      }
    }
    .activity-bottom {
      margin: 69 * @rem auto 0;
      height: 80 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      .bottom-btn {
        position: relative;
        width: 188 * @rem;
        height: 80 * @rem;
        display: flex;
        justify-content: center;
        align-items: center;
        border-image: linear-gradient(
            180deg,
            rgba(255, 254, 252, 1),
            rgba(255, 230, 191, 1)
          )
          2 2;
        .bottom-text {
          position: absolute;
          top: 20 * @rem;
          display: flex;
          align-items: center;
          .img1 {
            width: 58 * @rem;
            height: 20 * @rem;
          }
          .img2 {
            width: 83 * @rem;
            height: 20 * @rem;
          }
          .num {
            margin-left: 7 * @rem;
            font-weight: 500;
            font-size: 16 * @rem;
            color: #fffcec;
          }
        }
      }
      .bottom-icon1,
      .bottom-icon2,
      .bottom-icon3,
      .bottom-icon4 {
        position: absolute;
      }
      .bottom-icon1 {
        left: 11 * @rem;
        top: -78 * @rem;
        width: 62 * @rem;
        height: 67 * @rem;
        background: url(~@/assets/images/25newyear/25newyear-icon3.png) center
          top no-repeat;
        background-size: 62 * @rem 67 * @rem;
        .bottom-icon1-tips {
          position: absolute;
          top: -18 * @rem;
          width: 64 * @rem;
          height: 20 * @rem;
          background: url(~@/assets/images/25newyear/new-user-icon.png) center
            top no-repeat;
          background-size: 64 * @rem 20 * @rem;
        }
      }

      .bottom-icon2 {
        right: 12 * @rem;
        top: -78 * @rem;
        width: 62 * @rem;
        height: 67 * @rem;
        background: url(~@/assets/images/25newyear/25newyear-icon4.png) center
          top no-repeat;
        background-size: 62 * @rem 67 * @rem;
      }
      .bottom-icon3 {
        left: 0;
        bottom: -6 * @rem;
        width: 94 * @rem;
        height: 78 * @rem;
        background: url(~@/assets/images/25newyear/25newyear-icon5.png) center
          top no-repeat;
        background-size: 94 * @rem 78 * @rem;
      }
      .bottom-icon4 {
        right: 0;
        bottom: -6 * @rem;
        width: 94 * @rem;
        height: 77 * @rem;
        background: url(~@/assets/images/25newyear/25newyear-icon6.png) center
          top no-repeat;
        background-size: 94 * @rem 77 * @rem;
      }
      .bottom-tips {
        position: absolute;
        bottom: -18 * @rem;
        .tips-box {
          position: relative;
          img {
            width: 186 * @rem;
            height: 44 * @rem;
          }
          .tips-text {
            position: absolute;
            left: 17 * @rem;
            top: 18 * @rem;
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 14 * @rem;
            color: #550000;
            .number {
              font-weight: 500;
              font-size: 14 * @rem;
              color: #ee3630;
              padding: 0 2 * @rem;
            }
          }
          .tips-text1 {
            height: 14 * @rem;
            font-weight: 500;
            font-size: 14 * @rem;
            color: #550000;
          }
        }
        &.tips-vip {
          bottom: -40 * @rem;
        }
      }
    }
  }
}
@keyframes tipShow {
  0% {
    opacity: 0;
    top: 0;
  }
  100% {
    opacity: 1;
    top: -12 * @rem;
  }
}
@keyframes tipShowLast {
  0% {
    opacity: 0;
    top: 0;
  }
  100% {
    opacity: 1;
    top: -30 * @rem;
  }
}
@keyframes rotate-animation {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
