import { request } from '../index';

/**
 * 元旦 - 元旦首页
 * */
export function ApiDoingsYuandanIndex(params = {}) {
  return request('/doings/yuandan/index', params);
}

/**
 * 活动页/元旦活动-2025 - 任务奖励领取
 * */
export function ApiDoingsYuandanTaskPrize(params = {}) {
  return request('/doings/yuandan/taskPrize', params);
}

/**
 * 活动页/元旦活动-2025 - 任务领取
 * */
export function ApiDoingsYuandanTask(params = {}) {
  return request('/doings/yuandan/task', params);
}

/**
 * 活动页/元旦活动-2025 - 奖励记录
 * */
export function ApiDoingsYuandanTaskLog(params = {}) {
  return request('/doings/yuandan/taskLog', params);
}

/**
 * 活动页/元旦活动-2025 - 签到页面
 * */
export function ApiDoingsYuandanSignInfo(params = {}) {
  return request('/doings/yuandan/signInfo', params);
}

/**
 * 活动页/元旦活动-2025 - 游戏列表
 * */
export function ApiDoingsYuandanGetSuggestGames(params = {}) {
  return request('/doings/yuandan/getSuggestGames', params);
}
/**
 * 元旦 - 弹幕许愿
 * @param {string} content 许愿内容
 * */

export function ApiDoingsYuandanMessage(params = {}) {
  return request('/doings/yuandan/message', params);
}

/**
 * 元旦 - 弹幕列表
 * */

export function ApiDoingsYuandanMessageList(params = {}) {
  return request('/doings/yuandan/messagelist', params);
}

/**
 * 元旦 - 弹幕点赞
 * @param {int} id 用户id user_id
 * @param {string} action yes no
 * */

export function ApiDoingsYuandanThumb(params = {}) {
  return request('/doings/yuandan/thumb', params);
}

/**
 * 元旦 - 秒杀
 * @param {int} id
 * */

export function ApiDoingsYuandanSeckill(params = {}) {
  return request('/doings/yuandan/seckill', params);
}

/**
 * 元旦 - 秒杀列表
 * */

export function ApiDoingsYuandanSeckillList(params = {}) {
  return request('/doings/yuandan/seckilllist', params);
}

/**
 * 元旦 - 预约
 * */

export function ApiDoingsYuandanReservation(params = {}) {
  return request('/doings/yuandan/reservation', params);
}

/**
 * 元旦 - 查询微信绑定
 * */

export function ApiUserInviterExchangeWx(params = {}) {
  return request('/api/user/inviterExchangeWx ', params);
}

/**
 * 元旦 - 新年参与抽奖
 * */

export function ApiDoingsYuandanRaffle(params = {}) {
  return request('/doings/yuandan/raffle', params);
}

/**
 * 元旦 - 新年抽奖初始化
 * */

export function ApiDoingsYuandanCompleteRewardPage(params = {}) {
  return request('/doings/yuandan/completeRewardPage', params);
}
