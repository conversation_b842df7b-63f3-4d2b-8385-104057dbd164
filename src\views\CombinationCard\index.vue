<template>
  <div class="page combination-card-page">
    <div class="top-bar">
      <div class="gold-tips-btn" @click="goToRule"></div>
    </div>
    <div class="main">
      <div class="nav-tab-placeholder">
        <div class="nav-tab" :class="{ fixed: navTabFixed }" ref="navTabRef">
        <div class="nav-list">
          <div
            class="nav-item"
            v-for="(item, index) in navList"
            :key="index"
            :class="{ active: current === item.key }"
            @click="current = item.key"
          >
            {{ item.title }}
          </div>
        </div>
        <div class="tips">{{ tips[current] }}</div>
      </div>
      </div>
      <div class="card-list">
        <div
          class="card-item"
          v-for="(item, index) in pageData[current]"
          :key="index"
        >
          <div class="card-icon">
            <img :src="item.icon_url" alt="" />
          </div>
          <div class="card-right">
            <div class="card-name">{{ item.title }}</div>
            <div class="card-desc" v-html="formatPriceDesc(item.tips1)"></div>
            <div class="card-desc" v-html="formatPriceDesc(item.tips2)"></div>
            <div class="buy-info">
              <div class="price">¥{{ item.amount }}</div>
              <div class="num">已售{{ item.sold_num }}件</div>
              <div class="buy-btn btn" @click="clickBuy(item)">立即购买</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 支付弹窗抽屉 -->
    <pay-type-popup
      :show.sync="payPopupShow"
      :list="payList"
      @choosePayType="choosePayType"
      :money="Number(this.selectedMeal.amount)"
      unit="¥"
    ></pay-type-popup>
  </div>
</template>
<script>
import { BOX_login, BOX_openInNewWindow } from '@/utils/box.uni.js';
import { ApiWebGroupSvipIndex } from '@/api/views/combination_card.js';
import {
  ApiCreateOrderGroupSvip,
  ApiCreateOrderSvip,
  ApiSavingsCardCreateOrder,
  ApiGetPayUrl,
  ApiGetOrderStatus,
  ApiGetPaymentMethod,
} from '@/api/views/recharge.js';
import { getQueryVariable } from '@/utils/function.js';
import { mapActions } from 'vuex'
export default {
  name: 'combinationCard',
  data() {
    return {
      kf: getQueryVariable('kf'),
      current: 'group_svip',
      navList: [
        { title: '组合卡', key: 'group_svip' },
        { title: '省钱卡', key: 'saving_list' },
        { title: 'SVIP卡', key: 'svip_List' },
      ],
      tips: {
        group_svip:
          'tips：购买后获取的是金币，需要在金币商城手动兑换成平台币。',
        saving_list:
          'tips：购买后获取的是金币，需要开通SVIP身份后在金币商城手动兑换成平台币。',
        svip_List: 'tips：购买后获取的是金币，需要在金币商城手动兑换成平台币。',
      },
      pageData: {
        group_svip: [],
        saving_list: [],
        svip_List: [],
      },
      kf_msg: '', // 活动状态提示

      payPopupShow: false,
      selectedPayType: 'wx', // 支付方式

      payList: [],
      selectedMeal: {},

      navTabOffsetTop: 0,
      navTabFixed: false,
    };
  },
  computed: {
    orderType() {
      switch (this.current) {
        case 'group_svip':
          return 302;
        case 'saving_list':
          return 301;
        case 'svip_List':
          return 103;
      }
    },
  },
  async created() {
    document.title = '超值王炸双卡';
    await this.getIndexData();
  },
  mounted() {
    this.navTabOffsetTop = this.$refs.navTabRef?.offsetTop;
    window.addEventListener('scroll', this.handleScroll);
  },
  beforeDestroy() {
    window.removeEventListener('scroll', this.handleScroll);
  },
  methods: {
    ...mapActions({
      SET_USER_INFO: 'user/SET_USER_INFO',
    }),
    handleScroll() {
      const scrollTop =
        document.documentElement.scrollTop || document.body.scrollTop;
      // navTab要吸顶
      if (scrollTop > this.navTabOffsetTop && !this.navTabFixed) {
        this.navTabFixed = true;
      } else if (scrollTop <= this.navTabOffsetTop && this.navTabFixed) {
        this.navTabFixed = false;
      }
    },
    async getIndexData() {
      const res = await ApiWebGroupSvipIndex({
        kf: this.kf,
      });
      this.pageData = res.data;
      this.kf_msg = res.data.kf_msg;
      if (this.kf_msg) {
        this.$toast(this.kf_msg);
      }
    },
    async clickBuy(item) {
      if (this.kf_msg) {
        this.$toast(this.kf_msg);
        return;
      }

      if(this.userInfo.is_sqk_member && [301, 302].includes(this.orderType)) {
        this.$toast('您已享有省钱卡福利，暂不支持重复购买。');
        return
      }
      this.selectedMeal = item;

      await this.getPayMethod();
      this.payPopupShow = true;
    },
    async getPayMethod() {
      let res = await ApiGetPaymentMethod({
        orderType: this.orderType,
      });
      this.payList = res.data;
    },

    choosePayType(selectedPayType) {
      this.selectedPayType = selectedPayType.symbol;
      this.handlePay();
    },
    async handlePay() {
      this.payPopupShow = false;

      let orderParams = {}; // 订单参数
      let orderRes = {};

      // 分别下单
      switch (this.current) {
        case 'group_svip':
          orderParams = {
            id: this.selectedMeal.id,
            payWay: this.selectedPayType,
            kf: this.kf,
          };
          orderRes = await ApiCreateOrderGroupSvip(orderParams);
          break;
        case 'saving_list':
          orderParams = {
            type: this.selectedMeal.type,
            payWay: this.selectedPayType,
            kf: this.kf,
          };
          orderRes = await ApiSavingsCardCreateOrder(orderParams);
          break;
        case 'svip_List':
          orderParams = {
            day: this.selectedMeal.day,
            amount: this.selectedMeal.amount,
            rebate_gold: this.selectedMeal.rebate_gold,
            payWay: this.selectedPayType,
            is_cycle: 0,
            kf: this.kf,
          };
          orderRes = await ApiCreateOrderSvip(orderParams);
          break;
      }
      ApiGetPayUrl({
        orderId: orderRes?.data?.orderId,
        orderType: this.orderType,
        payWay: this.selectedPayType,
        packageName: '',
      }).finally(async () => {
        ApiGetOrderStatus({
          order_id: orderRes?.data?.orderId,
          order_type: this.orderType,
        });
        this.SET_USER_INFO();
        await this.getIndexData();
      });
    },
    goToRule() {
      BOX_openInNewWindow(
        { name: 'CombinationCardRule' },
        { url: `${window.location.origin}/#/combination_card_rule` },
      );
    },
    formatPriceDesc(str) {
      const regex = /(.+?)([\d\.]+元)(.+)/;
      return str.replace(regex, (match, p1, p2, p3) => {
        return `${p1}<span>${p2}</span>${p3}`;
      });
    },
  },
};
</script>

<style lang="less" scoped>
.combination-card-page {
  background-color: #161413;
  .top-bar {
    width: 100%;
    height: 435 * @rem;
    background: linear-gradient(
      180deg,
      #312d2b 0%,
      #191716 87%,
      rgba(22, 20, 19, 0) 100%
    );
    background: url(~@/assets/images/recharge/combination-top-bg.png) center top
      no-repeat;
    background-size: 100% 435 * @rem;
    position: relative;
    flex-shrink: 0;
    .gold-tips-btn {
      width: 31 * @rem;
      height: 89 * @rem;
      background: url(~@/assets/images/recharge/combination-gold-tips.png)
        center top no-repeat;
      background-size: 31 * @rem 89 * @rem;
      position: fixed;
      right: 0;
      top: 239 * @rem;
      z-index: 100;
    }
  }
  .main {
    // margin-top: -110*@rem;
    margin-top: -72 * @rem;
    background-color: #161413;
    .nav-tab-placeholder{
      height: 72 * @rem;
    }
    .nav-tab {
      box-sizing: border-box;
      max-width: 450px;
      position: relative;
      top: 0 * @rem;
      left: 50%;
      transform: translateX(-50%);
      width: 100%;
      padding-bottom: 20 * @rem;
      padding-top: 20 * @rem;
      background: url(~@/assets/images/recharge/combination-top-bg.png) center
        bottom no-repeat;
      background-size: 100% 435 * @rem;
      height: 72 * @rem;
      &.fixed {
        position: fixed;
        z-index: 10;
      }
      .nav-list {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 32 * @rem;
        .nav-item {
          box-sizing: border-box;
          width: 104 * @rem;
          height: 32 * @rem;
          background: #3a332b;
          border: 1 * @rem solid #85725d;
          border-radius: 8 * @rem;
          display: flex;
          justify-content: center;
          align-items: center;
          margin: 0 7 * @rem;
          font-size: 16 * @rem;
          color: #a6927a;
          font-weight: bold;
          &.active {
            transition: all 0.3s;
            color: #623712;
            border: 0;
            background: linear-gradient(90deg, #ffecd6 0%, #e1ae7e 100%);
          }
        }
      }
    }
    .tips {
      color: rgba(255, 0, 0, 0.863);
      // rgba(255, 0, 0, 0.753)
      height: 10 * @rem;
      line-height: 10 * @rem;
      flex-shrink: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 9 * @rem;
      position: absolute;
      bottom: 5 * @rem;
      width: 100%;
    }
    .card-list {
      box-sizing: border-box;
      width: 100%;
      border-radius: 16 * @rem 16 * @rem 0 0;
      background: #1f2020;
      border-top: 1px solid #5b534b;
      padding-bottom: 33 * @rem;
      .card-item {
        box-sizing: border-box;
        height: 131 * @rem;
        width: 351 * @rem;
        background: #393a3a;
        margin: 12 * @rem auto 0;
        border-radius: 8 * @rem;
        padding: 5 * @rem;
        display: flex;
        .card-icon {
          width: 121 * @rem;
          height: 121 * @rem;
          border-radius: 8 * @rem;
          overflow: hidden;
          img {
            object-fit: contain;
          }
        }
        .card-right {
          flex: 1;
          min-width: 0;
          margin-left: 12 * @rem;
          display: flex;
          flex-direction: column;

          .card-name {
            font-size: 16 * @rem;
            font-weight: bold;
            color: #ffffff;
            line-height: 32 * @rem;
          }
          .card-desc {
            font-size: 12 * @rem;
            color: #bba388;
            line-height: 22 * @rem;
            :deep(span) {
              color: #f3d0aa;
              font-weight: bold;
            }
          }
          .buy-info {
            display: flex;
            align-items: center;
            margin-top: auto;
            margin-right: 3 * @rem;
            margin-bottom: 2 * @rem;
            .price {
              font-size: 16 * @rem;
              color: #f3d0aa;
              font-weight: bold;
            }
            .num {
              font-size: 12 * @rem;
              color: #949696;
              margin-left: 6 * @rem;
              white-space: nowrap;
              overflow: hidden;
            }
            .buy-btn {
              flex-shrink: 0;
              width: 68 * @rem;
              height: 32 * @rem;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 12 * @rem;
              font-weight: bold;
              color: #ffffff;
              background: #1e1816;
              border-radius: 6 * @rem;
              margin-left: auto;
            }
          }
        }
      }
    }
  }
}
</style>
