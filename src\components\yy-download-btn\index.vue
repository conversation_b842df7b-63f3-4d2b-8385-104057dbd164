<template>
  <div class="yy-download-btn" @click.stop="handleClick()" v-if="dlConfig != 1">
    <slot>
      <div
        class="download-btn"
        :class="{
          'h5-url': gameInfo.h5_url,
          'buy':
            gameInfo.buy_info &&
            gameInfo.buy_info.status == 1 &&
            (!userInfo.token || userInfo.web_down_gq == 1),
        }"
        v-if="gameInfo.state != 2"
      >
        {{ buttonText }}
      </div>
      <yy-subcribe-btn :gameInfo="gameInfo" v-else></yy-subcribe-btn>
    </slot>
  </div>
</template>

<script>
import { downloadGame, startCloudGame, startH5Game } from '@/utils/function.js';
import { isIos, isAndroid } from '@/utils/userAgent';
import { mapGetters, mapMutations } from 'vuex';
import yySubcribeBtn from '@/components/yy-subscribe-btn';
export default {
  name: 'yyDownloadBtn',
  components: {
    yySubcribeBtn,
  },
  props: {
    gameInfo: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      loading: false,
      isIos,
      isAndroid,
    };
  },
  computed: {
    ...mapGetters({
      dlConfig: 'system/dlConfig',
    }),
    // 是否显示云玩
    showCloud() {
      return this.gameInfo.yyx_type
        ? parseInt(this.gameInfo.yyx_type.code) !== 0 && isIos
        : false;
    },
    // 按钮文案
    buttonText() {
      if (this.isIos && this.gameInfo.dl_config_i == 1) {
        if (
          this.gameInfo.buy_info?.status == 1 &&
          (!this.userInfo.token || this.userInfo.web_down_gq == 1)
        ) {
          // 需要购买并且充值了18元平台币了
          return `¥${this.gameInfo.buy_info?.buy_amount}`;
        }
        return this.$t('下载');
      } else if (this.gameInfo.h5_url) {
        return this.$t('开始玩');
      } else if (this.showCloud) {
        return this.$t('云玩');
      } else {
        if (this.gameInfo.buy_info?.status == 1) {
          // 需要购买
          return `¥${this.gameInfo.buy_info?.buy_amount}`;
        }
        return this.$t('下载');
      }
    },
  },
  methods: {
    ...mapMutations({
      setGameInfo: 'game/setGameInfo',
    }),
    async handleClick() {
      this.setGameInfo(this.gameInfo);
      if (this.isIos && this.gameInfo.dl_config_i == 1) {
        if (
          this.gameInfo.buy_info?.status == 1 ||
          this.userInfo.web_down_gq != 1
        ) {
          // 需要购买的或者充值不到18元平台币的
          this.toPage('GameDetail', { id: this.gameInfo.id });
          return;
        }
        downloadGame(this.gameInfo);
      } else if (this.gameInfo.h5_url) {
        this.startGame(this.gameInfo);
      } else if (this.showCloud) {
        startCloudGame(this.gameInfo.id);
      } else {
        if (this.gameInfo.buy_info?.status == 1) {
          this.toPage('GameDetail', { id: this.gameInfo.id });
          return;
        }
        downloadGame(this.gameInfo);
      }
    },
    // 开始游戏
    startGame(gameInfo) {
      this.$toast.loading({
        message: '加载中...',
        duration: 1000,
      });
      if (!this.userInfo.token) {
        this.$toast.clear();
      }
      startH5Game(gameInfo.h5_url, gameInfo.app_id);
    },
  },
};
</script>

<style lang="less" scoped>
.yy-download-btn {
  .download-btn {
    box-sizing: border-box;
    width: 64 * @rem;
    height: 30 * @rem;
    font-size: 12 * @rem;
    color: #fff;
    border-radius: 30 * @rem;
    background: @themeBg;
    display: flex;
    align-items: center;
    justify-content: center;
    &.h5-url {
      width: 64 * @rem;
      height: 30 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12 * @rem;
      color: #fffcfa;
      background: linear-gradient(270deg, #fe8661 0%, #fe4d42 100%);
      border-radius: 30 * @rem;
      border: none;
    }
    &.buy {
      width: 64 * @rem;
      height: 30 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12 * @rem;
      color: #fffcfa;
      background: #ff4d3b;
      border-radius: 30 * @rem;
      border: none;
    }
  }
}
</style>
