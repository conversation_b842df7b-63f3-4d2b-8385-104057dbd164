<template>
  <div class="black-wapper" v-if="isWechat">
    <div class="wechat-tips">
      <img class="big-img" src="@/assets/images/tips/wechat-tips.png" />
    </div>
  </div>
</template>

<script>
import {
  isWechat,
} from '@/utils/userAgent';

export default {
  data() {
    return {
      isWechat,
    };
  },
  created() {

    // 如果是微信引导防手势和超出隐藏
    if (this.isWechat) {
      document.getElementsByTagName('body')[0].style.overflow = 'hidden';
      document.addEventListener('gesturestart', function (event) {
        event.preventDefault();
      });
    }
  },
};
</script>

<style lang="less" scoped>
/deep/ .tips .van-overlay {
  z-index: 10000 !important;
}

/deep/ .tips-popup {
  z-index: 99999 !important;
}
.wechat-tips {
  background-color: rgba(0, 0, 0, 0.8);
  position: fixed;
  left: 0;
  top: 0;
  z-index: 10001;
  width: 100%;
  height: 100%;
  color: #fff;
  font-size: 0.388933rem;
}

.wechat-tips .big-img {
  position: absolute;
  top: 0;
  right: 0;
  width: 80%;
  height: auto;
}
</style>
