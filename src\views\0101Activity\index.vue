<template>
  <div class="shuangshier-page">
    <nav-bar-2
      ref="topNavBar"
      bgStyle="transparent-white"
      :azShow="true"
      :placeholder="false"
    ></nav-bar-2>
    <div class="bg1">
      <div class="time">12月31日00:00 - 01月02日23:59</div>
      <div class="user-container">
        <div class="user-info" v-if="userInfo.token">
          <div class="avatar">
            <UserAvatar />
          </div>
          <div class="nick-name">{{ userInfo.nickname }}</div>
        </div>
        <div class="user-info btn" @click="login" v-else>
          <div class="avatar">
            <div class="default-avatar"></div>
          </div>
          <div class="nick-name under">未登录</div>
        </div>
      </div>
      <div
        class="active-status btn"
        :class="{
          'not-yet': activity_status == 3,
          'active': activity_status == 1 && today_lottery_status != 3,
          'empty': activity_status == 1 && today_lottery_status == 3,
          'end': activity_status == 4,
        }"
        @click="handleLottery"
      ></div>
      <div class="point-container">
        <div class="left">
          <div class="text">
            剩余次数：<span>{{ lottery_count }}</span
            >次
          </div>
          <div @click="getActivityInfo" class="icon2 btn">【刷新】</div>
        </div>
        <a href="#section1" class="right btn"> 获取次数 </a>
      </div>
      <div class="right-button-container">
        <div class="button button1 btn" @click="toPage('0101Rule')"></div>
        <div class="button button2 btn" @click="getRecordList"></div>
      </div>
      <div class="bottom-button-container">
        <div
          :class="{ active: tab_current === 0 }"
          @click="tab_current = 0"
          class="button btn left"
        ></div>
        <div
          :class="{ active: tab_current === 1 }"
          @click="tab_current = 1"
          class="button btn right"
        ></div>
      </div>
    </div>
    <div class="tab-content">
      <div v-if="tab_current === 0" class="card-content">
        <div
          v-for="item in card_list"
          :key="item.prize_id"
          :class="[`card-item${item.prize_id}`, { empty: item.num == 0 }]"
          class="card-item"
        >
          <span>{{ item.num }}</span>
        </div>
      </div>
      <div v-if="tab_current === 1" class="card-exchange-content">
        <div class="list">
          <div v-for="(item, index) in card_exchange_list" class="item">
            <div class="left">
              <div class="big-text">
                {{ item.title
                }}<span v-if="item.count != 0"
                  >（{{ item.exchange_count }}/{{ item.count }}）</span
                >
              </div>
              <div class="small-text">{{ item.desc }}</div>
            </div>
            <div
              :class="{ empty: item.status == 1 }"
              @click="openCardExchangePopup(item)"
              class="button btn"
            >
              兑换
            </div>
          </div>
        </div>
      </div>
    </div>
    <div id="section1">
      <section class="section1">
        <div
          @click="explain_toast_show = !explain_toast_show"
          class="icon"
        ></div>
        <div v-if="explain_toast_show" class="tips">
          温馨提示：充值获取抽卡机会的，仅限游戏内使用微信/支付宝充值，如有使用金币/平台币/代金券抵扣的，则只计算抵扣后的现金充值金额
        </div>
        <div class="list">
          <div v-for="(item, index) in task_list" class="item">
            <div class="left">
              <div class="big-text">
                {{ item.title }}（{{ item.finish_count }}/{{ item.count }}）
              </div>
              <div class="small-text">{{ item.text }}</div>
            </div>
            <div
              :class="{ empty: item.is_finish != 1 }"
              @click="handleTaskExchange(item)"
              class="button btn"
            >
              兑换
            </div>
          </div>
        </div>
      </section>
    </div>
    <section class="section2">
      <div class="lottery-button">
        <div class="left">
          刮奖次数：<span class="number">{{ scratch_count }}</span
          >次
        </div>
        <div @click="getActivityInfo" class="button btn">刷新</div>
      </div>
      <div class="icon"></div>
      <div class="canvas-container">
        <div class="prize">{{ first_gold }}金币</div>
        <canvas
          id="canvas"
          @touchmove="touchMove"
          @touchstart="touchStart"
          @touchend="touchEnd"
        ></canvas>
      </div>
    </section>
    <section class="section3">
      <div class="left">
        <div class="big-text">2023金币</div>
        <div class="small-text">
          前30个集齐卡牌（2023元旦快乐）并兑换了最终奖励
        </div>
      </div>
      <div @click="toPage('0101NameList')" class="right button big-button btn">
        名单公示
      </div>
    </section>
    <!-- 刮奖记录 -->
    <van-popup
      v-model="record_list_popup"
      :lock-scroll="false"
      :close-on-click-overlay="false"
      class="popup record-list-popup"
    >
      <div class="popup-close" @click="closeRecordPopup"></div>
      <div class="title">刮奖记录</div>
      <div v-if="record_list.length > 0" class="list">
        <div v-for="(item, index) in record_list" :key="index" class="item">
          <div class="left">{{ item.title }}</div>
          <div v-html="item.desc" class="right"></div>
        </div>
      </div>
      <div v-else class="empty">暂无刮奖记录</div>
    </van-popup>
    <!-- 获取刮奖机会弹窗 -->
    <van-popup
      v-model="lottery_empty_popup"
      :lock-scroll="false"
      :close-on-click-overlay="false"
      class="popup lottery-empty-popup"
    >
      <div class="popup-close" @click="lottery_empty_popup = false"></div>
      <div class="title">当前未有剩余刮奖机会</div>
      <div class="text">集齐卡牌的玩家可获得一次瓜分2023万金币红包的机会</div>
      <div @click="lottery_empty_popup = false" class="bottom-button btn"></div>
    </van-popup>
    <!-- 获取抽奖机会弹窗 -->
    <van-popup
      v-model="point_empty_popup"
      :lock-scroll="false"
      :close-on-click-overlay="false"
      class="popup point-empty-popup"
    >
      <div class="popup-close" @click="point_empty_popup = false"></div>
      <div class="title">当前未有剩余抽卡机会</div>
      <div class="text">
        活动期间完成任务可获得抽卡机会每个用户每天最多可兑换七次
      </div>
      <div @click="point_empty_popup = false" class="bottom-button btn"></div>
    </van-popup>
    <!-- 卡片兑换确认弹窗 -->
    <van-popup
      v-model="card_exchange_popup"
      :lock-scroll="false"
      :close-on-click-overlay="true"
      class="popup message-popup"
    >
      <div class="text">
        {{
          current_card_id == 7
            ? '是否确认消耗卡牌兑换最终奖励？'
            : '兑换后将可能导致无法获得最终奖励，是否确认消耗卡牌兑换？'
        }}
      </div>
      <div @click="handleCardExchange" class="bottom-button btn"></div>
    </van-popup>
    <!-- 卡片兑换成功弹窗 -->
    <van-popup
      v-model="card_exchange_popup2"
      :lock-scroll="false"
      :show-confirm-button="false"
      :close-on-click-overlay="false"
      class="popup message-popup"
    >
      <div class="popup-close" @click="card_exchange_popup2 = false"></div>
      <div v-html="card_exchange_popup2_text" class="text"></div>
      <div
        @click="card_exchange_popup2 = false"
        class="bottom-button btn"
      ></div>
    </van-popup>
    <!-- 选择任意卡片弹窗 -->
    <van-popup
      v-model="choose_card_popup"
      :lock-scroll="false"
      class="popup choose-popup message-popup"
    >
      <div class="card-content">
        <div
          v-for="item in card_list.slice(0, 7)"
          :key="item.prize_id"
          :class="[
            `card-item${item.prize_id}`,
            { empty: choose_card_current != item.prize_id },
          ]"
          @click="
            choose_card_current =
              choose_card_current == item.prize_id ? -1 : item.prize_id
          "
          class="card-item"
        ></div>
      </div>
      <div @click="handleAnyCardExchange" class="bottom-button btn"></div>
    </van-popup>
    <!-- 引导svip充值弹窗 -->
    <van-popup
      :lock-scroll="false"
      v-model="svip_popup"
      class="popup svip-popup message-popup"
    >
      <div class="text">
        您当前SVIP剩余天数不足30天，续费SVIP满30天可获取机会哦~
      </div>
      <div @click="toRecharge" class="bottom-button btn"></div>
    </van-popup>
    <svip-recharge-popup @success="getActivityInfo"></svip-recharge-popup>
  </div>
</template>

<script>
import { BOX_login } from '@/utils/box.uni.js';
import {
  Api0101Index,
  Api0101CardList,
  Api0101TaskList,
  Api0101CardExchangeList,
  Api0101RecordExchange,
  Api0101Exchange,
  Api0101TaskExchange,
  Api0101Scratch,
  Api0101Lottery,
} from '@/api/views/0101.js';
import UserAvatar from '@/components/user-avatar';
import { mapGetters, mapMutations } from 'vuex';
import canvasImg from '@/assets/images/0101/gua-area.png';
import svipRechargePopup from '@/components/svip-recharge-popup';

export default {
  data() {
    return {
      explain_toast_show: false, //解释显示
      activity_status: 1, //1活动已开始 2 活动不存在 3活动未开始 4活动已结束
      scratch_count: 0, //今日刮奖剩余次数
      lottery_count: 0, //游戏充值抽奖剩余次数
      today_scratch_status: 0, //今日刮奖状态 1:可以刮奖 2不可刮奖有次数 3不可刮奖没次数
      today_lottery_status: 0, //今日抽奖状态 1:可以抽奖 2不可抽奖有次数 3不可抽奖没次数
      canvas: '', // 画布
      record_list_popup: false, //记录弹窗
      record_list: [], //记录列表
      lottery_empty_popup: false, //暂无刮奖机会弹窗
      point_empty_popup: false, //暂无获取积分机会弹窗
      scratch_status: 0, //刮奖状态 0未开始1刮奖中2刮奖完
      clearCount: 0, // 刮奖区被刮开的程度 最大150就全解开
      ctx: '', // 画笔
      ratio: 0,
      first_gold: 0, //刮奖中的金币
      tab_current: 0, //当前tab
      card_list: [], //卡片列表
      card_exchange_list: [], //卡牌兑好礼列表
      task_list: [], //任务列表
      finished: false, //防抖
      card_exchange_popup: false, //卡片兑换确认弹窗
      card_exchange_popup2: false, //卡片兑换成功弹窗
      card_exchange_popup2_text: false, //通用消息文案
      current_card_id: 0, //当前兑换的ID
      choose_card_popup: false, //选择卡片弹窗
      choose_card_current: -1, //当前选中卡片
      svip_popup: false, //svip引导弹窗
    };
  },
  computed: {
    active_status_text() {
      const arr = ['活动已开始', '活动不存在', '活动未开始', '活动已结束'];
      return arr[this.activity_status - 1];
    },
    ...mapGetters({
      userInfo: 'user/userInfo',
    }),
  },
  async created() {
    await this.getActivityInfo();
  },
  mounted() {
    this.ratio = document.body.clientWidth / 375;
    this.canvas = document.getElementById('canvas');
    this.canvas.width = this.canvas.clientWidth;
    this.canvas.height = this.canvas.clientHeight;
    this.ctx = this.canvas.getContext('2d');
    this.initCanvas();
  },
  activated() {
    this.getActivityInfo();
  },
  methods: {
    // 关闭记录弹窗
    closeRecordPopup() {
      this.record_list_popup = false;
    },
    // 初始化活动信息
    async getActivityInfo() {
      await this.getIndex();
      await this.getCardList();
      this.getTaskList();
      this.getCardExchangeList();
    },
    async getIndex() {
      const res = await Api0101Index();
      this.activity_status = parseInt(res.data.activity_status);
      this.first_gold = res.data.hb_gold;
      this.scratch_count = parseInt(res.data.remain_hb_count);
      this.lottery_count = parseInt(res.data.remain_lottery_count);
      this.today_scratch_status = parseInt(res.data.hb_status);
      this.today_lottery_status = parseInt(res.data.lottery_status);
    },
    // 获取卡牌列表
    async getCardList() {
      const res = await Api0101CardList();
      this.card_list = res.data.list;
    },
    // 获取任务列表
    async getTaskList() {
      const res = await Api0101TaskList();
      this.task_list = res.data.list;
    },
    // 获取卡牌兑换列表
    async getCardExchangeList() {
      const res = await Api0101CardExchangeList();
      this.card_exchange_list = res.data.list;
    },
    login() {
      BOX_login();
    },
    toRecharge() {
      if (!this.userInfo.token) {
        this.login();
        return false;
      }
      if (this.activity_status != 1) {
        this.$toast(this.active_status_text);
        return false;
      }
      this.svip_popup = false;
      this.setShowSvipRechargePopup(true);
    },
    async initCanvas() {
      this.ctx.globalCompositeOperation = 'source-over';
      let image = new Image();
      image.src = canvasImg;
      image.onload = async () => {
        await this.ctx.drawImage(
          image,
          0,
          0,
          this.canvas.width,
          this.canvas.height,
        );
      };
    },
    touchStart(e) {
      if (this.activity_status != 1) {
        this.$toast(this.active_status_text);
        return false;
      }
      if (!this.userInfo.token) {
        this.login();
        return false;
      }
      switch (this.scratch_status) {
        case 0: // 未开始
          if (this.today_scratch_status == 3) {
            this.$toast('次数已用完');
            return;
          } else if (this.scratch_count <= 0) {
            // 当没有刮奖次数时;
            this.lottery_empty_popup = true;
            return;
          } else {
            // 开始刮奖
            this.scratch_status = 1;
          }
          break;
        case 1: // 刮奖中
          break;
        case 2: // 已结束
          break;
      }
    },
    touchMove(e) {
      if (this.scratch_status == 1) {
        let x = e.touches[0].clientX - this.canvas.getBoundingClientRect().left,
          y = e.touches[0].clientY - this.canvas.getBoundingClientRect().top;
        e.preventDefault();
        this.ctx.clearRect(x, y, 15 * this.ratio, 15 * this.ratio);
        this.clearCount++;
      }
    },
    async touchEnd(e) {
      if (this.scratch_status == 1) {
        if (this.clearCount > 80) {
          // 当手指累计滑动超过80时触发清空矩形画布, 数值越大要刮得越久
          this.scratch_status = 2;
          this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
          await this.handleScratch();
        }
      }
    },
    // 处理刮奖
    async handleScratch() {
      try {
        const res = await Api0101Scratch();
        let {
          code,
          data: { gold },
        } = res;
        if (code > 0) {
          this.$toast(`恭喜获得${gold}金币`);
        }
      } finally {
        setTimeout(async () => {
          this.scratch_status = 0;
          this.clearCount = 0;
          this.initCanvas();
          await this.getActivityInfo();
        }, 500);
      }
    },
    // 处理任务兑换
    async handleTaskExchange(item) {
      // 未完成
      if (item.is_finish == 0) {
        if (item.mission_id == 1) {
          this.svip_popup = true;
        }
        return false;
      }
      // 已完成已领取
      if (item.is_finish == 2) {
        this.$toast('您已兑换过奖励~');
        return false;
      }
      if (item.mission_id == 4) {
        this.choose_card_popup = true;
        return false;
      }
      try {
        const res = await Api0101TaskExchange({ mission_id: item.mission_id });
        if (res.code == 3) {
          this.$toast(res.data);
        }
        this.getActivityInfo();
      } catch {}
    },
    // 处理卡牌兑换
    async handleCardExchange() {
      try {
        this.card_exchange_popup = false;
        const res = await Api0101Exchange({
          exchange_id: this.current_card_id,
        });
        this.card_exchange_popup2_text = res.data.title;
        this.card_exchange_popup2 = true;
        this.getActivityInfo();
      } catch {}
    },
    // 获取记录列表
    async getRecordList() {
      if (!this.userInfo.token) {
        this.login();
        return false;
      }
      if (this.activity_status != 1) {
        this.$toast(this.active_status_text);
        return false;
      }
      this.$toast({
        type: 'loading',
        duration: 0,
        message: '拼命加载中...',
        forbidClick: true,
      });
      try {
        const res = await Api0101RecordExchange();
        this.record_list = res.data.list;
        this.record_list_popup = true;
        this.$toast.clear();
      } finally {
      }
    },
    // 抽奖
    async handleLottery() {
      if (!this.userInfo.token) {
        this.login();
        return false;
      }
      if (this.activity_status != 1) {
        this.$toast(this.active_status_text);
        return false;
      }
      if (this.today_lottery_status == 2) {
        this.point_empty_popup = true;
        return false;
      }
      if (this.today_lottery_status == 3) {
        return false;
      }
      if (this.finished) return false;
      this.finished = true;
      this.$toast({
        type: 'loading',
        duration: 0,
      });
      try {
        const res = await Api0101Lottery();
        this.$toast(`恭喜获得“${res.data.title}”卡牌一张`);
        await this.getActivityInfo();
      } catch {
        this.$toast.clear();
      } finally {
        this.finished = false;
      }
    },
    // 打开卡片兑换确认弹窗
    openCardExchangePopup(item) {
      if (item.status == 1) {
        return false;
      }
      this.card_exchange_popup = true;
      this.current_card_id = item.exchange_id;
    },
    // 处理任意卡牌兑换
    async handleAnyCardExchange() {
      if (this.choose_card_current == -1) {
        this.$toast('请选择卡片');
        return false;
      }
      try {
        const res = await Api0101TaskExchange({
          mission_id: 4,
          prize_id: this.choose_card_current,
        });
        if (res.code == 3) {
          this.$toast(res.data);
          this.choose_card_popup = false;
          this.choose_card_current = -1;
        }
        this.getActivityInfo();
      } catch {}
    },
    ...mapMutations({
      setShowSvipRechargePopup: 'recharge/setShowSvipRechargePopup',
    }),
  },
  components: {
    svipRechargePopup,
    UserAvatar,
  },
};
</script>

<style lang="less" scoped>
.shuangshier-page {
  width: 100%;
  height: auto;
  background: #2f295c;
  font-family: PingFang SC-Medium, PingFang SC;
  overflow: hidden;
  .bg1 {
    position: relative;
    width: 100%;
    margin-bottom: 16 * @rem;
    height: 720 * @rem;
    .image-bg('~@/assets/images/0101/0101_bg1.png');
    .time {
      position: absolute;
      top: 180 * @rem;
      left: 50%;
      transform: translate(-50%, 0);
      width: 202 * @rem;
      height: 24 * @rem;
      color: #fff;
      text-align: center;
      line-height: 24 * @rem;
      background: rgba(0, 0, 0, 0.5);
      border-radius: 28 * @rem;
    }
    .user-container {
      position: absolute;
      top: 210 * @rem;
      left: 21 * @rem;
      .user-info {
        height: 100%;
        display: flex;
        align-items: center;
      }
      .avatar {
        width: 30 * @rem;
        height: 30 * @rem;
        .default-avatar {
          width: 100%;
          height: 100%;
          .image-bg('~@/assets/images/0101/0101_icon1.png');
        }
      }
      .nick-name {
        margin-left: 10 * @rem;
        color: #fff;
        font-size: 15 * @rem;
        &.under {
          text-decoration: underline;
        }
      }
    }
    .active-status {
      position: absolute;
      top: 500 * @rem;
      left: 54%;
      transform: translate(-50%, 0);
      width: 199 * @rem;
      height: 80 * @rem;
      &.not-yet {
        .image-bg('~@/assets/images/0101/0101_button6.png');
      }
      &.active {
        .image-bg('~@/assets/images/0101/0101_button7.png');
      }
      &.empty {
        .image-bg('~@/assets/images/0101/0101_button15.png');
      }
      &.end {
        .image-bg('~@/assets/images/0101/0101_button10.png');
      }
    }
    .point-container {
      position: absolute;
      top: 605 * @rem;
      left: 0 * @rem;
      width: 100%;
      box-sizing: border-box;
      display: flex;
      justify-content: space-between;
      padding: 0 18 * @rem;
      .left {
        display: flex;
        font-size: 14 * @rem;
        font-family: Alimama ShuHeiTi-Bold, Alimama ShuHeiTi;
        font-weight: bold;
        color: #ffffff;
        span {
          color: #12ff71;
        }
        .icon2 {
          margin-left: 13 * @rem;
        }
      }
      .right {
        color: #72fcbc;
        font-size: 14 * @rem;
        font-family: PingFang SC-Medium, PingFang SC;
        font-weight: 500;
        text-decoration: underline;
      }
    }
    .right-button-container {
      position: absolute;
      top: 180 * @rem;
      right: 5 * @rem;
      .button {
        width: 27 * @rem;
        height: 69 * @rem;
        &.button1 {
          .image-bg('~@/assets/images/0101/0101_button1.png');
          margin-bottom: 9 * @rem;
        }
        &.button2 {
          position: relative;
          .image-bg('~@/assets/images/0101/0101_button2.png');
        }
      }
    }
    .bottom-button-container {
      position: absolute;
      top: 641.5 * @rem;
      left: 0 * @rem;
      width: 100%;
      display: flex;
      .button {
        flex: 0 0 50%;
        height: 70 * @rem;
        &.active {
          height: 77 * @rem;
        }
        &.left {
          .image-bg('~@/assets/images/0101/0101_button8.png');
          &.active {
            .image-bg('~@/assets/images/0101/0101_button5.png');
          }
        }
        &.right {
          .image-bg('~@/assets/images/0101/0101_button3.png');
          &.active {
            .image-bg('~@/assets/images/0101/0101_button9.png');
          }
        }
      }
    }
  }
  .tab-content {
    .card-exchange-content {
      .list {
        .item {
          display: flex;
          flex-wrap: wrap;
          justify-content: space-between;
          align-items: center;
          background: #403877;
          margin: 12 * @rem;
          padding: 15 * @rem 10 * @rem;
          border-radius: 12 * @rem;
          .left {
            flex: 1;
            margin-right: 5 * @rem;
            .big-text {
              font-family: PingFang SC-Semibold, PingFang SC;
              font-weight: 600;
              font-size: 13 * @rem;
              margin-bottom: 8 * @rem;
              color: #fff;
            }
            .small-text {
              font-size: 12 * @rem;
              color: rgba(255, 255, 255, 0.8);
            }
          }
          .button {
            flex: 0 0 62 * @rem;
            width: 62 * @rem;
            height: 29 * @rem;
            .image-bg('~@/assets/images/0101/0101_button12.png');
            font-size: 14 * @rem;
            font-family: PingFang SC-Semibold, PingFang SC;
            font-weight: 600;
            color: #ffffff;
            text-align: center;
            line-height: 26 * @rem;
            &.empty {
              .image-bg('~@/assets/images/0101/0101_button11.png');
            }
          }
        }
      }
    }
  }
  .card-content {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    padding: 0 20 * @rem;
    .card-item {
      position: relative;
      width: 74 * @rem;
      height: 94 * @rem;
      margin-bottom: 16 * @rem;
      span {
        position: absolute;
        top: -5 * @rem;
        right: -5 * @rem;
        display: block;
        width: 22 * @rem;
        height: 22 * @rem;
        color: #fff;
        text-align: center;
        font-size: 11 * @rem;
        line-height: 22 * @rem;
        .image-bg('~@/assets/images/0101/0101_icon3.png');
      }
      &.card-item1 {
        .image-bg('~@/assets/images/0101/0101_img6.png');
      }
      &.card-item2 {
        .image-bg('~@/assets/images/0101/0101_img7.png');
      }
      &.card-item3 {
        .image-bg('~@/assets/images/0101/0101_img8.png');
      }
      &.card-item4 {
        .image-bg('~@/assets/images/0101/0101_img2.png');
      }
      &.card-item5 {
        .image-bg('~@/assets/images/0101/0101_img3.png');
      }
      &.card-item6 {
        .image-bg('~@/assets/images/0101/0101_img4.png');
      }
      &.card-item7 {
        .image-bg('~@/assets/images/0101/0101_img5.png');
      }
      &.card-item9 {
        .image-bg('~@/assets/images/0101/0101_img1.png');
      }
      &.empty {
        span {
          .image-bg('~@/assets/images/0101/0101_icon4.png');
        }
        &.card-item1 {
          .image-bg('~@/assets/images/0101/0101_img12.png');
        }
        &.card-item2 {
          .image-bg('~@/assets/images/0101/0101_img13.png');
        }
        &.card-item3 {
          .image-bg('~@/assets/images/0101/0101_img14.png');
        }
        &.card-item4 {
          .image-bg('~@/assets/images/0101/0101_img19.png');
        }
        &.card-item5 {
          .image-bg('~@/assets/images/0101/0101_img15.png');
        }
        &.card-item6 {
          .image-bg('~@/assets/images/0101/0101_img16.png');
        }
        &.card-item7 {
          .image-bg('~@/assets/images/0101/0101_img17.png');
        }
        &.card-item9 {
          .image-bg('~@/assets/images/0101/0101_img18.png');
        }
      }
    }
  }
  #section1 {
    overflow: hidden;
  }
  section {
    position: relative;
    border: 10 * @rem solid #9ea7f3;
    border-radius: 5 * @rem;
    margin: 53 * @rem 15 * @rem;
    padding: 32 * @rem 10 * @rem 16 * @rem 18 * @rem;
    color: #fff;
    &.section1 {
      &::before {
        position: absolute;
        top: -42 * @rem;
        left: -12 * @rem;
        content: '';
        width: 162 * @rem;
        height: 69 * @rem;
        .image-bg('~@/assets/images/0101/0101_img10.png');
      }
      .icon {
        position: absolute;
        top: 7 * @rem;
        left: 46%;
        width: 14 * @rem;
        height: 14 * @rem;
        .image-bg('~@/assets/images/0101/0101_icon2.png');
      }
      .tips {
        position: absolute;
        top: 35 * @rem;
        left: -13 * @rem;
        z-index: 999;
        padding: 10 * @rem 15 * @rem;
        line-height: 16 * @rem;
        border-radius: 10 * @rem;
        background: rgba(0, 0, 0, 1);
        font-size: 11 * @rem;
        color: #ffffff;
        &::before {
          content: '';
          position: absolute;
          top: -5 * @rem;
          left: 50%;
          z-index: 1;
          transform: translate(-50%) rotate(45deg);
          width: 10 * @rem;
          height: 10 * @rem;
          background-color: rgba(0, 0, 0, 1);
        }
      }
      .list {
        .item {
          display: flex;
          flex-wrap: wrap;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 18 * @rem;
          .left {
            .big-text {
              font-family: PingFang SC-Semibold, PingFang SC;
              font-weight: 600;
              font-size: 14 * @rem;
              margin-bottom: 6 * @rem;
            }
            .small-text {
              font-size: 12 * @rem;
              color: rgba(255, 255, 255, 0.8);
            }
          }
          .right {
          }
        }
      }
    }
    &.section2 {
      padding-top: 20 * @rem;
      &::before {
        position: absolute;
        top: -49 * @rem;
        left: -12 * @rem;
        content: '';
        width: 162 * @rem;
        height: 69 * @rem;
        .image-bg('~@/assets/images/0101/0101_img21.png');
      }
      .lottery-button {
        width: 100%;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .left {
          font-size: 14 * @rem;
          font-family: PingFang SC-Medium, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          .number {
            margin: 0 5 * @rem;
            color: #12ff71;
          }
        }
      }
      .icon {
        width: 272 * @rem;
        height: 20 * @rem;
        margin: 14 * @rem auto;
        .image-bg('~@/assets/images/0101/0101_img11.png');
      }
      .canvas-container {
        position: relative;
        width: 100%;
        .prize {
          width: 265 * @rem;
          height: 96 * @rem;
          margin: 0 auto;
          line-height: 96 * @rem;
          background: #f5f5f5;
          font-size: 35 * @rem;
          letter-spacing: 3 * @rem;
          text-align: center;
          color: #f7572d;
          font-weight: bold;
          border-radius: 12 * @rem;
        }
        #canvas {
          width: 265 * @rem;
          height: 96 * @rem;
          position: absolute;
          top: 0;
          left: 50%;
          transform: translate(-50%, 0);
        }
      }
    }
    &.section3 {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-bottom: 36 * @rem;
      .left {
        width: 190 * @rem;
        .big-text {
          font-size: 14 * @rem;
          font-family: PingFang SC-Semibold, PingFang SC;
          font-weight: 600;
          margin-bottom: 7 * @rem;
        }
        .small-text {
          font-size: 12 * @rem;
          color: rgba(255, 255, 255, 0.8);
        }
      }
      &::before {
        position: absolute;
        top: -49 * @rem;
        left: -12 * @rem;
        content: '';
        width: 162 * @rem;
        height: 69 * @rem;
        .image-bg('~@/assets/images/0101/0101_img20.png');
      }
    }
    .button {
      width: 62 * @rem;
      height: 29 * @rem;
      .image-bg('~@/assets/images/0101/0101_button12.png');
      font-size: 14 * @rem;
      font-family: PingFang SC-Semibold, PingFang SC;
      font-weight: 600;
      color: #ffffff;
      text-align: center;
      line-height: 26 * @rem;
      &.big-button {
        width: 72 * @rem;
        .image-bg('~@/assets/images/0101/0101_button13.png');
      }
      &.empty {
        .image-bg('~@/assets/images/0101/0101_button11.png');
      }
    }
  }
}
.popup {
  width: 290 * @rem;
  padding: 18 * @rem;
  box-shadow: 0 * @rem 2 * @rem 15 * @rem 0 * @rem rgba(248, 0, 0, 0.1);
  border-radius: 20 * @rem;
  background: #f8f9ff;
  overflow: hidden;
  .text {
    font-size: 14 * @rem;
    color: #835cad;
  }
  .popup-close {
    width: 33 * @rem;
    height: 27 * @rem;
    background: #835cad url(~@/assets/images/0101/popup-close.png) center center
      no-repeat;
    background-size: 22 * @rem 22 * @rem;
    position: absolute;
    right: -1 * @rem;
    top: -1 * @rem;
    border-radius: 0 12 * @rem 0 12 * @rem;
  }
  .title {
    margin-bottom: 15 * @rem;
    text-align: center;
    font-size: 16 * @rem;
    font-family: PingFang SC-Semibold, PingFang SC;
    font-weight: 600;
    color: #835cad;
  }
  &.message-popup {
    .text {
      width: 254 * @rem;
      margin: 0 auto;
      padding: 20 * @rem 0;
      font-size: 14 * @rem;
      font-family: PingFang SC-Semibold, PingFang SC;
      font-weight: 600;
      color: #7b74aa;
      line-height: 18 * @rem;
      text-align: center;
    }
    .bottom-button {
      .image-bg('~@/assets/images/0101/0101_button16.png');
    }
  }
  .bottom-button {
    width: 254 * @rem;
    height: 48 * @rem;
    margin: 15 * @rem auto 0;
    .image-bg('~@/assets/images/0101/0101_button14.png');
  }
  &.svip-popup {
    .bottom-button {
      .image-bg('~@/assets/images/0101/0101_button17.png');
    }
  }
}
.record-list-popup {
  padding: 18 * @rem 0;
  height: 245 * @rem;
  .list {
    height: 210 * @rem;
    overflow-y: scroll;
    padding: 0 18 * @rem;
    .item {
      display: flex;
      justify-content: space-between;
      height: 30 * @rem;
      align-items: center;
      font-size: 14 * @rem;
      color: #835cad;
      .left {
        flex: 0 0 90 * @rem;
      }
      .right {
        flex: 1;
        text-align: right;
      }
      span {
        color: #f8582e;
      }
    }
  }
  .empty {
    width: 100%;
    height: 210 * @rem;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #835cad;
  }
}
.choose-popup {
  .card-content {
    .card-item {
      &.current {
        border: 1 * @rem solid #f60;
        border-radius: 10 * @rem;
      }
    }
  }
}
</style>
<style lang="less">
.message-popup span {
  color: #f8582e;
}
.record-list-popup .item .right span {
  color: #f8582e;
}
</style>
