<template>
  <div class="pull-refresh">
    <van-pull-refresh
      v-model="loading"
      success-text="刷新成功"
      loosing-text="释放即可刷新..."
      pulling-text="下拉刷新..."
      loading-text="刷新中..."
      @refresh="onRefresh"
    >
      <slot></slot>
    </van-pull-refresh>
  </div>
</template>

<script>
import { PullRefresh } from 'vant';
export default {
  name: 'PullRefresh',
  components: {
    'van-pull-refresh': PullRefresh,
  },
  model: {
    prop: 'reloading',
    event: 'update',
  },
  props: {
    reloading: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      loading: this.reloading,
    };
  },
  watch: {
    loading(newVal) {
      this.$emit('update', newVal);
    },
    reloading(newVal) {
      this.loading = newVal;
    },
  },
  methods: {
    onRefresh() {
      this.$emit('refresh');
    },
  },
};
</script>

<style lang="less" scoped>
.pull-refresh {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  flex-shrink: 0;
  min-height: 0;
  /deep/ .van-pull-refresh {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    flex-shrink: 0;
  }
  /deep/ .van-pull-refresh__track {
    flex-grow: 1;
    flex-shrink: 0;
  }
}
</style>
