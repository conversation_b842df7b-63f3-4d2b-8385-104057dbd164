<template>
  <div>
    <van-popup
      v-model="popup"
      position="bottom"
      class="check-probability-popup"
      :lock-scroll="false"
    >
      <div class="title-container">
        <div class="top-title">
          <div class="title">查概率</div>
        </div>
        <div class="close-btn" @click="closePopup"></div>
      </div>
      <div class="check-probability-container">
        <div class="tips">* 手机需 4级及以上福袋可抽取</div>
        <div class="current-info">
          当前福袋等级：{{ currentlevel }}级，<span v-if="currentlevel < 10">
            {{ nextlevel }}级</span
          >奖品及概率如下
        </div>
        <div
          v-for="(group, groupIndex) in groupedProbabilityList"
          :key="groupIndex"
          class="current-box"
        >
          <div v-for="item in group" :key="item.id" class="box-item">
            <div class="item-probability">
              <div class="probability">{{ item.formattedProbability }}</div>
              <div
                v-if="item.probabilityChange"
                class="ranking"
                :class="{
                  'ranking-up': item.probabilityChange == 1,
                  'ranking-down': item.probabilityChange == 2,
                }"
              ></div>
              <div
                v-if="item.is_remove && currentlevel >= 4"
                class="clear-probability"
              ></div>
            </div>
            <div class="item-prize">
              <div class="prize">
                <div class="prize-img">
                  <!-- <img :src="item.prizeInfo.icon" alt="" /> -->
                  <img
                    v-if="
                      item.prizeInfo.prize_id == 1 ||
                      item.prizeInfo.prize_id == 2 ||
                      item.prizeInfo.prize_id == 3
                    "
                    class="img1"
                    src="@/assets/images/25newyear/gl-icon-jb.png"
                    alt=""
                  />
                  <img
                    v-if="
                      item.prizeInfo.prize_id == 4 ||
                      item.prizeInfo.prize_id == 5
                    "
                    class="img1"
                    src="@/assets/images/25newyear/gl-icon-ptb.png"
                    alt=""
                  />
                  <img
                    v-if="item.prizeInfo.prize_id == 6"
                    class="img2"
                    src="@/assets/images/25newyear/gl-icon-20.png"
                    alt=""
                  />
                  <img
                    v-if="item.prizeInfo.prize_id == 7"
                    class="img2"
                    src="@/assets/images/25newyear/gl-icon-50.png"
                    alt=""
                  />
                  <img
                    v-if="item.prizeInfo.prize_id == 8"
                    class="img2"
                    src="@/assets/images/25newyear/gl-icon-100.png"
                    alt=""
                  />
                  <img
                    v-if="item.prizeInfo.prize_id == 9"
                    class="img3"
                    src="@/assets/images/25newyear/gl-icon-hmsj.png"
                    alt=""
                  />
                </div>
                <span>{{ item.prizeInfo.title }}</span>
                <div
                  v-if="item.is_remove && currentlevel >= 4"
                  class="clear-prize1"
                ></div>
              </div>
            </div>
            <div v-if="item.is_add" class="new-prize"></div>
            <div
              v-if="item.is_remove && currentlevel >= 4"
              class="clear-prize2"
            ></div>
            <div class="probability-line"></div>
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { ApiDoingsChunjieProbability } from '@/api/views/25_new_year_activity.js';
export default {
  name: 'checkProbabilityPopup',
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    currentlevel: {
      type: Number,
      default: 0,
    },
    nextlevel: {
      type: Number,
      default: 0,
    },
    probabilityList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      // currentlevel: 0,
      // nextlevel: 0,
      // probabilityList: [
      //   {
      //     id: 1,
      //     icon: "",
      //     title: "888金币",
      //     isNewAdd: false,
      //     isClear: false,
      //     num: "78.00",
      //   },
      //   {
      //     id: 2,
      //     icon: "",
      //     title: "888金币",
      //     isNewAdd: false,
      //     isClear: false,
      //     num: "78.00",
      //   },
      //   {
      //     id: 3,
      //     icon: "",
      //     title: "888金币",
      //     isNewAdd: false,
      //     isClear: false,
      //     num: "78.00",
      //   },
      //   {
      //     id: 4,
      //     icon: "",
      //     title: "100元京东卡",
      //     isNewAdd: false,
      //     isClear: false,
      //     num: "78.00",
      //   },
      //   {
      //     id: 5,
      //     icon: "",
      //     title: "20元京东卡",
      //     isNewAdd: false,
      //     isClear: false,
      //     num: "78.00",
      //   },
      //   {
      //     id: 6,
      //     icon: "",
      //     title: "红米手机",
      //     isNewAdd: true,
      //     isClear: false,
      //     num: "78.00",
      //   },
      //   {
      //     id: 7,
      //     icon: "",
      //     title: "100元京东卡",
      //     isNewAdd: false,
      //     isClear: false,
      //     num: "78.00",
      //   },
      //   {
      //     id: 8,
      //     icon: "",
      //     title: "888金币",
      //     isNewAdd: false,
      //     isClear: true,
      //     num: "00.00",
      //   },
      //   {
      //     id: 9,
      //     icon: "",
      //     title: "888金币",
      //     isNewAdd: false,
      //     isClear: false,
      //     num: "78.00",
      //   },
      // ],
      // probabilityList: [],
    };
  },
  computed: {
    groupedProbabilityList() {
      // 将 probabilityList 按每三项分组
      const grouped = [];
      for (let i = 0; i < this.probabilityList.length; i += 3) {
        grouped.push(this.probabilityList.slice(i, i + 3));
      }
      return grouped;
    },
    popup: {
      get() {
        return this.show;
      },
      set(value) {
        this.$emit('update:show', value);
      },
    },
  },
  watch: {
    popup: {
      async handler(val, oldVal) {
        if (val) {
          // await this.getChunjieProbabilityInfo();
        }
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    closePopup() {
      this.popup = false;
    },
    async getChunjieProbabilityInfo() {
      const res = await ApiDoingsChunjieProbability();
      this.currentlevel = res.data.currentlevel;
      this.nextlevel = res.data.nextlevel;
      if (this.currentlevel < 4) {
        const filteredData = res.data.prizes.filter(
          item => item.formattedProbability !== '0%',
        );
        this.probabilityList = filteredData;
      } else {
        this.probabilityList = res.data.prizes;
      }
    },
  },
};
</script>

<style lang="less" scoped>
.check-probability-popup {
  box-sizing: border-box;
  padding: 20 * @rem 0 20 * @rem;
  border-radius: 20 * @rem 20 * @rem 0 0;
  // height: 568 * @rem;
  display: flex;
  flex-direction: column;
  background: #ffffff;
  .title-container {
    position: relative;
    height: 24 * @rem;
    .title-pic {
      width: 268 * @rem;
      height: 24 * @rem;
      margin: 0 auto;
    }
    .top-title {
      display: flex;
      align-items: center;
      justify-content: center;
      .title {
        position: relative;
        height: 23 * @rem;
        font-weight: 600;
        font-size: 18 * @rem;
        color: #5a2a2a;
        line-height: 23 * @rem;
        text-align: center;
        &::before {
          content: '';
          position: absolute;
          left: -38 * @rem;
          top: 50%;
          transform: translateY(-50%);
          width: 32 * @rem;
          height: 32 * @rem;
          background: url(~@/assets/images/25newyear/bt_left.png) no-repeat;
          background-size: 32 * @rem 32 * @rem;
        }
        &::after {
          content: '';
          position: absolute;
          right: -38 * @rem;
          top: 50%;
          transform: translateY(-50%);
          width: 32 * @rem;
          height: 32 * @rem;
          background: url(~@/assets/images/25newyear/bt_right.png) no-repeat;
          background-size: 32 * @rem 32 * @rem;
        }
      }
    }
    .close-btn {
      width: 13 * @rem;
      height: 13 * @rem;
      background: url('~@/assets/images/25newyear/btn-close.png') center top
        no-repeat;
      background-size: 13 * @rem 13 * @rem;
      position: absolute;
      top: -4 * @rem;
      right: 16 * @rem;
    }
  }
  .check-probability-container {
    box-sizing: border-box;
    flex: 1;
    overflow-y: auto;
    color: rgba(197, 102, 57, 1);
    margin-top: 11 * @rem;
    padding: 10 * @rem 20 * @rem;
    .tips {
      height: 20 * @rem;
      font-weight: 400;
      font-size: 13 * @rem;
      color: #7a5252;
      line-height: 20 * @rem;
      text-align: left;
    }
    .current-info {
      margin-top: 6 * @rem;
      height: 21 * @rem;
      font-weight: 500;
      font-size: 14 * @rem;
      color: #7a5252;
      line-height: 21 * @rem;
      text-align: left;
    }
    .current-box {
      margin-top: 34 * @rem;
      width: 100%;
      box-sizing: border-box;
      display: flex;
      // justify-content: space-around;
      .box-item {
        width: 33.3%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        position: relative;
        .item-probability {
          position: relative;
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 2;
          width: 72 * @rem;
          height: 24 * @rem;
          background: #ffd1d1;
          border-radius: 20 * @rem;
          .probability {
            position: relative;
            height: 24 * @rem;
            text-align: center;
            font-weight: 600;
            font-size: 13 * @rem;
            color: #a71111;
            display: flex;
            align-items: center;
            justify-content: center;
          }
          .ranking {
            margin-left: 3 * @rem;
            width: 7 * @rem;
            height: 10 * @rem;

            &.ranking-up {
              background: url(~@/assets/images/25newyear/ranking-up.png) center
                top no-repeat;
              background-size: 7 * @rem 10 * @rem;
            }
            &.ranking-down {
              background: url(~@/assets/images/25newyear/ranking-down.png)
                center top no-repeat;
              background-size: 7 * @rem 10 * @rem;
            }
          }
          .clear-probability {
            position: absolute;
            z-index: 9;
            border-radius: 20 * @rem;
            top: 0;
            width: 72 * @rem;
            height: 24 * @rem;
            background: rgba(179, 179, 179, 0.5);
          }
        }
        .item-prize {
          margin-top: 6 * @rem;
          width: 80 * @rem;
          height: 80 * @rem;

          .prize {
            position: relative;
            background: #fff3ea;
            border-radius: 12 * @rem;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: space-between;
            .prize-img {
              flex: 1;
              display: flex;
              align-items: center;
              justify-content: center;
              .img1 {
                width: 36 * @rem;
                height: 36 * @rem;
              }
              .img2 {
                width: 40 * @rem;
                height: 25 * @rem;
              }
              .img3 {
                width: 29 * @rem;
                height: 36 * @rem;
              }
            }
            span {
              margin-bottom: 9 * @rem;
              font-weight: 400;
              font-size: 12 * @rem;
              color: #7a5252;
            }
            .clear-prize1 {
              position: absolute;
              width: 80 * @rem;
              height: 80 * @rem;
              border-radius: 12 * @rem;
              top: 0;
              background: rgba(179, 179, 179, 0.5);
            }
          }
        }
        .new-prize {
          position: absolute;
          top: -22 * @rem;
          background: url('~@/assets/images/25newyear/new-prize-icon.png')
            center center no-repeat;
          background-size: 64 * @rem 20 * @rem;
          width: 64 * @rem;
          height: 20 * @rem;
          z-index: 9;
        }
        .clear-prize2 {
          position: absolute;
          top: -22 * @rem;
          background: url('~@/assets/images/25newyear/clear-prize-icon.png')
            center center no-repeat;
          background-size: 52 * @rem 20 * @rem;
          width: 52 * @rem;
          height: 20 * @rem;
          z-index: 9;
        }
        .probability-line {
          position: absolute;
          top: 0;
          box-sizing: border-box;
          width: 100%;
          margin-top: 10 * @rem;
          height: 4 * @rem;
          background: #f8f7f6;
          border-radius: 4 * @rem;
          z-index: 1;
        }
      }
    }
  }
}
</style>
