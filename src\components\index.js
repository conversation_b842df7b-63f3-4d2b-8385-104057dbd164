import Vue from 'vue';
import StatusBar from './status-bar';
import BottomSafeArea from './bottom-safe-area';
import NavBar2 from './nav-bar-2';
import PullRefresh from './pull-refresh';
import ContentEmpty from './content-empty';
import LoadMore from './load-more';
import YyList from './yy-list';
import UserAvatar from './user-avatar';
import RubberBand from './rubber-band';
import EwmPopup from './ewm-popup';
import PayTypePopup from './pay-type-popup';
import LoginPopup from './login-popup';
// vant
import {
  Dialog,
  Empty,
  Toast,
  List,
  Collapse,
  CollapseItem,
  DatetimePicker,
  Tab,
  Tabs,
  Sidebar,
  SidebarItem,
  DropdownMenu,
  DropdownItem,
  PullRefresh as PullRefresh2,
  Uploader,
  ImagePreview,
  RadioGroup,
  Radio,
  Swipe,
  SwipeItem,
  Popup,
  SwipeCell,
  Sticky,
  Lazyload,
  Loading,
  Rate,
  ActionSheet,
  IndexBar,
  IndexAnchor,
  Stepper,
  NoticeBar,
  Field,
  Area,
} from 'vant';
Vue.use(Dialog)
  .use(Empty)
  .use(Toast)
  .use(List)
  .use(Collapse)
  .use(CollapseItem)
  .use(DatetimePicker)
  .use(Tab)
  .use(Tabs)
  .use(Sidebar)
  .use(SidebarItem)
  .use(DropdownMenu)
  .use(DropdownItem)
  .use(PullRefresh2)
  .use(Uploader)
  .use(ImagePreview)
  .use(Radio)
  .use(RadioGroup)
  .use(Swipe)
  .use(SwipeItem)
  .use(SwipeCell)
  .use(Popup)
  .use(Sticky)
  .use(Loading)
  .use(Rate)
  .use(ActionSheet)
  .use(IndexBar)
  .use(IndexAnchor)
  .use(Stepper)
  .use(NoticeBar)
  .use(Lazyload)
  .use(Field)
  .use(Area);

// 复制插件
import VueClipboard from 'vue-clipboard2';
Vue.use(VueClipboard);

// 全局轮播
import VueAwesomeSwiper from 'vue-awesome-swiper';
import 'swiper/css/swiper.css';
Vue.use(VueAwesomeSwiper /* { default options with global component } */);

// 自定义组件对象
const components = {
  StatusBar,
  BottomSafeArea,
  NavBar2,
  PullRefresh,
  ContentEmpty,
  LoadMore,
  YyList,
  UserAvatar,
  RubberBand,
  EwmPopup,
  PayTypePopup,
  LoginPopup,
};
// 自定义组件全局注册 ---s
Object.keys(components).forEach(key => {
  Vue.component(`${key}`, components[key]);
});
