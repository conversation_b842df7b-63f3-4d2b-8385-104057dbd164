{"name": "activity_box3733_vue", "version": "0.1.0", "private": true, "scripts": {"serve": "set NODE_OPTIONS=--openssl-legacy-provider & vue-cli-service serve  && webpack-dev-server --open", "build": "set NODE_OPTIONS=--openssl-legacy-provider & vue-cli-service build --mode build && vue-cli-service build --mode test", "prettier": "prettier --write \"src/**/*.{js,vue,json,css,scss,less,md}\""}, "dependencies": {"@vant/area-data": "^2.0.0", "animate.css": "^4.1.1", "axios": "^0.21.1", "clipboard": "^2.0.8", "core-js": "^3.6.5", "cropperjs": "^1.5.12", "crypto-js": "^4.1.1", "dayjs": "^1.11.13", "html2canvas": "^1.4.1", "js-md5": "^0.7.3", "jsencrypt": "^3.2.1", "less": "^4.1.1", "less-loader": "^6.0.0", "lottie-web": "^5.12.2", "qrcodejs2": "0.0.2", "qs": "^6.10.1", "swiper": "^5.4.5", "vant": "^2.12.27", "vue": "^2.6.11", "vue-awesome-swiper": "^4.1.1", "vue-clipboard2": "^0.3.1", "vue-danmaku": "^1.7.2", "vue-i18n": "^8.26.7", "vue-router": "^3.2.0", "vuex": "^3.4.0"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "~4.5.0", "babel-plugin-import": "^1.13.3", "mockjs": "^1.1.0", "sass-loader": "^8.0.2", "style-resources-loader": "^1.4.1", "vue-template-compiler": "^2.6.11", "webpack-dev-server": "^4.9.0"}}