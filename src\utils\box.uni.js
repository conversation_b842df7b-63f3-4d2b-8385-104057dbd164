import { isIos, isIosBox, isAndroidBox, isInIframe } from '@/utils/userAgent';
import store from '@/store';
import router from '@/router';
import { devLog } from './function';

export let platform = platformInit();
export let authInfo = BOX_getAuthInfo();
export let packageName = BOX_getPackageName();
export let from = 0;

let token;
let uuid;

// 初始化，用来判断平台后做最初的判断
export async function boxInit () {
  switch (platform) {
    case 'android':
    case 'ios':
      from = fromInit();
      token = getToken();
      await handleStore();
      break;
    case 'iframe':
      break;
    default:
      token = store.getters['user/userInfo']?.token || '';
      // from 测试 ==> 安卓 775 ios 776    正式 ==> 安卓 501 ios 502
      if (process.env.NODE_ENV == 'development') {
        from = isIos ? 776 : 775;
      } else {
        from = isIos ? 502 : 501;
      }
      await handleStore();
      break;
  }
}

function platformInit () {
  let platform = ''; // 平台
  if (isInIframe) {
    platform = 'iframe';
    return platform;
  }
  try {
    if (isIos && getPostData() != undefined) {
      if (!isIosBox) {
        platform = 'ios'; // ios官包(暂时没有)
      } else {
        platform = 'iosBox'; // ios马甲包
      }
    } else {
      BOX.getFrom();
      if (!isAndroidBox) {
        platform = 'android'; // 安卓官包
      } else {
        platform = 'androidBox'; // 安卓马甲包
      }
    }
  } catch (e) {
    platform = 'h5'; // webapp
  }
  return platform;
}

function fromInit () {
  let from = '';
  switch (platform) {
    case 'iframe':
      break;
    case 'ios':
      from = getEval().from;
      break;
    case 'android':
      from = BOX.getFrom();
      break;
    case 'iosBox':
    case 'androidBox':
    case 'h5':
      break;
  }
  return from;
}
export function getToken () {
  let token = '';
  switch (platform) {
    case 'ios':
      token = getEval().token;
      break;
    case 'android':
      token = BOX.getToken();
      break;
    case 'iosBox':
    case 'androidBox':
    case 'h5':
      break;
  }
  return token;
}

export function BOX_getAuthInfo () {
  var result;
  try {
    switch (platform) {
      case 'ios':
      case 'iosBox':
        result = getEval();
        break;
      case 'android':
      case 'androidBox':
        result = JSON.parse(BOX.getAuthInfo());
        break;
      case 'h5':
        result = false;
        break;
    }
  } catch (e) {
    result = false;
  }
  return result;
}

/**
 * @param web webapp相关的参数 如 name(webapp页面的name)、 params(webapp页面的params)、 h5_url
 * @param app app相关的参数 如 url(h5链接)、 page(原生页)
 */
// 在浏览器中打开
export const BOX_openInBrowser = (web, app) => {
  let result;
  try {
    switch (platform) {
      case 'iframe':
        parent.postMessage(
          {
            from: 'activity',
            type: 'openInBrowser',
            data: { url: web.h5_url, open_type: web.open_type },
          },
          '*',
        );
        break;
      case 'iosBox':
        window.webkit.messageHandlers.openInBrowser.postMessage(web.h5_url);
        break;
      case 'android':
        BOX.openInBrowser(app.url);
        break;
      case 'androidBox':
        BOX.openInBrowser(web.h5_url);
        break;
      case 'h5':
        if (web.open_type) {
          window.open(web.h5_url);
        } else {
          window.location.href = web.h5_url;
        }
        break;
    }
    result = true;
  } catch (e) {
    result = false;
  }
  return result;
};

// APP内新窗口打开(不带导航栏)
// ios中的openInNewWindow 会刷新本来的页面(一进去就刷) 隐藏状态栏
// ios中的openInNewWindowHiddenToolBar 不会刷新本来的页面 不隐藏状态栏 --> 隐藏状态栏

export const BOX_openInNewWindow = (web, app) => {
  let result;
  try {
    switch (platform) {
      case 'iframe':
        parent.postMessage(
          {
            from: 'activity',
            type: 'openInNewWindow',
            data: { name: web.name, params: web.params },
          },
          '*',
        );
        break;
      case 'iosBox':
        if (web.h5_url) {
          window.webkit.messageHandlers.openInNewWindowHiddenToolBar.postMessage(
            web.h5_url,
          );
        } else {
          router.push({ name: web.name, params: web.params });
        }
        break;
      case 'android':
        BOX.openInNewWindowHiddenToolBar(app.url);
        break;
      case 'androidBox':
        if (web.h5_url) {
          //安卓马甲包开启无头新窗口
          BOX.openInNewFullScreenWindow(web.h5_url, web.title);
        } else {
          router.push({ name: web.name, params: web.params });
        }
        break;
      case 'h5':
        if (web.h5_url) {
          window.location.href = web.h5_url;
        } else {
          router.push({ name: web.name, params: web.params });
        }
        break;
    }
    result = true;
  } catch (e) {
    result = false;
  }
  return result;
};

// 打开带头部的新窗口
export const BOX_openInNewNavWindow = (web, app) => {
  let result;
  try {
    switch (platform) {
      case 'iosBox':
        if (web.h5_url) {
          window.webkit.messageHandlers.openInNewNavWindow.postMessage(
            web.h5_url,
          );
        } else {
          router.push({ name: web.name, params: web.params });
        }
        break;
      case 'android':
        BOX.openInNewWindow(app.url);
        break;
      case 'androidBox':
        if (web.h5_url) {
          BOX.openInNewWindow(web.h5_url);
        } else {
          router.push({ name: web.name, params: web.params });
        }
      case 'h5':
        if (web.h5_url) {
          window.location.href = web.h5_url;
        } else {
          router.push({ name: web.name, params: web.params });
        }
        break;
    }
    result = true;
  } catch (e) {
    result = false;
  }
  return result;
};

// APP内新窗口打开(返回时会刷新页面)
export const BOX_openInNewNavWindowRefresh = (web, app) => {
  let result;
  try {
    switch (platform) {
      case 'iosBox':
        if (web.h5_url) {
          window.webkit.messageHandlers.openInNewNavWindow.postMessage(
            web.h5_url,
          );
        } else {
          router.push({ name: web.name, params: web.params });
        }
        break;
      case 'android':
        BOX.openInNewWindowRefresh(app.url);
        break;
      case 'androidBox':
        if (web.h5_url) {
          BOX.openInNewWindowRefresh(web.h5_url);
        } else {
          router.push({ name: web.name, params: web.params });
        }
        break;
      case 'h5':
        if (web.h5_url) {
          window.location.href = web.h5_url;
        } else {
          router.push({ name: web.name, params: web.params });
        }
        break;
    }
    result = true;
  } catch (e) {
    result = false;
  }
  return result;
};

// 关闭窗口
export const BOX_close = refresh => {
  let result;
  try {
    switch (platform) {
      case 'ios':
        window.webkit.messageHandlers.close.postMessage(refresh);
        break;
      case 'iosBox':
        if (web.close) {
          window.webkit.messageHandlers.closeGameWindow.postMessage('close');
        } else {
          router.go(-1);
        }
        break;
      case 'android':
        BOX.close(refresh);
        break;
      case 'androidBox':
        if (web.close) {
          BOX.close(refresh);
        } else {
          router.go(-1);
        }
      case 'h5':
        router.go(-1);
        break;
    }
    result = true;
  } catch (e) {
    result = false;
  }
  return result;
};

/**
 * 页面跳转
 * 每日签到：qd
 * 分享（邀请好友）：fx
 * 个人中心：grzx
 * 赚金币：zjb
 * 首页：index
 * 小号回收：xhhs
 * 交易界面：jy
 * 金币转盘：jbzp
 * 下载管理：yygl
 * 金币商城：jbsc
 * 捡漏：jl
 */
export const BOX_showActivity = (web, app) => {
  // webapp传组件的name
  let result;
  try {
    switch (platform) {
      case 'iframe':
        parent.postMessage(
          {
            from: 'activity',
            type: 'showActivity',
            data: { name: web.name, params: web.params },
          },
          '*',
        );
        break;
      case 'iosBox':
        if (web.isIosBoxToNative) {
          window.webkit.messageHandlers.showActivity.postMessage(app.page);
        } else {
          router.push({ name: web.name, params: web.params });
        }
        break;
      case 'android':
        BOX.showActivity(app.page);
        break;
      case 'androidBox':
        if (web.isAndroidBoxToNative) {
          BOX.showActivity(app.page);
        } else {
          router.push({ name: web.name, params: web.params });
        }
        break;
      case 'h5':
        router.push({ name: web.name, params: web.params });
        break;
    }
    result = true;
  } catch (e) {
    result = false;
  }
  return result;
};

// 打开游戏详情页
export const BOX_goToGame = (web, app) => {
  let result;
  try {
    switch (platform) {
      case 'iframe':
        parent.postMessage(
          { from: 'activity', type: 'goToGame', data: { id: web.params.id } },
          '*',
        );
        break;
      case 'android':
        BOX.goToGame(app.id);
        break;
      case 'iosBox':
      case 'androidBox':
      case 'h5':
        router.push({ name: 'GameDetail', params: web.params });
        break;
    }
    result = true;
  } catch (e) {
    result = false;
  }
  return result;
};

// 打开游戏返利页面
export const BOX_goToGameFanli = (web, app) => {
  let result;
  try {
    switch (platform) {
      case 'iframe':
        parent.postMessage(
          { from: 'activity', type: 'goToGameFanli', data: web.params },
          '*',
        );
        break;
      case 'android':
        BOX.goToGameFanli(app.game_id, app.class_id);
        break;
      case 'iosBox':
      case 'androidBox':
      case 'h5':
        router.push({ name: 'GameNews', params: web.params });
        break;
    }
    result = true;
  } catch (e) {
    result = false;
  }
  return result;
};

// 打开游戏返利详情页面
export const BOX_goToFanliDetails = (web, app) => {
  let result;
  try {
    switch (platform) {
      case 'iframe':
        parent.postMessage(
          { from: 'activity', type: 'goToFanliDetails', data: web.params },
          '*',
        );
        break;
      case 'android':
        BOX.goToFanliDetails(app.url, app.news_id, app.title, apply_type);
        break;
      case 'iosBox':
      case 'androidBox':
      case 'h5':
        router.push({ name: 'Iframe', params: web.params });
        break;
    }
    result = true;
  } catch (e) {
    result = false;
  }
  return result;
};

// 打开游戏返利详情页面
export const BOX_goToGameCoupon = (web, app) => {
  let result;
  try {
    switch (platform) {
      case 'iframe':
        parent.postMessage(
          {
            from: 'activity',
            type: 'goToGameCoupon',
            data: { game_id: web.params.game_id },
          },
          '*',
        );
        break;
      case 'android':
        BOX.goToGameCoupon(app.game_id, app.class_id);
        break;
      case 'iosBox':
      case 'androidBox':
      case 'h5':
        router.push({ name: 'GameCoupon', params: web.params });
        break;
    }
    result = true;
  } catch (e) {
    result = false;
  }
  return result;
};

// 打开实名认证页面
export const BOX_memAuth = () => {
  let result;
  try {
    switch (platform) {
      case 'iframe':
        parent.postMessage({ from: 'activity', type: 'memAuth' }, '*');
        break;
      case 'android':
        BOX.memAuth();
        break;
      case 'iosBox':
      case 'androidBox':
      case 'h5':
        router.push({ name: 'IdCard' });
        break;
    }
    result = true;
  } catch (e) {
    result = false;
  }
  return result;
};

/**
 * 执行登录
 */
export const BOX_login = () => {
  let result;
  try {
    switch (platform) {
      case 'iframe':
        parent.postMessage({ from: 'activity', type: 'login' }, '*');
        break;
      case 'ios':
        window.webkit.messageHandlers.login.postMessage();
        break;
      case 'iosBox':
        router.push({ name: 'PhoneLogin' });
        break;
      case 'android':
        BOX.login();
        break;
      case 'androidBox':
        router.push({ name: 'PhoneLogin' });
        break;
      case 'h5':
        // window.location.href = "https://app.3733.com"
        // 新增登录页，跳转登录页
        router.push({ name: 'PhoneLogin' });
        break;
    }
    result = true;
  } catch (e) {
    result = false;
  }
  return result;
};

// 打开微信
export const BOX_openWx = () => {
  try {
    BOX.openApp('com.tencent.mm');
  } catch (e) {
    window.location.href = 'weixin://';
  }
};

// 微信验证
export const Box_wxOAuth2 = () => {
  let result;
  try {
    switch (platform) {
      case 'ios':
      case 'iosBox':
        window.webkit.messageHandlers.wxOAuth2.postMessage({});
        break;
      case 'android':
      case 'androidBox':
        BOX.wxOAuth2();
        break;
      case 'h5':
        Toast('请手动打开微信');
        break;
    }
    result = true;
  } catch (e) {
    result = false;
  }
  return result;
};

// 马甲包内扭转横竖屏
/**
 * @param {number} code 1横屏0竖屏
 */
export const BOX_setScreenOrientation = code => {
  try {
    switch (platform) {
      case 'iosBox':
        window.webkit.messageHandlers.setScreenOrientation.postMessage(code);
        break;
      case 'androidBox':
        BOX.setScreenOrientation(code);
        break;
      default:
        break;
    }
  } catch (e) { }
};

// 获取当前app包名
export function BOX_getPackageName () {
  let result;
  try {
    if (isIos && getPostData() != undefined) {
      result = getEval().packageName;
    } else {
      result = BOX.getPackageName();
    }
  } catch (e) {
    result = false;
  }
  return result;
}

// 获取ios套壳数据
function getPostData () {
  if (!(typeof window.getData == undefined)) {
    return window.getData;
  }
  return undefined;
}

// 遗留下来的方法,ios通讯用
function getEval () {
  let data = getPostData();
  return eval('(' + data + ')');
}

// 获取隐私政策url最后的参数，比如/4sf8u
export function BOX_getProtocolKey () {
  let result;
  try {
    if (isIos && getPostData() != undefined) {
      result = window.webkit.messageHandlers.getProtocolKey.postMessage({});
    } else {
      result = BOX.getProtocolKey();
    }
  } catch (e) {
    result = '4sf8u';
  }
  return result;
}

// 处理收到的消息
export async function windowMessage () {
  return new Promise(resolve => {
    window.addEventListener('message', async e => {
      devLog('activity=========================', e);
      if (e.data.from === 'webapp') {
        switch (e.data.type) {
          case 'init':
            from = e.data.data.f;
            token = e.data.data.t;
            uuid = e.data.data.u;

            await handleStore(e.data.data.loaded);
            break;
        }
        resolve();
      }
    });
  });
}
// 拿到token后的处理
async function handleStore (loaded = false) {
  if (uuid) {
    store.commit('user/setUuid', uuid);
  }
  if (from) {
    store.commit('user/setFrom', from);
  }
  if (token) {
    store.commit('user/setUserInfo', { token: token });
    if (!loaded) {
      await store.dispatch('user/SET_USER_INFO', false);
      await store.dispatch('system/SET_INIT_DATA');
    }
  } else {
    store.commit('user/setUserInfo', { token: 0 });
    if (!loaded) {
      // 未登录处理
      await store.dispatch('system/SET_INIT_DATA');
      await store.dispatch('user/SET_USER_INFO', false);
    }
  }
}

// 判断是否已安装游戏
export const BOX_checkInstall = packageName => {
  let result = true;
  try {
    result = BOX.checkInstall(packageName);
  } catch (e) {
    console.log(e);
  } finally {
    return result;
  }
};

// 游戏下载
export const BOX_downloadGame = game => {
  try {
    BOX.downloadGame(JSON.stringify(game));
  } catch (e) {
    console.log(e);
  }
};

export const iframeCopy = (
  text,
  successMsg = '链接已复制到剪贴板，快去邀请好友吧~',
) => {
  parent.postMessage(
    {
      from: 'activity',
      type: 'copy',
      data: { text: text, successMsg: successMsg },
    },
    '*',
  );
};
