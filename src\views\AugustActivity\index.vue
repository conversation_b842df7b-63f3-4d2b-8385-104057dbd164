<template>
  <div class="july-activity">
    <nav-bar-2
      ref="topNavBar"
      bgStyle="transparent-white"
      :azShow="true"
      :placeholder="false"
    >
      <template #left>
        <div class="back" @click="back"></div>
      </template>
    </nav-bar-2>
    <div class="main">
      <div class="section section-1">
        <!-- 活动说明 -->
        <div @click="toPage('AugustActivityRule')" class="rule btn"></div>
        <!-- 奖励记录 -->
        <div class="reward-record btn" @click="openRecordPopup"></div>
        <!-- 活动时间 -->
        <div class="activity-time">2023/08/25 00:00 - 2023/08/27 23:59</div>
        <!-- 扭蛋广播 -->
        <div class="broadcast">
          <van-swipe
            class="broadcast-swiper"
            vertical
            :autoplay="2000"
            :touchable="false"
            :show-indicators="false"
          >
            <van-swipe-item
              v-for="(item, index) in capsule_toys_log_list"
              :key="index"
            >
              <div class="broadcast-item">
                {{ item }}
              </div>
            </van-swipe-item>
          </van-swipe>
        </div>
      </div>
      <div class="section section-2">
        <div class="operation-bar">
          <div class="operation-btn get-coin" @click="openTaskPopup"></div>
          <div class="operation-btn play-btn" @click="play">
            <div class="times-num">X{{ remain_capsule_toys_num }}</div>
          </div>
        </div>
        <!-- 刮奖公告 -->
        <div class="coin-broadcast">
          <van-swipe
            class="coin-broadcast-swiper"
            vertical
            :autoplay="2000"
            :touchable="false"
            :show-indicators="false"
          >
            <van-swipe-item
              v-for="(item, index) in lottery_log_list"
              :key="index"
            >
              <div class="coin-broadcast-item">
                {{ item }}
              </div>
            </van-swipe-item>
          </van-swipe>
        </div>

        <!-- 刮奖容器 -->
        <div class="scratch-container">
          <div class="title-bar">
            <div class="scratch-times">
              刮奖次数：<span>{{ remain_lottery_ptb_count }}</span
              >次
            </div>
            <div class="refresh-btn btn" @click="refresh">刷新</div>
          </div>

          <div class="scratch-content">
            <div class="prize">{{ first_gold }}金币</div>
            <canvas
              id="canvas"
              @touchmove="touchMove"
              @touchstart="touchStart"
              @touchend="touchEnd"
            ></canvas>
          </div>
        </div>
        <!-- 充值按钮 -->
        <div class="recharge-btn" @click="toRecharge"></div>
      </div>
      <div class="section section-3">
        <!-- 刮奖任务 -->
        <div class="task-list">
          <div
            class="task-item"
            v-for="(item, index) in lottery_list"
            :key="index"
          >
            <div class="task-info">
              <div class="task-title">{{ item.title }}</div>
              <div class="task-desc" v-if="item.desc">
                {{ item.desc }}
              </div>
            </div>
            <div
              class="btn get-btn no-get"
              v-if="item.status == 0"
              @click="$toast('未满足条件，请完成后领取！')"
            >
              未达成
            </div>
            <div
              class="btn get-btn can-get"
              v-else-if="item.status == 1"
              @click="handleTakeLotteryReward(item)"
            >
              {{ item.lottery_id == 1 ? '领取次数' : '领取金币' }}
            </div>
            <div class="btn get-btn had-get" v-else>已领取</div>
          </div>
        </div>
      </div>
    </div>
    <!-- 扭蛋任务弹窗 -->
    <van-popup
      v-model="taskPopup"
      :lock-scroll="false"
      :close-on-click-overlay="false"
      class="popup task-popup"
    >
      <div class="task-popup-close" @click="closeTaskPopup"></div>
      <div class="task-popup-title"></div>
      <div class="popup-subtitle"> —活动期间游戏内实际充值— </div>
      <div class="task-list">
        <template v-for="(item, index) in task_list">
          <div class="task-item" :key="index">
            <div class="task-content">
              <div class="title">{{ item.title }}</div>
            </div>
            <div
              class="btn get-btn no-get"
              v-if="item.status == 0"
              @click="$toast('未满足条件，请完成后领取！')"
            >
              未达成
            </div>
            <div
              class="btn get-btn can-get"
              v-else-if="item.status == 1"
              @click="handleTakeTaskReward(item)"
            >
              领取次数
            </div>
            <div class="btn get-btn had-get" v-else>已领取</div>
          </div>
        </template>
      </div>
    </van-popup>
    <!-- 奖励记录弹窗 -->
    <van-popup
      v-model="recordPopup"
      :lock-scroll="false"
      :close-on-click-overlay="false"
      class="popup record-popup"
    >
      <div class="record-popup-close" @click="closeRecordPopup"></div>
      <div class="record-popup-title"></div>
      <div class="record-list" v-if="record_list.length">
        <div
          v-for="(item, index) in record_list"
          :key="index"
          class="record-item"
        >
          <div class="title">{{ item.title }}</div>
          <div class="desc">{{ item.desc }}</div>
        </div>
      </div>
      <div v-else class="record-list empty">暂无奖励记录</div>
    </van-popup>
    <ptb-recharge-popup @success="getIndexData"></ptb-recharge-popup>
  </div>
</template>

<script>
import { platform, boxInit } from '@/utils/box.uni.js';
import { BOX_login } from '@/utils/box.uni.js';
import { mapActions } from 'vuex';
import canvasImg from '@/assets/images/august-activity/scratch-bg.png';
import {
  ApiActivityAugustIndex,
  ApiActivityAugustScratchLottery,
  ApiActivityAugustCapsuleToys,
  ApiActivityAugustTask,
  ApiActivityAugustRewardLog,
  ApiActivityAugustTakeLottery,
} from '@/api/views/august_activity.js';
import ptbRechargePopup from '@/components/ptb-recharge-popup';
import { mapMutations } from 'vuex';
export default {
  components: {
    ptbRechargePopup,
  },
  data() {
    return {
      record_list: [], // 奖励记录

      canvas: '', // 画布
      ctx: '', // 画笔
      ismousedown: false,
      scratchStatus: 0, //0=未开始 1=刮奖中 2=刮奖完
      clearCount: 0, // 刮奖区被刮开的程度 最大150就全解开
      showPrize: false, // 显示奖品
      ratio: 0,

      activity_status: 3, // 活动状态 1活动已开始 2 活动不存在 3活动未开始 4活动已结束
      capsule_toys_log_list: [], // 扭蛋滚动广播
      first_gold: 0, // 第一次刮奖的金币数
      lottery_log_list: [], // 刮奖滚动广播
      remain_capsule_toys_num: 0, // 扭蛋次数
      task_list: [],
      remain_gold: 0, // 金币池剩余金币数
      remain_lottery_ptb_count: 0, // 刮奖次数
      remain_lottery_ptb_status: 0, // 平台币今日刮奖状态 1:可以刮奖 0不可刮奖
      lottery_list: [], // 刮奖任务

      taskPopup: false,
      recordPopup: false,
    };
  },
  computed: {
    activity_status_text() {
      const arr = [
        '活动已开始！',
        '活动不存在！',
        '活动未开始！',
        '活动已结束！',
      ];
      return arr[this.activity_status - 1];
    },
  },
  async mounted() {
    // 官包触发页面刷新（将onResume挂载在window上）
    if (platform == 'android') {
      window.onResume = this.onResume;
    }
    // 初始化canvas
    this.ratio = document.body.clientWidth / 375;
    this.canvas = document.getElementById('canvas');
    this.canvas.width = this.canvas.clientWidth;
    this.canvas.height = this.canvas.clientHeight;
    this.ctx = this.canvas.getContext('2d');
    this.initCanvas();
    // 页面数据初始化
    await this.init();
  },
  methods: {
    ...mapActions({
      SET_USER_INFO: 'user/SET_USER_INFO',
    }),
    async onResume() {
      await boxInit();
      await this.SET_USER_INFO(true);
      await this.init();
    },
    async init() {
      await this.getIndexData();
      if (this.first_finish_status == 2) {
        this.tabIndex = 1;
      } else {
        this.tabIndex = 0;
      }
    },
    async refresh() {
      this.$toast.loading('刷新中...');
      await this.getIndexData();
      this.scratchStatus = 0;
      this.clearCount = 0;
      this.initCanvas();
      this.$toast('刷新成功');
    },

    async getIndexData() {
      const res = await ApiActivityAugustIndex();
      this.activity_status = res.data.activity_status;
      this.capsule_toys_log_list = res.data.capsule_toys_log_list;
      this.first_gold = res.data.first_gold;
      this.lottery_log_list = res.data.lottery_log_list;
      this.remain_capsule_toys_num = res.data.remain_capsule_toys_num;
      this.task_list = res.data.task_list;
      this.remain_gold = res.data.remain_gold;
      this.remain_lottery_ptb_count = res.data.remain_lottery_ptb_count;
      this.remain_lottery_ptb_status = res.data.remain_lottery_ptb_status;
      this.lottery_list = res.data.lottery_list;
    },
    async getRewardLog() {
      const res = await ApiActivityAugustRewardLog();
      this.record_list = res.data.list;
    },
    // 扭蛋
    async play() {
      try {
        this.$toast.loading({
          message: '正在扭蛋...',
          duration: 0,
          forbidClick: true,
        });
        const res = await ApiActivityAugustCapsuleToys();
      } finally {
        this.getIndexData();
      }
    },
    async handleTakeTaskReward(item) {
      try {
        this.$toast.loading({
          message: '加载中',
          duration: 0,
          forbidClick: true,
        });
        const res = await ApiActivityAugustTask({
          task_id: item.task_id,
        });
      } finally {
        await this.getIndexData();
      }
    },
    async handleTakeLotteryReward(item) {
      try {
        this.$toast.loading({
          message: '加载中',
          duration: 0,
        });
        const res = await ApiActivityAugustTakeLottery({
          lottery_id: item.lottery_id,
        });
      } finally {
        await this.getIndexData();
      }
    },
    login() {
      BOX_login();
    },
    async openTaskPopup() {
      if (this.activity_status != 1) {
        this.$toast(this.activity_status_text);
        return false;
      }
      if (!this.userInfo.token) {
        this.$toast('请登录');
        this.login();
        return false;
      }
      this.taskPopup = true;
      await this.getIndexData();
    },
    // 关闭任务列表
    closeTaskPopup() {
      this.taskPopup = false;
    },
    async openRecordPopup() {
      // if (this.activity_status == 2 || this.activity_status == 3) {
      //   this.$toast(this.activity_status_text);
      //   return false;
      // }
      if (!this.userInfo.token) {
        this.$toast('请登录');
        this.login();
        return false;
      }
      this.recordPopup = true;
      await this.getRewardLog();
    },
    // 关闭记录列表
    closeRecordPopup() {
      this.recordPopup = false;
    },

    async initCanvas() {
      this.ctx.globalCompositeOperation = 'source-over';
      let image = new Image();
      image.src = canvasImg;
      image.onload = async () => {
        await this.ctx.drawImage(
          image,
          0,
          0,
          this.canvas.width,
          this.canvas.height,
        );
      };
    },
    touchStart(e) {
      if (this.activity_status != 1) {
        this.$toast(this.activity_status_text);
        return false;
      }
      if (!this.userInfo.token) {
        this.login();
        return false;
      }
      switch (this.scratchStatus) {
        case 0: // 未开始
          if (this.remain_lottery_ptb_count == 0) {
            this.$toast('刮奖次数已用完');
            return;
          } else {
            // 开始刮奖
            this.scratchStatus = 1;
          }
          break;
        case 1: // 刮奖中
          break;
        case 2: // 已结束
          break;
      }
    },
    touchMove(e) {
      if (this.scratchStatus == 1) {
        let x = e.touches[0].clientX - this.canvas.getBoundingClientRect().left,
          y = e.touches[0].clientY - this.canvas.getBoundingClientRect().top;
        e.preventDefault();
        this.ctx.clearRect(x, y, 15 * this.ratio, 15 * this.ratio);
        this.clearCount++;
      }
    },
    async touchEnd(e) {
      // if (this.scratchStatus == 1) {
      //   // 优化该地方的判断，计算已经刮过的区域占整个区域的百分比 是否大于20%，
      //   // 如果是，则结束刮奖，否则，则继续刮
      //   if (this.getFilledPercentage() > 0.2) {
      //     // 当手指累计滑动超过80时触发清空矩形画布, 数值越大要刮得越久
      //     this.scratchStatus = 2;
      //     this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
      //     await this.handleScratch();
      //   }
      // }

      if (this.scratchStatus == 1) {
        if (this.clearCount > 80) {
          // 当手指累计滑动超过80时触发清空矩形画布, 数值越大要刮得越久
          this.scratchStatus = 2;
          this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
          await this.handleScratch();
        }
      }
    },
    async handleScratch() {
      try {
        const res = await ApiActivityAugustScratchLottery();
        let {
          code,
          data: { remain_lottery_count, gold },
        } = res;
        if (code > 0) {
          this.remain_lottery_ptb_count = remain_lottery_count;
          this.first_gold = gold;
          this.$toast(`恭喜获得${gold}金币`);
        }
      } finally {
        setTimeout(async () => {
          this.scratchStatus = 0;
          this.clearCount = 0;
          this.initCanvas();
          await this.getIndexData();
        }, 2000);
      }
    },
    // // 计算已经刮过的区域占整个区域的百分比
    // getFilledPercentage() {
    //   let imgData = this.ctx.getImageData(
    //     0,
    //     0,
    //     this.canvas.width,
    //     this.canvas.height
    //   );
    //   // imgData.data是个数组，存储着指定区域每个像素点的信息，数组中4个元素表示一个像素点的rgba值
    //   let pixels = imgData.data;
    //   let transPixels = [];
    //   for (let i = 0; i < pixels.length; i += 4) {
    //     // 严格上来说，判断像素点是否透明需要判断该像素点的a值是否等于0，
    //     // 为了提高计算效率，这儿设置当a值小于128，也就是半透明状态时就可以了
    //     if (pixels[i + 3] < 128) {
    //       transPixels.push(pixels[i + 3]);
    //     }
    //   }
    //   return transPixels.length / (pixels.length / 4);
    // },

    toRecharge() {
      if (!this.userInfo.token) {
        this.login();
        return false;
      }
      if (this.activity_status != 1) {
        this.$toast(this.activity_status_text);
        return false;
      }
      this.setShowPtbRechargePopup(true);
    },
    ...mapMutations({
      setShowPtbRechargePopup: 'recharge/setShowPtbRechargePopup',
    }),
  },
};
</script>

<style lang="less" scoped>
.july-activity {
  width: 100%;
  overflow: hidden;
  .back {
    width: 28 * @rem;
    height: 28 * @rem;
    border-radius: 15 * @rem;
    background: rgba(0, 0, 0, 0.3) url(~@/assets/images/nav-bar-back-white.png)
      center center no-repeat;
    background-size: 8 * @rem 14 * @rem;
  }
  .main {
    .section {
      &.section-1 {
        width: 100%;
        height: 688 * @rem;
        .image-bg('~@/assets/images/august-activity/august-bg-1.png');
        background-size: 100% 688 * @rem;
        position: relative;
        .rule {
          position: absolute;
          right: 0;
          top: 115 * @rem;
          width: 27 * @rem;
          height: 60 * @rem;
          .image-bg('~@/assets/images/august-activity/rule-btn.png');
          background-size: 27 * @rem 60 * @rem;
        }
        .reward-record {
          position: absolute;
          right: 0;
          top: 183 * @rem;
          width: 27 * @rem;
          height: 60 * @rem;
          .image-bg('~@/assets/images/august-activity/reward-btn.png');
          background-size: 27 * @rem 60 * @rem;
        }
        .activity-time {
          position: absolute;
          left: 50%;
          transform: translateX(-50%);
          top: 128 * @rem;
          width: 271 * @rem;
          height: 20 * @rem;
          .image-bg('~@/assets/images/august-activity/time-bg.png');
          background-size: 271 * @rem 20 * @rem;
          text-align: center;
          line-height: 20 * @rem;
          font-size: 11 * @rem;
          color: #ffffff;
          font-weight: 500;
        }
        .broadcast {
          position: absolute;
          left: 50%;
          transform: translateX(-50%);
          top: 262 * @rem;
          width: 227 * @rem;
          height: 30 * @rem;
          .broadcast-swiper {
            height: 30 * @rem;
            .broadcast-item {
              height: 30 * @rem;
              white-space: nowrap;
              text-align: center;
              line-height: 30 * @rem;
              font-size: 11 * @rem;
              color: #ffffff;
              font-weight: 500;
            }
          }
        }
      }
      &.section-2 {
        width: 100%;
        height: 476 * @rem;
        .image-bg('~@/assets/images/august-activity/august-bg-2.png');
        background-size: 100% 476 * @rem;
        position: relative;
        .operation-bar {
          display: flex;
          justify-content: center;
          padding-top: 6 * @rem;
          .operation-btn {
            margin: 0 17 * @rem;
            width: 136 * @rem;
            height: 58 * @rem;

            &.get-coin {
              .image-bg('~@/assets/images/august-activity/get-coin.png');
              background-size: 136 * @rem 58 * @rem;
            }
            &.play-btn {
              .image-bg('~@/assets/images/august-activity/play-btn.png');
              background-size: 136 * @rem 58 * @rem;
              position: relative;
              .times-num {
                box-sizing: border-box;
                padding-left: 24 * @rem;
                font-size: 12 * @rem;
                color: #ffffff;
                line-height: 22 * @rem;
                text-align: center;
                position: absolute;
                right: -5 * @rem;
                top: -14 * @rem;
                width: 56 * @rem;
                height: 22 * @rem;
                .image-bg('~@/assets/images/august-activity/times-num-bg.png');
                background-size: 56 * @rem 22 * @rem;
              }
            }
          }
        }
        .coin-broadcast {
          width: 271 * @rem;
          height: 20 * @rem;
          .image-bg('~@/assets/images/august-activity/coin-broadcast-bg.png');
          background-size: 271 * @rem 20 * @rem;
          margin: 76 * @rem auto 0;
          .coin-broadcast-swiper {
            height: 20 * @rem;
            .coin-broadcast-item {
              height: 20 * @rem;
              white-space: nowrap;
              text-align: center;
              line-height: 20 * @rem;
              font-size: 11 * @rem;
              color: #f07b57;
              font-weight: 500;
            }
          }
        }
        .scratch-container {
          box-sizing: border-box;
          width: 340 * @rem;
          height: 219 * @rem;
          border-radius: 12 * @rem;
          margin: 18 * @rem auto;
          padding: 20 * @rem 34 * @rem 18 * @rem;
          .title-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            .scratch-times {
              font-size: 15 * @rem;
              font-weight: 500;
              color: #9b4031;
              span {
                color: #ff6d3b;
              }
            }
            .refresh-btn {
              width: 57 * @rem;
              height: 26 * @rem;
              display: flex;
              justify-content: center;
              align-items: center;
              font-size: 14 * @rem;
              color: #ffffff;
              font-weight: 500;
              background: linear-gradient(175deg, #ff8f85 0%, #ff6152 100%);
              border-radius: 30 * @rem;
            }
          }
          .scratch-content {
            width: 272 * @rem;
            height: 116 * @rem;
            position: relative;
            background: url(~@/assets/images/august-activity/scratch-bg.png)
              center center no-repeat;
            background-size: 272 * @rem 116 * @rem;
            overflow: hidden;
            border-radius: 8 * @rem;
            margin-top: 40 * @rem;
            .prize {
              width: 272 * @rem;
              height: 116 * @rem;
              line-height: 116 * @rem;
              background: #f5f5f5;
              font-size: 35 * @rem;
              letter-spacing: 3 * @rem;
              text-align: center;
              color: #f7572d;
              font-weight: bold;
            }
            #canvas {
              width: 100%;
              height: 100%;
              position: absolute;
              top: 0;
              left: 0;
            }
          }
        }
        .recharge-btn {
          width: 213 * @rem;
          height: 47 * @rem;
          .image-bg('~@/assets/images/august-activity/recharge-btn.png');
          background-size: 213 * @rem 47 * @rem;
          margin: 20 * @rem auto 0;
        }
      }
      &.section-3 {
        width: 100%;
        height: 236 * @rem;
        .image-bg('~@/assets/images/august-activity/august-bg-3.png');
        background-size: 100% 236 * @rem;
        position: relative;
        overflow: hidden;
        .task-list {
          box-sizing: border-box;
          width: 355 * @rem;
          height: 185 * @rem;
          margin: 10 * @rem auto 0;
          padding: 17 * @rem 10 * @rem 0;
          .task-item {
            box-sizing: border-box;
            padding: 0 10 * @rem;
            width: 335 * @rem;
            height: 70 * @rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-top: 10 * @rem;
            &:first-of-type {
              margin-top: 0;
            }
            .task-info {
              flex: 1;
              min-width: 0;
              .task-title {
                font-size: 14 * @rem;
                color: #9b4031;
                line-height: 18 * @rem;
                font-weight: bolder;
              }
              .task-desc {
                font-size: 11 * @rem;
                color: #9b4031;
                line-height: 14 * @rem;
                margin-top: 8 * @rem;
                white-space: nowrap;
              }
            }
            .get-btn {
              box-sizing: border-box;
              position: relative;
              width: 66 * @rem;
              height: 30 * @rem;
              padding-bottom: 4 * @rem;
              display: flex;
              align-items: center;
              justify-content: center;
              margin-left: 10 * @rem;
              font-size: 13 * @rem;
              color: #ffffff;
              border-radius: 10 * @rem;
              &.no-get {
                .image-bg('~@/assets/images/august-activity/get-off.png');
                background-size: 66 * @rem 30 * @rem;
              }
              &.can-get {
                .image-bg('~@/assets/images/august-activity/get-on.png');
                background-size: 66 * @rem 30 * @rem;
              }
              &.had-get {
                .image-bg('~@/assets/images/august-activity/get-off.png');
                background-size: 66 * @rem 30 * @rem;
              }
            }
          }
        }
      }
    }
  }
}
.popup {
  box-sizing: border-box;
  width: 349 * @rem;
  border-radius: 18 * @rem;
  background: #fcb078;
  overflow: visible;
  padding: 15 * @rem 12 * @rem 12 * @rem;
}
.task-popup {
  .task-popup-close {
    width: 36 * @rem;
    height: 36 * @rem;
    .image-bg('~@/assets/images/august-activity/popup-close.png');
    background-size: 36 * @rem 36 * @rem;
    position: absolute;
    right: -6 * @rem;
    top: -10 * @rem;
  }
  .task-popup-title {
    width: 190 * @rem;
    height: 24 * @rem;
    .image-bg('~@/assets/images/august-activity/get-coin-title.png');
    background-size: 190 * @rem 24 * @rem;
    margin: 0 auto;
  }
  .popup-subtitle {
    text-align: center;
    font-size: 11 * @rem;
    color: #ffffff;
    padding: 7 * @rem 0 0;
  }
  .task-list {
    border-radius: 18 * @rem;
    background: #ffeee7;
    padding: 15 * @rem 14 * @rem 13 * @rem;
    margin-top: 13 * @rem;
    .task-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 13 * @rem;
      &:first-of-type {
        margin-top: 0;
      }
      .task-content {
        flex: 1;
        min-width: 0;
        .title,
        .desc {
          font-size: 12 * @rem;
          color: #af3e1d;
          line-height: 18 * @rem;
        }
      }
      .get-btn {
        box-sizing: border-box;
        position: relative;
        width: 66 * @rem;
        height: 30 * @rem;
        padding-bottom: 4 * @rem;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 10 * @rem;
        font-size: 13 * @rem;
        color: #ffffff;
        border-radius: 10 * @rem;
        &.no-get {
          .image-bg('~@/assets/images/august-activity/get-off.png');
          background-size: 66 * @rem 30 * @rem;
        }
        &.can-get {
          .image-bg('~@/assets/images/august-activity/get-on.png');
          background-size: 66 * @rem 30 * @rem;
        }
        &.had-get {
          .image-bg('~@/assets/images/august-activity/get-off.png');
          background-size: 66 * @rem 30 * @rem;
        }
      }
    }
  }
}
.record-popup {
  .record-popup-close {
    width: 36 * @rem;
    height: 36 * @rem;
    .image-bg('~@/assets/images/august-activity/popup-close.png');
    background-size: 36 * @rem 36 * @rem;
    position: absolute;
    right: -6 * @rem;
    top: -10 * @rem;
  }
  .record-popup-title {
    width: 170 * @rem;
    height: 24 * @rem;
    .image-bg('~@/assets/images/august-activity/record-title.png');
    background-size: 170 * @rem 24 * @rem;
    margin: 0 auto;
  }
  .record-list {
    box-sizing: border-box;
    border-radius: 18 * @rem;
    background: #fceed8;
    padding: 18 * @rem 0 * @rem 18 * @rem 14 * @rem;
    margin-top: 18 * @rem;
    height: 378 * @rem;
    overflow-y: auto;
    .record-item {
      display: flex;
      align-items: flex-start;
      line-height: 16 * @rem;
      margin-top: 14 * @rem;
      &:first-of-type {
        margin-top: 0;
      }
      .title {
        flex: 1;
        min-width: 0;
        font-size: 12 * @rem;
        color: #934028;
      }
      .desc {
        width: 80 * @rem;
        font-size: 12 * @rem;
        color: #ff6235;
        margin-left: 10 * @rem;
      }
    }
    &.empty {
      height: 387 * @rem;
      font-size: 14 * @rem;
      color: rgba(147, 64, 40, 0.4);
      text-align: center;
      line-height: 300 * @rem;
    }
  }
}
.result-lottie-popup {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 100;
  background: rgba(0, 0, 0, 0.65);
}
.result-lottie {
  width: 206 * @rem;
  height: auto;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
</style>
