<template>
  <div class="activity-rule">
    <nav-bar-2
      ref="topNavBar"
      :bgStyle="navBgTransparent ? 'transparent-white' : 'white'"
      :azShow="true"
      :placeholder="false"
      title="活动规则"
    >
      <template #left>
        <div class="back" @click="back"></div>
      </template>
    </nav-bar-2>
    <div class="top-bg"></div>
    <div class="main">
      <div class="section">
        <div class="title"><span>活动介绍：</span></div>
        <div class="desc">
          深秋金桂飘香，万众瞩目的双十一终于来啦，快来参与活动，赢取超值福利！
        </div>
        <div class="desc color">
          活动时间：2023/11/11 00:00 ~ 2023/11/13 23:59
        </div>
      </div>
      <div class="section">
        <div class="container">
          <div class="big-text">一、瓜分千万金币红包</div>
          <div class="text">
            活动期间，每日前3次充值平台币，即可参与瓜分千万金币红包。金额随机，祝君好运满满！每个用户每日仅能参与3次。
          </div>
        </div>
        <div class="container">
          <div class="big-text">二、寻找双11锦鲤</div>
          <div class="text">
            每日在所有实付充值大于100元的玩家中随机抽取一个双11锦鲤，奖励1111绑定平台币当天的锦鲤在次日公布，即11号的锦鲤将在12号的0点自动抽取并公布，以此类推。<br />
            温馨提示：仅限
            <span class="color">游戏内使用微信/支付宝充值</span>
            ，如有使用金币/平台币/代金券抵扣的，则只计算抵扣后的现金充值金额。
          </div>
        </div>
        <div class="container">
          <div class="big-text">三、充值游戏得积分</div>
          <div class="text">
            活动期间，玩家在游戏内实付充值可获得积分，RMB对积分的比例为1:100，即充值1元获得100积分。<br />
            温馨提示：仅限游戏内使用微信/支付宝充值，如有使用金币/平台币/代金券抵扣的，则只计算抵扣后的现金充值金额。
          </div>
        </div>
        <div class="container">
          <div class="big-text">四、积分兑换好礼</div>
          <div class="text">
            活动期间可消耗积分兑换礼品<br />
            消耗1500积分可兑换188金币<br />
            消耗3000积分可兑换388金币<br />
            消耗5000积分可兑换588金币<br />
            消耗10000积分可兑换888金币<br />
            消耗30000积分可兑换1888金币+3天SVIP<br />
            消耗50000积分可兑换2888金币+7天SVIP<br />
            消耗100000积分可兑换8888金币
          </div>
        </div>
        <div class="container">
          <div class="big-text">五、活动说明：</div>
          <div class="text">
            1.
            活动期间充值完成后请返回本活动页面领取奖励，请及时领取累计奖励，活动结束后将清空所有奖励领取机会。
          </div>
          <div class="text">
            2.
            温馨提示：由于游戏厂商的要求，部分游戏内充值暂不支持使用金币支付，请先确定需要用金币充值的游戏在不在可使用名单内。
          </div>
          <div class="text">
            <span @click="game_dialog_show = true" class="color underline btn"
              >查看名单&gt;</span
            >
          </div>
        </div>
      </div>
    </div>
    <!-- 查看名单 -->
    <noGameList
      :game_dialog_show="game_dialog_show"
      @changeGameDialogShow="changeGameDialogShow"
    />
  </div>
</template>
<script>
import { ApiGameCardSearchGame } from '@/api/views/recharge.js';
import { BOX_goToGame, BOX_openInNewWindow } from '@/utils/box.uni.js';
import { envFun } from '@/utils/function';
import noGameList from '@/components/no-game-list';

export default {
  name: 'Rule',
  components: {
    noGameList,
  },
  data() {
    return {
      game_dialog_show: false, //查看名单弹窗
      navBgTransparent: true,
    };
  },
  mounted() {
    // 获取顶部导航栏的高度
    this.stickyOffsetTop =
      document.querySelector('.van-nav-bar').offsetHeight + 'px';
    window.addEventListener('scroll', this.handleScroll);
  },
  methods: {
    // 滚动处理
    handleScroll(e) {
      let scrollTop = e.target.scrollingElement.scrollTop;
      if (scrollTop > 100) {
        this.navBgTransparent = false;
      } else {
        this.navBgTransparent = true;
      }
    },
    changeGameDialogShow(show) {
      this.game_dialog_show = show;
    },
  },
};
</script>
<style lang="less" scoped>
.activity-rule {
  background-color: #f9a071;
  overflow: hidden;
  position: relative;

  .back {
    width: 30 * @rem;
    height: 30 * @rem;
    border-radius: 15 * @rem;
    background: rgba(0, 0, 0, 0.3) url(~@/assets/images/nav-bar-back-white.png)
      center center no-repeat;
    background-size: 8 * @rem 14 * @rem;
  }
  .top-bg {
    width: 100%;
    height: 193 * @rem;
    .image-bg('~@/assets/images/eleven-carnival-activity/rule-top-bg.png');
    background-size: 100% 193 * @rem;
  }
  .main {
    background-color: #fff;
    padding: 5 * @rem 20 * @rem 35 * @rem;
  }
  .title {
    margin-bottom: 15 * @rem;
    span {
      background-image: url('~@/assets/images/eleven-carnival-activity/rule-title-bg.png');
      background-size: 100%;
      background-repeat: no-repeat;
      background-position: 0 16 * @rem;
      font-size: 16 * @rem;
      font-family: PingFang HK-Semibold, PingFang HK;
      font-weight: 600;
      color: #333333;
    }
  }
  .big-text {
    margin-bottom: 7 * @rem;
    font-size: 14 * @rem;
    font-family: PingFang SC-Semibold, PingFang SC;
    font-weight: 600;
    color: #333333;
    line-height: 18 * @rem;
  }
  .desc {
    margin-bottom: 6 * @rem;
    font-size: 12 * @rem;
    color: #666666;
    line-height: 18 * @rem;
  }
  .text {
    margin-bottom: 6 * @rem;
    font-size: 11 * @rem;
    color: #666666;
    line-height: 18 * @rem;
  }
  .color {
    color: #ff642c;
  }
  .underline {
    text-decoration: underline;
    font-weight: bold;
  }
  .container {
    margin-top: 21 * @rem;
  }
}
.no-gold-game-popup {
  overflow: unset;
  width: 320 * @rem;
  .search-container {
    box-sizing: border-box;
    width: 320 * @rem;
    height: 450 * @rem;
    padding: 24 * @rem 19 * @rem 10 * @rem;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: unset;
    .close-search {
      width: 24 * @rem;
      height: 24 * @rem;
      background: url(~@/assets/images/close-search.png) center center no-repeat;
      background-size: 24 * @rem 24 * @rem;
      position: absolute;
      right: -10 * @rem;
      top: -10 * @rem;
    }
    .search-bar {
      display: flex;
      align-items: center;
      .input-text {
        width: 240 * @rem;
        height: 35 * @rem;
        border: 1 * @rem solid #e5e5e5;
        border-radius: 18 * @rem;
        flex: 1;
        overflow: hidden;
        form {
          display: block;
          width: 100%;
          height: 100%;
        }
        input {
          box-sizing: border-box;
          display: block;
          width: 100%;
          height: 100%;
          padding: 0 18 * @rem;
          font-size: 15 * @rem;
          color: #333333;
          background-color: #f6f6f6;
        }
      }
      .search-btn {
        font-size: 15 * @rem;
        color: #666666;
        padding-left: 13 * @rem;
        height: 35 * @rem;
        line-height: 35 * @rem;
      }
    }
    .game-list {
      flex: 1;
      overflow: auto;
      margin-top: 10 * @rem;
      .game-item {
        display: flex;
        align-items: center;
        padding: 21 * @rem 0;
        border-bottom: 1 * @rem solid #eeeeee;
        .game-icon {
          width: 50 * @rem;
          height: 50 * @rem;
          border-radius: 10 * @rem;
          background-color: #b5b5b5;
        }
        .right {
          flex: 1;
          min-width: 0;
          margin-left: 10 * @rem;
          .game-name {
            font-size: 16 * @rem;
            font-weight: bold;
            color: #000000;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
          }
          .use-card {
            font-size: 12 * @rem;
            color: #f72e2e;
            margin-top: 10 * @rem;
            &.can {
              color: #36b150;
            }
          }
        }
      }
    }
  }
}
</style>
