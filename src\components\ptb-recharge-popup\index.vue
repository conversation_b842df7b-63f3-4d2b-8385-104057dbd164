<template>
  <van-popup
    v-model="popup"
    :close-on-click-overlay="true"
    position="bottom"
    round
    class="ptb-recharge-popup"
    :lock-scroll="false"
  >
    <div class="close" @click="setShowPtbRechargePopup(false)"></div>
    <div class="popup-title">充值平台币</div>
    <div class="popup-title-tips">（1元=10平台币）</div>
    <div class="swiper">
      <swiper
        id="ptbSwiper"
        class="ptb-swiper"
        ref="ptbSwiper"
        :options="swiperOptions"
        :auto-update="true"
        style="width: 100%; margin: 0 auto"
        v-if="selectList.length > 0"
      >
        <swiper-slide
          class="select-item"
          v-for="(item, index) in selectList"
          :class="{
            on: selectMoney == item.money,
          }"
          :key="index"
        >
          <div class="coin-num">{{ item.date }}</div>
          <div class="name">{{ item.date_unit }}</div>
          <div class="money">{{ item.money }}{{ item.money_unit }}</div>
        </swiper-slide>
      </swiper>
      <div class="ptb-scrollbar"></div>
    </div>
    <!-- 金额输入框 -->
    <div class="input-container">
      <input
        type="number"
        class="text-input"
        :placeholder="placeholder"
        v-model="selectMoney"
      />
      <span class="text-right">(元)</span>
    </div>
    <div class="payway-title">请选择支付方式</div>
    <ul class="pay-list">
      <li
        class="pay-item"
        :class="{ on: payWay == item.key }"
        v-for="(item, index) in payWayList"
        :key="index"
        @click="payWay = item.key"
      >
        <i class="icon" :style="{ backgroundImage: `url(${item.icon})` }"></i>
        <span class="text">{{ item.name }}</span>
        <div class="select-icon"></div>
      </li>
    </ul>
    <div class="recharge btn" @click="handlePay">
      支付<span>{{ totalMoney }}</span
      >元
    </div>
    <bottom-safe-area></bottom-safe-area>
  </van-popup>
</template>
<script>
import { mapMutations, mapGetters } from 'vuex';
import {
  ApiCreateOrderPtb,
  ApiGetPayUrl,
  ApiPlatformGetInfo,
  ApiGetOrderStatus,
} from '@/api/views/recharge.js';
export default {
  data() {
    let that = this;
    return {
      swiperOptions: {
        observer: true, //开启动态检查器，监测swiper和slide
        observeSlideChildren: true, //监测Swiper的子元素wrapper、pagination、navigation、scrollbar或其他一级子元素
        slidesPerView: 'auto',
        freeMode: true,
        freeModeMomentumRatio: 0.3, // 运动惯量
        scrollbar: {
          el: '.ptb-scrollbar',
        },
        on: {
          click: function () {
            setTimeout(() => {
              let select = that.selectList[this.clickedIndex];
              that.selectMoney = select.money;
            }, 0);
          },
        },
      },
      payWay: '', // 支付方式
      selectMoney: 0, // 准备充值的金额
      maxMoney: 0, // 最大充值金额
      selectList: [], // 充值金额列表
      payWayList: [], // 支付方式
    };
  },
  computed: {
    popup: {
      get() {
        return this.showPtbRechargePopup;
      },
      set(value) {
        this.setShowPtbRechargePopup(value);
      },
    },
    // 总金额
    totalMoney() {
      return Number(this.selectMoney) || 0;
    },
    // 输入金额提示语
    placeholder() {
      return `请输入充值金额10~${this.maxMoney}`;
    },
    ...mapGetters({
      showPtbRechargePopup: 'recharge/showPtbRechargePopup',
    }),
  },
  watch: {
    selectMoney() {
      if (Math.ceil(this.selectMoney) !== Number(this.selectMoney)) {
        this.$toast.fail('请输入整数');
        this.selectMoney = parseInt(this.selectMoney);
      }
      if (this.selectMoney > this.maxMoney) this.selectMoney = this.maxMoney;
    },
  },
  async created() {
    await this.getPlatformInfo();
  },
  methods: {
    ...mapMutations({
      setShowPtbRechargePopup: 'recharge/setShowPtbRechargePopup',
    }),
    handlePay() {
      this.setShowPtbRechargePopup(false);
      ApiCreateOrderPtb({
        isNew: 1,
        money: this.totalMoney,
        payWay: this.payWay,
      }).then(orderRes => {
        ApiGetPayUrl({
          orderId: orderRes.data.orderId,
          orderType: 102,
          payWay: this.payWay,
          packageName: '',
        }).finally(() => {
          this.$emit('success');
          ApiGetOrderStatus({
            order_id: orderRes.data.orderId,
            order_type: 102,
          })
            .then(res2 => {
              // if (res2.code == 1) {
              //   this.$point("充值平台币", "充值", "成功");
              // } else {
              //   this.$point("充值平台币", "充值", "失败");
              // }
            })
            .catch(() => {
              // this.$point("充值平台币", "充值", "失败");
            });
        });
      });
    },
    async getPlatformInfo() {
      const res = await ApiPlatformGetInfo();
      let { maxMoney, payWayList, platiconList } = res.data;
      this.maxMoney = maxMoney;
      this.selectList = platiconList;
      this.payWayList = payWayList;
      this.payWay = this.payWayList[0].key;
      this.selectMoney = platiconList.find(item => {
        return item.is_recommend == 1;
      }).money;
    },
  },
};
</script>
<style lang="less" scoped>
.ptb-recharge-popup {
  width: 100%;
  .close {
    position: absolute;
    right: 9 * @rem;
    top: 9 * @rem;
    width: 22 * @rem;
    height: 22 * @rem;
    .image-bg('~@/assets/images/recharge/recharge-popup-close.png');
  }
  .popup-title {
    font-size: 18 * @rem;
    line-height: 23 * @rem;
    font-weight: bold;
    color: #333333;
    text-align: center;
    margin-top: 19 * @rem;
  }
  .popup-title-tips {
    font-size: 12 * @rem;
    color: #999999;
    line-height: 15 * @rem;
    text-align: center;
  }
  .swiper {
    width: 100%;
    height: 86 * @rem;
    margin-top: 26 * @rem;
    .ptb-scrollbar {
      margin: 8 * @rem auto 0;
      width: 60 * @rem;
      height: 4 * @rem;
      background-color: #e0e0e0;
      border-radius: 2 * @rem;
      overflow: hidden;
    }
    /deep/ .swiper-scrollbar-drag {
      background: @themeBg;
    }
    .ptb-swiper {
      .select-item {
        box-sizing: border-box;
        width: 98 * @rem;
        height: 74 * @rem;
        border-radius: 8 * @rem;
        border: 1 * @rem solid #e7e7e7;
        margin-right: 20 * @rem;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        &:first-of-type {
          margin-left: 22 * @rem;
        }
        .coin-num {
          font-size: 20 * @rem;
          color: #333333;
          font-weight: bold;
          line-height: 25 * @rem;
        }
        .name {
          font-size: 11 * @rem;
          font-weight: bold;
          color: #333333;
          line-height: 14 * @rem;
        }
        .money {
          font-size: 10 * @rem;
          line-height: 13 * @rem;
          color: #999999;
          margin-top: 4 * @rem;
        }
        &.on {
          .image-bg('~@/assets/images/recharge/ptb-selected.png');
          border-color: transparent;
          .coin-num,
          .name,
          .money {
            color: #fd6a33;
          }
        }
      }
    }
  }
  .input-container {
    margin: 16 * @rem 23 * @rem 0;
    border-radius: 19 * @rem;
    height: 36 * @rem;
    background-color: #f4f4f4;
    display: flex;
    align-items: center;
    padding: 0 14 * @rem;
    .text-input {
      flex: 1;
      display: block;
      height: 100%;
      background-color: transparent;
      font-size: 18 * @rem;
      color: #333333;
      font-weight: bold;
      &::-webkit-input-placeholder {
        color: #999999;
        font-size: 14px;
        font-weight: normal;
      }
    }
    .text-right {
      font-size: 14 * @rem;
      color: #333333;
      font-weight: bold;
    }
  }
  .payway-title {
    font-size: 16 * @rem;
    color: #333333;
    font-weight: 600;
    line-height: 20 * @rem;
    padding: 0 23 * @rem;
    margin-top: 20 * @rem;
  }
  .pay-list {
    overflow: hidden;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    margin-top: 12 * @rem;
    padding: 0 23 * @rem;
  }
  .pay-item {
    box-sizing: border-box;
    width: 48%;
    height: 52 * @rem;
    padding-top: 10 * @rem;
    text-align: center;
    border: 0.5 * @rem solid #cbcbcb;
    border-radius: 6 * @rem;
    font-size: 0;
    display: flex;
    align-items: center;
    padding: 0 12 * @rem;
    &:not(:nth-of-type(-n + 2)) {
      margin-top: 10 * @rem;
    }
    &.on {
      border-color: #21b98a;
      border-width: 1 * @rem;
    }
    .icon {
      display: block;
      width: 25 * @rem;
      height: 25 * @rem;
      background-repeat: no-repeat;
      background-size: 25 * @rem 25 * @rem;
    }
    .text {
      display: block;
      font-size: 14 * @rem;
      font-weight: 400;
      color: #333;
      flex: 1;
      min-width: 0;
      margin-left: 6 * @rem;
      text-align: left;
    }
    .select-icon {
      width: 16 * @rem;
      height: 16 * @rem;
      background: url(~@/assets/images/recharge/pay-no.png) no-repeat;
      background-size: 16 * @rem 16 * @rem;
    }
    &.on .select-icon {
      background-image: url(~@/assets/images/recharge/pay-yes.png);
    }
  }
  .recharge {
    width: 273 * @rem;
    height: 42 * @rem;
    .image-bg('~@/assets/images/recharge/recharge-btn-bg.png');
    margin: 23 * @rem auto;
    font-size: 18 * @rem;
    font-weight: bold;
    color: #ffffff;
    line-height: 21 * @rem;
    display: flex;
    align-items: center;
    justify-content: center;
    span {
      font-weight: bold;
    }
  }
}
</style>
