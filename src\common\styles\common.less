html,
body {
  height: 100%;
  max-width: @maxWidth;
  margin: 0 auto;
}

// @media screen and (min-width: 640px) {
//   html,body {
//     max-width: @pcmaxwidth;
//   }
// }

body {
  color: #333;
  line-height: 1.2;
  font-size: 12 * @rem;
  text-align: justify;
}

img {
  display: block;
  width: 100%;
  height: 100%;
}

[v-cloak] {
  display: none !important;
}

div {
  user-select: none;
}

.page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  box-sizing: border-box;
  padding-bottom: @safeAreaBottom;
  padding-bottom: @safeAreaBottomEnv;
}

// .switch-page {
//   display: flex;
//   flex-direction: column;
//   height: 100vh;
//   box-sizing: border-box;
//   padding-bottom: calc(50 * @rem + @safeAreaBottom);
//   padding-bottom: calc(50 * @rem + @safeAreaBottomEnv);
// }
.btn {
  position: relative;

  &::before {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100%;
    height: 100%;
    background-color: #000;
    border: inherit;
    border-color: #000;
    border-radius: inherit;
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    opacity: 0;
    content: ' ';
  }

  &:active::before {
    opacity: 0.1;
  }

  // position: relative;
  // &:active:before {
  //   content: "";
  //   position: absolute;
  //   left: 0;
  //   top: 0;
  //   width: 100%;
  //   height: 100%;
  //   background-color: rgba(50, 50, 50, 0.1);
  //   border-radius: inherit;
  // }
}

.van-button::before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  background-color: #000;
  border: inherit;
  border-color: #000;
  border-radius: inherit;
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  opacity: 0;
  content: ' ';
}

input:disabled {
  background: #fff;
}

.van-toast--text {
  z-index: 9999999 !important;
}

.van-toast__icon {
  font-size: 36 * @rem !important;
}

.van-dialog.portrait {
  top: 50%;
  transform: translate(-50%, -50%) rotate(90deg);
}

.van-dialog__confirm,
.van-dialog__confirm:active {
  color: #21b98a;
}
.van-image-preview__index {
  top: unset !important;
  bottom: 16px;
}

.van-nav-bar {
  z-index: 10 !important;
}
