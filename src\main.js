// box init
import { platform, getToken } from '@/utils/box.uni.js';

import Vue from 'vue';
import App from './App.vue';
import router from './router';
import store from './store';
import './api';

import '@/common/styles/reset.css';
import '@/common/styles/border.css';
import '@/common/styles/common.less';

// 全局注册组件
import '@/components';

import '@/utils/datetime.js';

//引入animate动画库样式
import 'animate.css';

// 全局注册变量
import h5Page from '@/utils/h5Page';
Vue.prototype.$h5Page = h5Page;

// 全局混入
import mixins from '@/utils/mixins.js';
Vue.mixin(mixins);

// 解决初始化接口异步返回的问题
Vue.prototype.$onAppFinished = new Promise(resolve => {
  Vue.prototype.$isResolve = resolve;
});

Vue.config.productionTip = false;

new Vue({
  router,
  store,
  render: h => h(App),
}).$mount('#app');
