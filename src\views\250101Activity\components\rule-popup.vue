<template>
  <div>
    <van-popup
      v-model="popup"
      position="bottom"
      class="rule-popup"
      :lock-scroll="false"
    >
      <div class="title-container">
        <img
          class="title-pic"
          src="@/assets/images/250101/rule-popup-title.png"
          alt=""
        />
        <div class="close-btn" @click="closePopup"></div>
      </div>
      <div class="rule-container">
        <h2>一、活动时间</h2>
        <p>
          本次活动自2024年12月28日0点开启，至2025年1月1日24点结束，其中【2025新年抽奖】的抽奖结果会于1月1日22点公示，【限时秒杀】只于1月1日上午10:37、下午15:33、晚上20:25开启。
        </p>

        <h2>二、活动玩法说明</h2>
        <p>
          活动一共设立三个会场，分别是【向新出发，步步为赢】【新年新惠，畅玩新年】【留言祈福，新年如意】，活动玩法如下：
        </p>
        <p>
          <span>【向新出发，步步为赢】</span>
          活动期间，用户每完成一个任务都可以前进一个格子，每个格子都放置了“新年礼物”，到达格子即可解锁对应奖励，其中完成【第五】【第九】任务时可获得"<span>1月1日秒杀活动参与资格</span>"和“<span>2025年新年抽奖活动参与券</span>”，格盘终点设有2025年新年抽奖；活动期间在页面内开通SVIP服务可享格子奖励（金币/平台币类型）翻倍；<br />
          拥有"<span>1月1日秒杀活动参与资格</span>"的用户可在1月1日的上午10:37、下午15:33、晚上20:25开启前往活动页内的【新年新惠，畅玩新年】会场参与限时秒杀，拥有“<span>2025年新年抽奖活动参与券</span>”的用户，可参与10楼天台的<span>2025新年抽奖活动*</span>，<span
            class="red"
            >抽奖结果将于1月1日22点公示；</span
          >
        </p>
        <p>
          <span>2025新年抽奖活动：</span
          >拥有2025年新年抽奖活动参与券的用户才可参与，活动会在1月1日22点从所有参与用户中抽取1位“新年好运锦鲤”，被抽中的用户可得到锦鲤礼包，内含【SVIP年卡（1张）+云挂机年卡（1张）+2025金币】，其余用户获得588金币的新年红包。
        </p>
        <p>
          <span>【新年新惠，畅玩新年】</span><br />
          会场内设有<span>限时秒杀*</span>和<span>新年福利折扣*</span>两个活动
        </p>

        <p>
          <span>限时秒杀*：</span>1月1日
          上午10:37、下午15:33、晚上20:25，三个时间段开启的限时秒杀活动，拥有秒杀参与资格的用户可以参与秒杀，同一时间段内每种秒杀商品单次购买上限数量为1；秒杀资格可在活动【向新出发，步步为赢】，完成任务到达第六格时获得；
        </p>

        <p>
          <span>新年福利折扣*：</span
          >活动期间全程开放，用户可以凭借优惠价格购买相应会员服务，单个账号单件商品至多购买一次；购买SVIP服务可享活动【向新出发，步步为赢】格子奖励双倍。
        </p>

        <p>
          <span>新年充值返利：</span
          >活动期间全程开放，SVIP用户充值指定档位立享3%平台币返利，普通用户享指定额度金币返利；每个商品每个用户每天仅可购买一次；
        </p>

        <p>
          <span>【留言祈福，新年如意】</span>
          活动时间内，用户可以在许愿墙上留下自己的新年心愿，或为大家送上新年祝福，参与次数及不限，字数需≥4字，不可包含辱骂/人身攻击/涉政等违规内容。
        </p>
      </div>
    </van-popup>
  </div>
</template>

<script>
export default {
  name: 'rulePopup',
  props: {
    show: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {};
  },
  computed: {
    popup: {
      get() {
        return this.show;
      },
      set(value) {
        this.$emit('update:show', value);
      },
    },
  },
  methods: {
    closePopup() {
      this.popup = false;
    },
  },
};
</script>

<style lang="less" scoped>
.rule-popup {
  box-sizing: border-box;
  padding: 20 * @rem 0 0;
  border-radius: 20 * @rem 20 * @rem 0 0;
  height: 628 * @rem;
  display: flex;
  flex-direction: column;
  background: rgba(249, 246, 239, 1);
  .title-container {
    position: relative;
    height: 24 * @rem;
    .title-pic {
      width: 268 * @rem;
      height: 24 * @rem;
      margin: 0 auto;
    }
    .close-btn {
      width: 16 * @rem;
      height: 16 * @rem;
      background: url('~@/assets/images/250101/250101_popup_close.png') center
        center no-repeat;
      background-size: 16 * @rem 16 * @rem;
      position: absolute;
      top: 0;
      right: 18 * @rem;
    }
  }
  .rule-container {
    box-sizing: border-box;
    flex: 1;
    overflow-y: auto;
    color: rgba(197, 102, 57, 1);
    margin-top: 10 * @rem;
    padding: 10 * @rem 20 * @rem;
    h2 {
      font-size: 15 * @rem;
      line-height: 26 * @rem;
      font-weight: bold;
      margin-bottom: 2 * @rem;
    }
    p {
      font-size: 13 * @rem;
      line-height: 22 * @rem;
      margin-bottom: 20 * @rem;
      span {
        font-weight: bold;
        &.red {
          color: #ff6464;
        }
      }
    }
  }
}
</style>
