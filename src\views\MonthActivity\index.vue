<template>
  <div class="month-activity-page">
    <nav-bar-2
      ref="topNavBar"
      bgStyle="transparent"
      :azShow="true"
      :placeholder="false"
    ></nav-bar-2>
    <!-- banner图 -->
    <div class="page-banner">
      <div class="activity-date" v-if="activityInfo.end_time">
        {{ activityDate }}
      </div>
    </div>
    <div class="activity-info-section">
      <div class="top-info">
        <div class="total-title">本次活动已累计发放金币</div>
        <div class="total-num">{{ activityInfo.gold_all }}</div>
      </div>
      <div class="user-gold">
        您已累计获得金币：<span>{{ activityInfo.gold_user }}</span>
      </div>
      <div class="bottom-info">
        <swiper
          :options="swiperOption"
          class="history-list"
          v-if="scrollingData.length && activityInfo.status != 1"
        >
          <!-- slides -->
          <swiper-slide
            v-for="(item, index) in scrollingData"
            :key="index"
            class="swiper-no-swiping"
          >
            <div class="history-item">
              <user-avatar
                class="avatar"
                :self="false"
                :src="item.avatar"
              ></user-avatar>
              <div class="nickname">{{ item.nickname }}</div>
              <div class="history-desc">
                获得<span>{{ item.gold }}金币</span>奖励
              </div>
            </div>
          </swiper-slide>
        </swiper>
        <div class="info-detail">
          <div
            class="info-intro"
            :class="{ before: activityInfo.status == 1 }"
            v-html="activityInfo.describe"
          ></div>
        </div>
      </div>
    </div>
    <!-- 平台币部分 -->
    <div class="section ptb-section">
      <div class="go-recharge btn" @click="goRechargePtb">立即充值</div>
      <div class="ptb-list">
        <div
          class="ptb-item"
          v-for="(item, index) in ptbInfo.list"
          :key="index"
        >
          <div class="ptb-left">
            <div class="title">{{ item.title }}</div>
            <div class="sub-title">{{ item.prize_text }}</div>
          </div>
          <div class="get-btn btn" v-if="!item.is_achieve" @click="noGetReward">
            抽奖
          </div>
          <div
            class="get-btn btn"
            v-else-if="item.is_receive"
            @click="hadGetReward"
          >
            已抽奖
          </div>
          <div
            class="get-btn can btn"
            v-else
            @click="getReward(ptbInfo.activity_info.type, item.level)"
          >
            抽奖
          </div>
        </div>
      </div>
    </div>

    <!-- svip部分 -->
    <div class="section svip-section">
      <div class="go-recharge btn" @click="goRechargeSvip">立即充值</div>
      <div class="svip-list">
        <div
          class="svip-item"
          v-for="(item, index) in svipInfo.list"
          :key="index"
        >
          <div class="title">
            {{ item.title }}（{{ item.receive_count }}/{{ item.frequency }}）
          </div>
          <div class="sub-title">{{ item.prize_text }}</div>
          <div class="gold-list">
            <div
              class="gold-item"
              v-for="(gold, goldIndex) in item.frequency"
              :key="goldIndex"
            >
              <div class="gold-icon">
                <img
                  src="@/assets/images/month-activity/gold-box-icon.png"
                  alt=""
                />
              </div>
              <div
                class="get-btn btn"
                v-if="gold <= item.receive_count"
                @click="hadGetReward"
              >
                已抽奖
              </div>
              <div
                class="get-btn can btn"
                v-else-if="gold <= item.achieve_count"
                @click="getReward(svipInfo.activity_info.type, item.level)"
              >
                抽奖
              </div>
              <div class="get-btn btn" v-else @click="noGetReward">抽奖</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 畅玩卡部分 -->
    <div class="section cwk-section">
      <div class="go-recharge btn" @click="goRechargeCwk">立即充值</div>
      <div class="cwk-list">
        <div
          class="cwk-item"
          v-for="(item, index) in cwkInfo.list"
          :key="index"
        >
          <div class="title">
            {{ item.title }}（{{ item.receive_count }}/{{ item.frequency }}）
          </div>
          <div class="sub-title">{{ item.prize_text }}</div>
          <div class="gold-list">
            <div
              class="gold-item"
              v-for="(gold, goldIndex) in item.frequency"
              :key="goldIndex"
            >
              <div class="gold-icon">
                <img
                  src="@/assets/images/month-activity/gold-box-icon.png"
                  alt=""
                />
              </div>
              <div
                class="get-btn btn"
                v-if="gold <= item.receive_count"
                @click="hadGetReward"
              >
                已抽奖
              </div>
              <div
                class="get-btn can btn"
                v-else-if="gold <= item.achieve_count"
                @click="getReward(cwkInfo.activity_info.type, item.level)"
              >
                抽奖
              </div>
              <div class="get-btn btn" v-else @click="noGetReward">抽奖</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 活动规则 -->
    <div class="rule-section">
      <div class="rule-content">
        <p>
          1.由于游戏厂商的要求，部分游戏内充值暂不支持使用金币支付，请先确定需要用金币充值的游戏在不在可使用名单内。<span
            @click="openGameDialog"
            >查看名单&gt;</span
          >
        </p>
        <p>
          2.活动期间仅限现金充值平台币（不包含游戏内现金充值）。累计金额满足相应条件即可领取对应随机奖励。每档奖励只能领取一次。
        </p>

        <p>3.现金开通畅玩卡，即可领取对应随机奖励。每档奖励最多领取3次。</p>
        <p>
          4.现金开通SVIP卡，即可领取对应随机奖励。每档奖励最多领取3次。奖励为额外赠与。可与开通SVIP立返金币同时领取。
        </p>
      </div>
    </div>

    <!-- 查询可用游戏 -->
    <van-dialog
      v-model="gameDialogShow"
      :lock-scroll="false"
      :show-confirm-button="false"
      :close-on-click-overlay="true"
      class="no-gold-game-popup"
    >
      <div class="search-container">
        <div class="close-search" @click="gameDialogShow = false"></div>
        <div class="search-bar">
          <div class="input-text">
            <form @submit.prevent="searchGame">
              <input
                type="text"
                v-model.trim="inputGame"
                placeholder="输入游戏名"
              />
            </form>
          </div>
          <div class="search-btn" @click="searchGame">搜索</div>
        </div>
        <yy-list
          class="yy-list game-list"
          v-model="loadingObj"
          :finished="finished"
          @refresh="onRefresh"
          @loadMore="loadMore"
          :empty="!gameList.length"
          tips="请输入您想搜索的游戏"
          :check="false"
        >
          <div
            class="game-item btn"
            v-for="item in gameList"
            :key="item.id"
            @click="toGame(item)"
          >
            <div class="game-icon">
              <img :src="item.titlepic" alt="" />
            </div>
            <div class="right">
              <div class="game-name">{{ item.title }}</div>
              <div class="use-card" :class="{ can: item.use_gold_pay == 1 }">
                {{ item.use_gold_pay == 1 ? '' : '不' }}支持使用金币支付
              </div>
            </div>
          </div>
        </yy-list>
      </div>
    </van-dialog>
    <van-dialog
      v-model="showRewardPopup"
      :lock-scroll="false"
      :show-confirm-button="false"
      :close-on-click-overlay="true"
      class="reward-popup"
    >
      <div class="reward-title">恭喜获得</div>
      <div class="reward-content">{{ rewardInfo.gold }}金币</div>
      <div class="reward-tips">{{ rewardInfo.text }}</div>
      <div class="reward-confirm" @click="showRewardPopup = false">知道了</div>
    </van-dialog>
    <ptb-recharge-popup @success="getActivityInfo"></ptb-recharge-popup>
    <svip-recharge-popup @success="getActivityInfo"></svip-recharge-popup>
    <cwk-recharge-popup @success="getActivityInfo"></cwk-recharge-popup>
  </div>
</template>

<script>
import { mapMutations } from 'vuex';
import { BOX_goToGame, platform, boxInit } from '@/utils/box.uni.js';
import {
  ApiActivityGetRechargeActivity,
  ApiActivityGetRechargeRewarded,
} from '@/api/views/activity.js';
import { ApiGameCardSearchGame } from '@/api/views/recharge.js';
import ptbRechargePopup from '@/components/ptb-recharge-popup';
import svipRechargePopup from '@/components/svip-recharge-popup';
import cwkRechargePopup from '@/components/cwk-recharge-popup';
export default {
  components: {
    ptbRechargePopup,
    svipRechargePopup,
    cwkRechargePopup,
  },
  data() {
    // activityInfo.status 1=>未开始 2=> 进行中 3=>已结束
    return {
      activityInfo: {},
      // 滚动列表
      scrollingData: [],
      cwkInfo: {
        activity_info: {},
        list: [],
      },
      ptbInfo: {
        activity_info: {},
        list: [],
      },
      svipInfo: {
        activity_info: {},
        list: [],
      },
      // swiper配置
      swiperOption: {
        observer: true,
        observeParents: true,
        noSwiping: true,
        direction: 'vertical',
        slidesPerView: 3,
        speed: 800,
        autoplay: {
          delay: 0,
        },
        loop: true,
        freeMode: true,
      },
      // 是否显示非金币支付游戏
      gameDialogShow: false,
      // 非金币支付的游戏列表
      gameList: [],
      inputGame: '',
      loadingObj: {
        loading: false,
        reloading: false,
      },
      finished: false,
      page: 1,
      listRows: 20,
      empty: false,
      showRewardPopup: false,
      rewardInfo: {
        gold: 0,
        text: '还有更多奖励等你拿哦~',
      },
    };
  },
  computed: {
    // 活动时间
    activityDate() {
      let { year, date } = this.$handleTimestamp(this.activityInfo.start_time);
      let { year: year2, date: date2 } = this.$handleTimestamp(
        this.activityInfo.end_time,
      );
      return `${year}-${date} 至 ${year2}-${date2}`;
    },
  },
  async created() {
    // 官包触发页面刷新（将onResume挂载在window上）
    if (platform == 'android') {
      window.onResume = this.onResume;
    }
    await this.getActivityInfo();
  },
  methods: {
    ...mapMutations({
      setShowPtbRechargePopup: 'recharge/setShowPtbRechargePopup',
      setShowSvipRechargePopup: 'recharge/setShowSvipRechargePopup',
      setShowCwkRechargePopup: 'recharge/setShowCwkRechargePopup',
    }),
    async onResume() {
      await boxInit();
      await this.getActivityInfo();
    },
    // 活动信息
    async getActivityInfo() {
      const res = await ApiActivityGetRechargeActivity();
      let { activity_info, scrolling_data, svip_info, cwk_info, ptb_info } =
        res.data;
      this.activityInfo = activity_info;
      this.scrollingData = scrolling_data;
      this.cwkInfo = cwk_info;
      this.svipInfo = svip_info;
      this.ptbInfo = ptb_info;
    },
    // 打开游戏弹窗
    openGameDialog() {
      this.gameDialogShow = true;
    },
    // 点击抽奖
    async getReward(type, level) {
      const res = await ApiActivityGetRechargeRewarded({
        type: type,
        level: level,
      });
      let { code, data } = res;
      if (code > 0) {
        this.rewardInfo = data;
        this.showRewardPopup = true;
      }
      await this.getActivityInfo();
    },
    toGame(item) {
      BOX_goToGame(
        {
          params: {
            id: item.id,
          },
        },
        { id: item.id },
      );
    },
    // 搜索不可使用金币的游戏
    async searchGame() {
      if (!this.inputGame) {
        this.$toast('请输入游戏名');
        return false;
      }
      this.$toast({
        type: 'loading',
        duration: 0,
        message: '拼命加载中...',
      });
      this.gameList = [];
      await this.getGameList();
    },
    async getGameList(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      let res = await ApiGameCardSearchGame({
        keyword: this.inputGame,
        listRows: 20,
        page: this.page,
      });
      this.$toast.clear();
      let { list } = res.data;
      if (action === 1 || this.page === 1) {
        this.gameList = [];
      }
      if (!list.length) {
        this.empty = true;
      }
      this.gameList.push(...list);
      if (list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
    },
    async onRefresh() {
      try {
        await this.getGameList();
      } finally {
        this.loadingObj.reloading = false;
      }
    },
    async loadMore() {
      try {
        await this.getGameList(2);
      } finally {
        this.loadingObj.loading = false;
      }
    },
    hadGetReward() {
      this.$toast('该奖励已领取');
    },
    noGetReward() {
      this.$toast('您还未达到领取标准哦~');
    },
    goRechargePtb() {
      if (this.activityInfo.status == 1) {
        this.$toast('当前活动还未开启，活动开启后再来哦~');
        return false;
      } else if (this.activityInfo.status == 3) {
        this.$toast('活动已结束啦~');
        return false;
      }
      this.setShowPtbRechargePopup(true);
    },
    goRechargeSvip() {
      if (this.activityInfo.status == 1) {
        this.$toast('当前活动还未开启，活动开启后再来哦~');
        return false;
      } else if (this.activityInfo.status == 3) {
        this.$toast('活动已结束啦~');
        return false;
      }
      this.setShowSvipRechargePopup(true);
    },
    goRechargeCwk() {
      if (this.activityInfo.status == 1) {
        this.$toast('当前活动还未开启，活动开启后再来哦~');
        return false;
      } else if (this.activityInfo.status == 3) {
        this.$toast('活动已结束啦~');
        return false;
      }
      this.setShowCwkRechargePopup(true);
    },
  },
};
</script>

<style lang="less" scoped>
.month-activity-page {
  min-height: 100vh;
  .go-recharge {
    width: 77 * @rem;
    height: 31 * @rem;
    .image-bg('~@/assets/images/month-activity/recharge-btn.png');
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14 * @rem;
    color: #ffffff;
    font-weight: bold;
  }
  .page-banner {
    width: 100%;
    height: 243 * @rem;
    background: url('~@/assets/images/month-activity/activity-banner.png')
      center top no-repeat;
    background-size: 100% 100%;
    overflow: hidden;
    .activity-date {
      width: 210 * @rem;
      height: 27 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14 * @rem;
      color: #ffffff;
      font-weight: bold;
      margin: 190 * @rem auto 0;
    }
  }
  .activity-info-section {
    box-sizing: border-box;
    padding: 0 28 * @rem;
    width: 100%;
    height: 390 * @rem;
    background: url('~@/assets/images/month-activity/activity-info.png') center
      top no-repeat;
    background-size: 100% 100%;
    .top-info {
      width: 247 * @rem;
      height: 70 * @rem;
      margin: 0 auto;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      .total-title {
        font-size: 16 * @rem;
        color: #8e4e1f;
        font-weight: bold;
        line-height: 20 * @rem;
        margin-top: 4 * @rem;
      }
      .total-num {
        font-size: 24 * @rem;
        color: #f43535;
        font-weight: bold;
        line-height: 30 * @rem;
      }
    }
    .user-gold {
      margin-top: 18 * @rem;
      font-size: 14 * @rem;
      line-height: 20 * @rem;
      color: #006487;
      display: flex;
      align-items: center;
      margin-left: 13 * @rem;
      span {
        font-size: 17 * @rem;
        color: #fe5c5c;
        font-weight: bold;
      }
    }
    .bottom-info {
      box-sizing: border-box;
      margin-top: 19 * @rem;
      background-color: #c1e7f3;
      border-radius: 7 * @rem;
      padding: 12 * @rem 0;
      min-height: 197 * @rem;
      .history-list {
        width: 100%;
        height: 105 * @rem;
        /deep/ .swiper-wrapper {
          transition-timing-function: linear !important;
        }
        .history-item {
          height: 35 * @rem;
          display: flex;
          align-items: center;
          padding: 0 8 * @rem;
          .avatar {
            width: 29 * @rem;
            height: 29 * @rem;
          }
          .nickname {
            margin-left: 7 * @rem;
            font-size: 12 * @rem;
            color: #006487;
            flex: 1;
            min-width: 0;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
          }
          .history-desc {
            width: 180 * @rem;
            color: #006487;
            font-size: 12 * @rem;
            text-align: right;
            span {
              color: #ff471f;
              font-weight: bold;
            }
          }
        }
      }
      .info-detail {
        margin-top: 12 * @rem;
        font-size: 12 * @rem;
        color: #024760;
        line-height: 16 * @rem;
        padding: 0 8 * @rem;
        .info-intro {
          &.before {
            font-size: 16px;
            line-height: 1.6;
          }
        }
      }
    }
  }
  .ptb-section {
    overflow: hidden;
    width: 100%;
    height: 396 * @rem;
    background: url('~@/assets/images/month-activity/section-1.png') center top
      no-repeat;
    background-size: 100% 100%;
    margin-top: -1 * @rem;
    position: relative;
    .go-recharge {
      position: absolute;
      right: 28 * @rem;
      top: 48 * @rem;
    }
    .ptb-list {
      margin-top: 90 * @rem;
      .ptb-item {
        display: flex;
        align-items: center;
        padding: 8 * @rem 30 * @rem;
        .ptb-left {
          flex: 1;
          min-width: 0;
          margin-right: 5 * @rem;
          .title {
            font-size: 14 * @rem;
            color: #1c657e;
            line-height: 18 * @rem;
            font-weight: bold;
          }
          .sub-title {
            font-size: 11 * @rem;
            color: #006487;
            line-height: 14 * @rem;
            margin-top: 2 * @rem;
          }
        }
        .get-btn {
          width: 56 * @rem;
          height: 24 * @rem;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 14 * @rem;
          color: #fff;
          background-color: #ade0f1;
          border-radius: 19 * @rem;
          &.can {
            height: 26 * @rem;
            .image-bg('~@/assets/images/month-activity/get-btn-icon.png');
          }
        }
      }
    }
  }
  .svip-section {
    width: 100%;
    height: 870 * @rem;
    background: url('~@/assets/images/month-activity/section-2.png') center top
      no-repeat;
    background-size: 100% 100%;
    overflow: hidden;
    position: relative;
    .go-recharge {
      position: absolute;
      right: 30 * @rem;
      top: 39 * @rem;
    }
    .svip-list {
      padding: 0 30 * @rem;
      margin-top: 86 * @rem;
      .svip-item {
        &:not(:first-of-type) {
          margin-top: 20 * @rem;
        }
        .title {
          font-size: 14 * @rem;
          font-weight: bold;
          line-height: 18 * @rem;
          color: #1c657e;
        }
        .sub-title {
          font-size: 12 * @rem;
          color: #3c8aa6;
          line-height: 15 * @rem;
          margin-top: 2 * @rem;
        }
        .gold-list {
          display: flex;
          align-content: center;
          justify-content: space-around;
          margin-top: 11 * @rem;
          .gold-item {
            .gold-icon {
              width: 54 * @rem;
              height: 54 * @rem;
            }
            .get-btn {
              margin-top: 6 * @rem;
              width: 56 * @rem;
              height: 24 * @rem;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 14 * @rem;
              color: #fff;
              background-color: #ade0f1;
              border-radius: 19 * @rem;
              &.can {
                height: 26 * @rem;
                .image-bg('~@/assets/images/month-activity/get-btn-icon.png');
              }
            }
          }
        }
      }
    }
  }
  .cwk-section {
    width: 100%;
    height: 433 * @rem;
    background: url('~@/assets/images/month-activity/section-3.png') center top
      no-repeat;
    background-size: 100% 100%;
    overflow: hidden;
    position: relative;
    .go-recharge {
      position: absolute;
      right: 30 * @rem;
      top: 47 * @rem;
    }
    .cwk-list {
      padding: 0 30 * @rem;
      margin-top: 94 * @rem;
      .cwk-item {
        &:not(:first-of-type) {
          margin-top: 20 * @rem;
        }
        .title {
          font-size: 14 * @rem;
          font-weight: bold;
          line-height: 18 * @rem;
          color: #1c657e;
        }
        .sub-title {
          font-size: 12 * @rem;
          color: #3c8aa6;
          line-height: 15 * @rem;
          margin-top: 2 * @rem;
        }
        .gold-list {
          display: flex;
          align-content: center;
          justify-content: space-around;
          margin-top: 11 * @rem;
          .gold-item {
            .gold-icon {
              width: 54 * @rem;
              height: 54 * @rem;
            }
            .get-btn {
              margin-top: 6 * @rem;
              width: 56 * @rem;
              height: 24 * @rem;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 14 * @rem;
              color: #fff;
              background-color: #ade0f1;
              border-radius: 19 * @rem;
              &.can {
                height: 26 * @rem;
                .image-bg('~@/assets/images/month-activity/get-btn-icon.png');
              }
            }
          }
        }
      }
    }
  }
  .rule-section {
    width: 100%;
    height: 375 * @rem;
    background: url('~@/assets/images/month-activity/section-rule.png') center
      top no-repeat;
    background-size: 100% 100%;
    overflow: hidden;
    .rule-content {
      margin-top: 83 * @rem;
      padding: 0 33 * @rem 0 27 * @rem;
      p {
        font-size: 13 * @rem;
        color: #024760;
        line-height: 18 * @rem;
        margin-bottom: 12 * @rem;
        span {
          color: #ff5029;
        }
      }
    }
  }
  .no-gold-game-popup {
    overflow: unset;
    width: 320 * @rem;
    .search-container {
      box-sizing: border-box;
      width: 320 * @rem;
      height: 450 * @rem;
      padding: 24 * @rem 19 * @rem 10 * @rem;
      display: flex;
      flex-direction: column;
      position: relative;
      overflow: unset;
      .close-search {
        width: 24 * @rem;
        height: 24 * @rem;
        background: url(~@/assets/images/close-search.png) center center
          no-repeat;
        background-size: 24 * @rem 24 * @rem;
        position: absolute;
        right: -10 * @rem;
        top: -10 * @rem;
      }
      .search-bar {
        display: flex;
        align-items: center;
        .input-text {
          width: 240 * @rem;
          height: 35 * @rem;
          border: 1px solid #e5e5e5;
          border-radius: 18 * @rem;
          flex: 1;
          overflow: hidden;
          form {
            display: block;
            width: 100%;
            height: 100%;
          }
          input {
            box-sizing: border-box;
            display: block;
            width: 100%;
            height: 100%;
            padding: 0 18 * @rem;
            font-size: 15 * @rem;
            color: #333333;
            background-color: #f6f6f6;
          }
        }
        .search-btn {
          font-size: 15 * @rem;
          color: #666666;
          padding-left: 13 * @rem;
          height: 35 * @rem;
          line-height: 35 * @rem;
        }
      }
      .game-list {
        flex: 1;
        overflow: auto;
        margin-top: 10 * @rem;
        .game-item {
          display: flex;
          align-items: center;
          padding: 21 * @rem 0;
          border-bottom: 1px solid #eeeeee;
          .game-icon {
            width: 50 * @rem;
            height: 50 * @rem;
            border-radius: 10 * @rem;
            background-color: #b5b5b5;
          }
          .right {
            flex: 1;
            min-width: 0;
            margin-left: 10 * @rem;
            .game-name {
              font-size: 16 * @rem;
              font-weight: bold;
              color: #000000;
              white-space: nowrap;
              text-overflow: ellipsis;
              overflow: hidden;
            }
            .use-card {
              font-size: 12 * @rem;
              color: #f72e2e;
              margin-top: 10 * @rem;
              &.can {
                color: #36b150;
              }
            }
          }
        }
      }
    }
  }
  .reward-popup {
    padding: 18 * @rem 0;
    width: 317 * @rem;
    .reward-title {
      text-align: center;
      font-size: 18 * @rem;
      color: #333333;
      font-weight: bold;
      line-height: 24 * @rem;
    }
    .reward-content {
      width: 185 * @rem;
      height: 72 * @rem;
      .image-bg('~@/assets/images/recharge/reward-bg.png');
      margin: 8 * @rem auto;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 20 * @rem;
      color: #ff3d00;
      font-weight: bold;
    }
    .reward-tips {
      text-align: center;
      margin-top: 7 * @rem;
      font-size: 14 * @rem;
      color: #666666;
      line-height: 18 * @rem;
    }
    .reward-confirm {
      width: 167 * @rem;
      height: 36 * @rem;
      .image-bg('~@/assets/images/recharge/reward-btn-bg.png');
      margin: 12 * @rem auto;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16 * @rem;
      color: #ffffff;
      font-weight: bold;
    }
  }
}
</style>
