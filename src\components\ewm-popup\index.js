import Vue from 'vue';
import ewmPopup from './index.vue';
import router from '@/router';
import store from '@/store';

const EwmPopup = Vue.extend(ewmPopup);

function useEwmPopup(options) {
  return new Promise((resolve, reject) => {
    const dialog = new EwmPopup({
      router,
      store,
    });

    dialog.$mount(document.createElement('div'));
    document.body.appendChild(dialog.$el);

    dialog.$el.addEventListener(
      'animationend',
      () => {
        if (dialog.show == false) {
          dialog.$destroy();
          dialog.$el.parentNode.removeChild(dialog.$el);
        }
      },
      false,
    );

    dialog.show = true;
    dialog.ewmSrc = options.ewmSrc || '';
    dialog.payInfo = options.payInfo || {};

    if (options.onClose) {
      dialog.onClose = options.onClose;
    }
  });
}

export default useEwmPopup;
