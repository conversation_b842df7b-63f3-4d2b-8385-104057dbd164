<template>
  <!-- 登录弹窗组件 -->
  <div>
    <van-popup
      v-model="show"
      :close-on-click-overlay="closeOnClickOverlay"
      :lock-scroll="lockScroll"
      class="login-popup popup-container"
      :show-confirm-button="false"
    >
      <div class="popup-header" v-if="showClose">
        <div class="close-btn" @click="onClose">
          <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
            <path
              d="M12 4L4 12M4 4L12 12"
              stroke="#999"
              stroke-width="1.5"
              stroke-linecap="round"
            />
          </svg>
        </div>
      </div>

      <div class="popup-body">
        <div class="popup-icon" v-if="icon">
          <img :src="icon" alt="icon" />
        </div>

        <div class="popup-title" v-if="title">{{ title }}</div>

        <div class="popup-desc" v-if="desc" v-html="desc"></div>

        <div class="popup-content" v-if="content" v-html="content"></div>

        <div class="popup-tips" v-if="tips" v-html="tips"></div>
      </div>

      <div class="operation-bar">
        <div
          class="btn operation-btn cancel"
          :style="{ background: cancelBgColor, color: cancelTextColor }"
          @click="onCancel"
          v-if="showCancel"
        >
          {{ cancelText }}
        </div>
        <div
          class="btn operation-btn login"
          :style="{ background: loginBgColor, color: loginTextColor }"
          @click="onLogin"
          v-if="showLogin"
        >
          {{ loginText }}
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
export default {
  name: 'LoginPopup',
  data() {
    return {
      show: false,
      title: '登录提示',
      desc: '',
      content: '请先登录后再进行操作',
      tips: '',
      icon: '',
      cancelText: '取消',
      loginText: '登录',
      cancelBgColor: '',
      cancelTextColor: '',
      loginBgColor: '',
      loginTextColor: '',
      showCancel: true,
      showLogin: true,
      showClose: false,
      closeOnClickOverlay: false,
      lockScroll: true,
    };
  },
  methods: {
    onClose() {
      this.show = false;
    },
    onCancel() {
      this.show = false;
    },
    onLogin() {
      this.show = false;
    },
  },
};
</script>

<style lang="less" scoped>
.popup-container {
  box-sizing: border-box;
  border-radius: 16 * @rem;
  width: 300 * @rem;
  padding: 0;
  position: relative;

  .popup-header {
    position: relative;
    padding: 15 * @rem 15 * @rem 0;

    .close-btn {
      position: absolute;
      top: 15 * @rem;
      right: 15 * @rem;
      width: 24 * @rem;
      height: 24 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      z-index: 10;
    }
  }

  .popup-body {
    padding: 20 * @rem 31 * @rem;

    .popup-icon {
      text-align: center;
      margin-bottom: 15 * @rem;

      img {
        width: 60 * @rem;
        height: 60 * @rem;
        object-fit: contain;
      }
    }

    .popup-title {
      font-size: 16 * @rem;
      color: #333333;
      text-align: center;
      font-weight: 600;
      line-height: 40 * @rem;
      overflow: hidden;
      white-space: nowrap;
    }

    .popup-desc {
      box-sizing: border-box;
      font-size: 14 * @rem;
      color: #777777;
      line-height: 20 * @rem;
      text-align: center;
      margin-top: 10 * @rem;
    }

    .popup-content {
      box-sizing: border-box;
      font-size: 14 * @rem;
      color: #333333;
      line-height: 20 * @rem;
      text-align: center;
      margin-top: 15 * @rem;
    }

    .popup-tips {
      box-sizing: border-box;
      font-size: 11 * @rem;
      color: #777777;
      line-height: 15 * @rem;
      text-align: center;
      margin-top: 10 * @rem;
    }
  }

  .operation-bar {
    display: flex;
    align-items: center;
    margin: 0;
    padding: 0 31 * @rem 29 * @rem;
    gap: 20 * @rem;

    .operation-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      flex: 1;
      min-width: 0;
      height: 36 * @rem;
      border-radius: 18 * @rem;
      font-size: 14 * @rem;
      cursor: pointer;
      transition: all 0.3s ease;

      &:active {
        transform: scale(0.95);
      }

      &.login {
        background: @themeBg;
        color: #fff;
      }

      &.cancel {
        background: #f5f5f5;
        color: #7d7d7d;
      }
    }
  }
}
</style>
