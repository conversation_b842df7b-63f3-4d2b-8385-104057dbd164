<template>
  <div class="guoqing-page">
    <nav-bar-2
      ref="topNavBar"
      bgStyle="transparent-white"
      :azShow="true"
      :placeholder="false"
    ></nav-bar-2>
    <!-- banner图 -->
    <div class="page-banner"></div>
    <!-- 今天你刮了吗 -->
    <div class="section section-activity">
      <div class="line-1">
        <div class="user-info">
          <div class="avatar">
            <user-avatar
              :src="userInfo.avatarUrl"
              v-if="userInfo.token"
            ></user-avatar>
            <img
              v-else
              class="no-avatar"
              src="@/assets/images/guoqing/avatar.png"
              alt=""
            />
          </div>
          <div class="nickname" v-if="userInfo.token">
            {{ userInfo.nickname }}
          </div>
          <div class="nickname underline" @click="login" v-else> 请登录 </div>
        </div>
        <div class="record-btn btn" @click="clickRecord">刮奖记录</div>
      </div>
      <div class="scratch-bar">
        <div class="count-line">
          <div class="count-text">
            刮奖次数：<span>{{ remain_lottery_count }}</span
            >次
          </div>
          <div class="refresh-btn btn" @click="handleRefresh">刷新</div>
        </div>
        <div class="gua-line">
          <img src="@/assets/images/guoqing/gua-line.png" alt="" />
        </div>
        <div class="gua-container">
          <div class="prize">{{ first_gold }}金币</div>
          <canvas
            id="canvas"
            @touchmove="touchMove"
            @touchstart="touchStart"
            @touchend="touchEnd"
          ></canvas>
        </div>
        <div class="gua-tips">每完成一笔充值都有机会获得刮奖机会哦</div>
      </div>
      <div
        class="scratch-btn btn"
        @click="goRechargePtb"
        v-if="remain_lottery_status"
      ></div>
      <div class="scratch-btn disabled" v-else></div>
    </div>
    <!-- 额外SVIP天数奖励 -->
    <div class="section section-reward">
      <div class="award">
        <div class="award-subtitle">
          累计刮奖达到限定次数可获得额外SVIP天数奖励
        </div>
        <div class="award-current">
          当前完成：<span>{{ user_lottery_count }}次</span>
        </div>
        <div class="box-list">
          <div
            class="box-item"
            :class="{ 'can-get': item.status == 1 }"
            v-for="(item, index) in box_list"
            :key="index"
            @click="getBox(item)"
          >
            <div class="title">{{ item.title }}</div>
            <div class="box-icon"></div>
            <div class="times">{{ item.num }}次</div>
            <div class="tag">{{ item.status == 2 ? '已领取' : '领取' }}</div>
          </div>
        </div>
      </div>
    </div>
    <!-- 活动说明 -->
    <div class="section section-introduction">
      <div class="introduction-content">
        <div class="title">1，活动时间：</div>
        <div class="desc">2022-10-01 00:00:00 至 2022-10-07 23:59:59</div>
        <div class="title">2，获取规则：</div>
        <div class="desc">
          每日充值任意金额必得1次刮奖机会。 （仅限<span
            >游戏内使用微信/支付宝充值</span
          >
          或者<span>充值平台币</span>），每笔订单获得一次机会，每日最多可获得3次机会。
        </div>
        <div class="tips">注：开通SVIP会员、开通省钱畅玩卡不参与本次活动。</div>
        <div class="title">3，发放规则：</div>
        <div class="desc">
          刮奖100%可得金币奖励；最高可赢充值金额等额金币奖励（<span>免单</span>）。金币奖励会自动发放至账号的金币钱包中，请在<span>【我的-金币】</span>中查看。
        </div>
        <div class="tips">
          温馨提示：由于游戏厂商的要求，部分游戏内充值暂不支持使用金币支付，请先确定需要用金币充值的游戏在不在可使用名单内。<b
            class="game-open-btn"
            @click="openGameDialog"
            >查看名单&gt;</b
          >
        </div>
        <div class="title">4，活动规则：</div>
        <div class="desc">
          活动期间充值完成后请返回本活动页面点击<span>【刷新】</span>查看刮奖次数及时刮奖领取金币奖励，活动结束后将清空所有刮奖次数。
        </div>
        <div class="title">5，奖励规则</div>
        <div class="desc">
          活动期间参与刮奖，可额外获得SVIP天数奖励。<br />
          累计参与刮奖3次，可免费领取1天SVIP（每个用户只能领取一次）<br />
          累计参与刮奖7次，可免费领取3天SVIP（每个用户只能领取一次）<br />
          累计参与刮奖15次，可免费领取7天SVIP（每个用户只能领取一次）
        </div>
        <div class="tips">注：免费领取的SVIP不赠送金币。</div>
        <div class="title">6，参与本活动需要完成防沉迷实名认证</div>
      </div>
    </div>
    <!-- 没有剩余的弹窗 -->
    <van-dialog
      v-model="noLeftPopup"
      :lock-scroll="false"
      :show-confirm-button="false"
      :close-on-click-overlay="false"
      class="popup no-left-popup"
    >
      <div class="popup-close" @click="noLeftPopup = false"></div>
      <div class="popup-title">当前未有剩余刮奖机会</div>
      <div class="popup-content">
        活动期间每完成充值一笔即有机会获得１次刮奖机会，刮奖即得充值金额百分比金币奖励，单笔充值金额越高，所得金币奖励越大哦～最高赢得等额金币奖励
      </div>
      <div class="popup-confirm btn" @click="getLotteryCount">获取刮奖机会</div>
    </van-dialog>

    <!-- 领取宝箱的弹窗 -->
    <van-dialog
      v-model="boxPopup"
      :lock-scroll="false"
      :show-confirm-button="false"
      :close-on-click-overlay="false"
      class="popup"
    >
      <div class="popup-tips">
        累计刮奖<span>{{ total }}</span
        >次 ,获得<span>{{ reward }}</span
        >天SVIP
      </div>
      <div class="popup-confirm btn" @click="boxPopup = false">确定</div>
    </van-dialog>

    <!-- 刮奖记录 -->
    <van-dialog
      v-model="recordPopup"
      :lock-scroll="false"
      :show-confirm-button="false"
      :close-on-click-overlay="false"
      class="popup"
    >
      <div class="popup-close" @click="recordPopup = false"></div>
      <div class="popup-title">刮奖记录</div>
      <div class="record-list" v-if="recordList.length">
        <div
          class="record-item"
          v-for="(item, index) in recordList"
          :key="index"
        >
          <div class="record-title">{{ item.title }}</div>
          <div class="record-num">
            <span>{{ item.desc }}</span>
          </div>
        </div>
      </div>
      <div class="record-list no-list" v-else>
        <div class="no-list-text">暂无记录</div>
      </div>
    </van-dialog>

    <!-- 查询可用游戏 -->
    <van-dialog
      v-model="gameDialogShow"
      :lock-scroll="false"
      :show-confirm-button="false"
      :close-on-click-overlay="true"
      class="no-gold-game-popup"
    >
      <div class="search-container">
        <div class="close-search" @click="gameDialogShow = false"></div>
        <div class="search-bar">
          <div class="input-text">
            <form @submit.prevent="searchGame">
              <input
                type="text"
                v-model.trim="inputGame"
                placeholder="输入游戏名"
              />
            </form>
          </div>
          <div class="search-btn" @click="searchGame">搜索</div>
        </div>
        <yy-list
          class="yy-list game-list"
          v-model="loadingObj"
          :finished="finished"
          @refresh="onRefresh"
          @loadMore="loadMore"
          :empty="!gameList.length"
          tips="请输入您想搜索的游戏"
          :check="false"
        >
          <div
            class="game-item btn"
            v-for="item in gameList"
            :key="item.id"
            @click="toGame(item)"
          >
            <div class="game-icon">
              <img :src="item.titlepic" alt="" />
            </div>
            <div class="right">
              <div class="game-name">{{ item.title }}</div>
              <div class="use-card" :class="{ can: item.use_gold_pay == 1 }">
                {{ item.use_gold_pay == 1 ? '' : '不' }}支持使用金币支付
              </div>
            </div>
          </div>
        </yy-list>
      </div>
    </van-dialog>
    <ptb-recharge-popup @success="getActivityInfo"></ptb-recharge-popup>
  </div>
</template>

<script>
import { mapMutations } from 'vuex';
import { BOX_goToGame, platform, boxInit, BOX_login } from '@/utils/box.uni.js';
import {
  ApiFestivalGetSelfInfo,
  ApiFestivalRemainLotteryCount,
  ApiFestivalScratchLottery,
  ApiFestivalTakeBox,
  ApiFestivalLotteryList,
} from '@/api/views/guoqing.js';
import { ApiGameCardSearchGame } from '@/api/views/recharge.js';
import ptbRechargePopup from '@/components/ptb-recharge-popup';
import canvasImg from '@/assets/images/guoqing/gua-area.png';
export default {
  components: {
    ptbRechargePopup,
  },
  data() {
    return {
      activity_status: 1, // 1活动已开始 2 活动不存在 3活动未开始 4活动已结束
      box_list: [], //宝箱数据
      first_gold: 0,
      remain_lottery_status: 1, // 当天还能否刮奖 0 不能 1 能
      user_lottery_count: 0, // 用户刮了多少次
      canvas: '', // 画布
      ctx: '', // 画笔
      clearCount: 0, // 刮奖区被刮开的程度 最大150就全解开
      ismousedown: false,
      scratchStatus: 0, //0=未开始 1=刮奖中 2=刮奖完
      fontem: '',
      showPrize: false, // 显示奖品
      ratio: 0,
      remain_lottery_count: 0, // 剩余刮奖次数
      noLeftPopup: false, // 没有剩余的弹窗
      boxPopup: false, // 领取宝箱的弹窗
      recordPopup: false, // 刮奖记录的弹窗
      // 是否显示非金币支付游戏
      gameDialogShow: false,
      // 非金币支付的游戏列表
      gameList: [],
      inputGame: '',
      loadingObj: {
        loading: false,
        reloading: false,
      },
      finished: false,
      page: 1,
      listRows: 20,
      empty: false,
      recordList: [], // 领取记录
      reward: '', // 点击领取的宝箱svip天数
      total: '', // 点击领取的宝箱刮奖次数
    };
  },
  computed: {
    activity_status_text() {
      let text = '';
      switch (this.activity_status) {
        case 1:
          text = '活动已开始';
          break;
        case 2:
          text = '活动不存在';
          break;
        case 3:
          text = '活动未开始';
          break;
        case 4:
          text = '活动已结束';
          break;
      }
      return text;
    },
  },
  async created() {
    // 官包触发页面刷新（将onResume挂载在window上）
    if (platform == 'android') {
      window.onResume = this.onResume;
    }
    await this.getActivityInfo();
  },
  mounted() {
    this.ratio = document.body.clientWidth / 375;
    this.canvas = document.getElementById('canvas');
    this.canvas.width = this.canvas.clientWidth;
    this.canvas.height = this.canvas.clientHeight;
    this.ctx = this.canvas.getContext('2d');
    this.initCanvas();
  },
  methods: {
    ...mapMutations({
      setShowPtbRechargePopup: 'recharge/setShowPtbRechargePopup',
    }),
    login() {
      BOX_login();
    },
    async initCanvas() {
      this.ctx.globalCompositeOperation = 'source-over';
      let image = new Image();
      image.src = canvasImg;
      image.onload = async () => {
        await this.ctx.drawImage(
          image,
          0,
          0,
          this.canvas.width,
          this.canvas.height,
        );
      };
    },
    touchStart(e) {
      if (this.activity_status != 1) {
        this.$toast(this.activity_status_text);
        return false;
      }
      if (!this.userInfo.token) {
        this.login();
        return false;
      }
      switch (this.scratchStatus) {
        case 0: // 未开始
          if (this.remain_lottery_status == 0) {
            this.$toast('今日次数已用完');
            return;
          } else if (this.remain_lottery_count <= 0) {
            // 当没有刮奖次数时;
            this.noLeftPopup = true;
            return;
          } else {
            // 开始刮奖
            this.scratchStatus = 1;
          }
          break;
        case 1: // 刮奖中
          break;
        case 2: // 已结束
          break;
      }
    },
    touchMove(e) {
      if (this.scratchStatus == 1) {
        let x = e.touches[0].clientX - this.canvas.getBoundingClientRect().left,
          y = e.touches[0].clientY - this.canvas.getBoundingClientRect().top;
        e.preventDefault();
        this.ctx.clearRect(x, y, 15 * this.ratio, 15 * this.ratio);
        this.clearCount++;
      }
    },
    async touchEnd(e) {
      if (this.scratchStatus == 1) {
        if (this.clearCount > 80) {
          // 当手指累计滑动超过80时触发清空矩形画布, 数值越大要刮得越久
          this.scratchStatus = 2;
          this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
          await this.handleScratch();
        }
      }
    },
    async handleScratch() {
      try {
        const res = await ApiFestivalScratchLottery();
        let {
          code,
          data: { remain_lottery_count, gold },
        } = res;
        if (code > 0) {
          this.remain_lottery_count = remain_lottery_count;
          this.first_gold = gold;
          this.$toast(`恭喜获得${gold}金币`);
        }
      } finally {
        setTimeout(async () => {
          this.scratchStatus = 0;
          this.clearCount = 0;
          this.initCanvas();
          await this.getActivityInfo();
        }, 2000);
      }
    },
    // 刷新刮奖次数，状态
    async handleRefresh() {
      const res = await ApiFestivalRemainLotteryCount({ refresh: 1 });
      this.remain_lottery_count = res.data.remain_lottery_count;
      this.clearCount = 0;
      this.scratchStatus = 0;
      this.initCanvas();
    },
    clickRecord() {
      if (!this.userInfo.token) {
        this.login();
        return false;
      }
      this.recordPopup = true;
      this.getRecordList();
    },
    async getRecordList() {
      const res = await ApiFestivalLotteryList();
      this.recordList = res.data.list;
    },
    async onResume() {
      await boxInit();
      await this.getActivityInfo();
    },
    async getBox(item) {
      // switch (item.status) {
      //   case 0:
      //     this.$toast("未达要求");
      //     break;
      //   case 1:
      //     const res = await ApiFestivalTakeBox({ type: item.type });
      //     this.reward = res.data.reward;
      //     this.total = res.data.title;
      //     this.boxPopup = true;
      //     break;
      //   case 2:
      //     this.$toast("该奖励已领取");
      //     break;
      // }
      const res = await ApiFestivalTakeBox({ type: item.type });
      this.reward = res.data.svip_day;
      this.total = res.data.user_lottery_count;
      this.boxPopup = true;
      await this.getActivityInfo();
    },
    getLotteryCount() {
      if (!this.userInfo.token) {
        this.login();
        return false;
      }
      this.noLeftPopup = false;
      this.goRechargePtb();
    },
    goRechargePtb() {
      if (this.activity_status != 1) {
        this.$toast(this.activity_status_text);
        return false;
      }
      if (!this.userInfo.token) {
        this.login();
        return false;
      }
      this.setShowPtbRechargePopup(true);
    },
    // 活动信息
    async getActivityInfo() {
      try {
        const res = await ApiFestivalGetSelfInfo();
        let {
          box_list,
          first_gold,
          nickname,
          remain_lottery_count,
          remain_lottery_status,
          user_lottery_count,
          activity_status,
        } = res.data;
        this.box_list = box_list;
        this.activity_status = activity_status;
        this.first_gold = first_gold;
        this.remain_lottery_count = remain_lottery_count;

        this.remain_lottery_status = remain_lottery_status;
        this.user_lottery_count = user_lottery_count;
      } catch (e) {}
    },
    // 打开游戏弹窗
    openGameDialog() {
      this.gameDialogShow = true;
    },
    toGame(item) {
      BOX_goToGame(
        {
          params: {
            id: item.id,
          },
        },
        { id: item.id },
      );
    },
    // 搜索不可使用金币的游戏
    async searchGame() {
      if (!this.inputGame) {
        this.$toast('请输入游戏名');
        return false;
      }
      this.$toast({
        type: 'loading',
        duration: 0,
        message: '拼命加载中...',
      });
      this.gameList = [];
      await this.getGameList();
    },
    async getGameList(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      let res = await ApiGameCardSearchGame({
        keyword: this.inputGame,
        listRows: 20,
        page: this.page,
      });
      this.$toast.clear();
      let { list } = res.data;
      if (action === 1 || this.page === 1) {
        this.gameList = [];
      }
      if (!list.length) {
        this.empty = true;
      }
      this.gameList.push(...list);
      if (list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
    },
    async onRefresh() {
      try {
        await this.getGameList();
      } finally {
        this.loadingObj.reloading = false;
      }
    },
    async loadMore() {
      try {
        await this.getGameList(2);
      } finally {
        this.loadingObj.loading = false;
      }
    },
  },
};
</script>

<style lang="less" scoped>
.guoqing-page {
  box-sizing: border-box;
  min-height: 100vh;
  background: linear-gradient(180deg, #df7455 0%, #dc6551 100%, #ff754a 100%);
  padding-bottom: 65 * @rem;
  .page-banner {
    width: 100%;
    height: 675 * @rem;
    background: url('~@/assets/images/guoqing/page-bg.png') center top no-repeat;
    background-size: 100% 100%;
    overflow: hidden;
  }
  .section {
    box-sizing: border-box;
    width: 345 * @rem;
    margin: 0 auto;
    background-color: #fff;
    position: relative;
    background-size: 345 * @rem 15 * @rem;
    background-image: url(~@/assets/images/guoqing/section-center.png);
    &::before {
      content: '';
      position: absolute;
      top: -44 * @rem;
      left: 0 * @rem;
      width: 345 * @rem;
      height: 44 * @rem;
      background-size: 100%;
      background-image: url(~@/assets/images/guoqing/section-top-1.png);
      background-repeat: no-repeat;
    }
    &::after {
      content: '';
      position: absolute;
      bottom: -45 * @rem;
      left: 0 * @rem;
      width: 345 * @rem;
      height: 45 * @rem;
      background-size: 100%;
      background-image: url(~@/assets/images/guoqing/section-bottom.png);
      background-repeat: no-repeat;
    }
    &.section-activity {
      height: 312 * @rem;
      margin-top: -247 * @rem;
      padding: 0 22 * @rem;
      &::before {
        background-image: url(~@/assets/images/guoqing/section-top-1.png);
      }
      .line-1 {
        display: flex;
        align-items: center;
        .user-info {
          flex: 1;
          min-width: 0;
          display: flex;
          align-items: center;
          .avatar {
            width: 30 * @rem;
            height: 30 * @rem;
          }
          .nickname {
            font-size: 15 * @rem;
            color: #9b4031;
            margin-left: 10 * @rem;
            flex: 1;
            min-width: 0;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            margin-right: 10 * @rem;
            &.underline {
              text-decoration: underline;
            }
          }
        }
        .record-btn {
          font-size: 15 * @rem;
          color: #f7572d;
          text-decoration: underline;
        }
      }
      .scratch-bar {
        box-sizing: border-box;
        height: 219 * @rem;
        background-color: #fff;
        box-shadow: 0px 2px 15px 0px rgba(248, 0, 0, 0.1);
        border-radius: 15 * @rem;
        margin-top: 15 * @rem;
        position: relative;
        padding: 20 * @rem 14 * @rem 0;
        .count-line {
          display: flex;
          align-items: center;
          justify-content: space-between;
          .count-text {
            font-size: 15 * @rem;
            color: #9b4031;
            span {
              color: #f7572d;
            }
          }
          .refresh-btn {
            width: 57 * @rem;
            height: 26 * @rem;
            background-color: #f7572d;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14 * @rem;
            color: #ffffff;
            border-radius: 5 * @rem;
          }
        }
        .gua-line {
          width: 272 * @rem;
          height: 20 * @rem;
          margin: 10 * @rem auto;
        }
        .gua-container {
          width: 272 * @rem;
          height: 96 * @rem;
          position: relative;
          background: url(~@/assets/images/guoqing/gua-area.png) center center
            no-repeat;
          background-size: 100%;
          border-radius: 12 * @rem;
          overflow: hidden;
          .prize {
            width: 272 * @rem;
            height: 96 * @rem;
            line-height: 96 * @rem;
            background: #f5f5f5;
            font-size: 35 * @rem;
            letter-spacing: 3 * @rem;
            text-align: center;
            color: #f7572d;
            font-weight: bold;
          }
          #canvas {
            width: 100%;
            height: 100%;
            position: absolute;
            top: 0;
            left: 0;
          }
        }
        .gua-tips {
          font-size: 12 * @rem;
          line-height: 15 * @rem;
          color: #9b4031;
          margin-top: 10 * @rem;
          text-align: center;
        }
      }
      .scratch-btn {
        width: 224 * @rem;
        height: 48 * @rem;
        background: url(~@/assets/images/guoqing/btn-charge.png) no-repeat;
        background-size: 224 * @rem 48 * @rem;
        margin: 20 * @rem auto 0;
        position: relative;
        z-index: 2;
        &.disabled {
          background-image: url(~@/assets/images/guoqing/btn-disabled.png);
        }
      }
    }
    &.section-reward {
      height: 140 * @rem;
      margin-top: 115 * @rem;
      &::before {
        background-image: url(~@/assets/images/guoqing/section-top-2.png);
      }
      .award {
        margin-bottom: 90 * @rem;
        position: relative;
        z-index: 2;
        .award-subtitle {
          padding: 10 * @rem 0 0;
          text-align: center;
          font-size: 14 * @rem;
          font-weight: bold;
          color: #9b4031;
        }
        .award-current {
          padding-left: 23 * @rem;
          font-size: 12 * @rem;
          color: #dd6652;
          font-weight: bold;
          margin-top: 10 * @rem;
          span {
            font-size: 16 * @rem;
            color: #ff5a41;
            font-weight: bold;
          }
        }
        .box-list {
          display: flex;
          justify-content: center;
          margin-top: 15 * @rem;
          .box-item {
            width: 80 * @rem;
            height: 85 * @rem;
            border-radius: 15 * @rem;
            background: rgba(255, 255, 255, 0.3);
            margin: 0 10 * @rem;
            position: relative;
            .title {
              font-size: 12 * @rem;
              font-weight: bold;
              line-height: 15 * @rem;
              color: #9b4031;
              text-align: center;
              margin-top: 12 * @rem;
            }
            .box-icon {
              width: 30 * @rem;
              height: 30 * @rem;
              background: url(~@/assets/images/guoqing/box-disabled.png)
                no-repeat;
              background-size: 30 * @rem 30 * @rem;
              margin: 2 * @rem auto 0;
            }
            .times {
              font-size: 14 * @rem;
              color: #9b4031;
              line-height: 18 * @rem;
              font-weight: bold;
              margin-top: 2 * @rem;
              text-align: center;
            }
            .tag {
              position: absolute;
              right: -16 * @rem;
              top: -5 * @rem;
              width: 41 * @rem;
              height: 16 * @rem;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 12 * @rem;
              color: #ffffff;
              background-color: #edccbb;
              border-radius: 6 * @rem 0 6 * @rem 0;
            }
            &.can-get {
              background: rgba(255, 255, 255, 0.7);
              .title {
                color: #ff5a41;
              }
              .box-icon {
                background-image: url(~@/assets/images/guoqing/box.png);
              }
              .times {
                color: #ff5a41;
              }
              .tag {
                background-color: #ff5a41;
              }
            }
          }
        }
      }
    }
    &.section-introduction {
      min-height: 312 * @rem;
      margin-top: 115 * @rem;
      padding: 0 20 * @rem;
      &::before {
        background-image: url(~@/assets/images/guoqing/section-top-3.png);
      }
      .introduction-content {
        padding-top: 10 * @rem;
        .title {
          font-size: 16 * @rem;
          color: #532525;
          font-weight: bold;
          line-height: 20 * @rem;
          margin-top: 20 * @rem;
        }
        .desc {
          font-size: 15 * @rem;
          color: #66493d;
          line-height: 22 * @rem;
          margin-top: 10 * @rem;
          span {
            color: #ff3616;
          }
        }
        .tips {
          font-size: 15 * @rem;
          color: #dd6551;
          line-height: 22 * @rem;
          margin-top: 10 * @rem;
        }
        .game-open-btn {
          font-size: 16 * @rem;
          font-weight: bold;
          color: #ff3616;
          text-decoration: underline;
        }
      }
    }
  }
  .popup {
    width: 290 * @rem;
    border: 2px solid #ffa362;
    box-shadow: 0px 2px 15px 0px rgba(248, 0, 0, 0.1);
    background: #fee5d7;
    border-radius: 14 * @rem;
    .popup-close {
      width: 33 * @rem;
      height: 27 * @rem;
      background: #ffa362 url(~@/assets/images/guoqing/popup-close.png) center
        center no-repeat;
      background-size: 22 * @rem 22 * @rem;
      position: absolute;
      right: 0;
      top: 0;
      border-radius: 0 12 * @rem 0 12 * @rem;
    }
    .popup-title {
      font-size: 16 * @rem;
      color: #9b4031;
      line-height: 20 * @rem;
      margin-top: 25 * @rem;
      text-align: center;
      font-weight: bold;
    }
    .popup-content {
      font-size: 14 * @rem;
      color: #9b4031;
      line-height: 19 * @rem;
      margin-top: 20 * @rem;
      padding: 0 18 * @rem;
    }
    .popup-tips {
      text-align: center;
      font-size: 16 * @rem;
      line-height: 20 * @rem;
      color: #9b4031;
      margin: 44 * @rem 0 30 * @rem;
      span {
        color: #f8582e;
      }
    }
    .popup-confirm {
      width: 254 * @rem;
      height: 48 * @rem;
      background: url(~@/assets/images/guoqing/btn-bg.png) no-repeat;
      background-size: 254 * @rem 48 * @rem;
      margin: 20 * @rem auto;
      font-size: 18 * @rem;
      line-height: 42 * @rem;
      color: #ffffff;
      text-align: center;
      font-weight: 500;
    }
    .record-list {
      box-sizing: border-box;
      padding: 10 * @rem 20 * @rem 10 * @rem;
      margin: 10 * @rem 0 20 * @rem;
      height: 200 * @rem;
      overflow-y: auto;
      &.no-list {
        display: flex;
        align-items: center;
        justify-content: center;
        .no-list-text {
          font-size: 16 * @rem;
          color: #9b4031;
          line-height: 20 * @rem;
          text-align: center;
        }
      }
      .record-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        &:not(:first-of-type) {
          margin-top: 15 * @rem;
        }
        .record-title {
          flex: 1;
          min-width: 0;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          font-size: 14 * @rem;
          color: #9b4031;
          line-height: 19 * @rem;
        }
        .record-num {
          font-size: 14 * @rem;
          color: #9b4031;
          line-height: 19 * @rem;
          span {
            color: #f8582e;
            font-weight: bold;
          }
        }
      }
    }
  }
}
.no-gold-game-popup {
  overflow: unset;
  width: 320 * @rem;
  .search-container {
    box-sizing: border-box;
    width: 320 * @rem;
    height: 450 * @rem;
    padding: 24 * @rem 19 * @rem 10 * @rem;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: unset;
    .close-search {
      width: 24 * @rem;
      height: 24 * @rem;
      background: url(~@/assets/images/close-search.png) center center no-repeat;
      background-size: 24 * @rem 24 * @rem;
      position: absolute;
      right: -10 * @rem;
      top: -10 * @rem;
    }
    .search-bar {
      display: flex;
      align-items: center;
      .input-text {
        width: 240 * @rem;
        height: 35 * @rem;
        border: 1px solid #e5e5e5;
        border-radius: 18 * @rem;
        flex: 1;
        overflow: hidden;
        form {
          display: block;
          width: 100%;
          height: 100%;
        }
        input {
          box-sizing: border-box;
          display: block;
          width: 100%;
          height: 100%;
          padding: 0 18 * @rem;
          font-size: 15 * @rem;
          color: #333333;
          background-color: #f6f6f6;
        }
      }
      .search-btn {
        font-size: 15 * @rem;
        color: #666666;
        padding-left: 13 * @rem;
        height: 35 * @rem;
        line-height: 35 * @rem;
      }
    }
    .game-list {
      flex: 1;
      overflow: auto;
      margin-top: 10 * @rem;
      .game-item {
        display: flex;
        align-items: center;
        padding: 21 * @rem 0;
        border-bottom: 1px solid #eeeeee;
        .game-icon {
          width: 50 * @rem;
          height: 50 * @rem;
          border-radius: 10 * @rem;
          background-color: #b5b5b5;
        }
        .right {
          flex: 1;
          min-width: 0;
          margin-left: 10 * @rem;
          .game-name {
            font-size: 16 * @rem;
            font-weight: bold;
            color: #000000;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
          }
          .use-card {
            font-size: 12 * @rem;
            color: #f72e2e;
            margin-top: 10 * @rem;
            &.can {
              color: #36b150;
            }
          }
        }
      }
    }
  }
}
</style>
