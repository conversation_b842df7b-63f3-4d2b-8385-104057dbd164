<template>
  <div class="april-activity-rank">
    <nav-bar-2
      ref="topNavBar"
      :bgStyle="'transparent-white'"
      :azShow="true"
      :placeholder="false"
    ></nav-bar-2>
    <div class="bg1"></div>
    <div class="bg2"></div>
    <main>
      <div class="list">
        <div class="item item2">
          <div class="left text">序号</div>
          <div class="center text">游戏账号</div>
          <div class="right text">游戏昵称</div>
        </div>
        <div v-if="list.length == 0 && ajaxFinish" class="empty"
          >暂无名单公示</div
        >
        <template v-if="list.length > 0 && ajaxFinish">
          <div v-for="(item, index) in list" :key="index" class="item">
            <div class="left text">{{ index + 1 }}</div>
            <div class="center text">{{ item.user.username }}</div>
            <div class="right text">{{ item.user.nickname }}</div>
          </div>
        </template>
      </div>

      <div class="explain">
        注：奖励将活动结束后1-2个工作日自动发放至获奖用户的账户之中，可在<span>我的-金币</span>中查看
      </div>
    </main>
    <div class="bg3"></div>
    <div class="bottom-container">
      <div class="bottom-left"></div>
      <div class="bottom-right"></div>
    </div>
  </div>
</template>
<script>
import { ApiAprilRankList } from '@/api/views/april_activity';

export default {
  name: 'AprilActivityRank',
  data() {
    return {
      list: [],
      ajaxFinish: true,
    };
  },
  created() {
    this.getList();
  },
  methods: {
    async getList() {
      const res = await ApiAprilRankList();
      this.list = res.data.list;
    },
  },
};
</script>
<style lang="less" scoped>
.april-activity-rank {
  position: relative;
  min-height: 100vh;
  background: #2d1513;
  overflow: hidden;
  .bg1 {
    width: 100%;
    height: 112 * @rem;
    .image-bg('~@/assets/images/april-activity/ac_bg16.png');
  }
  .bg2 {
    width: 100%;
    height: 74 * @rem;
    .image-bg('~@/assets/images/april-activity/ac_bg15.png');
    background-position: -0.5 * @rem 0;
  }
  .bottom-container {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
  }
  main {
    background: rgba(240, 220, 196, 1);
    .list {
      padding: 25 * @rem 20 * @rem;
      .item {
        display: flex;
        justify-content: space-between;
        height: 30 * @rem;
        &.item2 {
          .text {
            font-size: 16 * @rem;
            font-family: PingFang HK-Semibold, PingFang HK;
            font-weight: 600;
            color: #7c343f;
          }
        }
        .text {
          font-weight: 600;
          color: #a16b48;
          display: flex;
          justify-content: center;
          align-items: center;
        }
        .left {
          flex: 0 0 20%;
        }
        .center {
          flex: 0 0 40%;
        }
        .right {
          flex: 0 0 40%;
          justify-content: flex-end;
        }
      }
    }
    .explain {
      font-size: 11 * @rem;
      color: #a16b48;
      text-align: center;
    }
    .empty {
      text-align: center;
      padding: 140 * @rem 0;
      color: #a16b48;
    }
  }
  .bg3 {
    width: 100%;
    height: 60 * @rem;
    margin-bottom: 18 * @rem;
    .image-bg('~@/assets/images/april-activity/ac_bg2.png');
  }
  .bottom-container {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    .bottom-left {
      width: 60 * @rem;
      height: 60 * @rem;
      .image-bg('~@/assets/images/april-activity/ac_bg7.png');
    }
    .bottom-right {
      position: relative;
      top: 2 * @rem;
      width: 54 * @rem;
      height: 46 * @rem;
      .image-bg('~@/assets/images/april-activity/ac_bg8.png');
    }
  }
}
</style>
