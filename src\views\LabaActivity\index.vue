<template>
  <div class="laba-activity">
    <div class="main">
      <div class="top-section">
        <div
          class="top-line"
          :style="{ backgroundColor: `rgba(255, 40, 84, ${navbarOpacity})` }"
        >
          <div class="back" @click="back"></div>
          <div class="user-container">
            <div v-if="userInfo.nickname" class="user">
              <user-avatar class="avatar"></user-avatar>
              <div class="nickname">{{ userInfo.nickname }}</div>
            </div>
            <div @click="login" v-else class="user">
              <div class="avatar img">
                <img
                  src="@/assets/images/laba-activity/laba-avatar-default.png"
                  alt=""
                />
              </div>
              <div class="nickname">未登录</div>
            </div>
          </div>
        </div>
        <div class="rule-btn" @click="rulePopup = true">活动规则</div>
      </div>
      <section class="section section-1">
        <div class="section-content">
          <div class="broadcast">
            <div class="broadcast-icon"></div>
            <van-swipe
              class="broadcast-swiper"
              vertical
              :autoplay="2000"
              :touchable="false"
              :show-indicators="false"
            >
              <van-swipe-item v-for="(item, index) in fake_list" :key="index">
                <div class="broadcast-item">
                  {{ item }}
                </div>
              </van-swipe-item>
            </van-swipe>
          </div>
          <div class="lottery-container">
            <div
              class="lottery-list"
              :class="[`rotate${lotteryResult.lottery_id}`]"
            >
              <div
                class="lottery-item"
                v-for="(item, index) in lottery_list"
                :key="index"
                :style="{ '--deg': (index + 1) * 40 + 'deg' }"
              >
                <img :src="item.icon" alt="" />
              </div>
            </div>
            <div class="lottery-btn" @click="handleLottery">
              <div class="lottery-num">x {{ remain_lottery }}</div>
            </div>
          </div>
          <div class="my-card-container">
            <div class="title-bar">
              <div class="title">我的卡片</div>
              <div class="gift-record" @click="openRecordPopup">已兑换奖品</div>
            </div>
            <div class="card-bar">
              <div class="card-list" v-if="my_card.length">
                <div
                  class="card-item"
                  v-for="(item, index) in my_card"
                  :key="index"
                >
                  <img class="card-icon" :src="item.icon" alt="" />
                  <div class="card-num">{{ item.num }}</div>
                </div>
              </div>
              <div class="card-no" v-else>
                您当前尚未获得卡片，前往完成任务抽卡可获取哦~
              </div>
            </div>
          </div>
          <div
            class="compose-btn"
            v-if="is_auspiciously == 1"
            @click="handleCompose"
          ></div>
          <div
            class="compose-btn no"
            v-else-if="is_auspiciously == 0"
            @click="handleCompose"
          ></div>
          <div class="compose-btn had" v-else></div>

          <div class="big-price-tips">
            集卡合成腊八祥瑞卡获取奖励：888金币+7天SVIP+瓜分大奖机会
          </div>
        </div>
      </section>
      <div ref="taskPosition"></div>
      <section class="section section-2">
        <div class="section-content">
          <div class="task-list">
            <div
              class="task-item"
              v-for="(item, index) in task_list"
              :key="index"
            >
              <div class="left-content">
                <div class="task-title">{{ item.title }}</div>
                <div class="task-desc">{{ item.desc }}</div>
              </div>
              <div
                class="task-btn"
                v-if="item.status == 1"
                @click="clickTaskReward(item)"
              ></div>
              <div class="task-btn had" v-else-if="item.status == 2"></div>
              <template v-else-if="item.status == 0">
                <div
                  class="task-btn svip"
                  v-if="item.is_svip == 1"
                  @click="openSvipPopup"
                ></div>
                <div
                  class="task-btn no"
                  v-else
                  @click="clickTaskReward(item)"
                ></div>
              </template>
            </div>
          </div>
          <div class="task-tips">
            温馨提示：仅限游戏内使用<span>微信/支付宝/平台币</span>充值，如有使用金币/绑定平台币/代金券抵扣的，则只计算抵扣后的现金充值金额。
          </div>
        </div>
      </section>

      <section class="section section-3">
        <div class="section-content">
          <div class="exchange-list">
            <div
              class="exchange-item"
              v-for="(item, index) in exchange_list"
              :key="index"
            >
              <div class="prize">
                <img class="prize-icon" :src="item.icon" alt="" />
                <div class="prize-title">{{ item.title }}</div>
              </div>
              <div class="material">
                <div
                  class="material-item"
                  v-for="(material, materialIndex) in item.need_lottery"
                  :key="materialIndex"
                >
                  <div class="material-icon">
                    <img :src="material.icon" alt="" />
                  </div>
                  <div class="material-num">
                    {{ material.remain }}/{{ material.num }}
                  </div>
                </div>
              </div>
              <div
                class="exchange-btn"
                v-if="item.status == 1"
                @click="clickExchangeItem(item)"
              ></div>
              <div
                class="exchange-btn no"
                v-else
                @click="clickExchangeItem(item)"
              ></div>
            </div>
          </div>
        </div>
      </section>

      <section class="section section-4">
        <div class="section-content">
          <div class="desc">
            合成腊八祥瑞卡的玩家，即可获得一次瓜分1888万金币红包的机会，金额随机。
          </div>
          <div class="data-bar">
            <div class="num">当前刮奖次数：{{ is_scratching_prizes }}</div>
            <div class="refresh-btn" @click="clickRefresh"></div>
          </div>

          <div class="scratch-content">
            <div class="prize-bg">
              <div class="prize-title">恭喜您获得</div>
              <div class="prize">
                <span>{{ scratch_gold }}</span
                >金币
              </div>
            </div>
            <canvas
              id="canvas"
              :class="{ hide: !is_scratching_status }"
              @touchmove="touchMove"
              @touchstart="touchStart"
              @touchend="touchEnd"
            ></canvas>
          </div>
        </div>
      </section>

      <section class="section section-5">
        <div class="section-content">
          <div class="desc">
            前30个集齐腊八祥瑞卡材料并合成最终奖励的玩家还将额外获得2888金币，奖励于活动结束后次日9时自动发放。
          </div>
          <div class="tips">注：榜单每24小时更新一次显示</div>
          <div class="rank-container">
            <div class="title">排行榜</div>
            <div class="thread">
              <div class="th">排名</div>
              <div class="th">用户名</div>
              <div class="th">合成时间</div>
            </div>
            <div class="tbody tbody-no" v-if="!rank_list.length">
              排名暂未更新~
            </div>
            <div class="tbody" v-else>
              <div class="tr" v-for="(item, index) in rank_list" :key="index">
                <div class="td">{{ index + 1 }}</div>
                <div class="td">{{ item.user?.nickname }}</div>
                <div class="td">{{ item.date }}</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section class="section section-6">
        <div class="section-content">
          <div class="p">
            1.活动期间充值完成后，请返回本活动页面参与活动抽卡并及时兑换奖励，活动结束后将清空所有抽卡次数和卡牌。
            <br /><br />
            2.温馨提示：由于游戏厂商的要求，部分游戏内充值暂不支持使用金币支付，请先确定需要用金币充值的游戏在不在可使用名单内。
            <br /><br />
            <span class="read" @click="game_dialog_popup = true">
              查看名单
            </span>
          </div>
        </div>
      </section>
    </div>

    <!-- 转盘结果弹窗 -->
    <van-popup
      v-model="lotteryResultPopup"
      :lock-scroll="false"
      :close-on-click-overlay="false"
      class="popup"
    >
      <div class="section section-1 section-popup">
        <div class="section-content">
          <div class="popup-title">恭喜你抽中</div>
          <div class="card-one">
            <img :src="lotteryResult.card_icon" alt="" />
          </div>
          <div class="bottom-operation">
            <template v-if="remain_lottery > 0">
              <div class="cancel" @click="lotteryResultPopup = false"></div>
              <div class="continue" @click="continueLottery"></div>
            </template>
            <div
              class="confirm"
              v-else
              @click="lotteryResultPopup = false"
            ></div>
          </div>
        </div>
      </div>
    </van-popup>

    <!-- 合成祥瑞卡结果弹窗 -->
    <van-popup
      v-model="composeResultPopup"
      :lock-scroll="false"
      :close-on-click-overlay="false"
      class="popup"
    >
      <div class="section section-1 section-popup">
        <div class="section-content">
          <div class="popup-title">恭喜获得腊八祥瑞卡</div>
          <div class="card-one">
            <img :src="composeResult.icon" alt="" />
          </div>
          <div class="popup-desc">
            腊八祥瑞卡奖励： {{ composeResult.desc }}
          </div>
          <div class="bottom-operation">
            <div class="confirm" @click="composeResultPopup = false"></div>
          </div>
        </div>
      </div>
    </van-popup>

    <!-- 未满足合成祥瑞卡的弹窗 -->
    <van-popup
      v-model="noComposePopup"
      :lock-scroll="false"
      :close-on-click-overlay="false"
      class="popup"
    >
      <div class="section section-1 section-popup">
        <div class="section-content">
          <div class="popup-desc">
            集齐大米+小米+糯米+高粱米+红豆+黄豆+绿豆+黑豆，即可合成腊八祥瑞卡。
          </div>
          <div class="popup-desc">奖励：888金币+7天SVIP+瓜分大奖机会1次</div>
          <div class="bottom-operation">
            <div class="confirm" @click="noComposePopup = false"></div>
          </div>
        </div>
      </div>
    </van-popup>

    <!-- 已兑换奖品 -->
    <van-popup
      v-model="hadExchangePopup"
      :lock-scroll="false"
      :close-on-click-overlay="false"
      class="popup"
    >
      <div class="popup-close" @click="closeRecordPopup"></div>
      <div class="section section-7 section-popup">
        <div class="section-content">
          <div class="section-empty" v-if="!exchange_log_list.length">
            暂无兑换奖品
          </div>
          <div class="exchange-log-list">
            <div
              class="log-item"
              v-for="(item, index) in exchange_log_list"
              :key="index"
            >
              <span class="left">{{ item.title }}</span>
              <span class="right">{{ item.date }}</span>
            </div>
          </div>
        </div>
      </div>
    </van-popup>

    <!-- 兑换提醒弹窗 -->
    <van-popup
      v-model="exchangeTipsPopup"
      :lock-scroll="false"
      :close-on-click-overlay="false"
      class="popup"
    >
      <div class="section section-1 section-popup">
        <div class="section-content">
          <div class="popup-title">兑换提醒</div>
          <div
            class="popup-desc"
            v-if="
              is_auspiciously == 2 || seleted_exchange_item.exchange_id == 1
            "
          >
            您确认消耗{{ need_lottery }}兑换{{ seleted_exchange_item.title }}？
          </div>
          <div class="popup-desc" v-else>
            您当前尚未合成腊八祥瑞卡，兑换后将可能导致无法合成，您是否确认消耗卡牌兑换？
          </div>
          <div class="bottom-operation">
            <div
              class="cancel-exchange"
              @click="exchangeTipsPopup = false"
            ></div>
            <div class="continue-exchange" @click="confirmExchange"></div>
          </div>
        </div>
      </div>
    </van-popup>

    <!-- 兑换成功弹窗 -->
    <van-popup
      v-model="exchangeResultPopup"
      :lock-scroll="false"
      :close-on-click-overlay="false"
      class="popup"
    >
      <div class="section section-1 section-popup">
        <div class="section-content">
          <div class="popup-title">兑换成功</div>

          <div class="exchange-result-icon">
            <img :src="exchangeResult.icon" alt="" />
          </div>
          <div class="exchange-result-text">
            恭喜您兑换成功，获得{{ exchangeResult.title }}
          </div>
          <div class="bottom-operation">
            <div class="confirm" @click="exchangeResultPopup = false"></div>
          </div>
        </div>
      </div>
    </van-popup>

    <!-- 自选卡弹窗 -->
    <van-popup
      v-model="chooseCardPopup"
      :lock-scroll="false"
      :close-on-click-overlay="false"
      class="popup"
    >
      <div class="section section-1 section-popup">
        <div class="section-content">
          <div class="popup-title">可任选一张，你想要的卡片</div>
          <div class="choose-list">
            <div
              class="choose-item"
              v-for="(item, index) in choose_lottery_list"
              :key="index"
              :class="{ on: item.lottery_id == choose_lottery_id }"
              @click="choose_lottery_id = item.lottery_id"
            >
              <img :src="item.card_icon" alt="" />
            </div>
          </div>
          <div class="bottom-operation">
            <div class="cancel-choose" @click="chooseCardPopup = false"></div>
            <div class="continue-choose" @click="continueChoose"></div>
          </div>
        </div>
      </div>
    </van-popup>

    <!-- 活动规则 -->
    <van-popup
      v-model="rulePopup"
      :lock-scroll="false"
      :close-on-click-overlay="false"
      class="popup"
    >
      <div class="popup-close" @click="rulePopup = false"></div>
      <div class="section section-8 section-popup">
        <div class="section-content">
          <div class="rule-content">
            <p>
              活动期间，参与收集腊八祥瑞卡材料即可兑换超值奖励，更有机会瓜分1888万金币红包以及获取超稀有卡牌-腊八祈福卡，祝您接下来的日子好运连连～
            </p>
            <div class="h">卡牌抽卡概率公示</div>
            <p>
              大米、小米、糯米、高粱米、红豆、黄豆、绿豆、黑豆 抽卡概率均为12%。
              <br />腊八祈福卡 抽卡概率为4%。
            </p>
            <div class="h">每日领取抽奖机会</div>
            <p>
              活动期间，完成对应的任务可领取抽奖机会。
              <br />温馨提示：仅限游戏内使用<span>微信/支付宝/平台币</span>充值，如有使用金币/绑定平台币/代金券抵扣的，则只计算抵扣后的现金充值金额。
            </p>
            <div class="h">合成腊八祥瑞卡</div>
            <p>
              活动期间，集齐大米+小米+糯米+高粱米+红豆+黄豆+绿豆+黑豆，可合成腊八祥瑞卡领取奖励（活动期间仅一次）：1888金币+7天SVIP+1次瓜分大奖机会
            </p>
            <div class="h">兑换好礼</div>
            <p>只要材料足够，每个礼物均可不限次数兑换。</p>
            <div class="h">瓜分千万红包</div>
            <p>
              合成腊八祥瑞卡的玩家可获得一次瓜分1888万金币红包的机会，金额随机。
            </p>
            <div class="h">天选之人</div>
            <p>
              前30个集齐腊八祥瑞卡材料并合成腊八祥瑞卡的玩家，还将额外获得2888金币，奖励于活动结束后的次日9时自动发放。
            </p>
          </div>
        </div>
      </div>
    </van-popup>

    <noGameList
      :game_dialog_show="game_dialog_popup"
      @changeGameDialogShow="changeGameDialogShow"
    />
    <svip-new-popup @success="getIndexData"></svip-new-popup>
  </div>
</template>

<script>
import { mapMutations } from 'vuex';
import { platform, boxInit } from '@/utils/box.uni.js';
import { BOX_login } from '@/utils/box.uni.js';
import { mapActions } from 'vuex';
import canvasImg from '@/assets/images/laba-activity/laba-scratch-bg-new.png';
import noGameList from '@/components/no-game-list';
import {
  ApiActivityJanuaryIndex,
  ApiActivityJanuaryExchange,
  ApiActivityJanuaryScratchingPrizes, // 刮奖
  ApiActivityJanuaryRefreshAuspiciously, //刷新刮奖次数
  ApiActivityJanuaryLotteryList,
  ApiActivityJanuaryAuspiciously, // 合成祥瑞卡
  ApiActivityJanuaryLottery,
  ApiActivityJanuaryTask,
  ApiActivityJanuaryExchangeLog,
  ApiActivityJanuarySvipInfo,
} from '@/api/views/laba_activity.js';
import svipNewPopup from '@/components/svip-new-popup';

export default {
  components: {
    svipNewPopup,
    noGameList,
  },
  data() {
    return {
      navbarOpacity: 0,

      lotteryResult: {
        lottery_id: 0,
      }, // 抽奖结果
      is_doing_lottery: false, // 是否正在抽奖

      exchange_log_list: [], // 兑换记录记录

      canvas: '', // 画布
      ctx: '', // 画笔
      scratchStatus: 0, //0=未开始 1=刮奖中 2=刮奖完
      clearCount: 0, // 刮奖区被刮开的程度 最大150就全解开
      ratio: 0,
      scratch_gold: 0, // 刮奖金币

      activity_status: 3, // 活动状态 1活动已开始 2 活动不存在 3活动未开始 4活动已结束
      exchange_list: [], // 兑换列表
      seleted_exchange_item: {}, // 选中的兑换项
      fake_list: [], // 广播数据列表
      is_auspiciously: 0, // 是否可以合成祥瑞卡 0 不可合成，1可以合成 2已合成
      is_scratching_prizes: 0, // 刮奖次数
      lottery_list: [], // 所有卡片列表
      my_card: [], // 我的卡片
      rank_list: [], // 排行榜列表
      remain_lottery: 0, // 剩余转盘次数
      task_list: [], // 任务列表
      choose_lottery_list: [], // 自主选择的卡片列表

      choose_lottery_id: 0, // 自选的卡片id
      composeResult: {}, // 合成祥瑞卡结果
      composeResultPopup: false, // 合成祥瑞卡结果弹窗
      noComposePopup: false, // 未满足合成祥瑞卡的弹窗
      chooseCardPopup: false, // 选择卡片弹窗
      lotteryResultPopup: false, // 转盘结果弹窗
      hadExchangePopup: false, // 已兑换列表弹窗
      rulePopup: false, // 规则弹窗
      exchangeTipsPopup: false, //  兑换提示弹窗
      exchangeResultPopup: false, // 兑换结果弹窗
      exchangeResult: {}, // 兑换结果

      game_dialog_popup: false,
      chooseTaskItem: {
        task_id: 4,
      },

      svipPopup: true,

      is_scratching_status: false, // 是否已经刮奖过
    };
  },
  computed: {
    activity_status_text() {
      const arr = [
        '活动已开始！',
        '活动不存在！',
        '活动未开始！',
        '活动已结束！',
      ];
      return arr[this.activity_status - 1];
    },
    need_lottery() {
      return this.seleted_exchange_item?.need_lottery
        ?.map(item => {
          return `${item.title}*${item.num}`;
        })
        .join('+');
    },
  },
  created() {
    window.addEventListener('scroll', this.handleScroll);
    document.addEventListener('visibilitychange', this.handleRefresh);
  },
  async mounted() {
    // 官包触发页面刷新（将onResume挂载在window上）
    if (platform == 'android') {
      window.onResume = this.onResume;
    }
    // 初始化canvas
    this.ratio = document.body.clientWidth / 375;
    this.canvas = document.getElementById('canvas');
    this.canvas.width = this.canvas.clientWidth;
    this.canvas.height = this.canvas.clientHeight;
    this.ctx = this.canvas.getContext('2d');
    this.initCanvas();
    // 页面数据初始化
    try {
      await this.init();
    } catch {}
    try {
      await this.handleRefresh();
    } catch {}
  },

  beforeDestroy() {
    // 清除滚动事件监听
    window.removeEventListener('scroll', this.handleScroll);
    document.addEventListener('visibilitychange', this.handleRefresh);
  },

  methods: {
    ...mapActions({
      SET_USER_INFO: 'user/SET_USER_INFO',
    }),
    handleScroll() {
      let scrollTop =
        document.documentElement.scrollTop ||
        window.pageYOffset ||
        document.body.scrollTop;
      if (Math.abs(scrollTop) > 10) {
        this.navbarOpacity = Math.abs(scrollTop) / 120;
      } else {
        this.navbarOpacity = 0;
      }
    },
    async onResume() {
      await boxInit();
      await this.SET_USER_INFO(true);
      await this.init();
    },
    async init() {
      await this.getIndexData();
      await this.loadCardList();
    },
    // 转盘逻辑
    async handleLottery() {
      if (this.activity_status != 1) {
        this.$toast(this.activity_status_text);
        return false;
      }
      if (!this.userInfo.token) {
        this.$toast('请登录');
        this.login();
        return false;
      }
      if (this.is_doing_lottery) {
        return false;
      }
      this.is_doing_lottery = true;
      this.lotteryResult.lottery_id = 0;
      this.$toast.loading('加载中...');
      try {
        const res = await ApiActivityJanuaryLottery();
        this.$toast.clear();
        this.lotteryResult = res.data.lottery_info;
        setTimeout(async () => {
          this.is_doing_lottery = false;
          await this.getIndexData();
          this.lotteryResultPopup = true;
        }, 5000);
      } catch (e) {
        this.is_doing_lottery = false;
        await this.getIndexData();
      }
    },
    async continueLottery() {
      this.lotteryResultPopup = false;
      await this.handleLottery();
    },
    // 点击刷新按钮
    async clickRefresh() {
      this.$toast.loading('刷新中...');
      await this.handleRefresh();
      this.$toast('刷新成功');
    },
    // 刷新逻辑
    async handleRefresh() {
      if (document.visibilityState === 'hidden') {
        return false;
      }
      const res = await ApiActivityJanuaryRefreshAuspiciously();
      this.is_scratching_prizes = res.data.count;
      this.scratch_gold = res.data.amount;
      this.scratchStatus = 0;
      this.clearCount = 0;
      this.initCanvas();
    },
    // 获取首页数据
    async getIndexData() {
      const res = await ApiActivityJanuaryIndex();
      this.activity_status = res.data.activity_status;
      this.exchange_list = res.data.exchange_list;
      this.fake_list = res.data.fake_list;
      this.is_auspiciously = res.data.is_auspiciously;
      this.is_scratching_prizes = res.data.is_scratching_prizes;
      this.lottery_list = res.data.lottery_list;
      this.my_card = res.data.my_card;
      this.rank_list = res.data.rank_list;
      this.remain_lottery = res.data.remain_lottery;
      this.task_list = res.data.task_list;
      this.is_scratching_status = res.data.is_scratching_status;
    },
    async handleCompose() {
      if (this.is_auspiciously == 0) {
        this.noComposePopup = true;
        return false;
      }
      try {
        this.$toast.loading('加载中');
        const res = await ApiActivityJanuaryAuspiciously();
        this.composeResult = res.data.info;
        this.composeResultPopup = true;
      } finally {
        this.$toast.clear();
        await this.getIndexData();
        await this.handleRefresh();
      }
    },
    async getExchangeLog() {
      const res = await ApiActivityJanuaryExchangeLog();
      this.exchange_log_list = res.data.list;
    },
    // 选择兑换的选项，弹出确认弹窗

    clickExchangeItem(item) {
      if (this.activity_status != 1) {
        this.$toast(this.activity_status_text);
        return false;
      }
      if (!this.userInfo.token) {
        this.$toast('请登录');
        this.login();
        return false;
      }
      if (item.status != 1) {
        this.$toast('您的材料不足，可完成任务抽卡获得~');
        this.$refs.taskPosition.scrollIntoView({ behavior: 'smooth' });
        return false;
      }
      this.seleted_exchange_item = item;
      this.exchangeTipsPopup = true;
    },
    async confirmExchange() {
      await this.handleExchange();
    },
    // 好礼兑换逻辑
    async handleExchange() {
      this.$toast.loading({
        message: '加载中',
        duration: 0,
      });
      try {
        const params = {
          exchange_id: this.seleted_exchange_item.exchange_id,
        };
        this.exchangeTipsPopup = false;
        const res = await ApiActivityJanuaryExchange(params);
        this.exchangeResult = res.data.exchange_info;
        this.exchangeResultPopup = true;
      } finally {
        this.$toast.clear();
        await this.getIndexData();
      }
    },
    // 点击领取抽奖机会
    clickTaskReward(item) {
      if (this.activity_status != 1) {
        this.$toast(this.activity_status_text);
        return false;
      }
      if (!this.userInfo.token) {
        this.$toast('请登录');
        this.login();
        return false;
      }
      if (item.status == 0) {
        this.$toast('您暂无可领取机会');
        return false;
      }
      if (item.is_select == 1) {
        // 先打开弹窗自主选择卡片
        this.chooseCardPopup = true;
        this.chooseTaskItem = item;
        return;
      }
      this.handleTakeTaskReward(item);
    },
    // 领取任务奖励(抽卡机会)
    async handleTakeTaskReward(item) {
      try {
        this.$toast.loading({
          message: '加载中',
          duration: 0,
        });
        let params = {
          task_id: item.task_id,
        };
        if (item.is_select == 1) {
          params.lottery_id = this.choose_lottery_id;
        }
        const res = await ApiActivityJanuaryTask(params);
      } finally {
        this.choose_lottery_id = 0;
        await this.getIndexData();
      }
    },
    async continueChoose() {
      if (!this.choose_lottery_id) {
        this.$toast('请选择一张卡片');
        return;
      }
      this.chooseCardPopup = false;
      await this.handleTakeTaskReward(this.chooseTaskItem);
    },
    // 获取卡片列表(不包含祈福卡)
    async loadCardList() {
      const res = await ApiActivityJanuaryLotteryList();
      this.choose_lottery_list = res.data.lottery_list;
    },
    // 登录
    login() {
      BOX_login();
    },
    async openRecordPopup() {
      if (this.activity_status == 2 || this.activity_status == 3) {
        this.$toast(this.activity_status_text);
        return false;
      }
      if (!this.userInfo.token) {
        this.$toast('请登录');
        this.login();
        return false;
      }
      this.hadExchangePopup = true;
      await this.getExchangeLog();
    },
    // 关闭记录列表
    closeRecordPopup() {
      this.hadExchangePopup = false;
    },

    async initCanvas() {
      this.ctx.globalCompositeOperation = 'source-over';
      let image = new Image();
      image.src = canvasImg;
      image.onload = async () => {
        await this.ctx.drawImage(
          image,
          0,
          0,
          this.canvas.width,
          this.canvas.height,
        );
      };
    },
    touchStart(e) {
      if (this.activity_status != 1) {
        this.$toast(this.activity_status_text);
        return false;
      }
      if (!this.userInfo.token) {
        this.login();
        return false;
      }
      switch (this.scratchStatus) {
        case 0: // 未开始
          if (this.is_scratching_prizes == 0) {
            this.$toast('暂无刮奖次数');
            return;
          } else {
            // 开始刮奖
            this.scratchStatus = 1;
          }
          break;
        case 1: // 刮奖中
          break;
        case 2: // 已结束
          break;
      }
    },
    touchMove(e) {
      if (this.scratchStatus == 1) {
        let x = e.touches[0].clientX - this.canvas.getBoundingClientRect().left,
          y = e.touches[0].clientY - this.canvas.getBoundingClientRect().top;
        e.preventDefault();
        this.ctx.clearRect(x, y, 15 * this.ratio, 15 * this.ratio);
        this.clearCount++;
      }
    },
    async touchEnd(e) {
      // if (this.scratchStatus == 1) {
      //   // 优化该地方的判断，计算已经刮过的区域占整个区域的百分比 是否大于20%，
      //   // 如果是，则结束刮奖，否则，则继续刮
      //   if (this.getFilledPercentage() > 0.2) {
      //     // 当手指累计滑动超过80时触发清空矩形画布, 数值越大要刮得越久
      //     this.scratchStatus = 2;
      //     this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
      //     await this.handleScratch();
      //   }
      // }

      if (this.scratchStatus == 1) {
        if (this.clearCount > 60) {
          // 当手指累计滑动超过80时触发清空矩形画布, 数值越大要刮得越久
          this.scratchStatus = 2;
          this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
          await this.handleScratch();
        }
      }
    },
    // 刮奖逻辑
    async handleScratch() {
      try {
        const res = await ApiActivityJanuaryScratchingPrizes();
      } finally {
        setTimeout(async () => {
          await this.getIndexData();
        }, 2000);
      }
    },
    // 打开svip充值弹窗
    openSvipPopup() {
      if (!this.userInfo.token) {
        this.login();
        return false;
      }
      if (this.activity_status != 1) {
        this.$toast(this.activity_status_text);
        return false;
      }
      this.setShowSvipRechargePopup(true);
    },
    changeGameDialogShow(show) {
      this.game_dialog_popup = show;
    },
    ...mapMutations({
      setShowSvipRechargePopup: 'recharge/setShowSvipRechargePopup',
    }),
  },
};
</script>

<style lang="less" scoped>
.laba-activity {
  width: 100%;
  min-height: 100vh;
  background: url(~@/assets/images/laba-activity/bg-part.png) center center
    repeat;
  background-size: 118 * @rem 48 * @rem;
  padding-bottom: 38 * @rem;
  .back {
    width: 28 * @rem;
    height: 28 * @rem;
    margin-left: 20 * @rem;
    border-radius: 15 * @rem;
    background: rgba(0, 0, 0, 0.3) url(~@/assets/images/nav-bar-back-white.png)
      center center no-repeat;
    background-size: 8 * @rem 14 * @rem;
  }
  .main {
    .top-section {
      width: 100%;
      height: 515 * @rem;
      background: url(~@/assets/images/laba-activity/laba-top-bg.png) center
        center no-repeat;
      background-size: 100% 515 * @rem;
      .top-line {
        display: flex;
        box-sizing: border-box;
        padding: 0 15 * @rem 0 0;
        width: 100%;
        height: 50 * @rem;
        position: fixed;
        z-index: 999;
        top: @safeAreaTop;
        top: @safeAreaTopEnv;
        left: 0 * @rem;
        display: flex;
        align-items: center;
        .user-container {
          box-sizing: border-box;
          height: 30 * @rem;
          margin-left: 11 * @rem;
          color: #fff;
          line-height: 28 * @rem;
          background: rgba(40, 40, 40, 0.23);
          border: 1px solid rgba(255, 255, 255, 0.29);
          border-radius: 15 * @rem;
          overflow: hidden;
          .user {
            display: flex;
            .nickname {
              box-sizing: border-box;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              padding: 0 15 * @rem 0 10 * @rem;
              text-align: center;
              line-height: 28 * @rem;
            }
          }
          .avatar {
            width: 28 * @rem;
            height: 28 * @rem;
            border-radius: 50%;
          }
        }
      }
      .rule-btn {
        box-sizing: border-box;
        width: 28 * @rem;
        height: 80 * @rem;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-wrap: wrap;
        background: rgba(133, 0, 0, 0.26);
        font-size: 13 * @rem;
        color: #ffffff;
        padding: 4 * @rem;
        border-radius: 6 * @rem 0 0 6 * @rem;
        position: absolute;
        right: 0;
        top: 92 * @rem;
        text-align: center;
      }
    }
  }
  .section {
    box-sizing: border-box;
    width: 355 * @rem;
    margin: 0 auto;
    &::before {
      content: '';
      position: relative;
      display: block;
      width: 355 * @rem;
      height: 65 * @rem;
      background: url(~@/assets/images/laba-activity/section-bg-top-1.png)
        center top no-repeat;
      background-size: 355 * @rem 65 * @rem;
    }
    &::after {
      content: '';
      position: relative;
      display: block;
      width: 355 * @rem;
      height: 44 * @rem;
      background: url(~@/assets/images/laba-activity/section-bg-bottom.png)
        center bottom no-repeat;
      background-size: 355 * @rem 44 * @rem;
      z-index: 1;
    }
    .section-content {
      background: url(~@/assets/images/laba-activity/section-bg-center.png)
        center center repeat;
      background-size: 355 * @rem 29 * @rem;
      position: relative;
      z-index: 5;
      padding: 0.5 * @rem 16 * @rem;
      margin-top: -1 * @rem;
      margin-bottom: -1 * @rem;
    }
    &.section-1 {
      .section-content {
        padding: 0.5 * @rem 0;
        margin-bottom: -16 * @rem;
        .broadcast {
          box-sizing: border-box;
          width: 294 * @rem;
          height: 22 * @rem;
          border: 1 * @rem solid rgba(255, 39, 39, 0.05);
          border-radius: 11 * @rem;
          background-color: #fff0e1;
          position: relative;
          z-index: 2;
          display: flex;
          align-items: center;
          margin: -29 * @rem auto 0;
          .broadcast-icon {
            width: 14 * @rem;
            height: 13 * @rem;
            background: url(~@/assets/images/laba-activity/broadcast-icon.png)
              center center no-repeat;
            background-size: 14 * @rem 13 * @rem;
            margin-left: 10 * @rem;
          }
          .broadcast-swiper {
            flex: 1;
            height: 22 * @rem;
            margin: 0 4 * @rem;
            .broadcast-item {
              height: 22 * @rem;
              line-height: 22 * @rem;
              font-size: 12 * @rem;
              color: #ba3918;
            }
          }
        }
        .lottery-container {
          position: relative;
          width: 328 * @rem;
          height: 340 * @rem;
          background: url(~@/assets/images/laba-activity/lottery-bg-new.png)
            center center no-repeat;
          background-size: 328 * @rem 340 * @rem;
          margin: 11 * @rem auto 0;
          overflow: hidden;
          .lottery-list {
            width: 320 * @rem;
            height: 320 * @rem;
            margin: 15 * @rem auto 0;
            position: relative;

            &.rotate0 {
              transform: rotate(0deg);
            }
            &.rotate1 {
              transform: rotate(-1840deg);
              transition: all 5s ease;
            }
            &.rotate2 {
              transform: rotate(-1880deg);
              transition: all 5s ease;
            }
            &.rotate3 {
              transform: rotate(-1920deg);
              transition: all 5s ease;
            }
            &.rotate4 {
              transform: rotate(-1960deg);
              transition: all 5s ease;
            }
            &.rotate5 {
              transform: rotate(-2000deg);
              transition: all 5s ease;
            }
            &.rotate6 {
              transform: rotate(-2040deg);
              transition: all 5s ease;
            }
            &.rotate7 {
              transform: rotate(-3880deg);
              transition: all 5s ease;
            }
            &.rotate8 {
              transform: rotate(-3920deg);
              transition: all 5s ease;
            }
            &.rotate9 {
              transform: rotate(-3960deg);
              transition: all 5s ease;
            }
            .lottery-item {
              width: 320 * @rem;
              height: 320 * @rem;
              transform: rotateZ(var(--deg));
              position: absolute;
              left: 0;
              top: 0;
              img {
                width: 73 * @rem;
                height: 73 * @rem;
                margin: 0 auto;
              }
            }
          }
          .lottery-btn {
            width: 68 * @rem;
            height: 68 * @rem;
            position: absolute;
            top: 146 * @rem;
            left: 50%;
            transform: translateX(-50%);
            .lottery-num {
              text-align: center;
              font-size: 14 * @rem;
              color: #ffea7d;
              font-weight: 600;
              padding-top: 43 * @rem;
              line-height: 20 * @rem;
            }
          }
        }

        .my-card-container {
          width: 327 * @rem;
          height: 161 * @rem;
          margin: 17 * @rem auto 0;
          background: url(~@/assets/images/laba-activity/my-card-bg.png) center
            top no-repeat;
          background-size: 327 * @rem 161 * @rem;
          .title-bar {
            box-sizing: border-box;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 27 * @rem 8 * @rem 8 * @rem;
            .title {
              font-size: 14 * @rem;
              color: #ba3918;
              line-height: 16 * @rem;
              font-weight: 600;
            }
            .gift-record {
              font-size: 12 * @rem;
              color: #ba3918;
              line-height: 14 * @rem;
              padding-right: 10 * @rem;
              background: url(~@/assets/images/laba-activity/right-icon.png)
                right center no-repeat;
              background-size: 8 * @rem 8 * @rem;
            }
          }
          .card-list {
            box-sizing: border-box;
            padding: 10 * @rem 10 * @rem 0;
            display: flex;
            height: 110 * @rem;
            align-items: flex-start;
            overflow: auto;
            .card-item {
              flex-shrink: 0;
              height: 110 * @rem;
              height: 83 * @rem;
              position: relative;
              &:not(:first-of-type) {
                margin-left: 10 * @rem;
              }
              .card-icon {
                width: 68 * @rem;
                height: 83 * @rem;
              }
              .card-num {
                width: 48 * @rem;
                height: 13 * @rem;
                position: absolute;
                left: 50%;
                bottom: -4 * @rem;
                transform: translateX(-50%);
                background-color: #ff5f2c;
                line-height: 13 * @rem;
                font-size: 11 * @rem;
                color: #ffffff;
                text-align: center;
                border-radius: 10 * @rem;
              }
            }
          }
          .card-no {
            height: 110 * @rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12 * @rem;
            color: #ba3918;
            font-weight: 500;
          }
        }

        .compose-btn {
          width: 179 * @rem;
          height: 53 * @rem;
          background: url(~@/assets/images/laba-activity/compose-btn.png) center
            top no-repeat;
          background-size: 179 * @rem 53 * @rem;
          margin: 21 * @rem auto 0;
          &.no {
            background-image: url(~@/assets/images/laba-activity/compose-btn-no.png);
          }
          &.had {
            background-image: url(~@/assets/images/laba-activity/compose-btn-had.png);
          }
        }
        .big-price-tips {
          font-size: 11 * @rem;
          color: #ba3918;
          text-align: center;
          line-height: 13 * @rem;
          margin: 13 * @rem auto 0;
          white-space: nowrap;
          overflow: hidden;
        }
      }
    }
    &.section-2 {
      margin: 36 * @rem auto 0;
      &::before {
        width: 355 * @rem;
        height: 65 * @rem;
        background-image: url(~@/assets/images/laba-activity/section-bg-top-2.png);
        background-size: 355 * @rem 65 * @rem;
      }
      .section-content {
        .task-list {
          padding-top: 3 * @rem;
          .task-item {
            box-sizing: border-box;
            width: 321 * @rem;
            height: 67 * @rem;
            border-radius: 13 * @rem;
            background-color: #ffe5c8;
            margin: 0 auto;
            display: flex;
            align-items: center;
            padding: 0 11 * @rem;
            &:not(:first-of-type) {
              margin-top: 14 * @rem;
            }
            .left-content {
              flex: 1;
              min-width: 0;
              .task-title {
                font-size: 13 * @rem;
                color: #d84e00;
                font-weight: 600;
                white-space: nowrap;
              }
              .task-desc {
                font-size: 11 * @rem;
                color: #e55200;
                line-height: 13 * @rem;
                margin-top: 7 * @rem;
              }
            }
            .task-btn {
              width: 57 * @rem;
              height: 27 * @rem;
              background: url(~@/assets/images/laba-activity/task-btn.png)
                center center no-repeat;
              background-size: 57 * @rem 27 * @rem;
              margin-left: 10 * @rem;
              &.no {
                background-image: url(~@/assets/images/laba-activity/task-btn-no.png);
              }
              &.had {
                background-image: url(~@/assets/images/laba-activity/task-btn-had.png);
              }
              &.svip {
                background-image: url(~@/assets/images/laba-activity/task-btn-svip.png);
              }
            }
          }
        }
        .task-tips {
          font-size: 11 * @rem;
          color: #ba3918;
          line-height: 14 * @rem;
          margin: 16 * @rem auto -20 * @rem;
          padding: 0 18 * @rem;
          span {
            color: #ff2424;
          }
        }
      }
    }
    &.section-3 {
      margin: 36 * @rem auto 0;
      &::before {
        width: 355 * @rem;
        height: 65 * @rem;
        background-image: url(~@/assets/images/laba-activity/section-bg-top-3.png);
        background-size: 355 * @rem 65 * @rem;
      }
      .section-content {
        margin-bottom: -20 * @rem;
        .exchange-list {
          padding: 0 4 * @rem;
          .exchange-item {
            display: flex;
            padding: 15 * @rem 0 5 * @rem;
            &:not(:first-of-type) {
              border-top: 1 * @rem solid #ffe2c0;
            }
            .prize {
              width: 59 * @rem;
              .prize-icon {
                width: 59 * @rem;
                height: 51 * @rem;
              }
              .prize-title {
                font-size: 11 * @rem;
                font-weight: 600;
                line-height: 11 * @rem;
                color: #992b05;
                text-align: center;
                margin-top: 3 * @rem;
              }
            }
            .material {
              display: flex;
              flex: 1;
              min-width: 0;
              margin-left: 12 * @rem;
              .material-item {
                width: 39 * @rem;
                &:not(:first-of-type) {
                  margin-left: 5 * @rem;
                }
                .material-icon {
                  width: 39 * @rem;
                  height: 46 * @rem;
                }
                .material-num {
                  width: 30 * @rem;
                  height: 12 * @rem;
                  border-radius: 6 * @rem;
                  background-color: #ff5f2c;
                  line-height: 12 * @rem;
                  text-align: center;
                  font-size: 10 * @rem;
                  color: #ffffff;
                  margin: 5 * @rem auto 0;
                }
              }
            }
            .exchange-btn {
              width: 57 * @rem;
              height: 27 * @rem;
              margin-top: 13 * @rem;
              background: url(~@/assets/images/laba-activity/laba-exchange-btn.png)
                center center no-repeat;
              background-size: 57 * @rem 27 * @rem;
              &.no {
                background-image: url(~@/assets/images/laba-activity/exchange-btn-no.png);
              }
            }
          }
        }
      }
    }
    &.section-4 {
      margin: 36 * @rem auto 0;
      &::before {
        width: 355 * @rem;
        height: 65 * @rem;
        background-image: url(~@/assets/images/laba-activity/section-bg-top-4.png);
        background-size: 355 * @rem 65 * @rem;
      }
      .section-content {
        margin-bottom: -14 * @rem;
        .desc {
          padding: 0 18 * @rem;
          line-height: 16 * @rem;
          color: #ba3918;
          font-size: 12 * @rem;
        }
        .data-bar {
          padding: 0 18 * @rem;
          display: flex;
          align-items: center;
          margin-top: 7 * @rem;
          .num {
            font-size: 14 * @rem;
            font-weight: 600;
            color: #ba3918;
            line-height: 18 * @rem;
          }
          .refresh-btn {
            width: 59 * @rem;
            height: 27 * @rem;
            background: url(~@/assets/images/laba-activity/laba-refresh-btn.png)
              left center no-repeat;
            background-size: 59 * @rem 27 * @rem;
            margin-left: 10 * @rem;
          }
        }
        .scratch-content {
          width: 313 * @rem;
          height: 123 * @rem;
          position: relative;
          overflow: hidden;
          margin: 18 * @rem auto 0;
          .prize-bg {
            width: 313 * @rem;
            height: 123 * @rem;
            border-radius: 8 * @rem;
            background: url(~@/assets/images/laba-activity/laba-scratch-amount-bg.png)
              center center no-repeat;
            background-size: 313 * @rem 123 * @rem;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
          }
          .prize-title {
            font-size: 14 * @rem;
            color: #ffffff;
            text-align: center;
          }
          .prize {
            font-size: 20 * @rem;
            letter-spacing: 3 * @rem;
            text-align: center;
            color: #ffffff;
            font-weight: bold;
            line-height: 34 * @rem;
            margin-top: 4 * @rem;
            span {
              font-size: 34 * @rem;
              font-weight: bold;
            }
          }
          #canvas {
            width: 100%;
            height: 100%;
            position: absolute;
            top: 0;
            left: 0;
            &.hide {
              visibility: hidden;
            }
          }
        }
      }
    }
    &.section-5 {
      margin: 36 * @rem auto 0;
      &::before {
        width: 355 * @rem;
        height: 65 * @rem;
        background-image: url(~@/assets/images/laba-activity/section-bg-top-5.png);
        background-size: 355 * @rem 65 * @rem;
      }
      .section-content {
        box-sizing: border-box;
        width: 355 * @rem;
        padding: 0.5 * @rem 16 * @rem;
        margin-bottom: -15 * @rem;
        .desc {
          font-size: 12 * @rem;
          color: #ba3918;
          line-height: 16 * @rem;
        }
        .tips {
          font-size: 11 * @rem;
          color: rgba(186, 57, 24, 0.7);
          line-height: 14 * @rem;
          margin-top: 4 * @rem;
        }
        .rank-container {
          background-color: #fff1e1;
          border-radius: 13 * @rem;
          width: 321 * @rem;
          margin: 14 * @rem auto 0;
          overflow: hidden;
          .title {
            height: 41 * @rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16 * @rem;
            color: #ba3918;
            font-weight: 600;
            background-color: #ffe5c8;
          }
          .thread {
            height: 50 * @rem;
            line-height: 50 * @rem;
            display: flex;
            align-items: center;
            .th {
              font-size: 13 * @rem;
              color: #ba3918;
              text-align: center;
              font-weight: 500;
              &:first-of-type {
                width: 56 * @rem;
              }
              &:nth-of-type(2) {
                flex: 1;
                min-width: 0;
              }
              &:nth-of-type(3) {
                width: 120 * @rem;
              }
            }
          }
          .tbody {
            max-height: calc(48 * 7 * @rem);
            overflow: auto;
            min-height: calc(48 * 3 * @rem);
            &.tbody-no {
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 14 * @rem;
              color: #ba3918;
              background: rgba(255, 183, 98, 0.09);
            }
          }
          .tr {
            height: 48 * @rem;
            line-height: 48 * @rem;
            display: flex;
            align-items: center;
            &:nth-of-type(2n + 1) {
              background: rgba(255, 183, 98, 0.09);
            }
            .td {
              height: 48 * @rem;
              font-size: 13 * @rem;
              color: #ba3918;
              text-align: center;
              font-weight: 500;
              &:first-of-type {
                width: 56 * @rem;
              }
              &:nth-of-type(2) {
                flex: 1;
                min-width: 0;
              }
              &:nth-of-type(3) {
                width: 120 * @rem;
              }
            }
          }
        }
      }
    }
    &.section-6 {
      margin: 36 * @rem auto 0;
      &::before {
        width: 355 * @rem;
        height: 65 * @rem;
        background-image: url(~@/assets/images/laba-activity/section-bg-top-6.png);
        background-size: 355 * @rem 65 * @rem;
      }
      .section-content {
        margin-bottom: -15 * @rem;
        padding: 0.5 * @rem 18 * @rem;
        padding-top: 5 * @rem;
        .p {
          line-height: 16 * @rem;
          font-size: 12 * @rem;
          color: #ba3918;
          span {
            font-size: 13 * @rem;
            color: #ba3918;
            font-weight: 600;
            display: block;
            width: fit-content;
            padding-right: 10 * @rem;
            background: url(~@/assets/images/laba-activity/right-icon.png) right
              center no-repeat;
            background-size: 8 * @rem 8 * @rem;
          }
        }
      }
    }
    &.section-popup {
      .section-content {
        margin-top: -20 * @rem;
        margin-bottom: -15 * @rem;
        .popup-title {
          font-size: 16 * @rem;
          color: #ba3918;
          text-align: center;
          line-height: 22 * @rem;
        }
        .card-one {
          width: 98 * @rem;
          height: 119 * @rem;
          margin: 20 * @rem auto 0;
        }
        .exchange-result-icon {
          width: 119 * @rem;
          height: auto;
          margin: 10 * @rem auto 0;
        }
        .exchange-result-text {
          text-align: center;
          font-size: 16 * @rem;
          color: #ba3918;
          margin: 10 * @rem auto 0;
        }
        .popup-desc {
          margin: 16 * @rem auto 0;
          font-size: 16 * @rem;
          text-align: center;
          color: #ba3918;
          padding: 0 35 * @rem;
        }
        .choose-list {
          display: flex;
          flex-wrap: wrap;
          justify-content: center;
          margin: 11 * @rem auto 0;
          .choose-item {
            box-sizing: border-box;
            width: 78 * @rem;
            height: 93 * @rem;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 15 * @rem;
            opacity: 0.5;
            border: 1 * @rem solid transparent;
            transition: 0.2s;
            &.on {
              border: 1 * @rem solid #fe3131;
              opacity: 1;
            }
            img {
              display: block;
              width: 68 * @rem;
              height: 83 * @rem;
            }
          }
        }
        .bottom-operation {
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 20 * @rem auto 0;
          .cancel {
            width: 96 * @rem;
            height: 39 * @rem;
            margin: 0 25 * @rem;
            background: url(~@/assets/images/laba-activity/lottery-cancel.png)
              center center no-repeat;
            background-size: 96 * @rem 39 * @rem;
          }
          .continue {
            width: 96 * @rem;
            height: 39 * @rem;
            margin: 0 25 * @rem;
            background: url(~@/assets/images/laba-activity/lottery-continue.png)
              center center no-repeat;
            background-size: 96 * @rem 39 * @rem;
          }
          .confirm {
            width: 120 * @rem;
            height: 39 * @rem;
            margin: 0 25 * @rem;
            background: url(~@/assets/images/laba-activity/lottery-confirm.png)
              center center no-repeat;
            background-size: 120 * @rem 39 * @rem;
          }
          .cancel-exchange {
            width: 96 * @rem;
            height: 39 * @rem;
            margin: 0 25 * @rem;
            background: url(~@/assets/images/laba-activity/exchange-cancel.png)
              center center no-repeat;
            background-size: 96 * @rem 39 * @rem;
          }
          .continue-exchange {
            width: 96 * @rem;
            height: 39 * @rem;
            margin: 0 25 * @rem;
            background: url(~@/assets/images/laba-activity/exchange-continue.png)
              center center no-repeat;
            background-size: 96 * @rem 39 * @rem;
          }
          .cancel-choose {
            width: 96 * @rem;
            height: 39 * @rem;
            margin: 0 25 * @rem;
            background: url(~@/assets/images/laba-activity/choose-cancel.png)
              center center no-repeat;
            background-size: 96 * @rem 39 * @rem;
          }
          .continue-choose {
            width: 96 * @rem;
            height: 39 * @rem;
            margin: 0 25 * @rem;
            background: url(~@/assets/images/laba-activity/choose-continue.png)
              center center no-repeat;
            background-size: 96 * @rem 39 * @rem;
          }
        }
      }
      &.section-7 {
        &::before {
          width: 355 * @rem;
          height: 65 * @rem;
          background-image: url(~@/assets/images/laba-activity/section-bg-top-7.png);
          background-size: 355 * @rem 65 * @rem;
        }
        .section-content {
          margin-top: -1 * @rem;
          margin-bottom: -15 * @rem;
          height: 350 * @rem;
          .section-empty {
            height: 350 * @rem;
            display: flex;
            justify-content: center;
            align-items: center;
            text-align: center;
            font-size: 16 * @rem;
            color: #ba3918;
            line-height: 22 * @rem;
          }
          .exchange-log-list {
            height: 350 * @rem;
            overflow: auto;
            .log-item {
              display: flex;
              justify-content: space-between;
              padding: 0 12 * @rem;
              &:first-of-type {
                margin-top: 15 * @rem;
              }
              &:not(:first-of-type) {
                margin-top: 18 * @rem;
              }
              span {
                font-size: 14 * @rem;
                color: #ba3918;
                line-height: 22 * @rem;
                &.left {
                  flex: 1;
                  min-width: 0;
                  text-align: left;
                }
                &.right {
                  display: block;
                  width: 120 * @rem;
                  text-align: right;
                }
              }
            }
          }
        }
      }
      &.section-8 {
        &::before {
          width: 355 * @rem;
          height: 65 * @rem;
          background-image: url(~@/assets/images/laba-activity/section-bg-top-8.png);
          background-size: 355 * @rem 65 * @rem;
        }

        .section-content {
          margin-top: -1 * @rem;
          margin-bottom: -1 * @rem;
          overflow: hidden;
          .rule-content {
            padding: 0 4 * @rem;
            .h {
              font-size: 15 * @rem;
              line-height: 20 * @rem;
              margin-top: 16 * @rem;
              color: #ba3918;
              font-weight: 600;
            }
            p {
              font-size: 12 * @rem;
              line-height: 16 * @rem;
              margin-top: 3 * @rem;
              color: rgba(186, 57, 24, 0.7);
              span {
                font-size: 12 * @rem;
                font-weight: 500;
                color: #fc002d;
                text-decoration: underline;
              }
            }
          }
        }
      }
    }
  }
}
.popup {
  box-sizing: border-box;
  background: transparent;
  overflow: visible;
  .popup-close {
    position: absolute;
    z-index: 4;
    right: 12 * @rem;
    top: 7 * @rem;
    width: 30 * @rem;
    height: 30 * @rem;
    background: url(~@/assets/images/laba-activity/laba-popup-close.png) center
      center no-repeat;
    background-size: 30 * @rem 30 * @rem;
  }
}
</style>
