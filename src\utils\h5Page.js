import { envFun } from '@/utils/function';
import { BOX_getProtocolKey } from '@/utils/box.uni.js';
let protocolKey = BOX_getProtocolKey();

let h5Page = {};
// 去function.js修改本地测试环境
let env = envFun();

const DOMAIN = 'a3733.com';

const api_url = `https://${env}api.${DOMAIN}`; //默认接口域名
const api_url2 = `https://${env}u.${DOMAIN}`; //金币那堆的接口域名

h5Page.api_url = api_url;
h5Page.api_url2 = api_url2;

// 无交互类h5的URL
h5Page.daijinquanshuoming = `https://h5.a3733.com/h5/html/couponExplain`;
h5Page.shiyongzhinan = `https://h5.a3733.com/h5/html/useGuide`;
h5Page.mianzeshengming = `https://h5.a3733.com/html/disclaimer/c/${protocolKey}`;
h5Page.yonghuxieyi = `https://h5.a3733.com/html/agreement/c/${protocolKey}`;
h5Page.yinsizhengce = `https://h5.a3733.com/html/privacy/c/${protocolKey}`;
h5Page.fanlizhinan = `https://u.a3733.com/float.php/float/box/rebate_guide.html`;
h5Page.jiaoyixuzhi = `https://${env}h5.a3733.com/h5/html/tradeexplain`;
h5Page.zhuanyoushuoming = `https://h5.a3733.com/h5/zhuanyou/zyShow`;
h5Page.cloudGameQA = `https://m.3733.com/appnews/233272.html`;
h5Page.grqQA = `https://m.3733.com/appnews/234215.html`;
h5Page.qyqQA = `https://m.3733.com/appnews/233275.html`;
h5Page.h5GameQA = `https://m.3733.com/appnews/233273.html`;
h5Page.h5GameDownloadQA = `https://m.3733.com/appnews/311956.html`;
h5Page.upAgreement = `https://${env}api.a3733.com/h5/apk/upAgreement`;
h5Page.hejiguanli = `https://${env}api.a3733.com/html/hjgf`;

h5Page.env = env;

export default h5Page;
