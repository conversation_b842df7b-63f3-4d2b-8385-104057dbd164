<template>
  <div class="lucky-value-task-gage">
    <nav-bar-2
      ref="topNavBar"
      title="使用微信红包封面的步骤"
      :azShow="true"
      :placeholder="false"
    >
    </nav-bar-2>
    <div class="main">
      <div class="step-list">
        <div class="step-item">
          <div class="num">1</div>
          <div class="step-card">
            <div class="title">复制序列号</div>
            <div class="desc">
              <div class="msg">序列号 {{ serialNumber }}</div>
              <div class="copy" v-if="serialNumber" @click="clickCopy">
                点击复制
              </div>
            </div>
          </div>
        </div>
        <div class="step-item">
          <div class="num">2</div>
          <div class="step-card">
            <div class="title">点击微信红包</div>
            <div class="desc">
              <div class="msg">打开微信APP，点击聊天框内的红包按钮</div>
            </div>
            <div class="img">
              <img src="@/assets/images/25newyear/hb-step-icon1.png" alt="" />
            </div>
          </div>
        </div>
        <div class="step-item">
          <div class="num">3</div>
          <div class="step-card">
            <div class="title">点击红包封面</div>
            <div class="desc">
              <div class="msg">进入红包，点击红包封面</div>
            </div>
            <div class="img">
              <img src="@/assets/images/25newyear/hb-step-icon2.png" alt="" />
            </div>
          </div>
        </div>
        <div class="step-item">
          <div class="num">4</div>
          <div class="step-card">
            <div class="title">点击领封面</div>
            <div class="desc">
              <div class="msg">进入红包封面，点击领封面</div>
            </div>
            <div class="img">
              <img src="@/assets/images/25newyear/hb-step-icon3.png" alt="" />
            </div>
          </div>
        </div>
        <div class="step-item">
          <div class="num">5</div>
          <div class="step-card">
            <div class="title">点击兑换</div>
            <div class="desc">
              <div class="msg">进入领封面界面，点击兑换</div>
            </div>
            <div class="img">
              <img src="@/assets/images/25newyear/hb-step-icon4.png" alt="" />
            </div>
          </div>
        </div>
        <div class="step-item">
          <div class="num">6</div>
          <div class="step-card">
            <div class="title">输入序列号</div>
            <div class="desc">
              <div class="msg">将复制的序列号粘贴在输入框</div>
            </div>
            <div class="img">
              <img src="@/assets/images/25newyear/hb-step-icon5.png" alt="" />
            </div>
          </div>
        </div>
        <div class="step-item">
          <div class="num">7</div>
          <div class="step-card">
            <div class="title">点击添加</div>
            <div class="desc">
              <div class="msg">点击添加即可领取成功</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="tips">已经到底了</div>
  </div>
</template>

<script>
import { mapGetters, mapMutations, mapActions } from 'vuex';
import { platform, boxInit, BOX_login, iframeCopy } from '@/utils/box.uni.js';
import { ApiDoingsChunjieCover } from '@/api/views/25_new_year_activity.js';
export default {
  name: 'HbCoverStep',
  props: {},
  components: {},
  data() {
    return {};
  },
  async mounted() {
    // 官包触发页面刷新（将onResume挂载在window上）
    if (platform == 'android') {
      window.onResume = this.onResume;
    }
    document.body.addEventListener('touchstart', function () {});
  },
  beforeDestroy() {
    document.body.removeEventListener('touchstart', function () {});
    if (this.timeClock) {
      clearInterval(this.timeClock);
      this.timeClock = null;
    }
  },
  async activated() {
    await this.getInitData();
  },
  methods: {
    ...mapMutations({
      setSerialNumber: 'activity/setSerialNumber',
    }),
    ...mapActions({
      SET_USER_INFO: 'user/SET_USER_INFO',
    }),
    async onResume() {
      await boxInit();
      await this.SET_USER_INFO(true);
      await this.getInitData();
    },
    login() {
      BOX_login();
    },
    // 初始化信息
    async getInitData() {
      const res = await ApiDoingsChunjieCover();
      if (res.data && res.code == 3) {
        this.setSerialNumber(res.data.xlh);
      } else {
        this.setSerialNumber('');
      }
    },
    clickCopy() {
      if (platform == 'android') {
        this.$copyText(`${this.serialNumber}`).then(
          async res => {
            this.$toast('复制成功');
          },
          err => {
            this.$dialog.alert({
              message: '复制失败',
              lockScroll: false,
            });
          },
        );
      } else {
        iframeCopy(`${this.serialNumber}`, '复制成功');
      }
    },
  },
  computed: {
    ...mapGetters({
      serialNumber: 'activity/serialNumber',
    }),
  },
};
</script>

<style lang="less" scoped>
.lucky-value-task-gage {
  font-family: PingFang SC, PingFang SC;
  display: flex;
  flex-direction: column;
  width: 100%;
  overflow: hidden;
  min-height: 100vh;
  background: #ffffff;
  .main {
    width: 100%;
    position: relative;
    height: 100vh;
    flex: 1;
    min-height: 0;
    padding-top: @safeAreaTop;
    padding-top: @safeAreaTopEnv;
    margin: 68 * @rem 27 * @rem 0;
    // background: url(~@/assets/images/25newyear/25newyear-bg3.png) center top
    //   no-repeat;
    // background-size: 100% 200 * @rem;
    .step-list {
      .step-item {
        position: relative;
        display: flex;
        &:not(:last-child) {
          &::before {
            content: '';
            position: absolute;
            left: 10 * @rem;
            width: 0;
            height: 100%;
            border-left: 1 * @rem dashed #eea7a7;
            z-index: 1;
          }
        }

        .num {
          z-index: 9;
          width: 20 * @rem;
          height: 20 * @rem;
          background: #f65f5f;
          border-radius: 50%;
          overflow: hidden;
          font-weight: 500;
          font-size: 13 * @rem;
          color: #ffffff;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .step-card {
          flex: 1;
          min-width: 0;
          margin-left: 10 * @rem;
          display: flex;
          flex-direction: column;
          margin-bottom: 24 * @rem;
          .title {
            height: 18 * @rem;
            font-weight: bold;
            font-size: 14 * @rem;
            color: #333333;
            line-height: 18 * @rem;
          }
          .desc {
            margin-top: 6 * @rem;
            display: flex;
            align-items: center;
            .msg {
              user-select: text;
              height: 16 * @rem;
              font-weight: 400;
              font-size: 13 * @rem;
              color: #999999;
              line-height: 16 * @rem;
            }
            .copy {
              margin-left: 5 * @rem;
              font-weight: 600;
              font-size: 13 * @rem;
              color: #666666;
            }
          }
          .img {
            margin-top: 10 * @rem;
            width: 280 * @rem;
          }
        }
      }
    }
  }
  .tips {
    margin: 29 * @rem auto 29 * @rem;
    height: 12 * @rem;
    font-weight: 500;
    font-size: 12 * @rem;
    color: #999999;
    line-height: 12 * @rem;
    text-align: center;
  }
}
</style>
