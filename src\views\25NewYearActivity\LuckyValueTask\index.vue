<template>
  <div class="lucky-value-task-gage">
    <nav-bar-2
      ref="topNavBar"
      :bgStyle="bgStyle"
      title="领福气值中心"
      :azShow="true"
      :placeholder="false"
      :bgColor="`rgba(255,255,255,${navbarOpacity}`"
      :style="{ color: `rgba(0, 0, 0, ${navbarOpacity})` }"
    >
    </nav-bar-2>
    <!-- <div class="loading-content" v-if="!loadSuccess">
      <van-loading vertical>加载中...</van-loading>
    </div> -->
    <div class="main" v-if="TaskInfoList.length">
      <!-- 每日任务 -->
      <div class="task-box" v-if="TaskInfoList.length">
        <div class="task-title">每日任务</div>
        <template v-for="item in TaskInfoList">
          <div class="task-info-item" :key="item.id">
            <div class="left-box">
              <div class="task-icon">
                <img v-show="item.icon" :src="item.icon" alt="" />
              </div>
              <div class="task-info">
                <div class="signIn">
                  <div class="name">{{ item.title }}</div>
                </div>
                <div class="describe" v-if="item.task_id !== 5">
                  {{ item.reward }}
                </div>
                <div v-else class="describe">
                  在充值金额基础上<span>加赠</span>福气值x45
                </div>
              </div>
            </div>
            <div class="right-btn" :class="{ receive: item.status == 3 }">
              <div
                v-if="item.status == 0 && item.behavior_type == 2"
                @click="handleTask(item)"
              >
                签到
              </div>
              <div
                @click="handleTask(item)"
                v-if="
                  (item.status == 0 && item.behavior_type == 5) ||
                  (item.status == 1 && item.behavior_type == 5)
                "
              >
                去分享
              </div>
              <div
                @click="handleTask(item)"
                v-if="
                  (item.status == 0 &&
                    [3, 8, 16, 17].includes(item.behavior_type)) ||
                  (item.status == 1 &&
                    [3, 8, 16, 17].includes(item.behavior_type))
                "
              >
                去完成
              </div>
              <div
                @click="handleTask(item)"
                v-if="
                  (item.status == 0 && [11, 23].includes(item.behavior_type)) ||
                  (item.status == 1 && [11, 23].includes(item.behavior_type))
                "
              >
                去充值
              </div>
              <div
                @click="handleTask(item)"
                v-if="
                  (item.status == 0 && [18].includes(item.behavior_type)) ||
                  (item.status == 1 && [18].includes(item.behavior_type))
                "
              >
                去邀请
              </div>
              <div
                v-if="item.status == 2"
                @click="handleTaskPrize(item.task_id)"
              >
                领取
              </div>
              <div v-if="item.status == 3">已完成</div>
            </div>
          </div>
        </template>
      </div>
      <!-- 其他任务 -->
      <div class="task-box task-box1" v-if="otherTasks.length">
        <div class="task-title">其他任务</div>
        <template v-for="item in otherTasks">
          <div class="task-info-item" :key="item.id">
            <div class="left-box">
              <div class="task-icon">
                <img v-show="item.icon" :src="item.icon" alt="" />
              </div>
              <div class="task-info">
                <div class="signIn">
                  <div class="name">{{ item.title }}</div>
                </div>
                <div class="describe">{{ item.reward }}</div>
              </div>
            </div>
            <div class="right-btn" :class="{ receive: item.status == 3 }">
              <div
                v-if="item.status == 0 && item.behavior_type == 2"
                @click="handleTask(item)"
              >
                签到
              </div>
              <div
                @click="handleTask(item)"
                v-if="
                  (item.status == 0 && item.behavior_type == 5) ||
                  (item.status == 1 && item.behavior_type == 5)
                "
              >
                去分享
              </div>
              <div
                @click="handleTask(item)"
                v-if="
                  (item.status == 0 &&
                    [3, 8, 16, 17].includes(item.behavior_type)) ||
                  (item.status == 1 &&
                    [3, 8, 16, 17].includes(item.behavior_type))
                "
              >
                去完成
              </div>
              <div
                @click="handleTask(item)"
                v-if="
                  (item.status == 0 && [11, 23].includes(item.behavior_type)) ||
                  (item.status == 1 && [11, 23].includes(item.behavior_type))
                "
              >
                去充值
              </div>
              <div
                @click="handleTask(item)"
                v-if="
                  (item.status == 0 && [18].includes(item.behavior_type)) ||
                  (item.status == 1 && [18].includes(item.behavior_type))
                "
              >
                去邀请
              </div>
              <div
                v-if="item.status == 2"
                @click="handleTaskPrize(item.task_id)"
              >
                领取
              </div>
              <div v-if="item.status == 3">已完成</div>
            </div>
          </div>
        </template>
      </div>
      <!-- 任务提示弹窗 -->
      <van-dialog
        v-model="taskPopup"
        :show-confirm-button="false"
        :lock-scroll="false"
        class="lucky-bag-open-dialog"
      >
        <div class="dialog-content">
          <div class="close-icon" @click="closePopup()"></div>
          <div class="title">任务提示</div>
          <div class="msg" v-if="is18YuanShow">
            使用微信支付/支付宝支付充值满 30
            元，或累计充值平台币、开通SVIP会员满 30 元！
          </div>
          <div class="msg" v-else>
            活动期间站内充值实付金额1比1得福气值，即充值10元得10福气值，充值100元得100福气值！
          </div>
          <div class="btn-close btn" @click="goToPage(1)">充值平台币</div>
          <div class="btn-open btn" @click="goToPage(2)">开通SVIP会员</div>
        </div>
      </van-dialog>

      <!-- 下载游戏弹窗 -->
      <download-game-popup :show.sync="downloadGamePopup"></download-game-popup>
      <!-- 体验游戏弹窗 -->
      <experience-game-popup
        :show.sync="experienceGamePopup"
      ></experience-game-popup>
      <!-- 体验指定游戏弹窗 -->
      <experience-appoint-game-popup
        :show.sync="experienceAppointGamePopup"
      ></experience-appoint-game-popup>
    </div>
  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex';
import {
  platform,
  boxInit,
  BOX_openInNewWindow,
  BOX_login,
  iframeCopy,
  BOX_close,
} from '@/utils/box.uni.js';
import {
  ApiDoingsChunjieTaskList,
  ApiDoingsChunjieTask,
  ApiDoingsChunjieTaskPrize,
} from '@/api/views/25_new_year_activity.js';
import { ApiCommonShareInfo } from '@/api/views/system.js';
import { LOCAL_HOST } from '@/utils/constants.js';
import { envFun } from '@/utils/function.js';

export default {
  name: 'LuckyValueTask',

  props: {},
  components: {
    downloadGamePopup: () => import('../components/download-game-popup.vue'),
    experienceGamePopup: () =>
      import('../components/experience-game-popup.vue'),
    experienceAppointGamePopup: () =>
      import('../components/experience-appoint-game-popup.vue'),
  },
  data() {
    return {
      TaskInfoList: [],
      otherTasks: [],
      taskPopup: false, //任务提示弹窗
      loadSuccess: false, //加载完毕
      downloadGamePopup: false,
      experienceGamePopup: false,
      experienceAppointGamePopup: false,
      operationLoading: false,
      is18YuanShow: true,
      shareInfo: {}, //分享信息
      bgStyle: 'transparent-white',
      navbarOpacity: 0,
    };
  },
  async mounted() {
    // 官包触发页面刷新（将onResume挂载在window上）
    if (platform == 'android') {
      window.onResume = this.onResume;
    }
    document.body.addEventListener('touchstart', function () {});
  },
  beforeDestroy() {
    window.removeEventListener('scroll', this.handleScroll);
    document.body.removeEventListener('touchstart', function () {});
    if (this.timeClock) {
      clearInterval(this.timeClock);
      this.timeClock = null;
    }
  },
  async created() {
    window.addEventListener('scroll', this.handleScroll);
  },
  async activated() {
    await this.resetPopup();
    await this.getInitData();
  },
  methods: {
    ...mapActions({
      SET_USER_INFO: 'user/SET_USER_INFO',
    }),
    async onResume() {
      await boxInit();
      await this.SET_USER_INFO(true);
      await this.getInitData();
    },
    resetPopup() {
      this.taskPopup = false;
      this.downloadGamePopup = false;
      this.experienceGamePopup = false;
      this.experienceAppointGamePopup = false;
    },
    handleScroll() {
      let scrollTop =
        document.documentElement.scrollTop ||
        window.pageYOffset ||
        document.body.scrollTop;
      if (Math.abs(scrollTop) > 10) {
        this.navbarOpacity = Math.abs(scrollTop) / 50;
        this.bgStyle = 'transparent';
      } else {
        this.navbarOpacity = 0;
        this.bgStyle = 'transparent-white';
      }
    },
    login() {
      BOX_login();
    },
    // 初始化信息
    async getInitData() {
      try {
        const res = await ApiDoingsChunjieTaskList();
        this.TaskInfoList = res.data.dailyTasks;
        this.otherTasks = res.data.otherTasks;
      } catch (error) {
      } finally {
        this.loadSuccess = true;
      }
    },
    // 接任务
    async handleTask(item) {
      try {
        if (!item.status) {
          await ApiDoingsChunjieTask({
            task_id: item.task_id,
          });
          this.$nextTick(() => {
            this.getInitData();
          });
        }
        switch (item.behavior_type) {
          case 2: //签到任务
            break;
          case 3: //下载并登录新游戏
            this.downloadGamePopup = true;
            break;
          case 5: //分享任务
            this.handleShare(1);
            break;
          case 8: //体验游戏30分钟
            this.experienceGamePopup = true;
            break;
          case 11:
            //18现金累充
            this.is18YuanShow = true;
            this.taskPopup = true;
            break;
          case 16: //体验指定游戏30分钟
            this.experienceAppointGamePopup = true;
            break;
          case 17: //关注微信公众号
            this.goToWeChat();
            break;
          case 18: //邀请好友
            this.handleShare(2);
            break;
          case 23: //1比1充值
            this.is18YuanShow = false;
            this.taskPopup = true;
            break;

          default:
            break;
        }
      } catch (error) {}
    },
    // 领取任务奖励
    async handleTaskPrize(task_id) {
      try {
        await ApiDoingsChunjieTaskPrize({ task_id });
      } catch (error) {
      } finally {
        this.$nextTick(() => {
          setTimeout(() => {
            this.getInitData();
          }, 200);
        });
      }
    },
    closePopup() {
      this.taskPopup = false;
    },
    selectFromIframeUrl() {
      let url = '';
      if (process.env.NODE_ENV === 'development') {
        url = `${LOCAL_HOST}/#/25_new_year_activity/lucky_value_task`;
      } else {
        url = `https://${envFun()}activity.3733.com/#/25_new_year_activity/lucky_value_task`;
      }
      parent.postMessage(
        {
          from: 'activity',
          type: 'selectUrl',
          data: { url },
        },
        '*',
      );
    },
    goToPage(num) {
      this.taskPopup = false;
      this.$nextTick(() => {
        // this.selectFromIframeUrl();
        if (num === 1) {
          BOX_openInNewWindow(
            { name: 'PlatformCoin' },
            { url: `https://${envFun()}game.3733.com/#/platform_coin` },
          );
        } else if (num === 2) {
          BOX_openInNewWindow(
            { name: 'Svip' },
            { url: `https://${envFun()}game.3733.com/#/svip` },
          );
        }
      });
    },
    goToWeChat() {
      // this.selectFromIframeUrl();
      BOX_openInNewWindow(
        { name: 'BindWeChat' },
        { url: `https://${envFun()}game.3733.com/#/bind_we_chat` },
      );
    },
    // 获取分享信息
    // 当is_share为1时，表示上报分享成功
    async getShareInfo(is_share = 0) {
      let params = {
        type: 11,
        id: this.userInfo.user_id ? this.userInfo.user_id : 1,
      };
      if (is_share) {
        params.is_share = 1;
      }
      const res = await ApiCommonShareInfo(params);
      if (!is_share) {
        this.shareInfo = res.data;
      }
    },
    async handleShare(type) {
      // if ([2, 3, 4].includes(this.activity_status)) {
      //   this.$toast(this.activity_status_text);
      //   return false;
      // }

      await this.getShareInfo();
      if (this.initData?.share_info?.length) {
        if (this.operationLoading) {
          return false;
        }
        this.operationLoading = true;
        setTimeout(() => {
          this.operationLoading = false;
        }, 1000);
        if (type == 1) {
          window.BOX.mobShare(11, this.userInfo.user_id);
        } else {
          if (platform == 'android') {
            this.$copyText(
              `${this.shareInfo.share_text}${this.shareInfo.url}`,
            ).then(
              async res => {
                this.$toast('链接已复制到剪贴板，快去邀请好友吧~');
              },
              err => {
                this.$dialog.alert({
                  message: '复制失败',
                  lockScroll: false,
                });
              },
            );
          }
        }
      } else {
        // web的iframe安全策略导致无法复制，故需使用postMessage转移至父级窗口中复制
        await this.getShareInfo(1);
        iframeCopy(this.shareInfo.share_text + this.shareInfo.url);
      }
      setTimeout(async () => {
        await this.getInitData();
      }, 1000);
    },
  },
  computed: {
    ...mapGetters({
      initData: 'system/initData',
      userInfo: 'user/userInfo',
    }),
  },
};
</script>

<style lang="less" scoped>
.lucky-value-task-gage {
  font-family: PingFang SC, PingFang SC;
  display: flex;
  flex-direction: column;
  width: 100%;
  overflow: hidden;
  min-height: 100vh;
  background: #f6f7f8;
  .back {
    width: 30 * @rem;
    height: 50 * @rem;
    background: url(~@/assets/images/nav-bar-back-white.png) center center
      no-repeat;
    background-size: 10 * @rem 18 * @rem;
    &.black {
      width: 30 * @rem;
      height: 50 * @rem;
      background: url(~@/assets/images/nav-bar-back-black.png) center center
        no-repeat;
      background-size: 10 * @rem 18 * @rem;
    }
  }
  .main {
    width: 100%;
    position: relative;
    height: 100vh;
    flex: 1;
    min-height: 0;
    background: url(~@/assets/images/25newyear/25newyear-bg3.png) top center
      no-repeat;
    background-size: 100% 200 * @rem;
    .task-box {
      margin-top: 106 * @rem;
      padding-top: @safeAreaTop;
      padding-top: @safeAreaTopEnv;
      padding: 16 * @rem 16 * @rem 33 * @rem 16 * @rem;
      background: #ffffff;
      border-radius: 16 * @rem 16 * @rem 0 0;
      .task-title {
        height: 22 * @rem;
        font-weight: bold;
        font-size: 16 * @rem;
        color: #311717;
      }
      .task-info-item {
        display: flex;
        align-items: center;
        justify-content: space-between;

        &:nth-of-type(2) {
          margin-top: 12 * @rem;
        }
        .left-box {
          flex: 1;
          min-width: 0;
          display: flex;
          align-items: center;
          width: 250 * @rem;
          height: 40 * @rem;
          flex-shrink: 0;
          margin-right: 12 * @rem;
          .task-icon {
            width: 40 * @rem;
            height: 40 * @rem;
            border-radius: 12 * @rem;
            overflow: hidden;
            background: #faf3f0;
          }
          .task-info {
            margin-left: 8 * @rem;
            height: 100%;
            flex: 1;
            min-width: 0;
            display: flex;
            flex-direction: column;
            justify-content: space-evenly;
            .signIn {
              display: flex;
              align-items: center;
              .name {
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
                height: 20 * @rem;
                font-weight: bold;
                font-size: 14 * @rem;
                color: #5a2a2a;
                line-height: 20 * @rem;
              }
              .time-icon {
                margin-left: 4 * @rem;
                display: flex;
                align-items: center;
                flex-shrink: 0;
                .icon {
                  width: 16 * @rem;
                  height: 16 * @rem;
                }
              }
            }
            .describe {
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
              margin-top: 3 * @rem;
              height: 14 * @rem;
              font-weight: 400;
              font-size: 12 * @rem;
              color: #a17272;
              line-height: 14 * @rem;
              span {
                font-weight: bold;
              }
            }
          }
        }
        .right-btn {
          min-width: 72 * @rem;
          height: 32 * @rem;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #fff;
          border-radius: 20 * @rem;
          background: linear-gradient(90deg, #f64b4b 0%, #ffb07c 100%), #d9d9d9;
          font-size: 13 * @rem;
          font-weight: normal;
          white-space: nowrap;
          div {
            padding: 0 8 * @rem;
          }
          &.receive {
            background: #f1e8e4;
            color: #ffffff;
          }
        }

        &:not(:last-child) {
          margin-bottom: 24 * @rem;
        }
      }
      &.task-box1 {
        margin-top: 10 * @rem;
        border-radius: 0;
      }
    }
  }
  .lucky-bag-open-dialog {
    width: 300 * @rem;
    background: transparent;
    overflow: visible;
    .dialog-content {
      box-sizing: border-box;
      position: relative;
      border-radius: 16 * @rem;
      z-index: 2;
      padding: 28 * @rem 29 * @rem 0;
      width: 300 * @rem;
      // height: 230 * @rem;
      // background: url(~@/assets/images/25newyear/25newyear-bg1.png) top center
      //   no-repeat;
      background-color: #fff;
      text-align: center;
      display: flex;
      flex-direction: column;
      align-items: center;
      .close-icon {
        position: absolute;
        top: 16 * @rem;
        right: 16 * @rem;
        width: 13 * @rem;
        height: 13 * @rem;
        background: url(~@/assets/images/25newyear/btn-close.png) center top
          no-repeat;
        background-size: contain;
      }
      .title {
        font-family: 'Dream Han Sans CN', 'Dream Han Sans CN';
        font-weight: bold;
        font-size: 20 * @rem;
        height: 18 * @rem;
        line-height: 18 * @rem;
        text-align: center;
        font-style: normal;
        text-transform: none;
        color: #5a2a2a;
      }

      .msg {
        // min-height: 77 * @rem;
        width: 100%;
        font-weight: 400;
        font-size: 16 * @rem;
        color: #7a5252;
        line-height: 23 * @rem;
        text-align: left;
        font-style: normal;
        text-transform: none;
        margin: 22 * @rem 0 14 * @rem 0;
      }
      .btn-close,
      .btn-open {
        width: 238 * @rem;
        height: 40 * @rem;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 18 * @rem;
        border-radius: 20 * @rem;
        font-family: PingFang SC, PingFang SC;
        font-weight: 500;
        font-size: 15 * @rem;
        color: #ffffff;
        font-style: normal;
        text-transform: none;
      }
      .btn-close {
        background: linear-gradient(90deg, #f64b4b 0%, #ffb07c 100%), #d9d9d9;
      }
      .btn-open {
        background: linear-gradient(90deg, #8a60e3 0%, #ffbdde 100%), #d9d9d9;
      }
    }
  }
  .loading-content {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100vh;
  }
}
</style>
