<template>
  <div>
    <van-popup
      v-model="popup"
      :close-on-click-overlay="true"
      position="bottom"
      round
      class="ptb-recharge-popup"
      :lock-scroll="false"
    >
      <div class="close" @click="setShowPtbRechargePopup(false)"></div>
      <div class="popup-title">充值平台币</div>
      <div class="popup-title-tips">（{{ text_list.gold_welfare }}）</div>
      <div class="select-list">
        <div
          class="select-item"
          v-for="(item, index) in selectList"
          :class="{
            on: selectMoney == item.money && item.first != false,
            cant: item.first == false,
          }"
          :key="index"
          @click="changeMoney(item)"
        >
          <div class="money">
            <span>{{ item.money }}</span
            >{{ item.money_unit }}
          </div>
          <div class="coin-num">{{ item.date }}{{ item.date_unit }}</div>
          <div class="is-recommend" v-if="item.is_only">首充</div>
          <div class="is-recommend blue" v-if="item.is_recommend == 1">
            推荐
          </div>
        </div>
      </div>
      <!-- 金额输入框 -->
      <div class="input-container">
        <input
          type="number"
          class="text-input"
          :placeholder="placeholder"
          v-model="selectMoney"
        />
        <span class="text-right">({{ isHw ? '美元' : '元' }})</span>
      </div>
      <div class="pay-way-line" @click="payWayPopup = true">
        <div class="pay-way-title">支付方式</div>
        <div class="pay-way-current">
          <i
            class="icon"
            :style="{ backgroundImage: `url(${payWayItem.icon})` }"
          ></i>
          <span class="text">{{ payWayItem.name }}</span>
          <div class="right-icon"></div>
        </div>
      </div>
      <!-- <div class="recharge btn" @click="handlePay">
        支付<span>{{ totalMoney }}</span
        >元
      </div> -->
      <div class="recharge btn" @click="handlePay"></div>

      <bottom-safe-area></bottom-safe-area>
    </van-popup>

    <van-popup
      v-model="payWayPopup"
      :close-on-click-overlay="true"
      position="bottom"
      round
      class="ptb-recharge-popup"
      :lock-scroll="false"
    >
      <div class="close" @click="payWayPopup = false"></div>
      <div class="popup-title pay-way-popup-title">选择支付方式</div>
      <ul class="pay-way-list">
        <li
          class="pay-way-item"
          :class="{ on: payWayItem.key == item.key }"
          v-for="(item, index) in payWayList"
          :key="index"
          @click="handleSelectPayWay(item)"
        >
          <i class="icon" :style="{ backgroundImage: `url(${item.icon})` }"></i>
          <span class="text">{{ item.name }}</span>
          <div class="select-icon"></div>
        </li>
      </ul>
      <bottom-safe-area></bottom-safe-area>
    </van-popup>
  </div>
</template>
<script>
import { mapMutations, mapGetters } from 'vuex';
import {
  ApiCreateOrderPtb,
  ApiGetPayUrl,
  ApiPlatformGetInfo,
  ApiGetOrderStatus,
} from '@/api/views/recharge.js';
export default {
  data() {
    let that = this;
    return {
      payWay: '', // 支付方式
      selectMoney: 0, // 准备充值的金额
      maxMoney: 0, // 最大充值金额
      selectList: [], // 充值金额列表
      payWayList: [], // 支付方式

      payWayPopup: false, // 支付方式弹窗

      payWayItem: {}, // 支付方式

      text_list: {},
    };
  },
  computed: {
    popup: {
      get() {
        return this.showPtbRechargePopup;
      },
      set(value) {
        this.setShowPtbRechargePopup(value);
      },
    },
    // 总金额
    totalMoney() {
      return Number(this.selectMoney) || 0;
    },
    // 输入金额提示语
    placeholder() {
      return `请输入充值金额${this.isHw ? '10美元' : '30元'}起`;
    },
    ...mapGetters({
      showPtbRechargePopup: 'recharge/showPtbRechargePopup',
    }),
  },
  watch: {
    selectMoney() {
      if (Math.floor(this.selectMoney) !== Number(this.selectMoney)) {
        this.$toast.fail('请输入整数');
        this.selectMoney = Math.floor(this.selectMoney);
      }
    },
  },
  async created() {
    await this.getPlatformInfo();
  },
  methods: {
    ...mapMutations({
      setShowPtbRechargePopup: 'recharge/setShowPtbRechargePopup',
    }),
    handleSelectPayWay(item) {
      this.payWayItem = item;
      this.payWayPopup = false;
    },

    changeMoney(item) {
      if (item.first == false) {
        this.$toast('仅限首次充值');
        return false;
      }
      this.selectMoney = item.money;
    },
    handlePay() {
      this.setShowPtbRechargePopup(false);
      ApiCreateOrderPtb({
        isNew: 1,
        money: this.selectMoney,
        payWay: this.payWayItem.key,
      }).then(orderRes => {
        ApiGetPayUrl({
          orderId: orderRes.data.orderId,
          orderType: 102,
          payWay: this.payWayItem.key,
          packageName: '',
        }).finally(() => {
          this.$emit('success');
          ApiGetOrderStatus({
            order_id: orderRes.data.orderId,
            order_type: 102,
          })
            .then(res2 => {
              // if (res2.code == 1) {
              //   this.$point("充值平台币", "充值", "成功");
              // } else {
              //   this.$point("充值平台币", "充值", "失败");
              // }
            })
            .catch(() => {
              // this.$point("充值平台币", "充值", "失败");
            });
        });
      });
    },
    async getPlatformInfo() {
      const res = await ApiPlatformGetInfo();
      let { payWayList, platiconList, text_list } = res.data;
      this.selectList = platiconList;
      this.payWayList = payWayList;
      this.payWayItem = this.payWayList[0];
      this.selectMoney = platiconList.find(item => {
        return item.is_recommend == 1;
      }).money;

      this.text_list = text_list;
    },
  },
};
</script>
<style lang="less" scoped>
.ptb-recharge-popup {
  box-sizing: border-box;
  width: 100%;
  padding: 0 23 * @rem;
  .close {
    position: absolute;
    right: 9 * @rem;
    top: 9 * @rem;
    width: 22 * @rem;
    height: 22 * @rem;
    .image-bg('~@/assets/images/recharge/recharge-popup-close.png');
  }
  .popup-title {
    font-size: 18 * @rem;
    line-height: 23 * @rem;
    font-weight: bold;
    color: #333333;
    text-align: center;
    margin-top: 19 * @rem;
  }
  .popup-title-tips {
    font-size: 12 * @rem;
    color: #999999;
    line-height: 15 * @rem;
    text-align: center;
  }
  .select-list {
    display: flex;
    flex-wrap: wrap;
    margin-top: 20 * @rem;
    .select-item {
      box-sizing: border-box;
      position: relative;
      width: 98 * @rem;
      height: 74 * @rem;
      border-radius: 8 * @rem;
      border: 1 * @rem solid #e7e7e7;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      margin-left: 16 * @rem;
      margin-top: 10 * @rem;
      &:nth-of-type(3n + 1) {
        margin-left: 0 * @rem;
      }

      .is-recommend {
        position: absolute;
        left: -1 * @rem;
        top: -1 * @rem;
        width: 30 * @rem;
        height: 18 * @rem;
        background: @themeBg;
        border-radius: 6 * @rem 0 * @rem 6 * @rem 0 * @rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 10 * @rem;
        color: #ffffff;
        font-weight: 500;
        &.blue {
          background: #48b2ff;
        }
      }
      .coin-num {
        font-size: 11 * @rem;
        color: #333333;
        font-weight: 500;
        line-height: 14 * @rem;
        margin-top: 7 * @rem;
      }
      .money {
        font-size: 10 * @rem;
        color: #999999;
        margin-top: 4 * @rem;
        span {
          font-size: 22 * @rem;
          font-weight: 600;
          color: #333333;
          margin-right: 2 * @rem;
        }
      }
      &.on {
        .image-bg('~@/assets/images/recharge/ptb-selected.png');
        border-color: transparent;

        background-color: #fff7f2;
        .coin-num,
        .money,
        .money span {
          color: #fd6a33;
        }
      }
      &.cant {
        opacity: 0.6;
        border: 1 * @rem solid #e7e7e7;
        background: unset;
        .coin-num,
        .money,
        .money span {
          color: #333;
        }
      }
    }
  }
  .input-container {
    margin: 16 * @rem 0 0;
    border-radius: 20 * @rem;
    height: 40 * @rem;
    background-color: #f4f4f4;
    display: flex;
    align-items: center;
    padding: 0 14 * @rem;
    .text-input {
      flex: 1;
      display: block;
      height: 100%;
      background-color: transparent;
      font-size: 16 * @rem;
      color: #333333;
      font-weight: bold;
      &::-webkit-input-placeholder {
        color: #999999;
        font-size: 14px;
        font-weight: normal;
      }
    }
    .text-right {
      font-size: 14 * @rem;
      color: #333333;
      font-weight: bold;
    }
  }
  .pay-way-line {
    box-sizing: border-box;
    padding: 0 12 * @rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 52 * @rem;
    border-radius: 6 * @rem;
    background: #ffffff;
    margin: 19 * @rem auto 0;
    border: 1 * @rem solid #e1e1e1;
    .pay-way-title {
      font-size: 16 * @rem;
      color: #333333;
      font-weight: 600;
    }
    .pay-way-current {
      display: flex;
      align-items: center;
      .icon {
        display: block;
        width: 24 * @rem;
        height: 24 * @rem;
        background-position: center center;
        background-repeat: no-repeat;
        background-size: 20 * @rem 20 * @rem;
      }
      .text {
        margin-left: 6 * @rem;
        font-size: 14 * @rem;
        color: #000000;
      }
      .right-icon {
        margin-left: 17 * @rem;
        width: 9 * @rem;
        height: 15 * @rem;
        background: url(~@/assets/images/spring-activity/spring-right-icon.png)
          center center no-repeat;
        background-size: 9 * @rem 15 * @rem;
      }
    }
  }
  .payway-title {
    font-size: 16 * @rem;
    color: #333333;
    font-weight: 600;
    line-height: 20 * @rem;
    padding: 0 23 * @rem;
    margin-top: 20 * @rem;
  }
  .pay-way-list {
    overflow: hidden;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    margin-top: 30 * @rem;
    padding: 0 0 60 * @rem;
  }
  .pay-way-item {
    box-sizing: border-box;
    width: 100%;
    height: 52 * @rem;
    padding-top: 10 * @rem;
    text-align: center;
    border: 0.5 * @rem solid #e1e1e1;
    border-radius: 6 * @rem;
    font-size: 0;
    display: flex;
    align-items: center;
    padding: 0 12 * @rem;
    &:not(:first-of-type) {
      margin-top: 10 * @rem;
    }
    &.on {
      border-color: #ffd5ae;
      border-width: 1 * @rem;
    }
    .icon {
      display: block;
      width: 24 * @rem;
      height: 24 * @rem;
      background-repeat: no-repeat;
      background-size: 24 * @rem 24 * @rem;
    }
    .text {
      display: block;
      font-size: 14 * @rem;
      font-weight: 400;
      color: #333;
      flex: 1;
      min-width: 0;
      margin-left: 6 * @rem;
      text-align: left;
    }
    .select-icon {
      width: 16 * @rem;
      height: 16 * @rem;
      background: url(~@/assets/images/recharge/pay-no.png) no-repeat;
      background-size: 16 * @rem 16 * @rem;
    }
    &.on .select-icon {
      background-image: url(~@/assets/images/recharge/pay-yes.png);
    }
  }
  .recharge {
    width: 254 * @rem;
    height: 48 * @rem;
    .image-bg('~@/assets/images/spring-activity/spring-recharge-btn.png');
    margin: 23 * @rem auto;
    font-size: 18 * @rem;
    font-weight: bold;
    color: #ffffff;
    line-height: 21 * @rem;
    display: flex;
    align-items: center;
    justify-content: center;
    span {
      font-weight: bold;
    }
  }
}
</style>
