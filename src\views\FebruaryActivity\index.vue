<template>
  <div class="february-activity">
    <nav-bar-2
      ref="topNavBar"
      bgStyle="transparent-white"
      :azShow="true"
      :placeholder="false"
    ></nav-bar-2>
    <div class="bg1"></div>
    <div class="bg2"></div>
    <div class="bg3"></div>
    <div @click="toPage('FebruaryActivityRule')" class="rule btn"></div>
    <div @click="getRecordList" class="exchange-record btn"></div>
    <main>
      <div class="user-container center">
        <div class="container">
          <div v-if="userInfo.token" class="user">
            <UserAvatar class="avatar" />
            <div class="nickname">{{ userInfo.nickname }}</div>
          </div>
          <div @click="login" v-else class="no-login btn">
            <div class="img"></div>
            <div class="text">未登录</div>
          </div>
        </div>
        <div
          :class="{
            'no-start': activity_status == 3,
            'end': activity_status == 4,
          }"
          class="active-status center"
        ></div>
      </div>
      <section class="module1 center">
        <div class="title"></div>
        <div class="container1">
          <div class="left">
            当前投入金币：<span>{{ choose_investment_confirm.gold }}</span>
          </div>
          <div @click="openChooseInvestment" class="right"></div>
        </div>
        <div class="game">
          <div class="pointer"></div>
          <div class="arrow"></div>
          <div :class="[`animation${result}`]" class="rotate">
            <div class="text text1">2倍率金币</div>
            <div class="text text2">5倍率金币</div>
            <div class="text text3">8倍率金币</div>
            <div class="text text4">10倍率金币</div>
            <div class="text text5">1.2倍率金币</div>
            <div class="text text6">1.5倍率金币</div>
          </div>
        </div>
        <div class="container2">
          <div class="count">
            可投入次数：<span>{{ lottery_count }}</span
            >次
          </div>
          <div @click="getLotteryCount" class="button btn"></div>
        </div>
        <div
          :class="{
            already: choose_investment_confirm.id && activity_status == 1,
            empty:
              lottery_count <= 0 &&
              activity_status == 1 &&
              investment_status != 1,
          }"
          @click="handleLottery"
          class="bottom-button btn"
        ></div>
        <swiper
          v-if="user_list.length > 0"
          :options="swiperOption"
          class="user-list"
        >
          <!-- slides -->
          <swiper-slide
            v-for="(item, index) in user_list"
            :key="index"
            class="swiper-no-swiping"
          >
            <div class="swiper-item">
              <span>{{ item }}</span>
            </div>
          </swiper-slide>
        </swiper>
      </section>
      <section class="module2 center">
        <div class="title"></div>
        <div class="item">
          <div class="left">
            <div class="big-text">88礼金</div>
            <div class="small-text">活动期间SVIP天数大于30天</div>
          </div>
          <div
            :class="{ empty: svip_status == 2, already: svip_status == 3 }"
            @click="handleGetCashGift"
            class="right"
          ></div>
        </div>
      </section>
      <section class="module3 center">
        <div class="title"></div>
        <swiper
          v-if="user_list2.length > 0"
          :options="swiperOption2"
          class="user-list user-list2"
        >
          <!-- slides -->
          <swiper-slide
            v-for="(item, index) in user_list2"
            :key="index"
            class="swiper-no-swiping"
          >
            <div class="swiper-item">
              <span>{{ item }}</span>
            </div>
          </swiper-slide>
        </swiper>
        <div class="scratch-container">
          <div class="remain_gold">{{ remain_gold }}</div>
          <div class="scratch-button">
            <div class="left">
              刮奖次数：<span class="number">{{ scratch_count }}</span
              >次
            </div>
            <div @click="getScratchCount" class="right btn"></div>
          </div>
          <div class="canvas-container">
            <div class="prize">{{ first_gold }}金币</div>
            <canvas
              id="canvas"
              @touchmove="touchMove"
              @touchstart="touchStart"
              @touchend="touchEnd"
            ></canvas>
          </div>
        </div>
        <div class="bottom-text">
          每日前两次完成平台币充值即可参与瓜分千万金币~
        </div>
        <div
          @click="toRecharge"
          v-if="
            activity_status != 1 || today_scratch_status != 0 || !userInfo.token
          "
          class="bottom-button btn"
        ></div>
        <div v-else class="bottom-button empty"></div>
      </section>
    </main>
    <!-- 兑奖记录 -->
    <van-popup
      v-model="record_list_popup"
      :lock-scroll="false"
      :close-on-click-overlay="false"
      class="popup record-list-popup"
    >
      <div class="popup-close" @click="closeRecordPopup"></div>
      <div class="title">兑奖记录</div>
      <div v-if="record_list.length > 0" class="list">
        <div v-for="(item, index) in record_list" :key="index" class="item">
          <div class="left">{{ item.title }}</div>
          <div v-html="item.info" class="right"></div>
        </div>
      </div>
      <div v-else class="empty">暂无兑奖记录</div>
    </van-popup>
    <!-- 选择任意卡片弹窗 -->
    <van-popup
      v-model="choose_investment_popup"
      :lock-scroll="false"
      class="popup choose-popup message-popup"
    >
      <div class="popup-close" @click="choose_investment_popup = false"></div>
      <div class="title">投入金币选择</div>
      <div class="gold"
        >当前金币余额：<span>{{ userInfo.gold }}</span></div
      >
      <div class="explain">注：当游戏实付达标才可投入对应金币数</div>
      <div class="card-content">
        <div
          v-for="item in investment_list"
          :key="item.id"
          :class="{
            empty: !item.status,
            current: choose_investment_on.id == item.id,
          }"
          @click="handleChooseInvestment(item)"
          class="card-item"
        >
          <div class="big-text">{{ item.gold }}</div>
          <div class="small-text">金币</div>
        </div>
      </div>
      <div
        @click="handleChooseInvestmentConfirm"
        class="bottom-button btn"
      ></div>
    </van-popup>
    <!-- 获取刮奖机会弹窗 -->
    <van-popup
      v-model="scratch_empty_popup"
      :lock-scroll="false"
      :close-on-click-overlay="false"
      class="popup lottery-empty-popup"
    >
      <div class="popup-close" @click="scratch_empty_popup = false"></div>
      <div class="title">当前未有剩余刮奖机会</div>
      <div class="text">
        活动期间，每日前2次充值平台币，即可参与瓜分千万金币。金额随机，祝君好运满满！
      </div>
      <div @click="scratch_empty_popup = false" class="bottom-button btn"></div>
    </van-popup>
    <!-- 引导svip充值弹窗 -->
    <van-popup
      :lock-scroll="false"
      v-model="svip_popup"
      class="popup svip-popup message-popup"
    >
      <div class="text">
        您当前SVIP剩余天数不足30天，续费SVIP满30天可获取<span>88</span>礼金哦~
      </div>
      <div @click="toRechargeSvip" class="bottom-button btn"></div>
    </van-popup>
    <ptb-recharge-popup @success="getActivityInfo"></ptb-recharge-popup>
    <svip-recharge-popup @success="getActivityInfo"></svip-recharge-popup>
  </div>
</template>
<script>
import UserAvatar from '@/components/user-avatar';
import { BOX_login } from '@/utils/box.uni.js';
import {
  ApiFBIndex,
  ApiFBGetSelfInfo,
  ApiFBInvestmentList,
  ApiFBLotteryList,
  ApiFBScratchLottery,
  ApiFBRemainLotteryCount,
  ApiFBTakeSvip,
  ApiFBTakeInvestment,
} from '@/api/views/february_activity';
import { mapGetters, mapMutations } from 'vuex';
import canvasImg from '@/assets/images/february-activity/fa_bg8.png';
import ptbRechargePopup from '@/components/ptb-recharge-popup';
import svipRechargePopup from '@/components/svip-recharge-popup';

export default {
  data() {
    return {
      activity_status: 3, //1活动已开始 2 活动不存在 3活动未开始 4活动已结束
      remain_gold: 0, //剩余金币数
      lottery_count: 0, //抽奖次数
      scratch_count: 0, //今日刮奖剩余次数
      first_gold: 0, //刮奖中的金币
      today_scratch_status: 0, //平台币今日刮奖状态 1:可以刮奖 0不可刮奖
      scratch_status: 0, //刮奖状态 0未开始1刮奖中2刮奖完
      scratch_empty_popup: false, //暂无刮奖机会弹窗
      swiperOption: {
        observer: true,
        observeParents: true,
        noSwiping: true,
        direction: 'vertical',
        slidesPerView: 3,
        speed: 600,
        autoplay: {
          delay: 0,
        },
        loop: true,
        freeMode: true,
      }, //第一个用户列表展示轮播
      swiperOption2: {
        observer: true,
        observeParents: true,
        noSwiping: true,
        direction: 'vertical',
        slidesPerView: 1,
        speed: 600,
        autoplay: {
          delay: 0,
        },
        loop: true,
        freeMode: true,
      }, //第二个用户列表展示轮播
      user_list: [], //第一个用户列表
      user_list2: [], //第二个用户列表
      svip_status: 0, //svip状态1可领取2天数不足3已领取
      svip_popup: false, //svip引导弹窗
      canvas: '', // 画布
      clearCount: 0, // 刮奖区被刮开的程度 最大150就全解开
      ctx: '', // 画笔
      ratio: 0,
      record_list_popup: false, //记录弹窗
      record_list: [], //记录列表
      investment_list: [], //投入列表
      choose_investment_popup: false, //选择投入弹窗
      choose_investment_on: {}, //选择投入当前高亮
      choose_investment_confirm: { id: 0, gold: 0 }, //当前选择投入确定
      finished: false, //ajax防卡
      result: 0, //当前动画结果0:重置1:2倍2:5倍3:8倍4:10倍5:1.2倍6:1.5倍
      investment_status: 1, //1 没有用完5次 2用完5次
    };
  },
  computed: {
    active_status_text() {
      const arr = ['活动已开始', '活动不存在', '活动未开始', '活动已结束'];
      return arr[this.activity_status - 1];
    },
    ...mapGetters({
      userInfo: 'user/userInfo',
    }),
  },
  async created() {
    await this.getActivityInfo();
  },
  mounted() {
    this.ratio = document.body.clientWidth / 375;
    this.canvas = document.getElementById('canvas');
    this.canvas.width = this.canvas.clientWidth;
    this.canvas.height = this.canvas.clientHeight;
    this.ctx = this.canvas.getContext('2d');
    this.initCanvas();
  },
  methods: {
    async getActivityInfo() {
      const res = await ApiFBIndex();
      this.remain_gold = res.data.remain_gold;
      this.activity_status = res.data.activity_status;
      this.user_list = res.data.investment_list;
      this.user_list2 = res.data.record_list;
      const res2 = await ApiFBGetSelfInfo();
      this.scratch_count = res2.data.remain_lottery_ptb_count;
      this.today_scratch_status = res2.data.remain_lottery_ptb_status;
      this.lottery_count = res2.data.remain_investment_count;
      this.first_gold = res2.data.first_gold;
      this.svip_status = res2.data.remain_svip_status;
      this.investment_status = res2.data.investment_status;
      const res3 = await ApiFBInvestmentList();
      this.investment_list = res3.data.list;
    },
    login() {
      BOX_login();
    },
    toRecharge() {
      if (!this.userInfo.token) {
        this.login();
        return false;
      }
      if (this.activity_status != 1) {
        this.$toast(this.active_status_text);
        return false;
      }
      this.scratch_empty_popup = false;
      this.setShowPtbRechargePopup(true);
    },
    toRechargeSvip() {
      if (!this.userInfo.token) {
        this.login();
        return false;
      }
      if (this.activity_status != 1) {
        this.$toast(this.active_status_text);
        return false;
      }
      this.svip_popup = false;
      this.setShowSvipRechargePopup(true);
    },
    async initCanvas() {
      this.ctx.globalCompositeOperation = 'source-over';
      let image = new Image();
      image.src = canvasImg;
      image.onload = async () => {
        await this.ctx.drawImage(
          image,
          0,
          0,
          this.canvas.width,
          this.canvas.height,
        );
      };
    },
    touchStart(e) {
      if (this.activity_status != 1) {
        this.$toast(this.active_status_text);
        return false;
      }
      if (!this.userInfo.token) {
        this.login();
        return false;
      }
      switch (this.scratch_status) {
        case 0: // 未开始
          if (this.today_scratch_status == 0) {
            this.$toast('今日次数已用完');
            return;
          } else if (this.scratch_count <= 0) {
            // 当没有刮奖次数时;
            this.scratch_empty_popup = true;
            return;
          } else {
            // 开始刮奖
            this.scratch_status = 1;
          }
          break;
        case 1: // 刮奖中
          break;
        case 2: // 已结束
          break;
      }
    },
    touchMove(e) {
      if (this.scratch_status == 1) {
        let x = e.touches[0].clientX - this.canvas.getBoundingClientRect().left,
          y = e.touches[0].clientY - this.canvas.getBoundingClientRect().top;
        e.preventDefault();
        this.ctx.clearRect(x, y, 15 * this.ratio, 15 * this.ratio);
        this.clearCount++;
      }
    },
    async touchEnd(e) {
      if (this.scratch_status == 1) {
        if (this.clearCount > 80) {
          // 当手指累计滑动超过80时触发清空矩形画布, 数值越大要刮得越久
          this.scratch_status = 2;
          this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
          await this.handleScratch();
        }
      }
    },
    async handleScratch() {
      try {
        const res = await ApiFBScratchLottery();
        let {
          code,
          data: { remain_lottery_count, gold },
        } = res;
        if (code > 0) {
          this.scratch_count = remain_lottery_count;
          this.first_gold = gold;
          this.$toast(`恭喜获得${gold}金币`);
        }
      } finally {
        setTimeout(async () => {
          this.scratch_status = 0;
          this.clearCount = 0;
          this.initCanvas();
          await this.getActivityInfo();
        }, 500);
      }
    },
    // 关闭记录弹窗
    closeRecordPopup() {
      this.record_list_popup = false;
    },
    // 获取记录列表
    async getRecordList() {
      if (!this.userInfo.token) {
        this.login();
        return false;
      }
      if (this.activity_status != 1) {
        this.$toast(this.active_status_text);
        return false;
      }
      this.$toast({
        type: 'loading',
        duration: 0,
        message: '拼命加载中...',
        forbidClick: true,
      });
      try {
        const res = await ApiFBLotteryList();
        this.record_list = res.data.list;
        this.record_list_popup = true;
        this.$toast.clear();
      } finally {
      }
    },
    openChooseInvestment() {
      if (!this.userInfo.token) {
        this.login();
        return false;
      }
      if (this.activity_status != 1) {
        this.$toast(this.active_status_text);
        return false;
      }
      this.choose_investment_popup = true;
    },
    async handleLottery() {
      if (!this.userInfo.token) {
        this.login();
        return false;
      }
      if (this.activity_status != 1) {
        this.$toast(this.active_status_text);
        return false;
      }
      if (this.lottery_count <= 0) {
        return false;
      }
      if (!this.choose_investment_confirm.id) {
        this.$toast('请选择投入额度');
        return false;
      }
      if (this.finished) return false;
      this.finished = true;
      this.$toast({
        type: 'loading',
        duration: 0,
      });
      this.result = 0;
      try {
        const res = await ApiFBTakeInvestment({
          investment_id: this.choose_investment_confirm.id,
        });
        // 开始动画
        this.result = parseFloat(res.data.multiplier) * 10;
        setTimeout(() => {
          // 动画结束
          this.$toast(`恭喜获得${res.data.gold}金币`);
          this.choose_investment_confirm = { id: 0, gold: 0 };
          this.choose_investment_on = {};
          this.finished = false;
        }, 10000);
        await this.getActivityInfo();
      } catch {
      } finally {
        setTimeout(() => {
          this.finished = false;
        }, 10000);
      }
    },
    // 处理选择投入
    handleChooseInvestment(item) {
      if (!item.status) {
        return false;
      }
      this.choose_investment_on = JSON.parse(JSON.stringify(item));
    },
    // 处理选择投入确定
    handleChooseInvestmentConfirm() {
      this.choose_investment_confirm = JSON.parse(
        JSON.stringify(this.choose_investment_on),
      );
      this.choose_investment_popup = false;
    },
    async getScratchCount() {
      const res = await ApiFBRemainLotteryCount({ type: 1 });
      this.scratch_count = res.data.remain_count;
    },
    async getLotteryCount() {
      const res = await ApiFBRemainLotteryCount({ type: 2 });
      this.lottery_count = res.data.remain_count;
    },
    async handleGetCashGift() {
      if (this.svip_status == 2) {
        this.svip_popup = true;
        return false;
      }
      if (this.svip_status == 3) {
        return false;
      }
      const res = await ApiFBTakeSvip();
      if (res.data.num) {
        await this.getActivityInfo();
      }
    },
    ...mapMutations({
      setShowPtbRechargePopup: 'recharge/setShowPtbRechargePopup',
      setShowSvipRechargePopup: 'recharge/setShowSvipRechargePopup',
    }),
  },
  components: {
    ptbRechargePopup,
    svipRechargePopup,
    UserAvatar,
  },
};
</script>
<style lang="less" scoped>
.february-activity {
  position: relative;
  .center {
    margin: 0 auto;
  }
  .bg1 {
    width: 100%;
    height: 559 * @rem;
    .image-bg('~@/assets/images/february-activity/fa_bg1.png');
  }
  .bg2 {
    width: 100%;
    height: 598 * @rem;
    .image-bg('~@/assets/images/february-activity/fa_bg2.png');
  }
  .bg3 {
    width: 100%;
    height: 496 * @rem;
    .image-bg('~@/assets/images/february-activity/fa_bg3.png');
  }
  .rule {
    position: absolute;
    top: 60 * @rem;
    right: 0;
    width: 46 * @rem;
    height: 20 * @rem;
    .image-bg('~@/assets/images/february-activity/fa_button5.png');
  }
  .exchange-record {
    position: absolute;
    top: 110 * @rem;
    right: 5 * @rem;
    width: 46 * @rem;
    height: 46 * @rem;
    .image-bg('~@/assets/images/february-activity/fa_button9.png');
  }
  main {
    position: absolute;
    top: 353 * @rem;
    left: 50%;
    transform: translate(-50%, 0);
  }
  .user-container {
    top: 358 * @rem;
    width: 100%;
    box-sizing: border-box;
    padding: 0 20 * @rem;
    .container {
      .user {
        display: flex;
        align-items: center;
        .avatar {
          width: 24 * @rem;
          height: 24 * @rem;
        }
        .nickname {
          margin-left: 13 * @rem;
          font-size: 12 * @rem;
          color: #2a886c;
        }
      }
      .no-login {
        display: flex;
        align-items: center;
        .img {
          width: 24 * @rem;
          height: 24 * @rem;
          .image-bg('~@/assets/images/february-activity/fa_button8.png');
        }
        .text {
          margin-left: 13 * @rem;
          font-size: 12 * @rem;
          color: #2a886c;
          text-decoration: underline;
        }
      }
    }
    .active-status {
      position: absolute;
      top: 3.5 * @rem;
      left: 50%;
      transform: translate(-50%, 0);
      width: 89 * @rem;
      height: 17 * @rem;
      &.no-start {
        .image-bg('~@/assets/images/february-activity/fa_text4.png');
      }
      &.end {
        .image-bg('~@/assets/images/february-activity/fa_text5.png');
      }
    }
  }
  section {
    .title {
      width: 191 * @rem;
      height: 40 * @rem;
      margin: 0 auto;
    }
    .user-list {
      width: 98%;
      height: 86 * @rem;
      margin: 0 auto;
      background: linear-gradient(
        270deg,
        rgba(27, 106, 82, 0) 0%,
        rgba(27, 78, 106, 0.4) 23%,
        rgba(27, 78, 106, 0.4) 72%,
        rgba(27, 106, 82, 0) 100%
      );
      border-radius: 0 * @rem;
      opacity: 1;
      border: 1 * @rem solid;
      border-image: linear-gradient(
          270deg,
          rgba(32, 194, 152, 0),
          rgba(32, 194, 152, 1),
          rgba(32, 194, 152, 1),
          rgba(32, 194, 152, 0)
        )
        1 1;
      /deep/ .swiper-wrapper {
        transition-timing-function: linear !important;
      }
      &.user-list2 {
        height: 30 * @rem;
        margin-top: 15 * @rem;
      }
      .swiper-item {
        text-align: center;
        color: #fff;
        white-space: nowrap;
        span {
          color: rgba(68, 250, 38, 1);
        }
      }
    }
  }
  .module1 {
    margin-top: 17 * @rem;
    width: 375 * @rem;
    .title {
      .image-bg('~@/assets/images/february-activity/fa_text1.png');
    }
    .container1 {
      margin-top: 13 * @rem;
      width: 100%;
      box-sizing: border-box;
      padding: 0 28 * @rem;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .left {
        font-size: 16 * @rem;
        font-weight: bold;
        color: #155c47;
      }
      .right {
        width: 78 * @rem;
        height: 30 * @rem;
        .image-bg('~@/assets/images/february-activity/fa_button10.png');
      }
    }
    .game {
      position: relative;
      width: 100%;
      overflow: hidden;
      .pointer {
        position: absolute;
        top: 113 * @rem;
        left: 50%;
        z-index: 3;
        transform: translate(-50%, 0);
        width: 45 * @rem;
        height: 45 * @rem;
        .image-bg('~@/assets/images/february-activity/fa_button11.png');
      }
      .arrow {
        position: absolute;
        top: 97 * @rem;
        left: 50%;
        z-index: 2;
        transform: translate(-50%, 0);
        width: 25.5 * @rem;
        height: 24.25 * @rem;
        .image-bg('~@/assets/images/february-activity/fa_icon1.png');
      }
      .rotate {
        position: relative;
        width: 260 * @rem;
        height: 260 * @rem;
        margin: 5 * @rem auto 13 * @rem;
        .image-bg('~@/assets/images/february-activity/fa_bg4.png');
        &.animation0 {
          transform: rotate(0deg);
        }
        &.animation20 {
          transition: all 10s ease;
          transform: rotate(3600deg);
        }
        &.animation50 {
          transition: all 10s ease;
          transform: rotate(3900deg);
        }
        &.animation80 {
          transition: all 10s ease;
          transform: rotate(3840deg);
        }
        &.animation100 {
          transition: all 10s ease;
          transform: rotate(3780deg);
        }
        &.animation12 {
          transition: all 10s ease;
          transform: rotate(3720deg);
        }
        &.animation15 {
          transition: all 10s ease;
          transform: rotate(3660deg);
        }
        .text {
          position: absolute;
          top: 37 * @rem;
          left: 50%;
          transform: translate(-50%, 0);
          color: #256d5c;
          font-size: 12 * @rem;
          &.text2 {
            top: 83 * @rem;
            left: 82%;
            white-space: nowrap;
            transform: translate(-50%, 0) rotate(60deg);
          }
          &.text3 {
            top: 170 * @rem;
            left: 82%;
            white-space: nowrap;
            transform: translate(-50%, 0) rotate(120deg);
          }
          &.text4 {
            top: 222 * @rem;
            left: 50%;
            white-space: nowrap;
            transform: translate(-50%, 0) rotate(180deg);
          }
          &.text5 {
            top: 177 * @rem;
            left: 18%;
            white-space: nowrap;
            transform: translate(-50%, 0) rotate(240deg);
          }
          &.text6 {
            top: 83 * @rem;
            left: 18%;
            white-space: nowrap;
            transform: translate(-50%, 0) rotate(300deg);
          }
        }
      }
    }
    .container2 {
      display: flex;
      justify-content: center;
      align-items: center;
      position: relative;
      .count {
        display: block;
        height: 24 * @rem;
        padding: 0 10 * @rem;
        border-radius: 20 * @rem;
        background: rgba(48, 132, 114, 0.6);
        font-size: 13 * @rem;
        text-align: center;
        line-height: 24 * @rem;
        color: #fff;
        span {
          color: #faff0b;
        }
      }
      .button {
        position: absolute;
        top: 0;
        left: 50%;
        transform: translate(60 * @rem, 0);
        width: 57 * @rem;
        height: 24 * @rem;
        margin-left: 8 * @rem;
        .image-bg('~@/assets/images/february-activity/fa_button7.png');
      }
    }
    .bottom-button {
      width: 214 * @rem;
      height: 48 * @rem;
      margin: 14 * @rem auto;
      .image-bg('~@/assets/images/february-activity/fa_button14.png');
      &.already {
        .image-bg('~@/assets/images/february-activity/fa_button1.png');
      }
      &.empty {
        .image-bg('~@/assets/images/february-activity/fa_button2.png');
      }
    }
  }
  .module2 {
    margin-top: 35 * @rem;
    width: 375 * @rem;
    .title {
      .image-bg('~@/assets/images/february-activity/fa_text2.png');
    }
    .item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 80 * @rem;
      margin: 15 * @rem 30 * @rem;
      padding: 0 15 * @rem;
      background: rgba(27, 106, 82, 0.6);
      border-radius: 16 * @rem;
    }
    .left {
      flex: 0 0 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
      .big-text {
        font-size: 14 * @rem;
        color: #fff;
        font-weight: 600;
      }
      .small-text {
        margin-top: 5 * @rem;
        font-size: 12 * @rem;
        color: #fff;
      }
    }
    .right {
      width: 62 * @rem;
      height: 28 * @rem;
      .image-bg('~@/assets/images/february-activity/fa_button3.png');
      &.empty {
        .image-bg('~@/assets/images/february-activity/fa_button12.png');
      }
      &.already {
        .image-bg('~@/assets/images/february-activity/fa_button4.png');
      }
    }
  }
  .module3 {
    width: 375 * @rem;
    margin-top: 33 * @rem;
    .title {
      .image-bg('~@/assets/images/february-activity/fa_text3.png');
    }
    .scratch-container {
      width: 315 * @rem;
      height: 300 * @rem;
      margin: 15 * @rem auto 0;
      overflow: hidden;
      .image-bg('~@/assets/images/february-activity/fa_bg5.png');
      .remain_gold {
        margin: 33 * @rem 0 0;
        height: 53 * @rem;
        line-height: 53 * @rem;
        text-align: center;
        font-size: 40 * @rem;
        color: #fff;
        font-family: Arial-Bold, Arial;
      }
      .canvas-container {
        width: 100%;
        margin-top: 34 * @rem;
        position: relative;
        .prize {
          width: 262 * @rem;
          height: 108 * @rem;
          margin: 0 auto;
          line-height: 108 * @rem;
          background: #f5f5f5;
          font-size: 35 * @rem;
          letter-spacing: 3 * @rem;
          text-align: center;
          color: #f7572d;
          font-weight: bold;
          border-radius: 12 * @rem;
        }
        #canvas {
          width: 262 * @rem;
          height: 108 * @rem;
          position: absolute;
          top: 0;
          left: 50%;
          transform: translate(-50%, 0);
        }
      }
      .scratch-button {
        margin: 0 16 * @rem 0;
        height: 54 * @rem;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .left {
          color: #fff;
          font-size: 15 * @rem;
          font-weight: 500;
          .number {
            margin: 0 5 * @rem;
            color: rgba(255, 203, 18, 1);
          }
        }
        .right {
          width: 62 * @rem;
          height: 26 * @rem;
          .image-bg('~@/assets/images/february-activity/fa_button7.png');
        }
      }
    }
    .bottom-text {
      margin-top: 10 * @rem;
      text-align: center;
      color: rgba(255, 255, 255, 0.7);
    }
    .bottom-button {
      width: 214 * @rem;
      height: 48 * @rem;
      margin: 15 * @rem auto 0;
      .image-bg('~@/assets/images/february-activity/fa_button6.png');
      &.empty {
        .image-bg('~@/assets/images/february-activity/fa_button2.png');
      }
    }
  }
}
.popup {
  width: 290 * @rem;
  padding: 18 * @rem;
  border-radius: 20 * @rem;
  overflow: hidden;
  background: #f8fffc;
  box-shadow: 0 * @rem 2 * @rem 15 * @rem 0 * @rem rgba(248, 0, 0, 0.1);
  border: 2 * @rem solid #478671;
  .text {
    font-size: 14 * @rem;
    color: RGBA(46, 186, 139, 1);
  }
  .popup-close {
    width: 33 * @rem;
    height: 27 * @rem;
    background: RGBA(46, 186, 139, 1)
      url(~@/assets/images/february-activity/popup-close.png) center center
      no-repeat;
    background-size: 22 * @rem 22 * @rem;
    position: absolute;
    right: -1 * @rem;
    top: -1 * @rem;
    border-radius: 0 12 * @rem 0 12 * @rem;
  }
  .title {
    font-size: 16 * @rem;
    font-family: PingFang SC-Semibold, PingFang SC;
    font-weight: 600;
    color: #1b5b5b;
    line-height: 20 * @rem;
    text-align: center;
    margin-bottom: 30 * @rem;
  }
  .bottom-button {
    width: 255 * @rem;
    height: 47 * @rem;
    margin: 15 * @rem auto 0;
    .image-bg('~@/assets/images/february-activity/fa_button13.png');
  }
  &.message-popup {
    .text {
      width: 254 * @rem;
      margin: 0 auto;
      padding: 20 * @rem 0;
      font-size: 14 * @rem;
      font-family: PingFang SC-Semibold, PingFang SC;
      font-weight: 600;
      color: #7b74aa;
      line-height: 18 * @rem;
      text-align: center;
    }
  }
}
.record-list-popup {
  padding: 18 * @rem 0;
  height: 245 * @rem;
  .list {
    height: 210 * @rem;
    overflow-y: scroll;
    padding: 0 18 * @rem;
    .item {
      display: flex;
      justify-content: space-between;
      height: 30 * @rem;
      align-items: center;
      font-size: 14 * @rem;
      color: rgba(71, 134, 113, 1);
      .left {
        flex: 0 0 90 * @rem;
      }
      .right {
        flex: 1;
        text-align: right;
      }
      /deep/ span {
        color: rgba(248, 88, 46, 1);
      }
    }
  }
  .empty {
    width: 100%;
    height: 210 * @rem;
    display: flex;
    justify-content: center;
    align-items: center;
    color: RGBA(46, 186, 139, 1);
  }
}
.choose-popup {
  width: 293 * @rem;
  box-sizing: border-box;
  padding: 14 * @rem;
  .title {
    margin-bottom: 15 * @rem;
  }
  .gold {
    margin: 8 * @rem 0;
    font-size: 14 * @rem;
    span {
      color: #2eba8b;
    }
  }
  .explain {
    color: #999;
    font-size: 12 * @rem;
    margin-bottom: 10 * @rem;
  }
  .card-content {
    overflow: hidden;
    margin-bottom: 14 * @rem;
    .card-item {
      float: left;
      width: 80 * @rem;
      height: 70 * @rem;
      margin-right: 9 * @rem;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      background: #ffffff;
      border-radius: 8 * @rem 8 * @rem 8 * @rem 8 * @rem;
      border: 1 * @rem solid #b9dfd2;
      margin-bottom: 9 * @rem;
      &.current {
        position: relative;
        border: 2 * @rem solid RGBA(46, 186, 139, 1);
        background: #fff;
        overflow: hidden;
        .big-text {
          color: rgba(27, 91, 91, 1);
        }
        .small-text {
          color: rgba(27, 91, 91, 0.5);
        }
        &::before {
          content: '';
          position: absolute;
          top: -2 * @rem;
          right: -2 * @rem;
          width: 18 * @rem;
          height: 14 * @rem;
          .image-bg('~@/assets/images/february-activity/fa_icon2.png');
        }
      }
      &.empty {
        background: #e4f1ec;
        border: none;
        .big-text {
          color: rgba(27, 91, 91, 0.4);
        }
        .small-text {
          color: rgba(27, 91, 91, 0.3);
        }
      }
      &:nth-of-type(3n) {
        margin-right: 0;
      }
      .big-text {
        font-size: 20 * @rem;
        font-weight: 600;
        color: #1b5b5b;
        line-height: 25 * @rem;
        margin-bottom: 5 * @rem;
      }
      .small-text {
        height: 13 * @rem;
        font-size: 10 * @rem;
        color: rgba(27, 91, 91, 0.5);
        line-height: 13 * @rem;
      }
    }
  }
}
.svip-popup {
  span {
    color: rgba(248, 88, 46, 1);
  }
}
</style>
