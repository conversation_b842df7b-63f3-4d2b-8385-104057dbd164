<template>
  <div class="july-activity">
    <nav-bar-2
      ref="topNavBar"
      bgStyle="transparent-white"
      :azShow="true"
      :placeholder="false"
    >
      <template #left>
        <div class="back" @click="back"></div>
      </template>
    </nav-bar-2>
    <div class="main">
      <div class="top-bg">
        <div class="user-container">
          <div v-if="userInfo.token" class="user">
            <user-avatar class="avatar"></user-avatar>
            <div class="nickname">{{ userInfo.nickname }}</div>
          </div>
          <div @click="login" v-else class="user btn">
            <div class="avatar img"></div>
            <div class="text">未登录</div>
          </div>
        </div>

        <div @click="toPage('JulyActivityRule')" class="rule btn"></div>
        <div class="exchange-record btn" @click="openRecordPopup"></div>
        <div class="get-hammer-btn btn" @click="openTaskPopup"></div>
      </div>
      <!-- 活动主页面 -->
      <div class="content-container">
        <div class="nav-bar-bg" :class="{ second: tabIndex == 1 }">
          <div class="broadcast">
            <van-swipe
              class="broadcast-swiper"
              vertical
              :autoplay="2000"
              :touchable="false"
              :show-indicators="false"
            >
              <van-swipe-item
                v-for="(item, index) in reward_log_list"
                :key="index"
              >
                <div class="broadcast-item">
                  {{ item }}
                </div>
              </van-swipe-item>
            </van-swipe>
          </div>
          <div class="nav-list">
            <div
              class="nav-item"
              v-for="(item, index) in 2"
              :key="index"
              @click="clickNav(index)"
            ></div>
          </div>
        </div>
        <div class="content-bg">
          <div class="playground">
            <div
              class="play-row"
              v-for="(row, rowIndex) in activity_list"
              :key="rowIndex"
            >
              <div class="play-item" v-for="(item, index) in row" :key="index">
                <box-item
                  :info="item"
                  @refresh="refresh"
                  :frozen="tabIndex == 1 && first_finish_status != 2"
                  :key="tabIndex + '' + rowIndex + index"
                  :status="item.status"
                  :knockingInfo="knockingInfo"
                  :tabIndex="tabIndex"
                ></box-item>
              </div>
            </div>
          </div>
          <div class="play-tips">
            <div class="line-1">“敲完所有冰块即可获得夏日消暑大礼包！”</div>
            <div class="line-2" v-if="tabIndex == 0">
              开启可随机获取：2888 金币、SVIP月卡、588绑定平台币其中一个
            </div>
            <div class="line-2" v-if="tabIndex == 1">
              开启可随机获取：3888 金币、SVIP月卡、888绑定平台币其中一个
            </div>
          </div>
          <div class="operation-bar">
            <div
              class="next lock"
              @click="clickNext"
              v-if="tabIndex == 0 && first_finish_status == 0"
            ></div>
            <div
              class="next unlock"
              @click="clickNext"
              v-if="tabIndex == 0 && first_finish_status != 0"
            ></div>
            <div class="knock" @click="handleKnock">
              <div class="hammer-num">X{{ mem_remain_num }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 获取木锤 -->
    <van-popup
      v-model="taskPopup"
      :lock-scroll="false"
      :close-on-click-overlay="false"
      class="popup task-popup"
    >
      <div class="task-popup-close" @click="closeTaskPopup"></div>
      <div class="task-popup-title"></div>
      <div class="task-list">
        <template v-for="(item, index) in task_list">
          <template v-if="index == 0">
            <div class="popup-subtitle" :key="'subtitle' + index">
              ——活动期间每个用户仅可参与一次——
            </div>
          </template>
          <template v-if="index == 2">
            <div class="popup-subtitle" :key="'subtitle' + index">
              ——活动期间游戏内实际充值——
            </div>
          </template>
          <div class="task-item" :key="index">
            <div class="task-content">
              <div class="title">{{ item.title }}</div>
              <div class="desc">{{ item.desc }}</div>
            </div>
            <div
              class="btn get-btn no-get"
              v-if="item.status == 0"
              @click="$toast('未满足条件，请完成后领取！')"
            ></div>
            <div
              class="btn get-btn can-get"
              v-else-if="item.status == 1"
              @click="handleGetTaskReward(item)"
            ></div>
            <div class="btn get-btn had-get" v-else></div>
          </div>
        </template>
      </div>
    </van-popup>
    <!-- 奖励记录 -->
    <van-popup
      v-model="recordPopup"
      :lock-scroll="false"
      :close-on-click-overlay="false"
      class="popup record-popup"
    >
      <div class="record-popup-close" @click="closeRecordPopup"></div>
      <div class="record-popup-title"></div>
      <div class="record-list" v-if="record_list.length > 0">
        <div
          v-for="(item, index) in record_list"
          :key="index"
          class="record-item"
        >
          <div class="title">{{ item.title }}</div>
          <div class="desc">{{ item.desc }}</div>
        </div>
      </div>
      <div v-else class="record-list empty">暂无兑奖记录</div>
    </van-popup>
    <!-- 最终奖励动效 -->
    <div
      class="result-lottie-popup"
      v-if="finalRewardPopup"
      @click="finalRewardPopup = false"
    >
      <div
        ref="resultLottie"
        class="result-lottie"
        @click.stop="handleGetFinalReward"
      ></div>
    </div>
  </div>
</template>

<script>
import { platform, boxInit } from '@/utils/box.uni.js';
import { BOX_login } from '@/utils/box.uni.js';
import userAvatar from '@/components/user-avatar';
import { mapActions } from 'vuex';
import lottie from 'lottie-web';
import ae_result_data from '@/assets/ae/july-activity/ae_result_data/data.json';
import boxItem from './components/box-item';
import {
  ApiActivityJulyIndex,
  ApiActivityJulyTask,
  ApiActivityJulyRewardLog,
  ApiActivityJulyActivityList,
  ApiActivityJulyTakeIce,
  ApiActivityJulyCheckFinish,
  ApiActivityJulyTakeFinish,
} from '@/api/views/july_activity.js';
export default {
  components: {
    userAvatar,
    boxItem,
  },
  data() {
    return {
      resultLottie: null,
      taskPopup: false,
      recordPopup: false,
      finalRewardPopup: false,
      start_time: '',
      end_time: '',
      record_list: [], // 奖励记录
      task_list: [],
      activity_list: [],
      first_finish_status: 0, // 第一轮活动奖励是否领取 0=未完成 1=已完成未领取 2=已完成已领取
      activity_status: 3, // 活动状态 1活动已开始 2 活动不存在 3活动未开始 4活动已结束
      mem_remain_num: 0, // 用户剩余锤子数
      reward_log_list: [],
      tabIndex: 0, // 0 第一轮; 1 第二轮
      knockingInfo: {},
      isKnocking: false,
      finish_status: 0,
    };
  },
  computed: {
    activity_status_text() {
      const arr = [
        '活动已开始！',
        '活动不存在！',
        '活动未开始！',
        '活动已结束！',
      ];
      return arr[this.activity_status - 1];
    },
  },
  async mounted() {
    await this.$onAppFinished;
    // 官包触发页面刷新（将onResume挂载在window上）
    if (platform == 'android') {
      window.onResume = this.onResume;
    }
    await this.init();
    this.checkFinalReward();
  },
  methods: {
    ...mapActions({
      SET_USER_INFO: 'user/SET_USER_INFO',
    }),
    async onResume() {
      await boxInit();
      await this.SET_USER_INFO(true);
      await this.init();
    },
    async init() {
      await this.getIndexData();
      if (this.first_finish_status == 2) {
        this.tabIndex = 1;
      } else {
        this.tabIndex = 0;
      }
      this.getActivityList(this.tabIndex);
    },
    async refresh() {
      await this.getIndexData();
      this.getActivityList(this.tabIndex);
    },

    async getIndexData() {
      const res = await ApiActivityJulyIndex();
      this.activity_status = res.data.activity_status;
      this.first_finish_status = res.data.first_finish_status;
      this.finish_status = res.data.finish_status;
      this.start_time = res.data.start_time;
      this.end_time = res.data.end_time;
      this.reward_log_list = res.data.reward_log_list;
      this.task_list = res.data.task_list;
      this.mem_remain_num = res.data.mem_remain_num;
    },
    async getActivityList(tabIndex = 0) {
      this.tabIndex = tabIndex;
      try {
        const res = await ApiActivityJulyActivityList({
          num: this.tabIndex + 1,
        });
        this.activity_list = res.data.list;
      } finally {
        this.isKnocking = false;
      }
    },
    async getRewardLog() {
      const res = await ApiActivityJulyRewardLog();
      this.record_list = res.data.list;
    },
    clickNext() {
      if (this.first_finish_status == 0) {
        this.$toast('请先完成第一轮bingo挑战！');
        return false;
      }
      if (this.first_finish_status == 1) {
        this.openFinalRewardPopup();
        return false;
      }
      this.tabIndex = 1;
      this.getActivityList(1);
    },
    clickNav(index) {
      if (!this.userInfo.token) {
        this.$toast('请登录');
        this.login();
        return false;
      }
      if (this.tabIndex != index) {
        this.tabIndex = index;
        this.getActivityList(this.tabIndex);
      }
    },
    async handleKnock() {
      if (this.activity_status != 1) {
        this.$toast(this.activity_status_text);
        return false;
      }
      if (!this.userInfo.token) {
        this.$toast('请登录');
        this.login();
        return false;
      }
      if (this.tabIndex == 1 && this.first_finish_status != 2) {
        this.$toast('请先完成第一轮bingo挑战！');
        return false;
      }
      if (this.isKnocking) {
        return false;
      }
      if (this.tabIndex == 1 && this.finish_status == 2) {
        this.$toast('已全部破冰成功');
        return false;
      }
      this.isKnocking = true;
      try {
        const res = await ApiActivityJulyTakeIce({
          num: this.tabIndex + 1,
        });
        this.knockingInfo = res.data;
        if (res.data.is_finish) {
          setTimeout(() => {
            this.openFinalRewardPopup();
          }, 2000);
        }
      } catch (e) {
        await this.refresh();
      }
    },
    login() {
      BOX_login();
    },

    // 最终奖励动效
    loadResultLottie() {
      // 设置参数
      let params = {
        container: this.$refs.resultLottie, // 动效容器
        renderer: 'svg', // 渲染器类型
        loop: false, // 是否循环播放
        autoplay: true, // 是否自动播放
        animationData: ae_result_data, // 动效数据
      };
      // 加载动效
      this.resultLottie = lottie.loadAnimation(params);
    },
    openFinalRewardPopup() {
      if (this.activity_status != 1) {
        this.$toast(this.activity_status_text);
        return false;
      }
      if (!this.userInfo.token) {
        this.$toast('请登录');
        this.login();
        return false;
      }
      this.finalRewardPopup = true;
      this.$nextTick(() => {
        this.loadResultLottie();
      });
    },
    // 领取最终奖励
    async handleGetFinalReward() {
      this.$toast({
        type: 'loading',
        duration: 0,
        message: '拼命加载中...',
      });
      try {
        const res = await ApiActivityJulyTakeFinish({
          num: this.tabIndex + 1,
        });
        this.finalRewardPopup = false;
      } finally {
        await this.getIndexData();
      }
    },
    async checkFinalReward() {
      if (this.first_finish_status == 2) {
        // 第二轮
        const res = await ApiActivityJulyCheckFinish({
          num: this.tabIndex + 1,
        });
        if (res.data.status == 1) {
          this.openFinalRewardPopup();
        }
      } else if (this.first_finish_status == 1) {
        // 第一轮未领取
        this.openFinalRewardPopup();
      }
    },
    async openTaskPopup() {
      if (this.activity_status != 1) {
        this.$toast(this.activity_status_text);
        return false;
      }
      await this.refresh();
      this.taskPopup = true;
    },
    // 关闭任务列表
    closeTaskPopup() {
      this.taskPopup = false;
    },
    // 领取任务奖励
    async handleGetTaskReward(item) {
      this.$toast({
        type: 'loading',
        duration: 0,
        message: '拼命加载中...',
      });
      try {
        const res = await ApiActivityJulyTask({
          task_id: item.task_id,
        });
      } finally {
        this.$toast.clear();
        this.getIndexData();
      }
    },
    openRecordPopup() {
      if (this.activity_status == 2 || this.activity_status == 3) {
        this.$toast(this.activity_status_text);
        return false;
      }
      if (!this.userInfo.token) {
        this.$toast('请登录');
        this.login();
        return false;
      }
      this.recordPopup = true;
      this.getRewardLog();
    },
    // 关闭记录列表
    closeRecordPopup() {
      this.recordPopup = false;
    },
  },
};
</script>

<style lang="less" scoped>
.july-activity {
  width: 100%;
  overflow: hidden;
  .back {
    width: 28 * @rem;
    height: 28 * @rem;
    border-radius: 15 * @rem;
    background: rgba(0, 0, 0, 0.3) url(~@/assets/images/nav-bar-back-white.png)
      center center no-repeat;
    background-size: 8 * @rem 14 * @rem;
  }
  .main {
    .top-bg {
      width: 100%;
      height: 256 * @rem;
      .image-bg('~@/assets/images/july-activity/july_bg.png');
      background-size: 100% 256 * @rem;
      position: relative;

      .user-container {
        position: absolute;
        top: calc(15 * @rem + @safeAreaTop);
        top: calc(15 * @rem + @safeAreaTopEnv);
        left: 70 * @rem;
        z-index: 1000;
        height: 22 * @rem;
        background-color: rgba(0, 0, 0, 0.3);
        color: #fff;
        line-height: 22 * @rem;
        border-radius: 20 * @rem;
        .user {
          padding: 0 12 * @rem 0 30 * @rem;
          .text {
            white-space: nowrap;
          }
        }
        .avatar {
          position: absolute;
          top: -3.5 * @rem;
          left: -3.5 * @rem;
          width: 28 * @rem;
          height: 28 * @rem;
          border-radius: 50%;
          &.img {
            .image-bg('~@/assets/images/july-activity/avatar_default.png');
          }
        }
      }
      .exchange-record {
        position: absolute;
        right: 0;
        top: 154 * @rem;
        width: 27 * @rem;
        height: 60 * @rem;
        .image-bg('~@/assets/images/july-activity/record_icon.png');
        background-size: 27 * @rem 60 * @rem;
      }
      .rule {
        position: absolute;
        right: 0;
        top: 82 * @rem;
        width: 27 * @rem;
        height: 60 * @rem;
        .image-bg('~@/assets/images/july-activity/rule_icon.png');
        background-size: 27 * @rem 60 * @rem;
      }
      .get-hammer-btn {
        position: absolute;
        z-index: 2;
        left: 96 * @rem;
        top: 218 * @rem;
        width: 183 * @rem;
        height: 50 * @rem;
        .image-bg('~@/assets/images/july-activity/get_hammer_btn.png');
        background-size: 183 * @rem 50 * @rem;
      }
    }
    .content-container {
      .nav-bar-bg {
        width: 100%;
        height: 123 * @rem;
        position: relative;
        .image-bg('~@/assets/images/july-activity/july_bg_2.png');
        background-size: 100% 123 * @rem;
        &::before {
          content: '';
          width: 0;
          height: 0;
          position: absolute;
          .image-bg('~@/assets/images/july-activity/july_bg_3.png');
        }
        &.second {
          .image-bg('~@/assets/images/july-activity/july_bg_3.png');
          background-size: 100% 123 * @rem;
        }

        .broadcast {
          box-sizing: border-box;
          position: absolute;
          left: 57 * @rem;
          top: 18 * @rem;
          width: 300 * @rem;
          height: 20 * @rem;
          padding: 0 11 * @rem 0 18 * @rem;
          .image-bg('~@/assets/images/july-activity/broadcast_bg.png');
          background-size: 300 * @rem 20 * @rem;
          font-size: 11 * @rem;
          color: #ffffff;
          font-weight: 500;
          line-height: 20 * @rem;
          text-align: center;
          .broadcast-swiper {
            height: 20 * @rem;
            /deep/ .van-swipe-item {
              height: 20 * @rem;
            }
          }
          .broadcast-item {
            height: 20 * @rem;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            span {
              color: #fcec80;
            }
          }
        }
        .nav-list {
          width: 100%;
          left: 0;
          top: 48 * @rem;
          position: absolute;
          height: 60 * @rem;
          display: flex;
          align-items: center;
          justify-content: center;
          .nav-item {
            width: 50%;
            height: 60 * @rem;
          }
        }
      }
      .content-bg {
        width: 100%;
        height: 524 * @rem;
        .image-bg('~@/assets/images/july-activity/july_bg_4.png');
        background-size: 100% 524 * @rem;
        .playground {
          box-sizing: border-box;
          width: 328 * @rem;
          height: 340 * @rem;
          margin: 0 24 * @rem;
          padding: 29 * @rem 10 * @rem 11 * @rem 15 * @rem;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          flex-wrap: wrap;
          justify-content: space-between;
          .play-row {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
          }
        }
        .play-tips {
          margin-top: 12 * @rem;
          .line-1 {
            font-size: 13 * @rem;
            font-family: PingFang SC-Semibold, PingFang SC;
            font-weight: 600;
            color: #1e85be;
            line-height: 16 * @rem;
            text-align: center;
          }
          .line-2 {
            font-size: 10 * @rem;
            font-family: PingFang SC-Medium, PingFang SC;
            font-weight: 500;
            color: #60abd6;
            line-height: 13 * @rem;
            text-align: center;
            margin-top: 1 * @rem;
          }
        }
        .operation-bar {
          display: flex;
          justify-content: center;
          padding: 10 * @rem 22 * @rem 0;
          .next {
            &.lock {
              width: 160 * @rem;
              height: 63*@rem;
              .image-bg('~@/assets/images/july-activity/next.png');
              background-size: 156 * @rem 60 * @rem;
              margin-top: 5*@rem;
            }
            &.unlock {
            width: 160 * @rem;
            height: 64 * @rem;
              .image-bg('~@/assets/images/july-activity/next-unlock.png');
              background-size: 160 * @rem 64 * @rem;
            }
          }
          .knock {
            position: relative;
            width: 160 * @rem;
            height: 64 * @rem;
            .image-bg('~@/assets/images/july-activity/knock.png');
            background-size: 160 * @rem 64 * @rem;
            .hammer-num {
              box-sizing: border-box;
              padding-left: 26 * @rem;
              font-size: 12 * @rem;
              color: #ffffff;
              line-height: 23 * @rem;
              text-align: center;
              position: absolute;
              right: 6 * @rem;
              top: -6 * @rem;
              width: 56 * @rem;
              height: 23 * @rem;
              .image-bg('~@/assets/images/july-activity/hammer_num_bg.png');
              background-size: 56 * @rem 23 * @rem;
            }
          }
        }
      }
    }
  }
}
.popup {
  box-sizing: border-box;
  width: 349 * @rem;
  border-radius: 18 * @rem;
  background: #ffc056;
  overflow: visible;
  padding: 15 * @rem 12 * @rem 12 * @rem;
}
.task-popup {
  .task-popup-close {
    width: 36 * @rem;
    height: 36 * @rem;
    .image-bg('~@/assets/images/july-activity/popup-close.png');
    background-size: 36 * @rem 36 * @rem;
    position: absolute;
    right: -6 * @rem;
    top: -10 * @rem;
  }
  .task-popup-title {
    width: 170 * @rem;
    height: 24 * @rem;
    .image-bg('~@/assets/images/july-activity/popup-title-task.png');
    background-size: 170 * @rem 24 * @rem;
    margin: 0 auto;
  }
  .task-list {
    border-radius: 18 * @rem;
    background: #fceed8;
    padding: 4 * @rem 14 * @rem 18 * @rem;
    margin-top: 18 * @rem;
    .popup-subtitle {
      text-align: center;
      font-size: 10 * @rem;
      color: #974c37;
      padding: 15 * @rem 0 0 * @rem;
    }
    .task-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 15 * @rem;
      &:first-of-type {
        margin-top: 0;
      }
      .task-content {
        flex: 1;
        min-width: 0;
        .title,
        .desc {
          font-size: 12 * @rem;
          color: #934028;
          line-height: 18 * @rem;
        }
      }
      .get-btn {
        width: 72 * @rem;
        height: 30 * @rem;
        margin-left: 10 * @rem;
        &.no-get {
          .image-bg('~@/assets/images/july-activity/no_get_btn.png');
          background-size: 72 * @rem 30 * @rem;
        }
        &.can-get {
          .image-bg('~@/assets/images/july-activity/can_get_btn.png');
          background-size: 72 * @rem 30 * @rem;
        }
        &.had-get {
          .image-bg('~@/assets/images/july-activity/had_get_btn.png');
          background-size: 72 * @rem 30 * @rem;
        }
      }
    }
  }
}
.record-popup {
  .record-popup-close {
    width: 36 * @rem;
    height: 36 * @rem;
    .image-bg('~@/assets/images/july-activity/popup-close.png');
    background-size: 36 * @rem 36 * @rem;
    position: absolute;
    right: -6 * @rem;
    top: -10 * @rem;
  }
  .record-popup-title {
    width: 170 * @rem;
    height: 24 * @rem;
    .image-bg('~@/assets/images/july-activity/popup-title-record.png');
    background-size: 170 * @rem 24 * @rem;
    margin: 0 auto;
  }
  .record-list {
    box-sizing: border-box;
    border-radius: 18 * @rem;
    background: #fceed8;
    padding: 14 * @rem 0 * @rem 18 * @rem 14 * @rem;
    margin-top: 18 * @rem;
    height: 378 * @rem;
    overflow-y: auto;
    .record-item {
      display: flex;
      align-items: flex-start;
      line-height: 16 * @rem;
      margin-top: 12 * @rem;
      &:first-of-type {
        margin-top: 0;
      }
      .title {
        width: 150 * @rem;
        font-size: 12 * @rem;
        color: #934028;
      }
      .desc {
        flex: 1;
        min-width: 0;
        font-size: 12 * @rem;
        color: #ff6235;
        margin-left: 10 * @rem;
      }
    }
    &.empty {
      height: 378 * @rem;
      font-size: 14 * @rem;
      color: rgba(147, 64, 40, 0.4);
      text-align: center;
      line-height: 300 * @rem;
    }
  }
}
.result-lottie-popup {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 100;
  background: rgba(0, 0, 0, 0.65);
}
.result-lottie {
  width: 206 * @rem;
  height: auto;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
</style>
