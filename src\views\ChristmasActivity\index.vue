<template>
  <div class="christmas-activity-page page">
    <div class="main">
      <div class="top-bg">
        <div
          class="top-line"
          :style="{ backgroundColor: `rgba(18, 146, 201, ${navbarOpacity})` }"
        >
          <div class="back" @click="back"></div>
          <div class="user-container">
            <div v-if="userInfo.nickname" class="user">
              <user-avatar class="avatar"></user-avatar>
              <div class="nickname">{{ userInfo.nickname }}</div>
            </div>
            <div @click="login" v-else class="user btn">
              <div class="avatar img"></div>
              <div class="nickname">未登录</div>
            </div>
          </div>
          <div
            v-if="userInfo.nickname"
            @click="openRecordPopup"
            class="bell-bar btn"
          >
            <div class="bell-icon"></div>
            <div class="bell-num">{{ remain_num }}</div>
          </div>
        </div>
        <div class="activity-time">2023/12/23 00:00 - 2023/12/25 23:59</div>
        <div class="rule-btn btn" @click="rule_popup = true"></div>
        <div class="explain-btn btn" @click="explain_popup = true"></div>
        <div class="tree-container" :class="{ light: decorationAll }">
          <div class="tree" :class="{ on: decorationAll }"></div>
          <div class="star" v-if="decorationAll"></div>
          <div class="decoration-list">
            <div
              class="decoration-item"
              :class="
                step > index
                  ? `decoration-${index + 1}-on`
                  : `decoration-${index + 1}`
              "
              v-for="(item, index) in 9"
              :key="index"
            ></div>
          </div>
        </div>
      </div>
      <div class="section section-1">
        <div class="subtitle">完成每日任务即可装饰圣诞树领取铃铛奖励</div>
        <div class="task-list">
          <div
            class="task-item"
            v-for="(item, index) in task_list"
            :key="index"
          >
            <div class="title">{{ index + 1 }}. {{ item.title }}</div>
            <div
              @click="getTakeTask(item)"
              class="btn get-btn"
              :class="{
                'had-get': item.status == 2,
                'can-get': item.status == 1,
                'no-get': item.status == 0,
              }"
            ></div>
          </div>
        </div>
      </div>
      <div class="section section-2">
        <div class="subtitle">
          集齐9个圣诞树装饰，成为幸运之星赢取<span>圣诞大礼包</span>
        </div>
        <div class="lucky-content">
          圣诞大礼包：888绑定平台币+8000个圣诞铃铛+15天SVIP。
        </div>
        <div class="lucky-user">
          <div class="lucky-avatar" :class="{ on: lucky }">
            <img class="avatar" v-if="lucky" :src="lucky.avatar" alt="" />
            <div class="avatar-frame"></div>
          </div>
          <div class="lucky-nickname">
            {{ lucky ? lucky.nickname : '中奖者' }}
          </div>
        </div>
        <div class="lucky-tips">将在圣诞节当天23时50分自动抽取并公布</div>
      </div>
      <div class="section section-3">
        <div class="recharge-tips">
          活动期间，玩家实付充值可获得铃铛，RMB对铃铛的比例为1:100，即充值1元获得100铃铛。
        </div>
        <div class="recharge-tips">
          <span>温馨提示：</span
          >仅限游戏内使用<span>微信/支付宝</span>充值，如有使用金币/平台币/代金券抵扣的，则只计算抵扣后的现金充值金额。
        </div>
      </div>
      <div class="section section-4">
        <div class="gift-list">
          <div
            class="gift-item"
            v-for="(item, index) in exchange_list"
            :key="index"
          >
            <div class="gift-top">
              <div class="gift-icon">
                <img :src="item.icon" alt="" />
              </div>
              <div class="gift-content">
                {{ item.desc }}
              </div>
            </div>
            <div class="gift-bottom">
              <div class="cost">
                <div class="cost-text">消耗铃铛</div>
                <div class="cost-num">{{ item.num }}</div>
              </div>
              <div
                @click="getTakeExchange(item)"
                :class="{
                  'had-get': item.status == 2,
                  'can-get': item.status == 1,
                  'no-get': item.status == 0,
                }"
                class="exchange-btn btn"
              ></div>
            </div>
          </div>
        </div>
      </div>
      <div class="copy-right">本活动最终解释权归官方所有</div>
    </div>
    <!-- 规则弹窗 -->
    <van-popup
      v-model="rule_popup"
      :lock-scroll="false"
      class="popup rule-popup"
    >
      <div @click="rule_popup = false" class="close btn"></div>
      <div class="title">装饰圣诞树</div>
      <div class="text">
        1.活动期间，通过完成每日任务获得圣诞树装饰物件，集齐9个圣诞树装饰物件即可领取30绑定平台币。<br />2.每完成一个任务后即可获得一个圣诞树装饰物件，每次完成可以领取100个铃铛。
      </div>
      <div class="title">圣诞幸运星</div>
      <div class="text">
        1.在圣诞节当天23时50分，从已集齐9个圣诞树装饰物件的用户中，抽取一名幸运星，奖励圣诞大礼包：888绑定平台币+8000个圣诞铃铛+15天SVIP。
      </div>
      <div class="title">充值得铃铛</div>
      <div class="text">
        1.活动期间，玩家实付充值可获得铃铛，RMB对铃铛的比
        例为1:100，即充值1元获得100铃铛。<br />
        2.温馨提示：仅限游戏内使用微信/支付宝充值，如有使用
        金币/平台币/代金券抵扣的，则只计算抵扣后的现金充值金额。
      </div>
      <div class="title">圣诞礼盒</div>
      <div class="text">
        1.活动期间，玩家通过充值收集铃铛，消耗相应数量就可以换取圣诞礼盒哦！<br />2.每档奖励仅可兑换一次
      </div>
    </van-popup>
    <!-- 说明弹窗 -->
    <van-popup
      v-model="explain_popup"
      :lock-scroll="false"
      class="popup explain-popup"
    >
      <div @click="explain_popup = false" class="close btn"></div>
      <div class="text">
        1.
        活动期间充值完成后请返回本活动页面领取奖励，请及时兑换奖励，活动结束后将清空所有奖励兑换机会。
      </div>
      <div class="text">
        2.
        温馨提示：由于游戏厂商的要求，部分游戏内充值暂不支持使用金币支付，请先确定需要用金币充值的游戏在不在可使用名单内。<span
          class="btn"
          @click="game_dialog_show = true"
          >查看名单></span
        >
      </div>
    </van-popup>
    <!-- 记录弹窗 -->
    <van-popup
      v-model="record_popup"
      :lock-scroll="false"
      class="popup record-popup"
    >
      <div @click="record_popup = false" class="close btn"></div>
      <div class="record-list" v-if="record_list.length">
        <div
          v-for="(item, index) in record_list"
          :key="index"
          class="record-item"
        >
          <div class="title">{{ item.title }}</div>
          <div v-html="item.desc" class="desc"></div>
        </div>
      </div>
      <div v-else class="record-list empty">暂无奖励记录</div>
    </van-popup>
    <!-- 确认弹窗 -->
    <van-popup
      v-model="confirm_popup"
      :lock-scroll="false"
      :close-on-click-overlay="false"
      class="popup confirm-popup"
    >
      <div class="title">温馨提示</div>
      <div class="text">
        确认是否花费<span>{{ current_exchange.num }}铃铛</span>兑换{{
          current_exchange.desc
        }}
      </div>
      <div class="button-container">
        <div @click="confirm_popup = false" class="left-button btn">
          再考虑一下
        </div>
        <div @click="handleTakeExchange" class="right-button btn">确认兑换</div>
      </div>
    </van-popup>
    <!-- 查看名单弹窗 -->
    <van-dialog
      v-model="game_dialog_show"
      :lock-scroll="false"
      :show-confirm-button="false"
      :close-on-click-overlay="true"
      class="no-gold-game-popup"
    >
      <div class="search-container">
        <div class="close-search" @click="game_dialog_show = false"></div>
        <div class="search-bar">
          <div class="input-text">
            <form @submit.prevent="searchGame">
              <input
                type="text"
                v-model.trim="input_game"
                placeholder="输入游戏名"
              />
            </form>
          </div>
          <div class="search-btn" @click="searchGame">搜索</div>
        </div>
        <yy-list
          class="yy-list game-list"
          v-model="loading_obj"
          :finished="finished"
          @refresh="onRefresh"
          @loadMore="loadMore"
          :empty="!game_list.length"
          tips="请输入您想搜索的游戏"
          :check="false"
        >
          <div
            class="game-item btn"
            v-for="item in game_list"
            :key="item.id"
            @click="toGame(item)"
          >
            <div class="game-icon">
              <img :src="item.titlepic" alt="" />
            </div>
            <div class="right">
              <div class="game-name">{{ item.title }}</div>
              <div class="use-card" :class="{ can: item.use_gold_pay == 1 }">
                {{ item.use_gold_pay == 1 ? '' : '不' }}支持使用金币支付
              </div>
            </div>
          </div>
        </yy-list>
      </div>
    </van-dialog>
  </div>
</template>

<script>
import { BOX_login, BOX_goToGame, platform, boxInit } from '@/utils/box.uni.js';
import {
  ApiActivityChristmasIndex,
  ApiActivityChristmasTakeExchange,
  ApiActivityChristmasRewardLog,
  ApiActivityChristmasTask,
} from '@/api/views/christmas_activity.js';
import { ApiGameCardSearchGame } from '@/api/views/recharge.js';
import { mapActions } from 'vuex';

export default {
  data() {
    return {
      step: 0,
      task_list: [],
      lucky: null,
      remain_num: 0, //铃铛数
      activity_status: 0,
      exchange_list: [],
      finish_count: 0, //圣诞树点亮层数
      rule_popup: false,
      explain_popup: false,
      record_popup: false,
      record_list: [],
      confirm_popup: false,
      // 查看游戏相关数据
      game_list: [], //查看的游戏名单
      loading_obj: {
        loading: false,
        reloading: false,
      }, //loading对象
      input_game: '', //search的input框
      page: 1, //查看名单的页码
      listRows: 20, //查看名单的大小
      empty: false, //查看名单的空
      game_dialog_show: false, //查看名单弹窗
      finished: false, //防抖
      current_exchange: {}, //当前兑换信息
      navbarOpacity: 0,
      exchange_status: 0,
    };
  },
  computed: {
    decorationAll() {
      return this.step >= 9 ? true : false;
    },
    activity_status_text() {
      const arr = [
        '活动已开始！',
        '活动不存在！',
        '活动未开始！',
        '活动已结束！',
      ];
      return arr[this.activity_status - 1];
    },
  },
  created() {
    window.addEventListener('scroll', this.handleScroll);
  },
  beforeDestroy() {
    // 清除滚动事件监听
    window.removeEventListener('scroll', this.handleScroll);
  },
  async mounted() {
    // 官包触发页面刷新（将onResume挂载在window上）
    if (platform == 'android') {
      window.onResume = this.onResume;
    }

    // 页面数据初始化
    await this.getIndexData();
  },
  methods: {
    async onResume() {
      await boxInit();
      await this.SET_USER_INFO(true);
      await this.getIndexData();
    },
    async getIndexData() {
      const res = await ApiActivityChristmasIndex();
      let {
        task_list,
        lucky,
        activity_status,
        exchange_list,
        finish_count,
        remain_num,
        exchange_status,
      } = res.data;
      this.task_list = task_list;
      this.lucky = lucky;
      this.activity_status = activity_status;
      this.exchange_list = exchange_list;
      this.step = finish_count;
      this.remain_num = remain_num;
      this.exchange_status = exchange_status;
    },
    login() {
      BOX_login();
    },
    toGame(item) {
      BOX_goToGame(
        {
          params: {
            id: item.id,
          },
        },
        { id: item.id },
      );
    },
    async openRecordPopup() {
      this.$toast.loading({
        duration: 0,
        forbidClick: true,
      });
      await this.getRecordList();
      this.$toast.clear();
    },
    async getRecordList() {
      const res = await ApiActivityChristmasRewardLog();
      this.record_list = res.data.list;
      this.record_popup = true;
    },
    async getTakeTask(item) {
      if (this.activity_status != 1) {
        this.$toast(this.activity_status_text);
        return false;
      }
      if (!this.userInfo.token) {
        this.$toast('请登录');
        this.login();
        return false;
      }
      if (item.status == 0) {
        this.$toast('尚未达到领取条件');
        return false;
      }
      if (item.status == 2) {
        this.$toast('您已领取过了~');
        return false;
      }
      const res = await ApiActivityChristmasTask({
        task_id: item.task_id,
      });
      await this.getIndexData();
    },
    getTakeExchange(item) {
      if (this.activity_status != 1 && this.activity_status != 4) {
        this.$toast(this.activity_status_text);
        return false;
      }
      if (this.activity_status == 4 && this.exchange_status == 0) {
        this.$toast(this.activity_status_text);
        return false;
      }
      if (!this.userInfo.token) {
        this.$toast('请登录');
        this.login();
        return false;
      }
      if (item.status == 0) {
        this.$toast('尚未达到领取条件');
        return false;
      }
      if (item.status == 2) {
        this.$toast('您已领取过了~');
        return false;
      }
      this.current_exchange = item;
      this.confirm_popup = true;
    },
    async handleTakeExchange() {
      this.$toast.loading({
        message: '兑换中...',
        duration: 0,
      });
      const res = await ApiActivityChristmasTakeExchange({
        exchange_id: this.current_exchange.exchange_id,
      });
      this.confirm_popup = false;
      await this.getIndexData();
    },
    handleScroll() {
      let scrollTop =
        document.documentElement.scrollTop ||
        window.pageYOffset ||
        document.body.scrollTop;
      if (Math.abs(scrollTop) > 10) {
        this.navbarOpacity = Math.abs(scrollTop) / 120;
      } else {
        this.navbarOpacity = 0;
      }
    },
    // 搜索不可使用金币的游戏
    async searchGame() {
      if (!this.input_game) {
        this.$toast('请输入游戏名');
        return false;
      }
      this.$toast({
        type: 'loading',
        duration: 0,
        message: '拼命加载中...',
      });
      this.game_list = [];
      await this.getGameList();
    },
    async getGameList(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      let res = await ApiGameCardSearchGame({
        keyword: this.input_game,
        listRows: 20,
        page: this.page,
      });
      this.$toast.clear();
      let { list } = res.data;
      if (action === 1 || this.page === 1) {
        this.game_list = [];
      }
      if (!list.length) {
        this.empty = true;
      }
      this.game_list.push(...list);
      if (list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
    },
    async onRefresh() {
      try {
        await this.getGameList();
      } finally {
        this.loading_obj.reloading = false;
      }
    },
    async loadMore() {
      try {
        await this.getGameList(2);
      } finally {
        this.loading_obj.loading = false;
      }
    },
    ...mapActions({
      SET_USER_INFO: 'user/SET_USER_INFO',
    }),
  },
};
</script>

<style lang="less" scoped>
.christmas-activity-page {
  width: 100%;
  .back {
    width: 28 * @rem;
    height: 28 * @rem;
    margin-left: 20 * @rem;
    border-radius: 15 * @rem;
    background: rgba(0, 0, 0, 0.3) url(~@/assets/images/nav-bar-back-white.png)
      center center no-repeat;
    background-size: 8 * @rem 14 * @rem;
  }
  .main {
    background: #1292c9;
    padding-bottom: 20 * @rem;
    .top-bg {
      width: 100%;
      height: 597 * @rem;
      background: url('~@/assets/images/christmas-activity/christmas-bg.png')
        no-repeat;
      background-size: 100% 597 * @rem;
      position: relative;
      .top-line {
        display: flex;
        box-sizing: border-box;
        padding: 0 15 * @rem 0 0;
        width: 100%;
        height: 50 * @rem;
        position: fixed;
        z-index: 999;
        top: @safeAreaTop;
        top: @safeAreaTopEnv;
        left: 0 * @rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .user-container {
          flex: 1;
          width: 108 * @rem;
          height: 30 * @rem;
          margin-left: 11 * @rem;
          background: url('~@/assets/images/christmas-activity/user-bar.png')
            no-repeat;
          background-size: 108 * @rem 30 * @rem;
          color: #fff;
          line-height: 30 * @rem;
          .user {
            display: flex;
            align-items: center;
            .nickname {
              box-sizing: border-box;
              width: 78 * @rem;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              padding: 0 5 * @rem 0 5 * @rem;
              text-align: center;
            }
          }
          .avatar {
            width: 28 * @rem;
            height: 28 * @rem;
            border-radius: 50%;
            border: 1 * @rem solid transparent;
            margin-left: 1 * @rem;
          }
        }
        .bell-bar {
          display: flex;
          align-items: center;
          .bell-icon {
            width: 40 * @rem;
            height: 30 * @rem;
            background: url('~@/assets/images/christmas-activity/bell.png')
              no-repeat;
            background-size: 40 * @rem 30 * @rem;
            position: relative;
          }
          .bell-num {
            font-size: 16 * @rem;
            color: #ffffff;
            height: 28 * @rem;
            border-radius: 0 14 * @rem 14 * @rem 0;
            background: #1386b750;
            margin-left: -18 * @rem;
            padding: 0 10 * @rem 0 20 * @rem;
            line-height: 28 * @rem;
            font-weight: 600;
          }
        }
      }
      .activity-time {
        width: 271 * @rem;
        height: 25 * @rem;
        background: linear-gradient(
          270deg,
          rgba(19, 55, 183, 0) 0%,
          #1372b755 32%,
          #1386b755 79%,
          rgba(19, 104, 183, 0) 100%
        );
        position: absolute;
        top: 151 * @rem;
        left: 50%;
        transform: translateX(-50%);
        color: #fff;
        font-size: 12 * @rem;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 500;
      }
      .rule-btn {
        position: absolute;
        right: 0 * @rem;
        top: 163 * @rem;
        z-index: 2;
        width: 26 * @rem;
        height: 62 * @rem;
        .image-bg('~@/assets/images/christmas-activity/activity-rule.png');
        background-size: 26 * @rem 62 * @rem;
      }
      .explain-btn {
        position: absolute;
        right: 0 * @rem;
        top: 237 * @rem;
        z-index: 2;
        width: 26 * @rem;
        height: 62 * @rem;
        .image-bg('~@/assets/images/christmas-activity/activity-explain.png');
        background-size: 26 * @rem 62 * @rem;
      }
      .tree-container {
        width: 100%;
        height: 413 * @rem;
        background-size: 100% 413 * @rem;
        position: absolute;
        top: 160 * @rem;
        left: 0;
        &.light {
          .image-bg('~@/assets/images/christmas-activity/light.png');
        }
        .tree {
          width: 100%;
          height: 413 * @rem;
          .image-bg('~@/assets/images/christmas-activity/tree.png');
          background-size: 100% 413 * @rem;
          position: absolute;
          top: 0;
          left: 0;
          &.on {
            .image-bg('~@/assets/images/christmas-activity/tree-on.png');
            background-size: 100% 413 * @rem;
          }
        }
        .star {
          width: 327 * @rem;
          height: 272 * @rem;
          .image-bg('~@/assets/images/christmas-activity/star.png');
          background-size: 327 * @rem 272 * @rem;
          position: absolute;
          left: 13 * @rem;
          top: 48 * @rem;
        }
        .decoration-list {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 413 * @rem;
          .decoration-item {
            position: absolute;
            &.decoration-1 {
              width: 38 * @rem;
              height: 38 * @rem;
              .image-bg('~@/assets/images/christmas-activity/decoration-1.png');
              background-size: 38 * @rem 38 * @rem;
              left: 274 * @rem;
              top: 271 * @rem;
            }
            &.decoration-1-on {
              width: 38 * @rem;
              height: 38 * @rem;
              .image-bg('~@/assets/images/christmas-activity/decoration-1-on.png');
              background-size: 38 * @rem 38 * @rem;
              left: 274 * @rem;
              top: 271 * @rem;
            }
            &.decoration-2 {
              width: 45 * @rem;
              height: 66 * @rem;
              .image-bg('~@/assets/images/christmas-activity/decoration-2.png');
              background-size: 45 * @rem 66 * @rem;
              left: 175 * @rem;
              top: 248 * @rem;
            }
            &.decoration-2-on {
              width: 45 * @rem;
              height: 66 * @rem;
              .image-bg('~@/assets/images/christmas-activity/decoration-2-on.png');
              background-size: 45 * @rem 66 * @rem;
              left: 175 * @rem;
              top: 248 * @rem;
            }
            &.decoration-3 {
              width: 27 * @rem;
              height: 48 * @rem;
              .image-bg('~@/assets/images/christmas-activity/decoration-3.png');
              background-size: 27 * @rem 48 * @rem;
              left: 234 * @rem;
              top: 215 * @rem;
            }
            &.decoration-3-on {
              width: 27 * @rem;
              height: 48 * @rem;
              .image-bg('~@/assets/images/christmas-activity/decoration-3-on.png');
              background-size: 27 * @rem 48 * @rem;
              left: 234 * @rem;
              top: 215 * @rem;
            }
            &.decoration-4 {
              width: 41 * @rem;
              height: 54 * @rem;
              .image-bg('~@/assets/images/christmas-activity/decoration-4.png');
              background-size: 41 * @rem 54 * @rem;
              left: 104 * @rem;
              top: 211 * @rem;
            }
            &.decoration-4-on {
              width: 41 * @rem;
              height: 54 * @rem;
              .image-bg('~@/assets/images/christmas-activity/decoration-4-on.png');
              background-size: 41 * @rem 54 * @rem;
              left: 104 * @rem;
              top: 211 * @rem;
            }
            &.decoration-5 {
              width: 30 * @rem;
              height: 37 * @rem;
              .image-bg('~@/assets/images/christmas-activity/decoration-5.png');
              background-size: 30 * @rem 37 * @rem;
              left: 89 * @rem;
              top: 168 * @rem;
            }
            &.decoration-5-on {
              width: 30 * @rem;
              height: 37 * @rem;
              .image-bg('~@/assets/images/christmas-activity/decoration-5-on.png');
              background-size: 30 * @rem 37 * @rem;
              left: 89 * @rem;
              top: 168 * @rem;
            }
            &.decoration-6 {
              width: 30 * @rem;
              height: 25 * @rem;
              .image-bg('~@/assets/images/christmas-activity/decoration-6.png');
              background-size: 30 * @rem 25 * @rem;
              left: 145 * @rem;
              top: 161 * @rem;
            }
            &.decoration-6-on {
              width: 30 * @rem;
              height: 25 * @rem;
              .image-bg('~@/assets/images/christmas-activity/decoration-6-on.png');
              background-size: 30 * @rem 25 * @rem;
              left: 145 * @rem;
              top: 161 * @rem;
            }
            &.decoration-7 {
              width: 32 * @rem;
              height: 32 * @rem;
              .image-bg('~@/assets/images/christmas-activity/decoration-7.png');
              background-size: 32 * @rem 32 * @rem;
              left: 219 * @rem;
              top: 151 * @rem;
            }
            &.decoration-7-on {
              width: 32 * @rem;
              height: 32 * @rem;
              .image-bg('~@/assets/images/christmas-activity/decoration-7-on.png');
              background-size: 32 * @rem 32 * @rem;
              left: 219 * @rem;
              top: 151 * @rem;
            }
            &.decoration-8 {
              width: 44 * @rem;
              height: 45 * @rem;
              .image-bg('~@/assets/images/christmas-activity/decoration-8.png');
              background-size: 44 * @rem 45 * @rem;
              left: 123 * @rem;
              top: 104 * @rem;
            }
            &.decoration-8-on {
              width: 44 * @rem;
              height: 45 * @rem;
              .image-bg('~@/assets/images/christmas-activity/decoration-8-on.png');
              background-size: 44 * @rem 45 * @rem;
              left: 123 * @rem;
              top: 104 * @rem;
            }
            &.decoration-9 {
              width: 72 * @rem;
              height: 50 * @rem;
              .image-bg('~@/assets/images/christmas-activity/decoration-9.png');
              background-size: 72 * @rem 50 * @rem;
              left: 155 * @rem;
              top: 65 * @rem;
            }
            &.decoration-9-on {
              width: 72 * @rem;
              height: 50 * @rem;
              .image-bg('~@/assets/images/christmas-activity/decoration-9-on.png');
              background-size: 72 * @rem 50 * @rem;
              left: 155 * @rem;
              top: 65 * @rem;
            }
          }
        }
      }
    }
    .section {
      box-sizing: border-box;
      position: relative;
      width: 360 * @rem;
      margin: 10 * @rem auto 0;
      padding-top: 1 * @rem;
      .subtitle {
        width: 294 * @rem;
        height: 26 * @rem;
        border-radius: 4 * @rem;
        margin: 61 * @rem auto 0;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12 * @rem;
        color: #004142;
        background-color: rgba(2, 173, 120, 0.1);
        span {
          color: rgba(255, 77, 0, 1);
        }
      }
      &.section-1 {
        margin-top: -10 * @rem;
        height: 305 * @rem;
        background: url(~@/assets/images/christmas-activity/christmas-bg-1.png)
          center center no-repeat;
        background-size: 100% auto;
        .task-list {
          margin-top: 11 * @rem;
          .task-item {
            display: flex;
            align-items: center;
            padding: 0 22 * @rem;
            &:not(:first-of-type) {
              margin-top: 14 * @rem;
            }
            .title {
              flex: 1;
              flex-wrap: wrap;
              margin-right: 5 * @rem;
              font-size: 13 * @rem;
              color: rgba(0, 65, 66, 1);
              line-height: 16 * @rem;
            }
            .get-btn {
              flex: 0 0 66 * @rem;
              width: 66 * @rem;
              height: 30 * @rem;
              background-size: 66 * @rem 30 * @rem;
              background-position: center center;
              background-repeat: no-repeat;
              &.had-get {
                background-image: url(~@/assets/images/christmas-activity/had-get.png);
              }
              &.can-get {
                background-image: url(~@/assets/images/christmas-activity/can-get.png);
              }
              &.no-get {
                background-image: url(~@/assets/images/christmas-activity/no-get.png);
              }
            }
          }
        }
      }
      &.section-2 {
        height: 314 * @rem;
        background: url(~@/assets/images/christmas-activity/christmas-bg-2.png)
          center center no-repeat;
        background-size: 100% auto;
        .lucky-content {
          font-size: 12 * @rem;
          color: rgba(0, 65, 66, 1);
          line-height: 15 * @rem;
          margin-top: 10 * @rem;
          text-align: center;
        }
        .lucky-user {
          display: flex;
          flex-direction: column;
          align-items: center;
          margin: 8 * @rem auto 0;
          .lucky-avatar {
            width: 146 * @rem;
            position: relative;
            .avatar {
              width: 59 * @rem;
              height: 59 * @rem;
              position: absolute;
              border-radius: 50%;
              top: 22 * @rem;
              left: 44 * @rem;
            }
            .avatar-frame {
              position: relative;
              width: 146 * @rem;
              height: 102 * @rem;
              background: url(~@/assets/images/christmas-activity/user-frame-default.png)
                no-repeat;
              background-size: 146 * @rem 102 * @rem;
            }
            &.on {
              .avatar-frame {
                background: url(~@/assets/images/christmas-activity/user-frame.png)
                  no-repeat;
                background-size: 146 * @rem 102 * @rem;
              }
            }
          }
          .lucky-nickname {
            height: 19 * @rem;
            line-height: 19 * @rem;
            white-space: nowrap;
            min-width: 124 * @rem;
            text-align: center;
            font-size: 12 * @rem;
            color: rgba(0, 65, 66, 1);
            background: linear-gradient(
              270deg,
              rgba(19, 55, 183, 0) 0.37%,
              rgba(19, 114, 183, 0.2) 32.29%,
              rgba(19, 134, 183, 0.2) 78.65%,
              rgba(19, 104, 183, 0) 100%
            );
            margin: 4 * @rem auto 0;
            white-space: nowrap;
          }
        }
        .lucky-tips {
          font-size: 11 * @rem;
          color: rgba(0, 65, 66, 1);
          text-align: center;
          line-height: 14 * @rem;
          margin-top: 17 * @rem;
        }
      }
      &.section-3 {
        box-sizing: border-box;
        height: 208 * @rem;
        background: url(~@/assets/images/christmas-activity/christmas-bg-3.png)
          center center no-repeat;
        background-size: 100% auto;
        padding: 58 * @rem 32 * @rem 0;
        .recharge-tips {
          font-size: 12 * @rem;
          color: rgba(0, 65, 66, 1);
          line-height: 15 * @rem;
          margin-top: 16 * @rem;
          span {
            font-weight: 600;
            color: rgba(242, 88, 5, 1);
          }
        }
      }
      &.section-4 {
        height: 864 * @rem;
        background: url(~@/assets/images/christmas-activity/christmas-bg-4.png)
          center center no-repeat;
        background-size: 100% auto;
        .gift-list {
          display: flex;
          flex-wrap: wrap;
          justify-content: space-between;
          padding: 90 * @rem 20 * @rem 0;
          .gift-item {
            width: 155 * @rem;
            height: 175 * @rem;
            border-radius: 12 * @rem;
            overflow: hidden;
            box-shadow: 0px 0px 13px 0px rgba(67, 74, 255, 0.15);
            margin-top: 14 * @rem;
            &:nth-of-type(-n + 2) {
              margin-top: 0;
            }
            .gift-top {
              height: 114 * @rem;
              background: url(~@/assets/images/christmas-activity/gift-bg.png)
                no-repeat;
              background-size: 155 * @rem 114 * @rem;
              overflow: hidden;
              .gift-icon {
                width: 73 * @rem;
                height: 73 * @rem;
                margin: 5 * @rem auto 0;
              }
              .gift-content {
                font-size: 11 * @rem;
                color: rgba(255, 255, 255, 1);
                text-align: center;
                width: 125 * @rem;
                line-height: 14 * @rem;
                margin: 1 * @rem auto 0;
              }
            }
            .gift-bottom {
              padding: 10 * @rem 11 * @rem 0;
              background: #fff;
              display: flex;
              align-items: center;
              .cost {
                flex: 1;
                min-width: 0;
                .cost-text {
                  font-size: 10 * @rem;
                  line-height: 13 * @rem;
                  color: rgba(0, 65, 66, 1);
                }
                .cost-num {
                  font-size: 19 * @rem;
                  font-weight: 600;
                  color: rgba(245, 88, 0, 1);
                  margin-top: 2 * @rem;
                }
              }
              .exchange-btn {
                width: 60 * @rem;
                height: 30 * @rem;
                background: url(~@/assets/images/christmas-activity/get-btn.png)
                  center center no-repeat;
                background-size: 60 * @rem 30 * @rem;
                &.had-get {
                  background-image: url(~@/assets/images/christmas-activity/had-get2.png);
                }
                &.no-get {
                  background-image: url(~@/assets/images/christmas-activity/no-get2.png);
                }
              }
            }
          }
        }
      }
    }
    .copy-right {
      text-align: center;
      font-size: 12 * @rem;
      color: #0e303f;
      line-height: 30 * @rem;
      margin-top: 0 * @rem;
      opacity: 0.6;
    }
  }
}
.popup {
  width: 360 * @rem;
  background: transparent;
  box-sizing: border-box;
  padding: 72 * @rem 30 * @rem 0;
  .text {
    margin-bottom: 15 * @rem;
    font-size: 12 * @rem;
    color: #004142;
    line-height: 16 * @rem;
  }
  .close {
    position: absolute;
    top: -30 * @rem;
    right: 0;
    width: 25 * @rem;
    height: 25 * @rem;
    .image-bg('~@/assets/images/close-black.png');
  }
}
.rule-popup {
  height: 505 * @rem;
  .image-bg('~@/assets/images/christmas-activity/christmas-bg-6.png');
  .title {
    margin-bottom: 10 * @rem;
    font-size: 14 * @rem;
    font-weight: 600;
    color: #004142;
    line-height: 19 * @rem;
  }
}
.explain-popup {
  padding-top: 97 * @rem;
  height: 305 * @rem;
  .image-bg('~@/assets/images/christmas-activity/christmas-bg-7.png');
  span {
    color: #fa5a00;
    text-decoration: underline;
  }
}
.record-popup {
  padding-top: 78 * @rem;
  height: 290 * @rem;
  .image-bg('~@/assets/images/christmas-activity/christmas-bg-8.png');
  .record-list {
    height: 190 * @rem;
    overflow-y: scroll;
    .record-item {
      display: flex;
      align-items: flex-start;
      line-height: 16 * @rem;
      margin-top: 14 * @rem;
      &:first-of-type {
        margin-top: 0;
      }
      .title {
        flex: 1;
        min-width: 0;
        font-size: 12 * @rem;
        color: #004142;
      }
      .desc {
        width: 80 * @rem;
        font-size: 12 * @rem;
        margin-left: 10 * @rem;
        text-align: right;
      }
    }
  }
  .empty {
    height: 170 * @rem;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 18 * @rem;
    color: #004142;
  }
}
.confirm-popup {
  padding-top: 30 * @rem;
  width: 288 * @rem;
  height: 210 * @rem;
  .image-bg('~@/assets/images/christmas-activity/christmas-bg-5.png');
  .title {
    margin-bottom: 18 * @rem;
    font-size: 16 * @rem;
    font-weight: 600;
    color: #004142;
    line-height: 20 * @rem;
    text-align: center;
  }
  .text {
    height: 75 * @rem;
    margin-bottom: 0;
    font-size: 13 * @rem;
    color: #004142;
    line-height: 16 * @rem;
    span {
      color: #f8582e;
    }
  }
  .button-container {
    display: flex;
    justify-content: space-between;
    padding: 0 15 * @rem;
    .left-button {
      display: flex;
      justify-content: center;
      align-items: center;
      color: #1c7175;
      width: 85 * @rem;
      height: 31 * @rem;
      border-radius: 16 * @rem;
      border: 1 * @rem solid #1b6b58;
    }
    .right-button {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 85 * @rem;
      height: 31 * @rem;
      color: #fff;
      background: linear-gradient(180deg, #1e7db2 0%, #196232 100%);
      border: 1 * @rem solid;
      border-radius: 18 * @rem;
    }
  }
}
.no-gold-game-popup {
  overflow: unset;
  width: 320 * @rem;
  .search-container {
    box-sizing: border-box;
    width: 320 * @rem;
    height: 450 * @rem;
    padding: 24 * @rem 19 * @rem 10 * @rem;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: unset;
    .close-search {
      width: 24 * @rem;
      height: 24 * @rem;
      background: url(~@/assets/images/close-search.png) center center no-repeat;
      background-size: 24 * @rem 24 * @rem;
      position: absolute;
      right: -10 * @rem;
      top: -10 * @rem;
    }
    .search-bar {
      display: flex;
      align-items: center;
      .input-text {
        width: 240 * @rem;
        height: 35 * @rem;
        border: 1 * @rem solid #e5e5e5;
        border-radius: 18 * @rem;
        flex: 1;
        overflow: hidden;
        form {
          display: block;
          width: 100%;
          height: 100%;
        }
        input {
          box-sizing: border-box;
          display: block;
          width: 100%;
          height: 100%;
          padding: 0 18 * @rem;
          font-size: 15 * @rem;
          color: #333333;
          background-color: #f6f6f6;
        }
      }
      .search-btn {
        font-size: 15 * @rem;
        color: #666666;
        padding-left: 13 * @rem;
        height: 35 * @rem;
        line-height: 35 * @rem;
      }
    }
    .game-list {
      flex: 1;
      overflow: auto;
      margin-top: 10 * @rem;
      .game-item {
        display: flex;
        align-items: center;
        padding: 21 * @rem 0;
        border-bottom: 1 * @rem solid #eeeeee;
        .game-icon {
          width: 50 * @rem;
          height: 50 * @rem;
          border-radius: 10 * @rem;
          background-color: #b5b5b5;
        }
        .right {
          flex: 1;
          min-width: 0;
          margin-left: 10 * @rem;
          .game-name {
            font-size: 16 * @rem;
            font-weight: bold;
            color: #000000;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
          }
          .use-card {
            font-size: 12 * @rem;
            color: #f72e2e;
            margin-top: 10 * @rem;
            &.can {
              color: #36b150;
            }
          }
        }
      }
    }
  }
}
</style>
<style>
.desc .color1 {
  color: #00b95b;
}
.desc .color2 {
  color: #f8582e;
}
.van-popup {
  overflow: unset;
}
</style>
