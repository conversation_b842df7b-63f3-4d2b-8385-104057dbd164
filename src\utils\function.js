import { Toast, Dialog } from 'vant';
import store from '@/store';
import { request } from '@/api';
import router from '@/router';
import {
  platform,
  BOX_openInBrowser,
  BOX_openInNewWindow,
  BOX_checkInstall,
  authInfo,
} from '@/utils/box.uni.js';
import h5Page from '@/utils/h5Page';

import useConfirmDialog from '@/components/yy-confirm-dialog/index.js';
import useEwmPopup from '@/components/ewm-popup/index.js';

import { ApiUserInfoEx } from '@/api/views/users';

export function loginSuccess(res) {
  Toast.success('登录成功');
  store.commit('user/setUserInfo', res.data);

  ApiUserInfoEx().then(async res2 => {
    store.commit('user/setUserInfoEx', res2.data);
    await store.dispatch('system/SET_INIT_DATA');
    await store.dispatch('user/SET_USER_INFO');

    router.go(-1);
    localStorage.setItem('STORE', JSON.stringify(store.state));
  });
}

// 判断是否有安装支付方式
export function isInstallPayWay(payway) {
  if (
    (platform === 'android' || platform === 'androidBox') &&
    authInfo.versionCode < 4190
  ) {
    if (
      payway == 'zfb_dmf' &&
      !BOX_checkInstall('com.eg.android.AlipayGphone')
    ) {
      Dialog.alert({
        message: '您尚未安装支付宝应用，请先安装支付宝才能进行支付。',
        lockScroll: false,
        confirmButtonText: '我知道了',
      });
      return false;
    }
    if (payway == 'wx' && !BOX_checkInstall('com.tencent.mm')) {
      Dialog.alert({
        message: '您尚未安装微信应用，请先安装微信才能进行支付。',
        lockScroll: false,
        confirmButtonText: '我知道了',
      });
      return false;
    }
  }
  return true;
}

// 支付逻辑
export function pay() {
  const payWay = arguments[1].payWay;

  let isInstall = isInstallPayWay(payWay);
  if (!isInstall) {
    return false;
  }

  // 打开loading窗口
  const toast1 = Toast.loading({
    message: '加载中...',
    forbidClick: true,
    duration: 0,
  });

  return new Promise((resolve, reject) => {
    // 请求支付接口
    request(...arguments)
      .then(res => {
        toast1.clear();
        let { param_url, alipay_url, url, html, qrSrcUrl, pay_info } = res.data;
        // 做支付接口成功处理
        switch (payWay) {
          case 'wx': // 微信支付 ----------------------------------------
          case 'quick_wxpay': // 微信快支付 ----------------------------------------
            if (param_url) {
              let msg = '您尚未安装微信应用，请先安装微信才能进行支付。';
              // window.location.href = param_url;
              let can_open = BOX_openInBrowser(
                { h5_url: param_url, msg: msg },
                { url: param_url, msg: msg },
              );
              if (can_open) {
                setTimeout(() => {
                  Dialog.confirm({
                    message: '是否支付成功',
                    confirmButtonText: '是',
                    cancelButtonText: '否',
                    lockScroll: false,
                  })
                    .then(() => {
                      // on confirm
                      resolve(res);
                    })
                    .catch(() => {
                      resolve(res);
                    })
                    .finally(() => {
                      store.dispatch('user/SET_USER_INFO');
                    });
                }, 3000);
              } else {
                reject(res);
              }
            } else if (url) {
              if (url) {
                // on confirm
                if (platform === 'android' || platform === 'androidBox') {
                  BOX_openInNewWindow(
                    { h5_url: url, open_type: 1 },
                    { url: url },
                  );
                } else {
                  Dialog.confirm({
                    message: '微信需要跳转到外部支付，是否打开',
                    confirmButtonText: '是',
                    cancelButtonText: '否',
                    lockScroll: false,
                  })
                    .then(() => {
                      BOX_openInBrowser(
                        { h5_url: url, open_type: 1 },
                        { url: url },
                      );
                    })
                    .catch(() => {
                      resolve(res);
                    });
                }
                setTimeout(() => {
                  Dialog.confirm({
                    message: '是否支付成功',
                    confirmButtonText: '是',
                    cancelButtonText: '否',
                    lockScroll: false,
                  }).finally(() => {
                    resolve(res);
                    store.dispatch('user/SET_USER_INFO');
                  });
                }, 3000);
              }
              // window.location.href = url;
            }
            break;
          case 'zfb': // 支付宝支付 ------------------------------------------
            if (alipay_url) {
              useConfirmDialog({
                title: '温馨提示',
                desc: '即将前往充值页面完成充值',
                confirmText: '确定',
                onConfirm: () => {
                  BOX_openInBrowser(
                    { h5_url: alipay_url, open_type: 1 },
                    { url: alipay_url },
                  );
                  setTimeout(() => {
                    Dialog.confirm({
                      message: '是否支付成功',
                      confirmButtonText: '是',
                      cancelButtonText: '否',
                      lockScroll: false,
                    })
                      .then(() => {
                        // on confirm
                        resolve(res);
                      })
                      .catch(() => {
                        resolve(res);
                      })
                      .finally(() => {
                        store.dispatch('user/SET_USER_INFO');
                      });
                  }, 3000);
                },
              });
            }
            break;
          case 'zfb_dmf': // 支付宝支付当面付 ------------------------------------------
          case 'quick_alipay': //支付宝快支付 --------------------------------------------
            if (alipay_url) {
              let msg = '您尚未安装支付宝应用，请先安装支付宝才能进行支付。';
              let can_open = BOX_openInBrowser(
                { h5_url: alipay_url, msg: msg },
                { url: alipay_url, msg: msg },
              );
              if (can_open) {
                setTimeout(() => {
                  Dialog.confirm({
                    message: '是否支付成功',
                    confirmButtonText: '是',
                    cancelButtonText: '否',
                    lockScroll: false,
                  })
                    .then(() => {
                      // on confirm
                      resolve(res);
                    })
                    .catch(() => {
                      resolve(res);
                    })
                    .finally(() => {
                      store.dispatch('user/SET_USER_INFO');
                    });
                }, 3000);
              } else {
                reject(res);
              }
            } else if (html) {
              // 如果是有html（即支付宝h5支付类型）
              /* 此处form就是后台返回接收到的数据 */
              const div = document.createElement('div');
              div.setAttribute('class', 'yy-alipay');
              div.innerHTML = res.data.html;
              document.body.appendChild(div);
              document.forms[0].submit();
              setTimeout(() => {
                Dialog.confirm({
                  message: '是否支付成功',
                  confirmButtonText: '是',
                  cancelButtonText: '否',
                  lockScroll: false,
                })
                  .then(() => {
                    // on confirm
                    resolve(res);
                  })
                  .catch(() => {
                    resolve(res);
                  })
                  .finally(() => {
                    store.dispatch('user/SET_USER_INFO');
                    document.getElementsByClassName('yy-alipay')[0].remove();
                  });
              }, 3000);
            }
            break;
          case 'wx_ewm': // 微信二维码支付 ---------------------------------------
          case 'zfb_dmf_ewm': // 支付宝二维码支付 ------------------------------------
          case 'quick_ewm': // 微信/支付宝/云闪付二维码快支付 ----------------------------------------
            if (!qrSrcUrl) {
              Toast('暂不支持扫码支付');
              return false;
            }
            setTimeout(() => {
              // 延时器保证二维码在下面的确认框上层(不然会被遮住)
              useEwmPopup({
                ewmSrc: qrSrcUrl,
                payInfo: pay_info,
                onClose: () => {
                  resolve(res);
                },
              });
            }, 0);
            break;
          case 'gold': // 金币抵扣 ---------------------------------------
          case 'ptb': // 平台币支付 ------------------------------------
            // 支付方式是平台币或者金币的话直接显示支付结果(isPay字段有值)并跳转支付成功页面
            Toast.success('支付成功');
            resolve(res);
            break;
          case 'paypal': // paypal支付 ------------------------------------
            if (url) {
              Dialog.confirm({
                message: 'paypal需要跳转到外部支付，是否打开',
                confirmButtonText: '是',
                cancelButtonText: '否',
                lockScroll: false,
              })
                .then(() => {
                  // on confirm
                  BOX_openInBrowser(
                    { h5_url: url, open_type: 1 },
                    { url: url },
                  );
                  setTimeout(() => {
                    Dialog.confirm({
                      message: '是否支付成功',
                      confirmButtonText: '是',
                      cancelButtonText: '否',
                      lockScroll: false,
                    }).finally(() => {
                      resolve(res);
                      store.dispatch('user/SET_USER_INFO');
                    });
                  }, 3000);
                })
                .catch(() => {
                  resolve(res);
                });
            }
            break;
          case 'mycard': // mycard支付 ------------------------------------
            if (url) {
              Dialog.confirm({
                message: 'mycard需要跳转到外部支付，是否打开',
                confirmButtonText: '是',
                cancelButtonText: '否',
                lockScroll: false,
              })
                .then(() => {
                  // on confirm
                  BOX_openInBrowser(
                    { h5_url: url, open_type: 1 },
                    { url: url },
                  );
                  setTimeout(() => {
                    Dialog.confirm({
                      message: '是否支付成功',
                      confirmButtonText: '是',
                      cancelButtonText: '否',
                      lockScroll: false,
                    }).finally(() => {
                      resolve(res);
                      store.dispatch('user/SET_USER_INFO');
                    });
                  }, 3000);
                })
                .catch(() => {
                  resolve(res);
                });
            }
            break;
          case 'ysf':
            if (url) {
              Dialog.confirm({
                message: '云闪付需要跳转到外部支付，是否打开',
                confirmButtonText: '是',
                cancelButtonText: '否',
                lockScroll: false,
              })
                .then(() => {
                  // on confirm
                  BOX_openInBrowser(
                    { h5_url: url, open_type: 1 },
                    { url: url },
                  );
                  setTimeout(() => {
                    Dialog.confirm({
                      message: '是否支付成功',
                      confirmButtonText: '是',
                      cancelButtonText: '否',
                      lockScroll: false,
                    }).finally(() => {
                      resolve(res);
                      store.dispatch('user/SET_USER_INFO');
                    });
                  }, 3000);
                })
                .catch(() => {
                  resolve(res);
                });
            }
            break;
          default:
            if (url) {
              BOX_openInBrowser({ h5_url: url }, { url: url });
              setTimeout(() => {
                Dialog.confirm({
                  message: '是否支付成功',
                  confirmButtonText: '是',
                  cancelButtonText: '否',
                  lockScroll: false,
                })
                  .then(() => {
                    // on confirm
                    resolve(res);
                  })
                  .catch(() => {
                    resolve(res);
                  })
                  .finally(() => {
                    store.dispatch('user/SET_USER_INFO');
                  });
              }, 3000);
            }
            break;
        }
        return false;
      })
      .catch(res => {
        // 做支付接口失败处理
        reject(res);
        toast1.clear();
      });
  });
}

// 获取链接所带参数(根据key获取value)
export function getQueryVariable(variable) {
  let query = window.location.search.substring(1);
  let vars = query.split('&');
  for (let i = 0; i < vars.length; i++) {
    let pair = vars[i].split('=');
    if (pair[0] == variable) {
      return pair[1];
    }
  }
  return false;
}
// 判断url是否含有参数
export function hasUrlQuery(url) {
  if (!url) {
    let query = window.location.search.substring(1);
    if (query) return true;
  } else {
    let index = url.indexOf('?');
    if (index != -1) return true;
  }
  return false;
}
// 日志打印
export function devLog() {
  if (
    process.env.NODE_ENV == 'development' ||
    h5Page.env == 'aa' ||
    h5Page.env == 'cc'
  ) {
    window.console.log(...arguments);
  }
}

// 一天中0时的时间戳
export function getDayZeroTimestamp(timestamp) {
  // 秒
  timestamp = Number(timestamp) * 1000;
  return new Date(new Date(timestamp).toLocaleDateString()).getTime();
}

// 判断当前处于什么环境
export function envFun() {
  let url = window.location.href;
  let arr = url.split('.');

  if (process.env.NODE_ENV === 'development') {
    // 在这里调环境 'aa' 'cc' '';
    return 'aa';
  }

  if (arr[0].indexOf('aa') > -1) {
    return 'aa';
  } else if (arr[0].indexOf('cc') > -1) {
    return 'cc';
  } else {
    return '';
  }
}


/**
 * 尝试打开APP scheme 如失败则执行回调函数
 * @param {string} schemeUrl - APP的scheme链接 如 "a3733://action?"
 * @param {function} fallBack - 回调函数
 * @param {number} timeOut - 超时时间
 * @param {boolean} isLoading - 是否显示loading
 */
export function openSchemeWithCheck(schemeUrl = 'a3733://action?', fallBack, timeOut = 3000, isLoading = true) {
  let toast = null;
  if (isLoading) {
    toast = Toast.loading({
      forbidClick: true,
      duration: 0,
    });
  }

  const hiddenProperty =
    'hidden' in document ? 'hidden' :
      'msHidden' in document ? 'msHidden' :
        'webkitHidden' in document ? 'webkitHidden' :
          null;

  const visibilityChangeEvent =
    hiddenProperty === 'hidden' ? 'visibilitychange' :
      hiddenProperty === 'msHidden' ? 'msvisibilitychange' :
        hiddenProperty === 'webkitHidden' ? 'webkitvisibilitychange' :
          null;

  if (!hiddenProperty || !visibilityChangeEvent) {
    if (toast) toast.clear();
    fallBack();
    return;
  }

  let hasHidden = false;

  function handleVisibilityChange() {
    if (document[hiddenProperty]) {
      hasHidden = true;
      clearTimeout(timer);
      if (toast) toast.clear();
    }
  }

  document.addEventListener(visibilityChangeEvent, handleVisibilityChange);

  // 设置超时定时器
  const timer = setTimeout(() => {
    document.removeEventListener(visibilityChangeEvent, handleVisibilityChange);
    if (toast) toast.clear();
    if (!hasHidden) {
      fallBack();
    }
  }, timeOut);

  try {
    window.location.href = schemeUrl;
  } catch (e) {
    console.warn('跳转 scheme 失败', e);
    if (toast) toast.clear();
    clearTimeout(timer);
    fallBack();
  }
}
