import { mapGetters } from 'vuex';
import { BOX_close, platform } from './box.uni';
export default {
  computed: {
    ...mapGetters({
      userInfo: 'user/userInfo',
      appName: 'system/appName',
      defaultAvatar: 'system/defaultAvatar',
      isHw: 'system/isHw',
    }),
  },
  methods: {
    toPage(name, params = {}, replace = 0) {
      replace
        ? this.$router.replace({ name: name, params: params })
        : this.$router.push({ name: name, params: params });
    },
    back() {
      if (window.sessionStorage.firstUrl === window.location.href) {
        // 首次进入的页面
        if (platform == 'android') {
          // 安卓--关闭窗口
          BOX_close();
        } else {
          // 其他-- 返回首页
          this.toPage('QualitySelect');
        }
      } else {
        this.$router.back();
      }
    },
  },
};
