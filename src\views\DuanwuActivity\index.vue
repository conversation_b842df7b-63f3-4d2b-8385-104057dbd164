<template>
  <div class="duanwu-activity">
    <nav-bar-2
      ref="topNavBar"
      bgStyle="transparent-white"
      :azShow="true"
      :placeholder="false"
    ></nav-bar-2>
    <div @click="rule_popup = true" class="rule btn"></div>
    <div @click="probability_popup = true" class="probability btn"></div>
    <div @click="getRecordList" class="exchange-record btn"></div>
    <div class="user-container">
      <div v-if="userInfo.token" class="user">
        <UserAvatar class="avatar" />
        <div class="nickname text">{{ userInfo.nickname }}</div>
      </div>
      <div @click="login" v-else class="user no-login btn">
        <div class="avatar2"></div>
        <div class="text">未登录</div>
      </div>
    </div>
    <div class="point-container">
      <div class="point">{{ point }}</div>
    </div>
    <div class="active-time">06月22日00:00 - 06月25日23:59</div>
    <main>
      <div class="bg1">
        <div class="container1">
          <div class="text">
            五月五，过端午！迎接端午，除了吃粽子，还少不了紧张刺激的龙舟比赛，快来参与划龙舟活动，领取缤纷好礼
          </div>
          <div @click="getzongzi_popup = true" class="button btn"></div>
        </div>
      </div>
      <div class="bg2">
        <div class="game">
          <div class="game-bg">
            <div :style="{ left: `${percentage}%` }" class="pic"></div>
          </div>
          <div class="progress-container">
            <div class="progress">
              <div
                v-if="prize_list.length > 0"
                :style="{ width: `${percentage}%` }"
                class="content"
              ></div>
            </div>
            <div class="list">
              <div
                v-for="item in this.prize_list.slice(
                  0,
                  this.prize_list.length - 1,
                )"
                :key="item.reward_id"
                class="item"
              >
                <div class="big-text">{{ item.need_num }}</div>
                <div class="small-text">{{ item.title }}</div>
              </div>
            </div>
          </div>
        </div>
        <div class="container2">
          <div v-for="item in prize_list" :key="item.reward_id" class="item">
            <div class="task-top">
              <div class="task-title">
                {{ item.title }}：<span
                  class="small-text"
                  v-html="item.desc"
                ></span>
              </div>
              <div
                v-if="item.need_num != 0"
                :class="{
                  empty: item.reward_status == 0,
                  already: item.reward_status == 2,
                }"
                @click="takeReward(item.reward_status, item.reward_id)"
                class="task-button"
              ></div>
            </div>
            <div v-if="item.need_num != 0" class="task-bottom">
              <span class="color">奖励：</span>{{ item.reward_desc }}
            </div>
          </div>
        </div>
      </div>
      <div class="bg3">
        <div
          @click="openRankPopup"
          :class="{ empty: is_pk == 0 }"
          class="rank-button btn"
        >
          我的阵营排行
        </div>
        <div class="pic-container">
          <div class="left">
            <div class="text">粽子数：{{ camp_sweet_count }}</div>
            <div
              @click="openCampPopup(1)"
              :class="{
                empty: point <= 50,
                already: is_pk == 1,
                empty2: is_pk == 2,
                btn: is_pk != 2,
              }"
              class="button"
            >
              {{ point <= 50 ? '加入阵营' : '' }}
            </div>
          </div>
          <div class="right">
            <div class="text">粽子数：{{ camp_all_count }}</div>
            <div
              @click="openCampPopup(2)"
              :class="{
                empty: point <= 50,
                already: is_pk == 2,
                empty2: is_pk == 1,
                btn: is_pk != 1,
              }"
              class="button"
            >
              {{ point <= 50 ? '加入阵营' : '' }}
            </div>
          </div>
        </div>
        <div class="explain">注：粽子数大于50才可选择加入阵营</div>
        <div class="article">
          <div class="text">
            截止至2023/6/25 23:50
            粽子总合高的一方获胜，时间截止后获取的粽子将不再计入总数和排行，活动结束后自动发放奖励。
          </div>
          <div class="text">
            <span class="color">获胜的阵营</span
            >，粽子数量第一的用户可获得888平台币+14天SVIP，前2-10的用户可获得3888金币，其余用户将获得888金币。
          </div>
          <div class="text">
            <span class="color">落败的阵营</span
            >，粽子数量第一的用户可获得588平台币+7天SVIP，前2-10的用户可获得1888金币，其余用户将获得588金币。
          </div>
          <div class="text">
            温馨提醒：排行榜10分钟刷新一次，截止时间2023/6/25 23:50
            前10分钟将无法再加入阵营。请您及时选择心仪阵营，错过时间将无法获得奖励。若阵营内有出现粽子最高数量相同的，则加入时间更早的用户为第一。
          </div>
        </div>
      </div>
    </main>
    <!-- 活动规则 -->
    <van-popup
      :lock-scroll="false"
      v-model="rule_popup"
      class="popup rule-popup"
    >
      <div class="text">
        1.活动期间充值完成后请返回本活动页面领取奖励，请及时领取累计奖励，活动结束后将清空所有粽子和奖励领取机会。
      </div>
      <div class="text">
        2.
        温馨提示：由于游戏厂商的要求，部分游戏内充值暂不支持使用金币支付，请先确定需要用金币充值的游戏在不在可使用名单内。<span
          class="btn"
          @click="game_dialog_popup = true"
          >查看名单></span
        >
      </div>
    </van-popup>
    <!-- 概率公示 -->
    <van-popup
      :lock-scroll="false"
      v-model="probability_popup"
      class="popup probability-popup"
    >
      <div class="small-title">一马当先：</div>
      <div class="text">
        <span>概率：</span>188金币 (80%)、288金币+1天SVIP (15%)、88平台币 (5%)
      </div>
      <div class="small-title">乘风破浪：</div>
      <div class="text">
        <span>概率：</span>388金币 (80%)、588金币+3天SVIP (15%)、188平台币 (5%)
      </div>
      <div class="small-title">气势如虹：</div>
      <div class="text">
        <span>概率：</span>688金币 (80%)、888金币+7天SVIP (15%)、388平台币 (5%)
      </div>
      <div class="small-title">冲向终点：</div>
      <div class="text">
        <span>概率：</span>1888金币 (70%)、3888金币+14天SVIP (20%)、588平台币
        (10%)
      </div>
      <div class="small-title">龙舟宝箱：</div>
      <div class="text">
        <span>概率：</span>2888金币 (70%)、30天SVIP (20%)、888平台币（10%）
      </div>
    </van-popup>
    <!-- 获取粽子 -->
    <van-popup
      :lock-scroll="false"
      v-model="getzongzi_popup"
      class="popup getzongzi-popup"
    >
      <div class="tab-list">
        <div
          @click="getzongzi_tab_current = 0"
          :class="{ current: getzongzi_tab_current == 0 }"
          class="tab-item"
        >
          粽子补给站
        </div>
        <div
          @click="getzongzi_tab_current = 1"
          :class="{ current: getzongzi_tab_current == 1 }"
          class="tab-item right-tab-item"
        >
          累计充值奖励
        </div>
      </div>
      <div v-show="getzongzi_tab_current == 0" class="tab-content">
        <div class="list">
          <div v-for="(item, index) in task_list2" :key="index" class="item">
            <div class="left">
              <div class="text">
                {{ index + 1 }}.{{ item.title }}，可领取{{ item.num }}个粽子
              </div>
              <div class="text">（{{ item.desc }}）</div>
            </div>
            <div
              :class="{
                empty: item.task_status == 0,
                already: item.task_status == 2,
              }"
              @click="handleTakeTask(item.task_status, item.task_id)"
              class="right"
            ></div>
          </div>
        </div>
        <div class="explain">
          温馨提示：仅限游戏内使用<span>微信/支付宝</span>充值，如有使用金币/平台币/代金券抵扣的，则只计算抵扣后的现金充值金额。
        </div>
      </div>
      <div v-show="getzongzi_tab_current == 1" class="tab-content">
        <div class="list">
          <div v-for="(item, index) in task_list" :key="index" class="item">
            <div class="left">{{ index + 1 }}.{{ item.title }}</div>
            <div
              :class="{
                empty: item.task_status == 0,
                already: item.task_status == 2,
              }"
              @click="handleTakeTask(item.task_status, item.task_id)"
              class="right"
            ></div>
          </div>
        </div>
        <div class="explain">
          温馨提示：仅限游戏内使用<span>微信/支付宝</span>充值，如有使用金币/平台币/代金券抵扣的，则只计算抵扣后的现金充值金额。（以上各档奖励，活动期间每个用户仅可领取一次）
        </div>
      </div>
    </van-popup>
    <!-- 加入阵营 -->
    <van-popup
      :lock-scroll="false"
      v-model="camp_popup"
      class="popup camp-popup"
    >
      <div class="title">提示</div>
      <div class="text">
        加入阵营后将不可更改，确认加入{{
          current_camp == 1 ? '甜' : '咸'
        }}粽子阵营？
      </div>
      <div class="button-container">
        <div @click="camp_popup = false" class="left-button btn"></div>
        <div @click="handleJoinCamp" class="right-button btn"></div>
      </div>
    </van-popup>
    <!-- 排行 -->
    <van-popup
      :lock-scroll="false"
      v-model="rank_popup"
      class="popup rank-popup"
    >
      <div class="rank">
        <div class="big-text">
          {{ is_pk == 1 ? ' 甜' : '咸' }}阵营粽子数排行
        </div>
        <div class="rank-title">
          <div class="left">排名</div>
          <div class="center">用户昵称</div>
          <div class="right">粽子数</div>
        </div>
        <div class="rank-list">
          <div
            v-for="(item, index) in rank_list"
            :key="index"
            class="rank-item"
          >
            <div class="left">
              <span :class="{ num: index <= 2 }">{{ index + 1 }}</span>
            </div>
            <div class="center">{{ item.user.nickname }}</div>
            <div class="right">{{ item.num }}</div>
          </div>
        </div>
      </div>
      <div class="user">
        <UserAvatar class="avatar" />
        <div class="center">
          我的粽子数：<span>{{ point }}</span>
        </div>
        <div class="my-rank">
          排名：<span>{{ my_rank }}</span>
        </div>
      </div>
    </van-popup>
    <!-- 领取记录 -->
    <van-popup
      v-model="record_list_popup"
      :lock-scroll="false"
      class="popup record-list-popup"
    >
      <div class="title"></div>
      <div v-if="record_list.length > 0" class="list">
        <div v-for="(item, index) in record_list" :key="index" class="item">
          <div class="left">{{ item.title }}</div>
          <div v-html="item.num" class="right"></div>
        </div>
      </div>
      <div v-else class="empty">暂无领取记录</div>
    </van-popup>
    <noGameList
      :game_dialog_show="game_dialog_popup"
      @changeGameDialogShow="changeGameDialogShow"
    />
  </div>
</template>
<script>
import { platform, boxInit } from '@/utils/box.uni.js';
import UserAvatar from '@/components/user-avatar';
import { BOX_login } from '@/utils/box.uni.js';
import {
  ApiDWIndex,
  ApiDWTaskTake,
  ApiDWExchangeRank,
  ApiDWJoinCamp,
  ApiDWTaskReward,
  ApiDWRecordList,
} from '@/api/views/duanwu_activity';
import { mapGetters } from 'vuex';
import noGameList from '@/components/no-game-list';

export default {
  data() {
    return {
      activity_status: 3, //1活动已开始 2 活动不存在 3活动未开始 4活动已结束
      prize_list: [], //奖励列表
      task_list: [], //累计任务列表
      point: 0, //当前积分
      take_list: [], //累计任务列表
      task_list2: [], //每日任务列表
      finished: false, //ajax防卡
      camp_sweet_count: 0,
      camp_all_count: 0,
      rule_popup: false, //规则弹窗
      game_dialog_popup: false, //游戏弹窗
      probability_popup: false, //概率弹窗
      is_pk: 0, //当前加入阵营，0没加1舔2咸
      current_camp: 1, //当前选择加入的阵营1甜2咸
      camp_popup: false, //阵营弹窗
      getzongzi_popup: false, //获取粽子弹窗
      getzongzi_tab_current: 0, //获取粽子弹窗当前tab
      rank_popup: false, //排行弹窗
      rank_list: [], //排行榜
      my_rank: 0, //个人排行
      record_list_popup: false, //领取记录弹窗
      record_list: [], //领取记录列表
    };
  },
  computed: {
    active_status_text() {
      const arr = ['活动已开始', '活动不存在', '活动未开始', '活动已结束'];
      return arr[this.activity_status - 1];
    },
    percentage() {
      let temp = 0;
      if (this.point >= 150) {
        return 100;
      }
      let temp_arr = this.prize_list.slice(0, this.prize_list.length - 1);
      for (let i = 0; i < temp_arr.length; i++) {
        // 判断他在哪一个区间内
        if (
          temp_arr[i].need_num <= this.point &&
          i < temp_arr.length - 1 &&
          temp_arr[i + 1].need_num >= this.point
        ) {
          temp =
            (100 / (temp_arr.length - 1)) * i +
            ((this.point - temp_arr[i].need_num) /
              (temp_arr[i + 1].need_num - temp_arr[i].need_num)) *
              100 *
              (1 / (temp_arr.length - 1));
        }
      }
      return temp;
    },
    ...mapGetters({
      userInfo: 'user/userInfo',
    }),
  },
  async mounted() {
    // 官包触发页面刷新（将onResume挂载在window上）
    if (platform == 'android') {
      window.onResume = this.onResume;
    }
    await this.getActivityInfo();
  },
  methods: {
    async onResume() {
      await boxInit();
      await this.getActivityInfo();
      this.SET_USER_INFO(true);
    },
    async getActivityInfo() {
      const res = await ApiDWIndex();
      this.point = res.data.user_num;
      this.is_pk = res.data.is_pk;
      this.camp_all_count = res.data.camp_all_count;
      this.camp_sweet_count = res.data.camp_sweet_count;
      this.activity_status = res.data.activity_status;
      this.prize_list = res.data.reward_list;
      this.task_list2 = res.data.supply_list;
      this.task_list = res.data.only_list;
    },
    login() {
      BOX_login();
    },
    changeGameDialogShow(show) {
      this.game_dialog_popup = show;
    },
    openCampPopup(camp) {
      if (this.point <= 50) {
        return false;
      }
      if (this.is_pk != 0) {
        this.$toast(
          `您当前已加入${this.is_pk == 1 ? '舔' : '咸'}粽子阵营，请勿重复加入`,
        );
        return false;
      }
      this.current_camp = camp;
      this.camp_popup = true;
    },
    async openRankPopup() {
      if (!this.userInfo.token) {
        this.login();
        return false;
      }
      if (this.activity_status != 1 && this.activity_status != 4) {
        this.$toast(this.active_status_text);
        return false;
      }
      if (this.is_pk == 0) {
        this.$toast('您尚未加入阵营');
        return false;
      }
      this.$toast({
        type: 'loading',
        duration: 0,
        message: '拼命加载中...',
      });
      try {
        const res = await ApiDWExchangeRank();
        this.$toast.clear();
        this.rank_list = res.data.list;
        this.my_rank = res.data.my_rank.rank;
        this.point = res.data.my_rank.sum_num;
        this.rank_popup = true;
      } finally {
        // this.$toast.clear();
      }
    },
    async handleTakeTask(status, id) {
      if (!this.userInfo.token) {
        this.login();
        return false;
      }
      if (this.activity_status != 1) {
        this.$toast(this.active_status_text);
        return false;
      }
      if (status != 1) {
        this.$toast(`您${status == 0 ? '未达成领取资格' : '已领取过奖励'}`);
        return false;
      }
      this.$toast({
        type: 'loading',
        duration: 0,
        message: '拼命加载中...',
      });
      const res = await ApiDWTaskTake({ task_id: id });
      this.getActivityInfo();
    },
    async takeReward(status, id) {
      if (!this.userInfo.token) {
        this.login();
        return false;
      }
      if (this.activity_status != 1) {
        this.$toast(this.active_status_text);
        return false;
      }
      if (status != 1) {
        this.$toast(`您${status == 0 ? '未达成领取资格' : '已领取过奖励'}`);
        return false;
      }
      this.$toast({
        type: 'loading',
        duration: 0,
        message: '拼命加载中...',
      });
      const res = await ApiDWTaskReward({ reward_id: id });
      this.getActivityInfo();
    },
    async handleJoinCamp() {
      const res = await ApiDWJoinCamp({ camp_type: this.current_camp });
      this.camp_popup = false;
      this.getActivityInfo();
    },
    // 获取记录列表
    async getRecordList() {
      if (!this.userInfo.token) {
        this.login();
        return false;
      }
      if (this.activity_status != 1 && this.activity_status != 4) {
        this.$toast(this.active_status_text);
        return false;
      }
      this.$toast({
        type: 'loading',
        duration: 0,
        message: '拼命加载中...',
      });
      try {
        const res = await ApiDWRecordList();
        this.record_list = res.data.list;
        this.record_list_popup = true;
      } finally {
        this.$toast.clear();
      }
    },
  },
  components: {
    UserAvatar,
    noGameList,
  },
};
</script>
<style lang="less" scoped>
.duanwu-activity {
  position: relative;
  min-height: 100vh;
  background-color: rgba(10, 107, 129, 1);
  overflow: hidden;

  .active-time {
    position: absolute;
    top: 130 * @rem;
    left: 50%;
    transform: translate(-50%, 0);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 205 * @rem;
    height: 25 * @rem;
    color: #fff;
    font-size: 12 * @rem;
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 15 * @rem;
    z-index: 100;
  }

  .user-container {
    .user {
      position: absolute;
      top: 10 * @rem;
      left: 85 * @rem;
      z-index: 1000;
      height: 32 * @rem;
      color: #fff;
      line-height: 22 * @rem;
      border-radius: 20 * @rem;
      &.no-login {
        left: 70 * @rem;
      }
      .avatar2 {
        position: absolute;
        top: -1 * @rem;
        left: -10 * @rem;
        width: 34 * @rem;
        height: 34 * @rem;
        .image-bg('~@/assets/images/duanwu-activity/dw_bg7.png');
      }

      .text {
        width: 108 * @rem;
        height: 34 * @rem;
        text-align: center;
        line-height: 32 * @rem;
        font-size: 12 * @rem;
        .image-bg('~@/assets/images/duanwu-activity/dw_bg5.png');
        white-space: nowrap;
        &.nickname {
          font-size: 11 * @rem;
        }
      }
    }

    .avatar {
      position: absolute;
      top: -1 * @rem;
      left: -25 * @rem;
      width: 34 * @rem;
      height: 34 * @rem;
      border-radius: 50%;
    }
  }

  .point-container {
    position: absolute;
    top: 12 * @rem;
    right: 0 * @rem;
    z-index: 1000;
    display: flex;
    align-items: center;
    width: 79 * @rem;
    height: 29 * @rem;
    .image-bg('~@/assets/images/duanwu-activity/dw_bg6.png');

    .point {
      flex: 1;
      white-space: nowrap;
      font-size: 15 * @rem;
      padding-right: 8 * @rem;
      text-align: right;
      line-height: 34 * @rem;
      font-weight: bold;
      color: #ffffff;
    }
  }

  .rule {
    position: absolute;
    top: 121 * @rem;
    right: 0 * @rem;
    z-index: 1000;
    width: 27 * @rem;
    height: 62 * @rem;
    .image-bg('~@/assets/images/duanwu-activity/dw_button2.png');
  }

  .probability {
    position: absolute;
    top: 193 * @rem;
    right: 0 * @rem;
    z-index: 1000;
    width: 27 * @rem;
    height: 62 * @rem;
    .image-bg('~@/assets/images/duanwu-activity/dw_button3.png');
  }

  .exchange-record {
    position: absolute;
    top: 265 * @rem;
    right: 0 * @rem;
    z-index: 1000;
    width: 27 * @rem;
    height: 62 * @rem;
    .image-bg('~@/assets/images/duanwu-activity/dw_button12.png');
  }
}

main {
  .bg1 {
    position: relative;
    width: 100%;
    height: 608 * @rem;
    .image-bg('~@/assets/images/duanwu-activity/dw_bg1.png');

    .container1 {
      position: absolute;
      top: 475 * @rem;
      padding: 0 22 * @rem;

      .text {
        font-size: 13 * @rem;
        color: rgba(2, 127, 78, 1);
        line-height: 18 * @rem;
      }

      .button {
        width: 155 * @rem;
        height: 53 * @rem;
        margin: 22 * @rem auto 0;
        .image-bg('~@/assets/images/duanwu-activity/dw_button1.png');
      }
    }
  }

  .bg2 {
    position: relative;
    width: 375 * @rem;
    height: 581 * @rem;
    margin-top: 23 * @rem;
    .image-bg('~@/assets/images/duanwu-activity/dw_bg2.png');

    .game {
      position: absolute;
      top: 105 * @rem;
      left: 0;
      width: 100%;

      .game-bg {
        position: relative;
        width: 275 * @rem;
        margin: 0 auto;

        .pic {
          position: absolute;
          top: -41 * @rem;
          left: 0;
          transform: translate(-20 * @rem, 0);
          width: 40 * @rem;
          height: 36 * @rem;
          .image-bg('~@/assets/images/duanwu-activity/dw_bg8.png');
        }
      }

      .progress {
        width: 275 * @rem;
        height: 14 * @rem;
        background-color: RGBA(164, 195, 185, 1);
        border-radius: 15 * @rem;
        margin: 0 auto;

        .content {
          height: 14 * @rem;
          border-radius: 15 * @rem;
          background: linear-gradient(
            to bottom,
            rgba(219, 255, 0, 1),
            rgba(27, 151, 100, 1) 50%,
            rgba(32, 153, 103, 1)
          );
        }
      }

      .list {
        display: flex;
        justify-content: space-between;
        padding: 0 26 * @rem;
        margin-top: 10 * @rem;

        .big-text {
          text-align: center;
          color: rgba(250, 90, 0, 1);
          font-weight: 600;
        }

        .small-text {
          margin-top: 5 * @rem;
          color: rgba(41, 79, 87, 1);
        }
      }
    }

    .container2 {
      position: absolute;
      top: 188 * @rem;
      left: 0;
      width: 100%;
      box-sizing: border-box;
      padding: 10 * @rem 31 * @rem;

      .item {
        color: rgba(41, 79, 87, 1);
        margin-bottom: 8 * @rem;

        .task-top {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .task-title {
            font-weight: 600;

            .small-text {
              font-weight: 400;

              /deep/ span {
                color: rgba(250, 90, 0, 1);
              }
            }
          }

          .task-button {
            width: 60 * @rem;
            height: 25 * @rem;
            .image-bg('~@/assets/images/duanwu-activity/dw_button6.png');

            &.already {
              .image-bg('~@/assets/images/duanwu-activity/dw_button7.png');
            }

            &.empty {
              .image-bg('~@/assets/images/duanwu-activity/dw_button8.png');
            }
          }
        }

        .task-bottom {
          margin-top: 2 * @rem;

          .color {
            color: rgba(250, 90, 0, 1);
          }
        }
      }
    }
  }

  .bg3 {
    position: relative;
    width: 375 * @rem;
    height: 595 * @rem;
    margin: 23 * @rem 0 50 * @rem;
    .image-bg('~@/assets/images/duanwu-activity/dw_bg3.png');

    .rank-button {
      position: absolute;
      top: 35 * @rem;
      right: 17 * @rem;
      width: 96 * @rem;
      height: 31 * @rem;
      color: #fff;
      font-size: 12 * @rem;
      display: flex;
      justify-content: center;
      align-items: center;
      .image-bg('~@/assets/images/duanwu-activity/dw_bg4.png');

      &.empty {
        .image-bg('~@/assets/images/duanwu-activity/dw_button13.png');
      }
    }

    .pic-container {
      position: absolute;
      top: 200 * @rem;
      left: 0;
      width: 100%;
      color: #fff;
      display: flex;

      .left,
      .right {
        flex: 0 0 50%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        .text {
          font-size: 12 * @rem;
        }

        .button {
          width: 106 * @rem;
          height: 31 * @rem;
          margin-top: 5 * @rem;

          &.empty {
            .image-bg('~@/assets/images/duanwu-activity/dw_button13.png');
            text-align: center;
            line-height: 31 * @rem;
            font-size: 15 * @rem;
          }

          &.already {
            .image-bg('~@/assets/images/duanwu-activity/dw_button11.png');
          }

          &.empty2 {
            background: none;
          }
        }
      }

      .left {
        .button {
          .image-bg('~@/assets/images/duanwu-activity/dw_title3.png');
        }
      }

      .right {
        .button {
          .image-bg('~@/assets/images/duanwu-activity/dw_title4.png');
        }
      }
    }

    .explain {
      position: absolute;
      top: 270 * @rem;
      left: 20 * @rem;
      color: rgba(89, 146, 76, 1);
    }

    .article {
      position: absolute;
      top: 310 * @rem;
      left: 0;
      width: 100%;
      box-sizing: border-box;
      padding: 0 25 * @rem;

      .text {
        line-height: 17 * @rem;
        margin-bottom: 10 * @rem;
        color: rgba(41, 79, 87, 1);

        .color {
          color: rgba(250, 90, 0, 1);
        }
      }
    }
  }
}

.popup {
  width: 290 * @rem;
  border-radius: 20 * @rem;
  overflow: hidden;
  background-color: transparent;

  .text {
    font-size: 14 * @rem;
    color: #478671;
  }

  .popup-close {
    width: 33 * @rem;
    height: 27 * @rem;
    background: #478671 url(~@/assets/images/popup-close.png) center center
      no-repeat;
    background-size: 22 * @rem 22 * @rem;
    position: absolute;
    right: -1 * @rem;
    top: -1 * @rem;
    border-radius: 0 12 * @rem 0 12 * @rem;
  }

  .title {
    font-size: 16 * @rem;
    font-family: PingFang SC-Semibold, PingFang SC;
    font-weight: 600;
    color: #478671;
    line-height: 20 * @rem;
    text-align: center;
    margin-bottom: 30 * @rem;
  }
}

.rule-popup {
  width: 349 * @rem;
  height: 235 * @rem;
  box-sizing: border-box;
  .image-bg('~@/assets/images/duanwu-activity/dw_bg9.png');
  padding: 80 * @rem 22 * @rem 0;

  .text {
    line-height: 16 * @rem;
    margin-bottom: 15 * @rem;
    color: rgba(41, 79, 87, 1);

    span {
      color: rgba(250, 90, 0, 1);
      text-decoration: underline;
    }
  }
}

.record-list-popup {
  width: 326 * @rem;
  height: 247 * @rem;
  box-sizing: border-box;
  .image-bg('~@/assets/images/duanwu-activity/dw_bg14.png');
  .list {
    width: 270 * @rem;
    height: 170 * @rem;
    overflow-y: scroll;
    margin-top: 55 * @rem;
    padding: 0 18 * @rem;
    .item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 10 * @rem;
      align-items: center;
      font-size: 14 * @rem;
      color: #478671;
      .left {
        flex: 0 0 155 * @rem;
      }
      .right {
        flex: 1;
        text-align: right;
      }
      /deep/ span {
        color: #f8582e;
      }
    }
  }
  .empty {
    width: 100%;
    height: 227 * @rem;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #478671;
  }
}

.probability-popup {
  width: 349 * @rem;
  height: 450 * @rem;
  box-sizing: border-box;
  .image-bg('~@/assets/images/duanwu-activity/dw_bg10.png');
  padding: 80 * @rem 22 * @rem 0;

  .small-title {
    margin-bottom: 5 * @rem;
    color: rgba(41, 79, 87, 1);
    font-weight: 800;
  }

  .text {
    margin-bottom: 12 * @rem;
    color: rgba(41, 79, 87, 1);

    span {
      color: rgba(250, 90, 0, 1);
    }
  }
}

.camp-popup {
  width: 240 * @rem;
  height: 150 * @rem;
  box-sizing: border-box;
  .image-bg('~@/assets/images/duanwu-activity/dw_bg13.png');
  padding: 20 * @rem 27 * @rem 0;

  .title {
    font-size: 16 * @rem;
    font-weight: 600;
    color: #529d6b;
    margin-bottom: 13 * @rem;
  }

  .text {
    color: rgba(71, 135, 70, 1);
    line-height: 18 * @rem;
    text-align: center;
  }

  .button-container {
    display: flex;
    justify-content: space-between;
    margin-top: 12 * @rem;

    .left-button,
    .right-button {
      width: 88 * @rem;
      height: 31 * @rem;
    }

    .left-button {
      .image-bg('~@/assets/images/duanwu-activity/dw_button10.png');
    }

    .right-button {
      .image-bg('~@/assets/images/duanwu-activity/dw_button9.png');
    }
  }
}

.getzongzi-popup {
  width: 355 * @rem;
  height: 438 * @rem;
  box-sizing: border-box;
  .image-bg('~@/assets/images/duanwu-activity/dw_bg11.png');
  padding: 60 * @rem 22 * @rem 0;

  .tab-list {
    display: flex;
    justify-content: space-between;
    width: 310 * @rem;
    height: 35 * @rem;
    box-sizing: border-box;

    .tab-item {
      flex: 0 0 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 16 * @rem;
      color: rgba(118, 189, 142, 1);
      overflow: hidden;
      border: 1 * @rem solid rgba(118, 189, 142, 1);
      border-radius: 10 * @rem 0 0 10 * @rem;

      &.right-tab-item {
        border-radius: 0 10 * @rem 10 * @rem 0;
      }

      &.current {
        color: #fff;
        background-color: rgba(118, 189, 142, 1);
      }
    }
  }

  .tab-content {
    .list {
      margin-top: 20 * @rem;

      .item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15 * @rem;

        .left {
          .text {
            font-size: 11 * @rem;
            line-height: 14 * @rem;
            margin-bottom: 2 * @rem;
            color: rgba(41, 79, 87, 1);
          }
        }

        .right {
          flex: 0 0 60 * @rem;
          width: 60 * @rem;
          height: 25 * @rem;
          margin-left: 20 * @rem;
          .image-bg('~@/assets/images/duanwu-activity/dw_button6.png');

          &.empty {
            .image-bg('~@/assets/images/duanwu-activity/dw_button8.png');
          }

          &.already {
            .image-bg('~@/assets/images/duanwu-activity/dw_button7.png');
          }
        }
      }
    }

    .explain {
      line-height: 16 * @rem;
      color: rgba(89, 146, 76, 1);

      span {
        color: rgba(255, 111, 6, 1);
      }
    }
  }
}

.rank-popup {
  width: 355 * @rem;

  .big-text {
    padding-top: 57 * @rem;
    text-align: center;
    font-size: 16 * @rem;
    font-weight: 600;
    color: #76bd8e;
  }

  .rank {
    width: 355 * @rem;
    height: 438 * @rem;
    box-sizing: border-box;
    .image-bg('~@/assets/images/duanwu-activity/dw_bg11.png');

    .rank-title {
      display: flex;
      justify-content: space-between;
      width: 312 * @rem;
      height: 29 * @rem;
      align-items: center;
      color: #fff;
      font-size: 14 * @rem;
      font-weight: 600;
      background: rgba(118, 189, 142, 1);
      box-sizing: border-box;
      padding: 0 10 * @rem;
      border-radius: 5 * @rem;
      margin: 14 * @rem auto 0;

      .left {
        flex: 0 0 20%;
      }

      .center {
        flex: 1;
        text-align: left;
      }
    }

    .rank-item {
      display: flex;
      justify-content: space-between;
      width: 312 * @rem;
      height: 29 * @rem;
      align-items: center;
      color: rgba(41, 79, 87, 1);
      font-size: 14 * @rem;
      font-weight: 600;
      box-sizing: border-box;
      margin: 0 auto;

      .left {
        flex: 0 0 14%;
        margin-right: 10%;
        display: flex;
        justify-content: center;
        align-items: center;
        color: rgba(118, 189, 142, 1);

        span {
          display: block;
          width: 16 * @rem;
          height: 16 * @rem;
          text-align: center;
          line-height: 16 * @rem;

          &.num {
            background: rgba(118, 189, 142, 1);
            border-radius: 50%;
            color: #fff;
          }
        }
      }

      .center {
        flex: 1;
        text-align: left;
      }

      .right {
        flex: 0 0 20%;
        text-align: center;
      }
    }
  }

  .user {
    width: 355 * @rem;
    height: 62 * @rem;
    box-sizing: border-box;
    display: flex;
    padding: 0 25 * @rem;
    align-items: center;
    justify-content: space-between;
    margin-top: 5 * @rem;
    font-size: 14 * @rem;
    color: rgba(41, 79, 87, 1);
    .image-bg('~@/assets/images/duanwu-activity/dw_bg12.png');

    .avatar {
      width: 32 * @rem;
      height: 32 * @rem;
    }

    .center {
      flex: 1;
      text-align: left;
      margin: 0 5 * @rem;
    }

    span {
      color: rgba(255, 76, 0, 1);
    }
  }
}
</style>
