import { request } from '../index';

/**
 * 活动页/春节 - 刷新爆竹数量
 */
export function ApiChunJieBaozhuCount(params = {}) {
  return request('/activity/chun_jie/baozhuCount', params);
}

/**
 * 春节 - 天降福星
 */
export function ApiChunJieGetTjfxList(params = {}) {
  return request('/activity/chun_jie/getTjfxList', params);
}

/**
 * 春节 - 奖品领取记录
 */
export function ApiChunJieRecordExchange(params = {}) {
  return request('/activity/chun_jie/recordExchange', params);
}

/**
 * 春节 - 春节活动首页
 */
export function ApiChunJieIndex(params = {}) {
  return request('/activity/chun_jie/index', params);
}

/**
 * 活动页/春节 - 爆竹页面
 */
export function ApiChunJieBaozhu(params = {}) {
  return request('/activity/chun_jie/baozhu', params);
}

/**
 * 活动页/春节 - 领取奖励
 */
export function ApiChunJieExtraReward(params = {}) {
  return request('/activity/chun_jie/ExtraReward', params);
}

/**
 * 活动页/春节 - 领取爆竹接口
 */
export function ApiChunJieBaozhuExtraReward(params = {}) {
  return request('/activity/chun_jie/baozhuExtraReward', params);
}
