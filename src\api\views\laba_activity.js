import { request } from '../index';

/**
 * 活动页腊八节
 */

/**
 * 活动页/腊八 - 腊八活动首页
 */
export function ApiActivityJanuaryIndex(params = {}) {
  return request('/activity/january/index', params);
}

/**
 * 活动页/腊八 - 兑换
 */
export function ApiActivityJanuaryExchange(params = {}) {
  return request('/activity/january/exchange', params);
}

/**
 * 活动页/腊八 - 刮奖
 */
export function ApiActivityJanuaryScratchingPrizes(params = {}) {
  return request('/activity/january/scratchingPrizes', params);
}

/**
 * 活动页/腊八 - 刷新金额及刮奖次数
 */
export function ApiActivityJanuaryRefreshAuspiciously(params = {}) {
  return request('/activity/january/refreshAuspiciously', params);
}

/**
 * 活动页/腊八 - 卡片列表
 */
export function ApiActivityJanuaryLotteryList(params = {}) {
  return request('/activity/january/lotteryList', params);
}

/**
 * 活动页/腊八 - 合成祥瑞卡
 */
export function ApiActivityJanuaryAuspiciously(params = {}) {
  return request('/activity/january/auspiciously', params);
}

/**
 * 活动页/腊八 - 抽卡
 */
export function ApiActivityJanuaryLottery(params = {}) {
  return request('/activity/january/lottery', params);
}

/**
 * 活动页/腊八 - 领取抽卡机会
 */
export function ApiActivityJanuaryTask(params = {}) {
  return request('/activity/january/task', params);
}

/**
 * 活动页/腊八 - 兑换记录
 */
export function ApiActivityJanuaryExchangeLog(params = {}) {
  return request('/activity/january/exchangeLog', params);
}

/**
 * 活动页/腊八 - 获取svip信息
 */
export function ApiActivityJanuarySvipInfo(params = {}) {
  return request('/activity/january/svipInfo', params);
}
