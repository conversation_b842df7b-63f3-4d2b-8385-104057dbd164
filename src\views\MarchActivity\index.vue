<template>
  <div class="march-activity">
    <nav-bar-2
      ref="topNavBar"
      bgStyle="transparent-white"
      :azShow="true"
      :placeholder="false"
    ></nav-bar-2>
    <div @click="toPage('MarchActivityRule')" class="rule btn"></div>
    <div @click="getRecordList" class="exchange-record btn"></div>
    <div class="user-container">
      <div v-if="userInfo.token" class="user">
        <UserAvatar class="avatar" />
        <div class="nickname">{{ userInfo.nickname }}</div>
      </div>
      <div @click="login" v-else class="user btn">
        <div class="avatar img"></div>
        <div class="text">未登录</div>
      </div>
    </div>
    <main>
      <div class="game">
        <div @click="toBottom" class="icon1 btn"></div>
        <div @click="toTask" class="icon2 btn"></div>
        <div :class="[gameBg]" class="bg"></div>
        <div class="text">
          当前养分<span>{{ remain_num }}</span
          >点<span @click="refreshCount" class="refresh btn">【刷新】</span>
        </div>
        <div class="progress-container">
          <div class="progress">
            <div
              v-if="prize_list.length > 0"
              :style="{ width: `${percentage}%` }"
              class="content"
            ></div>
          </div>
          <div class="list">
            <div
              v-for="item in prize_list"
              :index="item.prize_id"
              :class="{
                red: item.status == 1,
                gray: item.status == 0 && item.prize_id != 1,
              }"
              class="item"
            >
              {{ item.title }}
            </div>
          </div>
        </div>
        <div
          :class="{
            'no-start': activity_status == 3,
            'end': activity_status == 4,
            'can-take': take_list.length > 0 && activity_status == 1,
            'already':
              activity_status == 1 &&
              remain_num >= 100 &&
              take_list.length == 0,
          }"
          @click="handleGetPrize"
          class="bottom-button btn"
        ></div>
      </div>
      <swiper
        v-if="user_list.length > 0"
        :options="swiperOption"
        class="user-list"
      >
        <!-- slides -->
        <swiper-slide
          v-for="(item, index) in user_list"
          :key="index"
          class="swiper-no-swiping"
        >
          <div class="swiper-item" v-html="item"></div>
        </swiper-slide>
      </swiper>
      <section id="task" class="module module2">
        <div class="title"></div>
        <div
          v-for="item in task_list.filter(item => item.is_every == 1)"
          :key="item.take_id"
          class="item"
        >
          <div class="left">
            <div class="big-text">{{ item.desc }}</div>
            <div class="small-text">{{ item.title }}</div>
          </div>
          <div
            :class="{ empty: item.status == 0, already: item.status == 2 }"
            @click="handleGetCashGift(item)"
            class="right"
          ></div>
        </div>
      </section>
      <section class="module module3">
        <div class="title"></div>
        <div
          v-for="item in task_list.filter(item => item.is_every == 0)"
          :key="item.take_id"
          class="item"
        >
          <div class="left">
            <div class="big-text">{{ item.desc }}</div>
            <div class="small-text">{{ item.title }}</div>
          </div>
          <div
            :class="{ empty: item.status == 0, already: item.status == 2 }"
            @click="handleGetCashGift(item)"
            class="right"
          ></div>
        </div>
      </section>
      <section class="module module4">
        <div class="title"></div>
        <div class="text"><span>种子期：</span>初始状态，无奖励。</div>
        <div class="text">
          <span>幼苗期：</span>
          所需养分10奖励：<br />随机获得188金币+1天SVIP或88平台币其中一个（活动期间限一次）
        </div>
        <div class="text">
          <span>生长期：</span
          >所需养分30奖励：<br />随机获得888金币+3天SVIP或188平台币其中一个（活动期间限一次）
        </div>
        <div class="text">
          <span>开花期：</span>
          所需养分60奖励：<br />随机获得1888金币+7天SVIP或388平台币其中一个（活动期间限一次）
        </div>
        <div class="text">
          <span>结果期：</span>
          所需养分90奖励：<br />随机获得8888金币+14天SVIP或1888平台币其中一个（活动期间限一次）<br />每次领取奖励，均有几率触发幸运大奖，额外奖励黄金橘子一个，开启可获得（1888金币，5888金币，1888平台币）其中一个。
        </div>
      </section>
    </main>
    <!-- 兑奖记录 -->
    <van-popup
      v-model="record_list_popup"
      :lock-scroll="false"
      :close-on-click-overlay="false"
      class="popup record-list-popup"
    >
      <div class="popup-close" @click="closeRecordPopup"></div>
      <div class="title">兑奖记录</div>
      <div v-if="record_list.length > 0" class="list">
        <div v-for="(item, index) in record_list" :key="index" class="item">
          <div class="left">{{ item.title }}</div>
          <div v-html="item.desc" class="right"></div>
        </div>
      </div>
      <div v-else class="empty">暂无兑奖记录</div>
    </van-popup>
    <!-- 奖品列表弹窗 -->
    <van-popup
      :lock-scroll="false"
      v-model="prize_popup"
      class="popup prize-popup message-popup"
    >
      <div v-for="(item, index) in prize_popup_list" :key="index" class="text">
        {{ item }}
      </div>
      <div @click="prize_popup = false" class="bottom-button btn"></div>
    </van-popup>
    <ptb-recharge-popup @success="getActivityInfo"></ptb-recharge-popup>
    <svip-recharge-popup @success="getActivityInfo"></svip-recharge-popup>
  </div>
</template>
<script>
import { platform, boxInit } from '@/utils/box.uni.js';
import UserAvatar from '@/components/user-avatar';
import { BOX_login } from '@/utils/box.uni.js';
import {
  ApiMARIndex,
  ApiMARTaskTake,
  ApiMARExchangeRecord,
  ApiMARRefresh,
  ApiMARTakePrize,
} from '@/api/views/march_activity';
import { mapGetters, mapMutations } from 'vuex';
import ptbRechargePopup from '@/components/ptb-recharge-popup';
import svipRechargePopup from '@/components/svip-recharge-popup';
import { setTimeout } from 'timers';

export default {
  data() {
    return {
      activity_status: 3, //1活动已开始 2 活动不存在 3活动未开始 4活动已结束
      swiperOption: {
        observer: true,
        observeParents: true,
        noSwiping: true,
        direction: 'vertical',
        slidesPerView: 3,
        speed: 600,
        autoplay: {
          delay: 0,
        },
        loop: true,
        freeMode: true,
      }, //第一个用户列表展示轮播
      user_list: [], //第一个用户列表
      svip_popup: false, //svip引导弹窗
      record_list_popup: false, //记录弹窗
      record_list: [], //记录列表
      prize_list: [], //培养计划表
      task_list: [], //任务列表
      remain_num: 0, //当前养分
      take_list: [], //当前可领取养分的列表ID
      finished: false, //ajax防卡
      svip_status: 0,
      task_scroll_top: 0, //任务距离顶部距离
      prize_popup: false, //奖品弹窗
      prize_popup_list: [], //奖品弹窗列表
    };
  },
  computed: {
    active_status_text() {
      const arr = ['活动已开始', '活动不存在', '活动未开始', '活动已结束'];
      return arr[this.activity_status - 1];
    },
    percentage() {
      let temp = 0;
      this.take_list.splice(0, this.take_list.length);
      // try {
      for (let i = 0; i < this.prize_list.length; i++) {
        if (this.prize_list[i].status == 1) {
          this.take_list.push(this.prize_list[i].prize_id);
        }
        // 判断他在哪一个区间内
        if (
          this.prize_list[i].need_num < this.remain_num &&
          i < this.prize_list.length - 1 &&
          this.prize_list[i + 1].need_num >= this.remain_num
        ) {
          temp =
            (100 / this.prize_list.length) *
            (i +
              (this.remain_num - this.prize_list[i].need_num) /
                (this.prize_list[i + 1].need_num -
                  this.prize_list[i].need_num));
        } else if (
          i == this.prize_list.length - 1 &&
          this.prize_list[i].need_num < this.remain_num
        ) {
          temp =
            (100 / this.prize_list.length) *
            (i +
              (this.remain_num - this.prize_list[i].need_num) /
                (100 - this.prize_list[i].need_num));
        }
      }
      // } catch {
      //   temp = 0;
      // }
      // 对齐效果图
      if (temp == 20) temp = temp + 1;
      if (temp == 90) temp = temp - 1;
      return temp;
    },
    gameBg() {
      let temp = 'bg1';
      if (this.percentage >= 20 && this.percentage < 40) {
        temp = 'bg2';
      } else if (this.percentage >= 40 && this.percentage < 60) {
        temp = 'bg3';
      } else if (this.percentage >= 60 && this.percentage < 80) {
        temp = 'bg4';
      } else if (this.percentage >= 80) {
        temp = 'bg5';
      }
      return temp;
    },
    ...mapGetters({
      userInfo: 'user/userInfo',
    }),
  },
  async mounted() {
    // 官包触发页面刷新（将onResume挂载在window上）
    if (platform == 'android') {
      window.onResume = this.onResume;
    }
    await this.getActivityInfo();
    setTimeout(() => {
      this.task_scroll_top = document.getElementById('task').offsetTop;
    }, 2000);
  },
  methods: {
    async onResume() {
      await boxInit();
      await this.getActivityInfo();
      this.SET_USER_INFO(true);
    },
    async getActivityInfo() {
      const res = await ApiMARIndex();
      this.activity_status = res.data.activity_status;
      this.prize_list = res.data.prize_list;
      this.user_list = res.data.fake_list;
      this.task_list = res.data.take_list;
      this.remain_num = res.data.remain_num;
    },
    login() {
      BOX_login();
    },
    toRecharge() {
      if (!this.userInfo.token) {
        this.login();
        return false;
      }
      if (this.activity_status != 1) {
        this.$toast(this.active_status_text);
        return false;
      }
      this.scratch_empty_popup = false;
      this.setShowPtbRechargePopup(true);
    },
    async refreshCount() {
      const res = await ApiMARRefresh();
      this.remain_count = res.data.remain_count;
    },
    toRechargeSvip() {
      if (!this.userInfo.token) {
        this.login();
        return false;
      }
      if (this.activity_status != 1) {
        this.$toast(this.active_status_text);
        return false;
      }
      this.svip_popup = false;
      this.setShowSvipRechargePopup(true);
    },
    // 去任务锚点
    toTask() {
      window.scrollTo(0, this.task_scroll_top);
    },
    toBottom() {
      window.scrollTo(0, document.documentElement.scrollHeight);
    },
    // 关闭记录弹窗
    closeRecordPopup() {
      this.record_list_popup = false;
    },
    // 获取记录列表
    async getRecordList() {
      if (!this.userInfo.token) {
        this.login();
        return false;
      }
      if (this.activity_status != 1) {
        this.$toast(this.active_status_text);
        return false;
      }
      this.$toast({
        type: 'loading',
        duration: 0,
        message: '拼命加载中...',
        forbidClick: true,
      });
      try {
        const res = await ApiMARExchangeRecord();
        this.record_list = res.data.list;
        this.record_list_popup = true;
        this.$toast.clear();
      } finally {
      }
    },
    // 获取养分奖励
    async handleGetPrize() {
      if (!this.userInfo.token) {
        this.login();
        return false;
      }
      if (this.activity_status != 1) {
        this.$toast(this.active_status_text);
        return false;
      }
      if (this.take_list.length == 0) {
        return false;
      }
      if (this.finished) return false;
      this.finished = true;
      this.$toast({
        type: 'loading',
        duration: 0,
      });
      this.result = 0;
      try {
        const res = await ApiMARTakePrize({
          prize_id: this.take_list,
        });
        this.prize_popup_list = res.data.msg;
        this.prize_popup = true;
        await this.getActivityInfo();
        this.$toast.clear();
        this.finished = false;
      } catch {
        this.$toast.clear();
        this.finished = false;
      }
    },
    // 领取任务奖励
    async handleGetCashGift(item) {
      if (!this.userInfo.token) {
        this.login();
        return false;
      }
      if (this.activity_status != 1) {
        this.$toast(this.active_status_text);
        return false;
      }
      if (item.status == 0) {
        if (item.take_id == 2) {
          this.$dialog
            .confirm({
              message: '今日未充值平台币是否前往充值',
              confirmButtonText: '前往',
              lockScroll: false,
            })
            .then(() => {
              this.setShowPtbRechargePopup(true);
            });
        }
        if (item.take_id == 3) {
          this.$dialog
            .confirm({
              message: 'SVIP天数不足30天，是否前往续费',
              confirmButtonText: '前往',
              lockScroll: false,
            })
            .then(() => {
              this.setShowSvipRechargePopup(true);
            });
        }
        return false;
      }
      if (item.status == 2) {
        return false;
      }
      const res = await ApiMARTaskTake({ take_id: item.take_id });
      await this.getActivityInfo();
    },
    ...mapMutations({
      setShowPtbRechargePopup: 'recharge/setShowPtbRechargePopup',
      setShowSvipRechargePopup: 'recharge/setShowSvipRechargePopup',
    }),
  },
  components: {
    ptbRechargePopup,
    svipRechargePopup,
    UserAvatar,
  },
};
</script>
<style lang="less" scoped>
.march-activity {
  position: relative;
  min-height: 100vh;
  .image-bg('~@/assets/images/march-activity/mar_bg2.png');
  background-color: #064b68;
  overflow: hidden;
  /deep/ .swiper-wrapper {
    transition-timing-function: linear !important;
  }
  .swiper-item {
    text-align: center;
    color: #fff;
    white-space: nowrap;
    /deep/ span {
      color: #e75e37;
    }
  }
}
section {
  .title {
    width: 207 * @rem;
    height: 43 * @rem;
    margin: 0 auto;
  }
}
.user-list {
  height: 86 * @rem;
  margin: 0 14 * @rem;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 15 * @rem;
  opacity: 1;
  border: 1 * @rem solid;
  border-image: linear-gradient(
      270deg,
      rgba(238, 235, 255, 0),
      rgba(60, 118, 107, 1),
      rgba(47, 110, 99, 0)
    )
    2 2;
}
.user-container {
  position: absolute;
  top: 25 * @rem;
  left: 70 * @rem;
  z-index: 1000;
  height: 22 * @rem;
  background-color: rgba(0, 0, 0, 0.3);
  color: #fff;
  line-height: 22 * @rem;
  border-radius: 20 * @rem;
  .user {
    padding: 0 12 * @rem 0 30 * @rem;
    .text {
      white-space: nowrap;
    }
  }
  .avatar {
    position: absolute;
    top: -3.5 * @rem;
    left: -3.5 * @rem;
    width: 28 * @rem;
    height: 28 * @rem;
    border-radius: 50%;
    &.img {
      .image-bg('~@/assets/images/march-activity/mar_icon2.png');
    }
  }
}
.rule {
  position: absolute;
  top: 34 * @rem;
  right: 10 * @rem;
  z-index: 1000;
  width: 46 * @rem;
  height: 18 * @rem;
  .image-bg('~@/assets/images/march-activity/mar_button7.png');
}
.exchange-record {
  position: absolute;
  top: 72 * @rem;
  right: 0 * @rem;
  z-index: 1000;
  width: 63 * @rem;
  height: 49 * @rem;
  .image-bg('~@/assets/images/march-activity/mar_button6.png');
}
main {
  position: relative;
  margin-top: 187 * @rem;
  .game {
    position: relative;
    .bg {
      width: 240 * @rem;
      height: 240 * @rem;
      margin: 0 auto;
      &.bg1 {
        .image-bg('~@/assets/images/march-activity/mar_sp2.png');
      }
      &.bg2 {
        .image-bg('~@/assets/images/march-activity/mar_sp4.png');
      }
      &.bg3 {
        .image-bg('~@/assets/images/march-activity/mar_sp5.png');
      }
      &.bg4 {
        .image-bg('~@/assets/images/march-activity/mar_sp6.png');
      }
      &.bg5 {
        .image-bg('~@/assets/images/march-activity/mar_sp7.png');
      }
    }
    .icon1 {
      position: absolute;
      top: 148 * @rem;
      left: 17 * @rem;
      z-index: 2;
      width: 86 * @rem;
      height: 86 * @rem;
      .image-bg('~@/assets/images/march-activity/mar_icon3.png');
    }
    .icon2 {
      position: absolute;
      top: 80 * @rem;
      right: 0 * @rem;
      z-index: 2;
      width: 64 * @rem;
      height: 68 * @rem;
      .image-bg('~@/assets/images/march-activity/mar_icon1.png');
    }
    .text {
      margin: 43 * @rem 0 14 * @rem;
      color: #ffffff;
      line-height: 13 * @rem;
      text-align: center;
      font-size: 13 * @rem;
      span {
        color: #ffe600;
        &.refresh {
          margin-left: 12 * @rem;
        }
      }
    }
    .progress-container {
      width: 260 * @rem;
      margin: 0 auto;
      .progress {
        width: 260 * @rem;
        height: 27 * @rem;
        .image-bg('~@/assets/images/march-activity/mar_bg1.png');
        .content {
          width: 0;
          height: 100%;
          .image-bg('~@/assets/images/march-activity/mar_bg6.png');
          background-size: 260 * @rem;
        }
      }
      .list {
        width: 260 * @rem;
        display: flex;
        margin: 5 * @rem 0;
        .item {
          flex: 0 0 20%;
          text-align: center;
          color: #fff;
          position: relative;
          &.red::before {
            position: absolute;
            top: -3 * @rem;
            right: 0 * @rem;
            width: 6 * @rem;
            height: 6 * @rem;
            content: '';
            background: #ff2e00;
            border: 1 * @rem solid #fff;
            border-radius: 50%;
          }
          &.gray {
            // color: #6e6e6e;
          }
        }
      }
    }
    .bottom-button {
      width: 202 * @rem;
      height: 48 * @rem;
      margin: 20 * @rem auto;
      .image-bg('~@/assets/images/march-activity/mar_button10.png');
      &.end {
        .image-bg('~@/assets/images/march-activity/mar_button4.png');
      }
      &.no-start {
        .image-bg('~@/assets/images/march-activity/mar_button5.png');
      }
      &.can-take {
        .image-bg('~@/assets/images/march-activity/mar_button1.png');
      }
      &.already {
        .image-bg('~@/assets/images/march-activity/mar_button3.png');
      }
    }
  }
  .module {
    position: relative;
    margin: 35 * @rem 14 * @rem;
    background: #e5f0cf;
    border: 5 * @rem solid #7aa197;
    border-radius: 25 * @rem;
    padding-top: 35 * @rem;
    .title {
      position: absolute;
      left: 50%;
      top: -15 * @rem;
      transform: translate(-50%, 0);
      .image-bg('~@/assets/images/march-activity/mar_bg4.png');
    }
    .item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 60 * @rem;
      margin-bottom: 5 * @rem;
      padding: 0 15 * @rem;
    }
    .left {
      flex: 0 0 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
      .big-text {
        font-size: 14 * @rem;
        color: #205b12;
        font-weight: 600;
      }
      .small-text {
        margin-top: 5 * @rem;
        font-size: 12 * @rem;
        color: #477940;
      }
    }
    .right {
      width: 62 * @rem;
      height: 28 * @rem;
      .image-bg('~@/assets/images/march-activity/mar_button2.png');
      &.empty {
        .image-bg('~@/assets/images/march-activity/mar_button9.png');
      }
      &.already {
        .image-bg('~@/assets/images/march-activity/mar_button8.png');
      }
    }
    &.module3 {
      .title {
        .image-bg('~@/assets/images/march-activity/mar_bg3.png');
      }
    }
    &.module4 {
      padding-bottom: 19 * @rem;
      .title {
        .image-bg('~@/assets/images/march-activity/mar_bg7.png');
      }
      .text {
        padding: 0 18 * @rem;
        font-size: 11 * @rem;
        color: #205b12;

        line-height: 15 * @rem;
        span {
          color: #ff602b;
        }
      }
    }
  }
}
.popup {
  width: 290 * @rem;
  padding: 18 * @rem;
  border-radius: 20 * @rem;
  overflow: hidden;
  background: #f8fffc;
  box-shadow: 0 * @rem 2 * @rem 15 * @rem 0 * @rem rgba(248, 0, 0, 0.1);
  border: 2 * @rem solid #478671;
  .text {
    font-size: 14 * @rem;
    color: #478671;
  }
  .popup-close {
    width: 33 * @rem;
    height: 27 * @rem;
    background: #478671 url(~@/assets/images/february-activity/popup-close.png)
      center center no-repeat;
    background-size: 22 * @rem 22 * @rem;
    position: absolute;
    right: -1 * @rem;
    top: -1 * @rem;
    border-radius: 0 12 * @rem 0 12 * @rem;
  }
  .title {
    font-size: 16 * @rem;
    font-family: PingFang SC-Semibold, PingFang SC;
    font-weight: 600;
    color: #478671;
    line-height: 20 * @rem;
    text-align: center;
    margin-bottom: 30 * @rem;
  }
  .bottom-button {
    width: 255 * @rem;
    height: 47 * @rem;
    margin: 15 * @rem auto 0;
    .image-bg('~@/assets/images/february-activity/fa_button13.png');
  }
  &.message-popup {
    .text {
      width: 254 * @rem;
      margin: 0 auto;
      padding: 20 * @rem 0;
      font-size: 14 * @rem;
      font-family: PingFang SC-Semibold, PingFang SC;
      font-weight: 600;
      color: #7b74aa;
      line-height: 18 * @rem;
      text-align: center;
    }
  }
}
.record-list-popup {
  padding: 18 * @rem 0;
  height: 245 * @rem;
  .list {
    height: 210 * @rem;
    overflow-y: scroll;
    padding: 0 18 * @rem;
    .item {
      display: flex;
      justify-content: space-between;
      height: 30 * @rem;
      align-items: center;
      font-size: 14 * @rem;
      color: rgba(71, 134, 113, 1);
      .left {
        flex: 0 0 90 * @rem;
      }
      .right {
        flex: 1;
        text-align: right;
      }
      /deep/ span {
        color: rgba(248, 88, 46, 1);
      }
    }
  }
  .empty {
    width: 100%;
    height: 210 * @rem;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #478671;
  }
}
.prize-popup {
  &.message-popup {
    .text {
      padding: 10 * @rem 0;
    }
  }
}
</style>
