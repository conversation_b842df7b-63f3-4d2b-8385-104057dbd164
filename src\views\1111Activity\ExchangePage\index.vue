<template>
  <div class="exchange-page">
    <nav-bar-2
      ref="topNavBar"
      bgStyle="transparent-white"
      :azShow="true"
      :placeholder="false"
    ></nav-bar-2>
    <div class="user-info">
      <div class="left">
        <UserAvatar />
      </div>
      <div class="center">
        <div class="text">当前账号：{{ userInfo.nickname }}</div>
        <div class="text">
          当前积分：<span>{{ integral }}</span>
        </div>
      </div>
    </div>
    <div class="container">
      <div class="big-title"></div>
      <div class="list">
        <div v-for="(item, index) in list" :key="index" class="item">
          <div class="left">
            <div class="big-text">{{ item.title }}</div>
            <div class="small-text">{{ item.desc }}</div>
          </div>
          <div
            @click="handleExchange(item.status, item.type)"
            :class="{ empty: item.status == 1 }"
            class="right"
          >
            兑换
          </div>
        </div>
      </div>
    </div>
    <!-- 通用消息弹窗 -->
    <van-dialog
      v-model="message_popup"
      :lock-scroll="false"
      :show-confirm-button="false"
      :close-on-click-overlay="false"
      class="popup"
    >
      <div class="popup-close" @click="message_popup = false"></div>
      <div class="title">
        <span
          >消耗<i>{{ reduce_integral }}</i
          >积分,获得</span
        >
        <span v-if="get_obj.gold"
          ><i>{{ get_obj.gold }}</i
          >金币</span
        >
        <span v-if="get_obj.svip"
          ><i>{{ get_obj.svip }}</i
          >天VIP</span
        >
        <span v-if="get_obj.ptb"
          ><i>{{ get_obj.ptb }}</i
          >平台币</span
        >
      </div>
      <div @click="message_popup = false" class="bottom-button"></div>
    </van-dialog>
  </div>
</template>
<script>
import { Api1111TakeExchange, Api1111ExchangeList } from '@/api/views/1111.js';
import { mapGetters } from 'vuex';

export default {
  data() {
    return {
      integral: 0, //当前积分
      list: [], //兑换列表
      message_popup: false, //通用消息弹窗
      reduce_integral: 0, //消耗积分
      get_obj: {
        gold: 0, //获得金币
        svip: 0, //获得svip天数
        ptb: 0, //获得平台币
      },
    };
  },
  computed: {
    ...mapGetters({
      userInfo: 'user/userInfo',
    }),
  },
  async created() {
    try {
      const res = await Api1111ExchangeList();
      this.list = res.data.list;
      this.integral = res.data.info.integral;
    } catch {}
  },
  methods: {
    async handleExchange(status, type) {
      if (status == 1) {
        return false;
      }
      try {
        const res = await Api1111TakeExchange({ type: type });
        this.reduce_integral = res.data.reduce_integral;
        this.get_obj = {
          gold: res.data.gold || 0,
          svip: res.data.svip || 0,
          ptb: res.data.ptb || 0,
        };
        this.message_popup = true;
        const res2 = await Api1111ExchangeList();
        this.list = res2.data.list;
        this.integral = res2.data.info.integral;
      } catch {}
    },
  },
};
</script>
<style lang="less" scoped>
.exchange-page {
  font-family: PingFang SC-Regular, PingFang SC;
  .image-bg('~@/assets/images/1111/1111_bg4.png');
  background-color: #510a6a;
  overflow: hidden;
  .user-info {
    margin: 65 * @rem 0 20 * @rem;
    display: flex;
    padding: 0 24 * @rem;
    .left {
      width: 40 * @rem;
      height: 40 * @rem;
      margin-right: 10 * @rem;
    }
    .center {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .text {
        font-size: 15 * @rem;
        color: #fff;
        span {
          font-size: 14 * @rem;
          color: #f7572d;
        }
      }
    }
  }
  .container {
    background: linear-gradient(180deg, #510a6a 0%, #400b7c 100%);
  }
  .big-title {
    width: 272 * @rem;
    height: 20 * @rem;
    margin: 0 auto;
    .image-bg('~@/assets/images/1111/1111_title2.png');
  }
  .list {
    padding: 20 * @rem 0 30 * @rem;
    .item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 335 * @rem;
      height: 70 * @rem;
      box-sizing: border-box;
      padding: 0 10 * @rem;
      margin: 0 auto 12 * @rem;
      background: linear-gradient(113deg, #99a3ff 0%, #8f48ff 93%);
      border-radius: 12 * @rem 12 * @rem 12 * @rem 12 * @rem;
      .left {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        padding: 15 * @rem 0;
        font-size: 12 * @rem;
        font-family: PingFang SC-Regular, PingFang SC;
        color: #ffffff;
        .big-text {
          font-size: 14 * @rem;
          font-family: PingFang SC-Semibold, PingFang SC;
          font-weight: 600;
        }
        .small-text {
        }
      }
      .right {
        width: 77 * @rem;
        height: 27 * @rem;
        text-align: center;
        line-height: 27 * @rem;
        font-size: 14 * @rem;
        color: #fff;
        .image-bg('~@/assets/images/1111/1111_button10.png');
        &.empty {
          .image-bg('~@/assets/images/1111/1111_button11.png');
        }
      }
    }
  }
  .popup {
    width: 290 * @rem;
    padding: 18 * @rem;
    box-shadow: 0 * @rem 2 * @rem 15 * @rem 0 * @rem rgba(248, 0, 0, 0.1);
    border: 2 * @rem solid #9680d9;
    background: #f8f9ff;
    .popup-close {
      width: 33 * @rem;
      height: 27 * @rem;
      background: #835cad url(~@/assets/images/1111/popup-close.png) center
        center no-repeat;
      background-size: 22 * @rem 22 * @rem;
      position: absolute;
      right: -1 * @rem;
      top: -1 * @rem;
      border-radius: 0 12 * @rem 0 12 * @rem;
    }
    .title {
      margin: 25 * @rem 0;
      text-align: center;
      font-size: 16 * @rem;
      font-family: PingFang SC-Semibold, PingFang SC;
      font-weight: 600;
      color: #835cad;
      i {
        color: #f8582e;
      }
    }
    .bottom-button {
      width: 254 * @rem;
      height: 48 * @rem;
      margin: 15 * @rem auto 0;
      .image-bg('~@/assets/images/1111/1111_button12.png');
    }
  }
}
</style>
