import { request } from '../index';

/**
 * 活动页4月活动
 */

export function Api0501Index(params = {}) {
  return request('/activity/may/index', params);
}

export function Api0501NameList(params = {}) {
  return request('/activity/may/awardsList', params);
}

export function Api0501TakeExchange(params = {}) {
  return request('/activity/may/take', params);
}

export function Api0501ExchangeRecord(params = {}) {
  return request('/activity/may/mayLog', params);
}

export function Api0501BuyCode(params = {}) {
  return request('/activity/may/buyCode', params);
}

export function Api0501MyCode(params = {}) {
  return request('/activity/may/myCode', params);
}
