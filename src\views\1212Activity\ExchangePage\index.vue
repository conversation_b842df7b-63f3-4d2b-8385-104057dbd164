<template>
  <div class="exchange-page">
    <nav-bar-2
      ref="topNavBar"
      bgStyle="transparent-white"
      :azShow="true"
      :placeholder="false"
    ></nav-bar-2>
    <div class="user-info">
      <div class="left">
        <UserAvatar />
      </div>
      <div class="center">
        <div class="text">当前账号：{{ userInfo.nickname }}</div>
        <div class="text">
          当前积分：<span>{{ point }}</span>
        </div>
      </div>
    </div>
    <div class="container">
      <div v-if="type == 2" class="explain">
        注：每个用户每日仅能参与3次，每日可兑换次数都将进行刷新
      </div>
      <div v-if="type == 1" class="big-title"></div>
      <div v-if="type == 2" class="big-title big-title2"></div>
      <div class="list">
        <div v-for="(item, index) in list" :key="index" class="item">
          <div class="left">
            <div class="big-text">
              {{ item.title }}<span>{{ `${item.count}/${item.num}` }}</span>
            </div>
            <div class="small-text">{{ item.desc }}</div>
          </div>
          <div
            @click="handleExchange(item.is_exchange, index, item.expend)"
            :class="{ empty: item.is_exchange == 0 }"
            class="right"
          >
            兑换
          </div>
        </div>
      </div>
    </div>
    <!-- 通用消息弹窗 -->
    <van-dialog
      v-model="message_popup"
      :lock-scroll="false"
      :show-confirm-button="false"
      :close-on-click-overlay="false"
      class="popup"
    >
      <div class="popup-close" @click="message_popup = false"></div>
      <div class="title">
        <span
          >消耗<i>{{ reduce_integral }}</i
          >积分,获得</span
        >
        <span v-if="get_obj.gold"
          ><i>{{ get_obj.gold }}</i
          >金币</span
        >
        <span v-if="get_obj.svip"
          ><i>{{ get_obj.svip }}</i
          >天VIP</span
        >
        <span v-if="get_obj.ptb"
          ><i>{{ get_obj.ptb }}</i
          >平台币</span
        >
        <span v-if="get_obj.draw"
          ><i>{{ get_obj.draw }}</i
          >次抽奖机会</span
        >
      </div>
      <div @click="message_popup = false" class="bottom-button"></div>
    </van-dialog>
  </div>
</template>
<script>
import {
  Api1212PointsRedemption,
  Api1212PointsRedemptionInfo,
} from '@/api/views/1212.js';
import { mapGetters } from 'vuex';

export default {
  data() {
    return {
      type: 1, //当前类型，1积分兑好礼2积分兑次数
      point: 0, //当前积分
      list: [], //兑换列表
      message_popup: false, //通用消息弹窗
      reduce_integral: 0, //消耗积分
      get_obj: {
        gold: 0, //获得金币
        svip: 0, //获得svip天数
        ptb: 0, //获得平台币
      },
    };
  },
  computed: {
    ...mapGetters({
      userInfo: 'user/userInfo',
    }),
  },
  async created() {
    await this.init();
  },
  methods: {
    async init() {
      this.type = this.$route.params.type;
      try {
        const res = await Api1212PointsRedemptionInfo({ type: this.type });
        this.list = res.data.list;
        this.point = res.data.int;
      } catch {}
    },
    async handleExchange(status, index, expend) {
      if (status == 0) {
        return false;
      }
      try {
        const res = await Api1212PointsRedemption({ id: index });
        this.reduce_integral = expend;
        this.get_obj = {
          gold: res.data.gold || 0,
          svip: res.data.svip || 0,
          ptb: res.data.ptb || 0,
          draw: res.data.draw || 0,
        };
        this.message_popup = true;
        await this.init();
      } catch {}
    },
  },
};
</script>
<style lang="less" scoped>
.exchange-page {
  font-family: PingFang SC-Regular, PingFang SC;
  min-height: 100vh;
  .image-bg('~@/assets/images/1212/1212_bg7.png');
  background-color: #400b7c;
  overflow: hidden;
  padding-bottom: 1 * @rem;
  .user-info {
    margin: 65 * @rem 0 10 * @rem;
    display: flex;
    padding: 0 24 * @rem;
    .left {
      width: 40 * @rem;
      height: 40 * @rem;
      margin-right: 10 * @rem;
    }
    .center {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .text {
        font-size: 15 * @rem;
        color: #fff;
        span {
          font-size: 14 * @rem;
          color: #f7572d;
        }
      }
    }
  }
  .explain {
    margin-bottom: 20 * @rem;
    color: #fff;
    text-align: center;
  }
  .container {
    background: linear-gradient(180deg, #510a6a 0%, #400b7c 100%);
  }
  .big-title {
    width: 272 * @rem;
    height: 20 * @rem;
    margin: 0 auto;
    .image-bg('~@/assets/images/1212/1212_title1.png');
    &.big-title2 {
      .image-bg('~@/assets/images/1212/1212_title2.png');
    }
  }
  .list {
    padding: 20 * @rem 0 30 * @rem;
    .item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 335 * @rem;
      height: 70 * @rem;
      box-sizing: border-box;
      padding: 15 * @rem 10 * @rem;
      margin: 0 auto 10 * @rem;
      background: linear-gradient(113deg, #99a3ff 0%, #8f48ff 93%);
      border-radius: 12 * @rem 12 * @rem 12 * @rem 12 * @rem;
      .left {
        flex: 1;
        display: flex;
        height: 100%;
        flex-direction: column;
        justify-content: space-between;
        font-size: 12 * @rem;
        font-family: PingFang SC-Regular, PingFang SC;
        color: #ffffff;
        .big-text {
          font-size: 14 * @rem;
          font-family: PingFang SC-Semibold, PingFang SC;
          font-weight: 600;
          span {
            margin-left: 5 * @rem;
          }
        }
      }
      .right {
        width: 57 * @rem;
        height: 29 * @rem;
        text-align: center;
        line-height: 26 * @rem;
        font-size: 14 * @rem;
        color: #fff;
        .image-bg('~@/assets/images/1212/1212_button10.png');
        &.empty {
          .image-bg('~@/assets/images/1212/1212_button8.png');
        }
      }
    }
  }
  .popup {
    width: 290 * @rem;
    padding: 18 * @rem;
    box-shadow: 0 * @rem 2 * @rem 15 * @rem 0 * @rem rgba(248, 0, 0, 0.1);
    border: 2 * @rem solid #9680d9;
    background: #f8f9ff;
    .popup-close {
      width: 33 * @rem;
      height: 27 * @rem;
      background: #835cad url(~@/assets/images/1212/popup-close.png) center
        center no-repeat;
      background-size: 22 * @rem 22 * @rem;
      position: absolute;
      right: -1 * @rem;
      top: -1 * @rem;
      border-radius: 0 12 * @rem 0 12 * @rem;
    }
    .title {
      margin: 25 * @rem 0;
      text-align: center;
      font-size: 16 * @rem;
      font-family: PingFang SC-Semibold, PingFang SC;
      font-weight: 600;
      color: #835cad;
      i {
        color: #f8582e;
      }
    }
    .bottom-button {
      width: 254 * @rem;
      height: 48 * @rem;
      margin: 15 * @rem auto 0;
      .image-bg('~@/assets/images/1212/1212_button11.png');
    }
  }
}
</style>
