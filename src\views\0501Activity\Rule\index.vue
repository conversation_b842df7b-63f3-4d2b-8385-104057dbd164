<template>
  <div class="april-activity-rule">
    <nav-bar-2
      ref="topNavBar"
      :bgStyle="navbarOpacity < 0.5 ? 'transparent-white' : 'transparent'"
      :title="'活动规则'"
      :azShow="true"
      :bgColor="`rgba(255, 255, 255, ${navbarOpacity})`"
      :placeholder="false"
    ></nav-bar-2>
    <div class="bg1"></div>
    <main>
      <div class="big-title">活动介绍：</div>
      <div class="container">
        <div class="text">
          五一活动期间，现金充值游戏可获得等额夺宝币和超值累计奖励，参与夺宝更可赢取大额平台币奖励。
        </div>
        <div class="color mg10">
          活动时间: 2023-4-29 00:00:00 ~ 2023-5-3 23:59:59
        </div>
      </div>
      <div class="container">
        <div class="big-text">1.登录游戏领好礼</div>
        <div class="text">活动期间，累计登录游戏满3天可领取188礼金。</div>
      </div>
      <div class="container">
        <div class="big-text">2.五一超值回馈</div>
        <div class="text">
          活动期间，每日任意游戏实付大于10元，且累计达到3天，可免费领取388金币+3天SVIP。（活动期间每个用户仅可参与一次）
        </div>
        <div class="text mg10">
          温馨提示：<span class="color">仅限游戏内使用微信/支付宝充值</span
          >，如有使用金币/平台币/代金券抵扣的，则只计算抵扣后的现金充值金额。
        </div>
      </div>
      <div class="container">
        <div class="big-text">3.充值获取夺宝币</div>
        <div class="text">
          活动期间，玩家使用现金充值游戏可获得等额夺宝币奖励（<span
            class="color"
            >仅限游戏内使用微信/支付宝充值</span
          >）RMB对夺宝币的比例为1：10，即充值1元获得10夺宝币。如有使用金币/平台币/代金券抵扣的，则只计算抵扣后的现金充值金额。
        </div>
      </div>
      <div class="container">
        <div class="big-text">4.夺宝币累计奖励</div>
        <div class="text">
          累计获得500夺宝币，可领取388金币+1天SVIP<br />
          累计获得3000夺宝币，可领取888金币+3天SVIP<br />
          累计获得5000夺宝币，可领取1888金币+7天SVIP<br />
          累计获得8000夺宝币，可领取3733金币+14天SVIP
        </div>
      </div>
      <div class="container">
        <div class="big-text">5.夺宝乐翻天</div>
        <div class="text">
          玩家可消耗夺宝币进行夺宝，获取一个抽奖码。在所有抽奖码里随机产生一个获奖者。<br />
          每日两期<br />
          夺宝投入时间：早上0-12点，开奖时间：12点后。自动发放给获奖账号，并公示结果。<br />
          夺宝投入时间：中午12点-24点，开奖时间：0点后。自动发放给获奖账号，并公示结果。<br />
          <div class="mg10">奖品：</div>
          <div class="mg10">
            1000平台币，每次夺宝需要消耗1000夺宝币。<br />
            30天SVP，每次夺宝需要消耗500夺宝币。<br />
            8888金币，每次夺宝需要消耗300夺宝币。<br />
            14天SVIP，每次夺宝需要消耗200夺宝币。<br />
            1888金币，每次夺宝需要消耗50夺宝币。<br />
          </div>
        </div>
      </div>
      <div class="container">
        <div class="big-text">活动说明：</div>
        <div class="text">
          1.活动期间充值完成后请返回本活动页面领取夺宝币参与夺宝，并及时领取累计奖励，活动结束后将清空所有夺宝币和奖励领取机会。
        </div>
        <div class="text mg10">
          2.温馨提示：由于游戏厂商的要求，部分游戏内充值暂不支持使用金币支付，请先确定需要用金币充值的游戏在不在可使用名单内。<span
            @click="game_dialog_show = true"
            class="color btn"
            >查看名单></span
          >
        </div>
      </div>
      <div class="bg2"></div>
    </main>
    <!-- 查看名单 -->
    <van-dialog
      v-model="game_dialog_show"
      :lock-scroll="false"
      :show-confirm-button="false"
      :close-on-click-overlay="true"
      class="no-gold-game-popup"
    >
      <div class="search-container">
        <div class="close-search" @click="game_dialog_show = false"></div>
        <div class="search-bar">
          <div class="input-text">
            <form @submit.prevent="searchGame">
              <input
                type="text"
                v-model.trim="input_game"
                placeholder="输入游戏名"
              />
            </form>
          </div>
          <div class="search-btn" @click="searchGame">搜索</div>
        </div>
        <yy-list
          class="yy-list game-list"
          v-model="loading_obj"
          :finished="finished"
          @refresh="onRefresh"
          @loadMore="loadMore"
          :empty="!game_list.length"
          tips="请输入您想搜索的游戏"
          :check="false"
        >
          <div
            class="game-item btn"
            v-for="item in game_list"
            :key="item.id"
            @click="toGame(item)"
          >
            <div class="game-icon">
              <img :src="item.titlepic" alt="" />
            </div>
            <div class="right">
              <div class="game-name">{{ item.title }}</div>
              <div class="use-card" :class="{ can: item.use_gold_pay == 1 }">
                {{ item.use_gold_pay == 1 ? '' : '不' }}支持使用金币支付
              </div>
            </div>
          </div>
        </yy-list>
      </div>
    </van-dialog>
  </div>
</template>
<script>
import { ApiGameCardSearchGame } from '@/api/views/recharge.js';
import { BOX_goToGame, BOX_openInNewWindow } from '@/utils/box.uni.js';
import { envFun } from '@/utils/function';

export default {
  name: 'Rule',
  data() {
    return {
      game_list: [], //查看的游戏名单
      loading_obj: {
        loading: false,
        reloading: false,
      }, //loading对象
      input_game: '', //search的input框
      page: 1, //查看名单的页码
      listRows: 20, //查看名单的大小
      empty: false, //查看名单的空
      game_dialog_show: false, //查看名单弹窗
      finished: false, //防抖
      navbarOpacity: 0,
    };
  },
  mounted() {
    window.addEventListener('scroll', this.handleScroll);
  },
  beforeDestroy() {
    window.removeEventListener('scroll', this.handleScroll);
  },
  methods: {
    // 滚动处理
    handleScroll() {
      let scrollTop =
        document.documentElement.scrollTop ||
        window.pageYOffset ||
        document.body.scrollTop;
      if (Math.abs(scrollTop) > 10) {
        this.navbarOpacity = Math.abs(scrollTop) / 120;
      } else {
        this.navbarOpacity = 0;
      }
    },
    // 搜索不可使用金币的游戏
    async searchGame() {
      if (!this.input_game) {
        this.$toast('请输入游戏名');
        return false;
      }
      this.$toast({
        type: 'loading',
        duration: 0,
        message: '拼命加载中...',
      });
      this.game_list = [];
      await this.getGameList();
    },
    async getGameList(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      let res = await ApiGameCardSearchGame({
        keyword: this.input_game,
        listRows: 20,
        page: this.page,
      });
      this.$toast.clear();
      let { list } = res.data;
      if (action === 1 || this.page === 1) {
        this.game_list = [];
      }
      if (!list.length) {
        this.empty = true;
      }
      this.game_list.push(...list);
      if (list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
    },
    async onRefresh() {
      try {
        await this.getGameList();
      } finally {
        this.loading_obj.reloading = false;
      }
    },
    async loadMore() {
      try {
        await this.getGameList(2);
      } finally {
        this.loading_obj.loading = false;
      }
    },
    toGame(item) {
      BOX_goToGame(
        {
          params: {
            id: item.id,
          },
        },
        { id: item.id },
      );
    },
    toCashGift() {
      BOX_openInNewWindow(
        { name: 'MyCashGift' },
        { url: `https://${envFun()}game.3733.com/#/my_cashgift` },
      );
    },
  },
};
</script>
<style lang="less" scoped>
.april-activity-rule {
  position: relative;
  background: rgba(93, 50, 33, 1);
  overflow: hidden;
  padding-top: 50 * @rem;
  // .bg1 {
  //   width: 100%;
  //   height: 21 * @rem;
  //   .image-bg("~@/assets/images/0501/51_bg1.png");
  // }
  main {
    position: relative;
    background: #f0dcc4;
    overflow: hidden;
    padding: 30 * @rem 0 80 * @rem;
    background-image: url('~@/assets/images/0501/51_bg15.png'),
      url('~@/assets/images/0501/51_bg6.png'),
      url('~@/assets/images/0501/51_bg5.png');
    background-position: 0 0, bottom center, 0 0;
    background-repeat: no-repeat, no-repeat, repeat;
    background-size: 375 * @rem 100 * @rem, 375 * @rem 80 * @rem,
      375 * @rem 30 * @rem;
    .big-title {
      margin: 19 * @rem auto 10 * @rem;
      font-size: 16 * @rem;
      font-weight: 600;
      text-align: center;
      color: #7c343f;
    }
    .container {
      margin: 25 * @rem 24 * @rem 0;
      .big-text {
        font-size: 14 * @rem;
        font-family: PingFang SC-Semibold, PingFang SC;
        font-weight: 600;
        color: #7c343f;
        line-height: 18 * @rem;
        margin-bottom: 7 * @rem;
      }
      .text {
        color: #97603d;
        line-height: 16 * @rem;
      }
    }
    .color {
      color: rgba(255, 7, 7, 1);
    }
    .mg10 {
      margin-top: 10 * @rem;
    }
  }
}
.no-gold-game-popup {
  overflow: unset;
  width: 320 * @rem;
  .search-container {
    box-sizing: border-box;
    width: 320 * @rem;
    height: 450 * @rem;
    padding: 24 * @rem 19 * @rem 10 * @rem;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: unset;
    .close-search {
      width: 24 * @rem;
      height: 24 * @rem;
      background: url(~@/assets/images/close-search.png) center center no-repeat;
      background-size: 24 * @rem 24 * @rem;
      position: absolute;
      right: -10 * @rem;
      top: -10 * @rem;
    }
    .search-bar {
      display: flex;
      align-items: center;
      .input-text {
        width: 240 * @rem;
        height: 35 * @rem;
        border: 1 * @rem solid #e5e5e5;
        border-radius: 18 * @rem;
        flex: 1;
        overflow: hidden;
        form {
          display: block;
          width: 100%;
          height: 100%;
        }
        input {
          box-sizing: border-box;
          display: block;
          width: 100%;
          height: 100%;
          padding: 0 18 * @rem;
          font-size: 15 * @rem;
          color: #333333;
          background-color: #f6f6f6;
        }
      }
      .search-btn {
        font-size: 15 * @rem;
        color: #666666;
        padding-left: 13 * @rem;
        height: 35 * @rem;
        line-height: 35 * @rem;
      }
    }
    .game-list {
      flex: 1;
      overflow: auto;
      margin-top: 10 * @rem;
      .game-item {
        display: flex;
        align-items: center;
        padding: 21 * @rem 0;
        border-bottom: 1 * @rem solid #eeeeee;
        .game-icon {
          width: 50 * @rem;
          height: 50 * @rem;
          border-radius: 10 * @rem;
          background-color: #b5b5b5;
        }
        .right {
          flex: 1;
          min-width: 0;
          margin-left: 10 * @rem;
          .game-name {
            font-size: 16 * @rem;
            font-weight: bold;
            color: #000000;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
          }
          .use-card {
            font-size: 12 * @rem;
            color: #f72e2e;
            margin-top: 10 * @rem;
            &.can {
              color: #36b150;
            }
          }
        }
      }
    }
  }
}
</style>
