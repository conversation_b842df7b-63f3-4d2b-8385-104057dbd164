import { request } from '../index';

/**
 * 双十一活动内容 - 首页
 */
export function Api1111Index(params = {}) {
  return request('/activity/eleven/index', params);
}

/**
 * 双十一活动内容 - 兑换列表
 */
export function Api1111ExchangeList(params = {}) {
  return request('/activity/eleven/exchangeList', params);
}

/**
 * 双十一活动内容 - 获取用户信息
 */
export function Api1111GetSelfInfo(params = {}) {
  return request('/activity/eleven/getSelfInfo', params);
}

/**
 * 双十一抽奖
 */
export function Api1111IntegralLottery(params = {}) {
  return request('/activity/eleven/integralLottery', params);
}

/**
 * 双十一活动内容 - 领取记录
 */
export function Api1111LotteryList(params = {}) {
  return request('/activity/eleven/lotteryList', params);
}

/**
 * 双十一活动内容 - 用户刷新剩余次数
 * @param refresh 是否刷新 1是 0否
 * @param isPtb 是否刮奖次数 1是 0否
 */
export function Api1111RemainLotteryCount(params = {}) {
  return request('/activity/eleven/remainLotteryCount', params);
}

/**
 * 双十一活动内容 - 刮奖
 */
export function Api1111ScratchLottery(params = {}) {
  return request('/activity/eleven/scratchLottery', params);
}

/**
 * 双十一活动内容 - 兑换
 * @param type 兑换类型
 */
export function Api1111TakeExchange(params = {}) {
  return request('/activity/eleven/takeExchange', params);
}
