<template>
  <div class="lucky-value-task-gage">
    <nav-bar-2
      ref="topNavBar"
      title="活动规则"
      :azShow="true"
      :placeholder="false"
    >
    </nav-bar-2>
    <div class="main">
      <div class="rule-container">
        <h2>一、活动时间</h2>
        <p>2025.01.27（0点）——2025.02.05（24点）</p>
        <p>
          注：【迎新接福】活动的福气值获取渠道会在2月4日24点关闭，2月5日当天不可再获得福气值，仅供开启福袋。
        </p>

        <h2>二、活动玩法说明</h2>
        <p>
          活动一共设立三个会场，分别是【迎新接福】【妙笔贺岁】【入会见喜】，活动玩法如下：
        </p>
        <p>
          <span>【迎新接福】 </span><br />
          每位用户进入活动页登录账号后，自动获得1个1级福袋，2025年1月27日至2025年2月4日期间<span>完成指定任务或平台充值</span>均可获得福气值，其中<span
            class="red"
            >充值金额与福气值比例为1比1</span
          >，即每充值1元即可获得1点福气值，充值任务所给福气值与充值金额所得福气值可叠加；当福气值累积到指定数值，福袋会自动升至下一级；
          福袋共十个等级，每个等级福袋内含奖品及概率会存在些许不同，<span
            class="red"
          >
            福袋等级越高，开启时能获得的奖励越好； </span
          >普通用户仅有1次福袋开启机会，SVIP用户额外增加一次，即<span>SVIP用户拥有两次福袋开启机会；</span>福袋需要<span
            class="red"
            >手动开启</span
          >，活动期间用户可以在任意时刻自行决定是否打开福袋，但若希望活动收益最大化，<span
            >请注意福气值的积累和开启福袋的时机；</span
          ><br />
          *平台充值指：充值平台币/开通SVIP服务/开通省钱卡/开通云挂机卡，暂不包含游戏内实际付费；<br />
          注：2025年2月5日全天不可再获得福气值，仅可开启福袋。
        </p>
        <p>
          福袋升级所需福气值及对应奖励如下：<br />
          0~100福气值 1级初始福袋<br />
          101~200福气值 2级福袋<br />
          201~300福气值 3级福袋<br />
          301~500福气值 4级福袋<br />
          501~800福气值 5级福袋<br />
          801~900福气值 6级福袋<br />
          901~1000福气值 7级福袋<br />
          1001~1200福气值 8级福袋<br />
          1201~1500福气值 9级福袋<br />
          1501及以上福气值 10级福袋<br />
          注*：2025年2月5日全天不可再获得福气值，仅可开启福袋。<br />
        </p>
        <p>
          <span>【妙笔贺岁】 </span><br />
          活动提供三个上联，用户可通过自输入文本或点击下方字符的方式完成下联，活动期间对对联次数不限，首次完成对联并成功分享，可获得微信红包封面兑换码*1（微信红包封面限量200份，先到先得）；用户所创作出的下联会进行集中展示，用户可根据喜好自行点赞。
          <br />* 此外，获得红包封面须确保您的应用为最新版，请及时升级应用。
        </p>
        <p>
          <span>【入会见喜】 </span><br />
          活动期间用户开通SVIP可得新年红包，每一笔SVIP成功付费订单可以获得1个新年红包，单个用户活动期间最多获得三个红包。新年红包内含奖励：
          88金币、288金币、5平台币、10平台币、30平台币、2025金币、满10-6代金券、10元京东卡，奖励随机掉落100%有奖。
          <br />
          10元京东卡限量50张，奖励先到先得；<br />
          本次活动折扣价格<span class="red"
            >不可与其他优惠活动如“svip首单109”等叠加使用 </span
          >。
        </p>
      </div>
    </div>
  </div>
</template>

<script>
import { mapActions } from 'vuex';
import { platform, boxInit, BOX_login } from '@/utils/box.uni.js';
export default {
  name: 'LuckyValueTask',
  props: {},
  components: {},
  data() {
    return {};
  },
  async created() {
    await this.getInitData();
  },
  async mounted() {
    // 官包触发页面刷新（将onResume挂载在window上）
    if (platform == 'android') {
      window.onResume = this.onResume;
    }
    document.body.addEventListener('touchstart', function () {});
  },
  beforeDestroy() {
    document.body.removeEventListener('touchstart', function () {});
    if (this.timeClock) {
      clearInterval(this.timeClock);
      this.timeClock = null;
    }
  },
  async activated() {
    await this.getInitData();
  },
  methods: {
    ...mapActions({
      SET_USER_INFO: 'user/SET_USER_INFO',
    }),
    async onResume() {
      await boxInit();
      await this.SET_USER_INFO(true);
      await this.getInitData();
    },
    login() {
      BOX_login();
    },
    // 初始化信息
    getInitData() {},
  },
};
</script>

<style lang="less" scoped>
.lucky-value-task-gage {
  font-family: PingFang SC, PingFang SC;
  display: flex;
  flex-direction: column;
  width: 100%;
  overflow: hidden;
  min-height: 100vh;
  background: #fafafa;
  .main {
    width: 100%;
    position: relative;
    height: 100vh;
    flex: 1;
    min-height: 0;
    margin-top: 54 * @rem;
    padding-top: @safeAreaTop;
    padding-top: @safeAreaTopEnv;
    .rule-container {
      box-sizing: border-box;
      flex: 1;
      overflow-y: auto;
      color: #7a5252;
      margin-top: 10 * @rem;
      padding: 10 * @rem 20 * @rem;
      h2 {
        font-size: 15 * @rem;
        line-height: 26 * @rem;
        font-weight: bold;
        margin-bottom: 2 * @rem;
        color: #5a2a2a;
      }
      p {
        font-size: 13 * @rem;
        line-height: 22 * @rem;
        margin-bottom: 20 * @rem;
        span {
          font-weight: bold;
          &.red {
            color: #ff6464;
          }
        }
      }
    }
  }
}
</style>
