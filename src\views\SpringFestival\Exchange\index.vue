<template>
  <div class="exchange-page">
    <nav-bar-2
      ref="topNavBar"
      bgStyle="transparent-white"
      :azShow="true"
      :title="'爆竹获取'"
      :placeholder="false"
      v-if="navBgTransparent"
    ></nav-bar-2>
    <nav-bar-2
      :title="'爆竹获取'"
      :placeholder="false"
      :azShow="true"
      :border="true"
      v-else
    >
    </nav-bar-2>
    <div class="user-info">
      <div class="left">
        <UserAvatar />
      </div>
      <div class="center">
        <div class="text">当前账号：{{ userInfo.nickname }}</div>
        <div class="text">
          当前爆竹数：<span>{{ baozhu_count }}</span
          >个
        </div>
      </div>
      <div class="bg"></div>
    </div>
    <section class="section1" v-if="list[1]?.length">
      <div class="item" v-for="(item, index) in list[1]" :key="index">
        <div class="left">
          <div class="big-text">{{ item.title }}</div>
          <div class="small-text">{{ item.desc }}</div>
        </div>
        <div
          class="right btn"
          :class="{ empty: !item.can_receive }"
          @click="handleExchange(item.id, item.is_svip)"
        >
          {{ item.is_receive ? '已领取' : '领取' }}
        </div>
      </div>
    </section>
    <section class="section2" v-if="list[1]?.length">
      <div class="item" v-for="(item, index) in list[0]" :key="index">
        <div class="left">
          <div class="big-text">{{ item.title }}</div>
          <div class="small-text">{{ item.desc }}</div>
        </div>
        <div
          class="right btn"
          :class="{ empty: !item.can_receive }"
          @click="handleExchange(item.id, item.is_svip)"
        >
          {{ item.is_receive ? '已领取' : '领取' }}
        </div>
      </div>
    </section>
    <!-- 通用消息弹窗 -->
    <van-popup
      v-model="message_popup"
      :lock-scroll="false"
      :show-confirm-button="false"
      :close-on-click-overlay="false"
      class="popup exchange-popup"
    >
      <div class="text" v-html="message_content"></div>
      <div @click="message_popup = false" class="bottom-button">确定</div>
    </van-popup>
    <!-- vip提醒 -->
    <van-popup
      v-model="svip_popup"
      :lock-scroll="false"
      :show-confirm-button="false"
      :close-on-click-overlay="false"
      class="popup vip-popup"
    >
      <div class="text">
        您当前SVIP天数不足30天，<br />续费SVIP满30天可获取爆竹哦~
      </div>
      <div class="button-container">
        <div @click="svip_popup = false" class="left btn">取消</div>
        <div @click="openSvipRecharge" class="right btn">立即开通</div>
      </div>
    </van-popup>
    <svip-recharge-popup @success="init"></svip-recharge-popup>
  </div>
</template>
<script>
import {
  ApiChunJieBaozhu,
  ApiChunJieBaozhuExtraReward,
} from '@/api/views/spring_festival.js';
import { mapGetters, mapMutations } from 'vuex';
import UserAvatar from '@/components/user-avatar';
import svipRechargePopup from '@/components/svip-recharge-popup';

export default {
  data() {
    return {
      baozhu_count: 0, // 当前爆竹数
      list: [], //任务
      message_popup: false, //通用消息弹窗
      message_content: '', // 通用消息内容
      svip_popup: false, //vip不足弹窗
      navBgTransparent: true,
      svip_diff_day: 0, // svip剩余天数
    };
  },
  computed: {
    ...mapGetters({
      userInfo: 'user/userInfo',
    }),
  },
  async created() {
    await this.init();
  },
  mounted() {
    // 获取顶部导航栏的高度
    this.stickyOffsetTop =
      document.querySelector('.van-nav-bar').offsetHeight + 'px';
    window.addEventListener('scroll', this.handleScroll);
  },
  methods: {
    // 滚动处理
    handleScroll(e) {
      let scrollTop = e.target.scrollingElement.scrollTop;
      if (scrollTop > 50) {
        this.navBgTransparent = false;
      } else {
        this.navBgTransparent = true;
      }
    },
    async init() {
      try {
        const res = await ApiChunJieBaozhu();
        let { baozhu_count, info, men_info } = res.data;
        this.baozhu_count = baozhu_count;
        this.list = info;
        console.log(men_info);
        this.svip_diff_day = men_info?.svip_diff_day ?? 0;
      } catch {}
    },
    async handleExchange(id, is_svip) {
      if (is_svip) {
        if (this.svip_diff_day < 30) {
          console.log(is_svip, this.svip_diff_day);
          this.svip_popup = true;
          return false;
        }
      }
      try {
        const res = await ApiChunJieBaozhuExtraReward({ id });
        this.message_content = res.msg;
        this.message_popup = true;
        await this.init();
      } catch {}
    },
    openSvipRecharge() {
      this.setShowSvipRechargePopup(true);
      this.svip_popup = false;
    },
    ...mapMutations({
      setShowSvipRechargePopup: 'recharge/setShowSvipRechargePopup',
    }),
  },
  components: {
    svipRechargePopup,
    UserAvatar,
  },
};
</script>
<style lang="less" scoped>
.exchange-page {
  min-height: 100vh;
  padding-top: 50 * @rem;
  background-color: rgba(255, 82, 62, 1);
  .user-info {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 90 * @rem;
    padding: 0 0 0 24 * @rem;
    .left {
      width: 40 * @rem;
      height: 40 * @rem;
      border: 1 * @rem solid rgba(254, 234, 204, 1);
      border-radius: 50%;
      margin-right: 10 * @rem;
    }
    .center {
      flex: 1;
      height: 40 * @rem;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .text {
        font-size: 13 * @rem;
        letter-spacing: 0.5 * @rem;
        color: rgba(255, 248, 169, 1);
        span {
          color: rgba(255, 240, 102, 1);
        }
      }
    }
    .bg {
      flex: 0 0 109 * @rem;
      width: 109 * @rem;
      height: 90 * @rem;
      .image-bg('~@/assets/images/spring-festival/sf_icon15.png');
    }
  }
  section {
    position: relative;
    width: 351 * @rem;
    box-sizing: border-box;
    padding: 0 22 * @rem;
    margin: 35 * @rem auto 60 * @rem;
    background: #feeacc;
    border-radius: 20 * @rem;
    min-height: 100 * @rem;
    &::before {
      content: '';
      position: absolute;
      top: -30 * @rem;
      left: 50%;
      transform: translate(-50%, 0);
      width: 244 * @rem;
      height: 49 * @rem;
    }
    &.section1 {
      &::before {
        .image-bg('~@/assets/images/spring-festival/sf_title4.png');
      }
    }
    &.section2 {
      &::before {
        .image-bg('~@/assets/images/spring-festival/sf_title5.png');
      }
    }
    .item {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 60 * @rem;
      border-bottom: 1 * @rem solid #eed8b6;
      &:last-of-type {
        border: none;
      }
      .left {
        flex: 1;
        .big-text {
          font-size: 14 * @rem;
          font-weight: 600;
          color: #a6470e;
          line-height: 20 * @rem;
        }
        .small-text {
          font-size: 11 * @rem;
          color: #b17836;
          line-height: 16 * @rem;
        }
      }
      .right {
        flex: 0 0 58 * @rem;
        width: 58 * @rem;
        height: 28 * @rem;
        background: linear-gradient(360deg, #ff6363 0%, #ff6e6e 100%);
        box-shadow: inset 0 * @rem 6 * @rem 10 * @rem 0 * @rem
            rgba(255, 233, 233, 0.71),
          inset 0 * @rem -4 * @rem 9 * @rem 0 * @rem rgba(255, 37, 0, 0.69);
        border-radius: 26 * @rem;
        font-size: 13 * @rem;
        color: #ffffff;
        text-align: center;
        line-height: 28 * @rem;
        &.empty {
          box-shadow: none;
          background: #dabd91;
        }
      }
    }
  }
  .exchange-popup {
    &::before {
      content: '';
      position: absolute;
      top: -41 * @rem;
      left: 50%;
      transform: translate(-50%, 0);
      width: 82 * @rem;
      height: 82 * @rem;
      .image-bg('~@/assets/images/spring-festival/sf_icon16.png');
    }
    .text {
      margin: 50 * @rem auto 30 * @rem;
      text-align: center;
    }
  }
  .vip-popup {
    .text {
      margin: 30 * @rem auto;
      text-align: center;
    }
    .button-container {
      display: flex;
      justify-content: space-between;
      padding: 0 20 * @rem;
      .btn {
        display: flex;
        justify-content: center;
        align-items: center;
      }
      .left {
        width: 84 * @rem;
        height: 35 * @rem;
        border-radius: 18 * @rem;
        border: 1 * @rem solid #ff523e;
        font-size: 13 * @rem;
        font-family: PingFangHK-Regular, PingFangHK;
        font-weight: 400;
        color: #ff523e;
      }
      .right {
        width: 132 * @rem;
        height: 35 * @rem;
        background: #ff523e;
        border-radius: 18 * @rem;
        font-size: 13 * @rem;
        color: #feeacc;
      }
    }
  }
  .popup {
    width: 268 * @rem;
    height: 162 * @rem;
    border-radius: 20 * @rem;
    background: rgba(254, 234, 204, 1);
    overflow: unset;
    .text {
      font-size: 15 * @rem;
      color: #a6470e;
      line-height: 23 * @rem;
    }
    .bottom-button {
      width: 102 * @rem;
      height: 35 * @rem;
      background: rgba(255, 82, 62, 1);
      border-radius: 17.5 * @rem;
      margin: 15 * @rem auto 0;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 13 * @rem;
      color: rgba(254, 234, 204, 1);
    }
  }
}
</style>
