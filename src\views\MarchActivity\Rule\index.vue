<template>
  <div class="shuangshier-rule">
    <nav-bar-2
      ref="topNavBar"
      bgStyle="transparent-white"
      :azShow="true"
      :title="'活动规则'"
      :placeholder="false"
      v-if="navBgTransparent"
    ></nav-bar-2>
    <nav-bar-2 :title="'活动规则'" :placeholder="false" :border="true" v-else>
    </nav-bar-2>
    <div class="section">
      <div class="title"><span>活动介绍：</span></div>
      <div class="text">
        五月暖春，阳光明媚，万物生长。活动期间，玩家可获得一棵橘树种子，通过完成任务获取养分对种子进行培养，生长至不同形态可领取对应奖励一份，更有机率额外获得黄金橘子，获取超值福利。
      </div>
      <div class="text color">活动时间：5月26日0点0分-5月28日23点59分</div>
    </div>
    <div class="section">
      <div class="container">
        <div class="big-text">一．培养得好礼</div>
        <div class="text">
          <span class="color">种子期：</span>初始状态，无奖励。<br />
          <span class="color">幼苗期：</span>
          所需养分10奖励：<br />随机获得188金币+1天SVIP或88平台币其中一个（活动期间限一次）<br />
          <span class="color">生长期：</span>
          所需养分30奖励：<br />随机获得888金币+3天SVIP或188平台币其中一个（活动期间限一次）<br />
          <span class="color">开花期：</span>
          所需养分60奖励：<br />随机获得1888金币+7天SVIP或388平台币其中一个（活动期间限一次）<br />
          <span class="color">结果期：</span>
          所需养分90奖励：<br />随机获得8888金币+14天SVIP或1888平台币其中一个（活动期间限一次）<br />每次领取奖励，均有几率触发幸运大奖，额外奖励黄金橘子一个，开启可获得（1888金币，5888金币，1888平台币）其中一个。
        </div>
      </div>
      <div class="container">
        <div class="big-text">二．每日任务</div>
        <div class="text">
          每日登录游戏，可领取1点养分。（每日仅一次）<br />
          每日充值任意平台币，可领取2点养分。（每日仅一次）<br />
          SVIP大于30天的用户每日可领取1点养分。（每日仅一次）<br />
          每日游戏内实付达到10元，可领取2点养分。（每日仅一次）<br />
          每日游戏内实付达到100元，可领取5点养分。（每日仅一次）<br />
          完成当日全部每日任务可额外领取10点养分和惊喜宝箱一个。（每日仅一次）<br />
          温馨提示：仅限<span class="color2">游戏内使用微信/支付宝充值</span
          >，如有使用金币/平台币/代金券抵扣的，则只计算抵扣后的现金充值金额。<br />
          惊喜宝箱可随机开出（388金币，888金币，3天SVIP，388平台币）其中一个。
        </div>
      </div>
      <div class="container">
        <div class="big-text">三．累计任务</div>
        <div class="text">
          活动期间<br />
          游戏累计实付达到500元，可领取10点养分。（活动期间仅一次）<br />
          游戏累计实付达到1000元，可领取20点养分。（活动期间仅一次）<br />
          游戏累计实付达到2000元，可领取40点养分。（活动期间仅一次）<br />
          温馨提示：仅限游戏内使用微信/支付宝充值，如有使用金币/平台币/代金券抵扣的，则只计算抵扣后的现金充值金额。
        </div>
      </div>
      <div class="container">
        <div class="big-text">四．活动规则</div>
        <div class="text">
          1.概率公示：<br />
          惊喜宝箱奖励：388金币（50%），888金币（35%），3天SVIP（10%），388平台币（5%）<br />
          幼苗期奖励：188金币+1天SVIP（90%），88平台币（10%）<br />
          生长期奖励：888金币+3天SVIP（90%），188平台币（10%）<br />
          开花期奖励：1888金币+7天SVIP（90%），388平台币（10%）<br />
          结果期奖励：8888金币+14天SVIP（90%），1888平台币（10%）<br />
          黄金橘子奖励：1888金币（60%），5888金币（30%），1888平台币（10%）<br />
          幸运大奖触发几率：幼苗期（1%），生长期（3%），开花期（5%），结果期（8%）<br />
          2.活动期间充值完成后请返回本活动页面点击<span class="color3"
            >【刷新】</span
          >领取养分，并及时培养兑换奖励，活动结束后将清空所有养分和奖励领取次数。<br />
          3.温馨提示：由于游戏厂商的要求，部分游戏内充值暂不支持使用金币支付，请先确定需要用金币充值的游戏在不在可使用名单内。
        </div>
        <div class="text">
          <span @click="game_dialog_show = true" class="color4 btn"
            >查看名单></span
          >
        </div>
      </div>
    </div>
    <!-- 查看名单 -->
    <van-dialog
      v-model="game_dialog_show"
      :lock-scroll="false"
      :show-confirm-button="false"
      :close-on-click-overlay="true"
      class="no-gold-game-popup"
    >
      <div class="search-container">
        <div class="close-search" @click="game_dialog_show = false"></div>
        <div class="search-bar">
          <div class="input-text">
            <form @submit.prevent="searchGame">
              <input
                type="text"
                v-model.trim="input_game"
                placeholder="输入游戏名"
              />
            </form>
          </div>
          <div class="search-btn" @click="searchGame">搜索</div>
        </div>
        <yy-list
          class="yy-list game-list"
          v-model="loading_obj"
          :finished="finished"
          @refresh="onRefresh"
          @loadMore="loadMore"
          :empty="!game_list.length"
          tips="请输入您想搜索的游戏"
          :check="false"
        >
          <div
            class="game-item btn"
            v-for="item in game_list"
            :key="item.id"
            @click="toGame(item)"
          >
            <div class="game-icon">
              <img :src="item.titlepic" alt="" />
            </div>
            <div class="right">
              <div class="game-name">{{ item.title }}</div>
              <div class="use-card" :class="{ can: item.use_gold_pay == 1 }">
                {{ item.use_gold_pay == 1 ? '' : '不' }}支持使用金币支付
              </div>
            </div>
          </div>
        </yy-list>
      </div>
    </van-dialog>
  </div>
</template>
<script>
import { ApiGameCardSearchGame } from '@/api/views/recharge.js';
import { BOX_goToGame, BOX_openInNewWindow } from '@/utils/box.uni.js';
import { envFun } from '@/utils/function';

export default {
  name: 'Rule',
  data() {
    return {
      game_list: [], //查看的游戏名单
      loading_obj: {
        loading: false,
        reloading: false,
      }, //loading对象
      input_game: '', //search的input框
      page: 1, //查看名单的页码
      listRows: 20, //查看名单的大小
      empty: false, //查看名单的空
      game_dialog_show: false, //查看名单弹窗
      finished: false, //防抖
      navBgTransparent: true,
    };
  },
  mounted() {
    // 获取顶部导航栏的高度
    this.stickyOffsetTop =
      document.querySelector('.van-nav-bar').offsetHeight + 'px';
    window.addEventListener('scroll', this.handleScroll);
  },
  methods: {
    // 滚动处理
    handleScroll(e) {
      let scrollTop = e.target.scrollingElement.scrollTop;
      if (scrollTop > 100) {
        this.navBgTransparent = false;
      } else {
        this.navBgTransparent = true;
      }
    },
    // 搜索不可使用金币的游戏
    async searchGame() {
      if (!this.input_game) {
        this.$toast('请输入游戏名');
        return false;
      }
      this.$toast({
        type: 'loading',
        duration: 0,
        message: '拼命加载中...',
      });
      this.game_list = [];
      await this.getGameList();
    },
    async getGameList(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      let res = await ApiGameCardSearchGame({
        keyword: this.input_game,
        listRows: 20,
        page: this.page,
      });
      this.$toast.clear();
      let { list } = res.data;
      if (action === 1 || this.page === 1) {
        this.game_list = [];
      }
      if (!list.length) {
        this.empty = true;
      }
      this.game_list.push(...list);
      if (list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
    },
    async onRefresh() {
      try {
        await this.getGameList();
      } finally {
        this.loading_obj.reloading = false;
      }
    },
    async loadMore() {
      try {
        await this.getGameList(2);
      } finally {
        this.loading_obj.loading = false;
      }
    },
    toGame(item) {
      BOX_goToGame(
        {
          params: {
            id: item.id,
          },
        },
        { id: item.id },
      );
    },
    toCashGift() {
      BOX_openInNewWindow(
        { name: 'MyCashGift' },
        { url: `https://${envFun()}game.3733.com/#/my_cashgift` },
      );
    },
  },
};
</script>
<style lang="less" scoped>
.shuangshier-rule {
  background-color: #1e5f65;
  padding: 50 * @rem 22 * @rem 22 * @rem;
  overflow: hidden;
  .title {
    margin: 20 * @rem 0 15 * @rem;
    span {
      background-image: url('~@/assets/images/march-activity/mar_bg5.png');
      background-size: 100%;
      background-repeat: no-repeat;
      background-position: 0 16 * @rem;
      font-size: 16 * @rem;
      font-family: PingFang HK-Semibold, PingFang HK;
      font-weight: 600;
      color: #ffffff;
    }
  }
  .big-text {
    margin-bottom: 6 * @rem;
    font-size: 14 * @rem;
    font-family: PingFang SC-Semibold, PingFang SC;
    font-weight: 600;
    color: #ffffff;
    line-height: 21 * @rem;
  }
  .text {
    margin-bottom: 6 * @rem;
    font-size: 11 * @rem;
    color: #ffffff;
    line-height: 18 * @rem;
  }
  .color {
    color: #c3f82e;
  }
  .color2 {
    color: #ff8853;
  }
  .color3 {
    color: #0ff4b0;
  }
  .color4 {
    color: #ffe922;
  }
  .underline {
    text-decoration: underline;
  }
  .container {
    margin-top: 15 * @rem;
  }
}
.no-gold-game-popup {
  overflow: unset;
  width: 320 * @rem;
  .search-container {
    box-sizing: border-box;
    width: 320 * @rem;
    height: 450 * @rem;
    padding: 24 * @rem 19 * @rem 10 * @rem;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: unset;
    .close-search {
      width: 24 * @rem;
      height: 24 * @rem;
      background: url(~@/assets/images/close-search.png) center center no-repeat;
      background-size: 24 * @rem 24 * @rem;
      position: absolute;
      right: -10 * @rem;
      top: -10 * @rem;
    }
    .search-bar {
      display: flex;
      align-items: center;
      .input-text {
        width: 240 * @rem;
        height: 35 * @rem;
        border: 1 * @rem solid #e5e5e5;
        border-radius: 18 * @rem;
        flex: 1;
        overflow: hidden;
        form {
          display: block;
          width: 100%;
          height: 100%;
        }
        input {
          box-sizing: border-box;
          display: block;
          width: 100%;
          height: 100%;
          padding: 0 18 * @rem;
          font-size: 15 * @rem;
          color: #333333;
          background-color: #f6f6f6;
        }
      }
      .search-btn {
        font-size: 15 * @rem;
        color: #666666;
        padding-left: 13 * @rem;
        height: 35 * @rem;
        line-height: 35 * @rem;
      }
    }
    .game-list {
      flex: 1;
      overflow: auto;
      margin-top: 10 * @rem;
      .game-item {
        display: flex;
        align-items: center;
        padding: 21 * @rem 0;
        border-bottom: 1 * @rem solid #eeeeee;
        .game-icon {
          width: 50 * @rem;
          height: 50 * @rem;
          border-radius: 10 * @rem;
          background-color: #b5b5b5;
        }
        .right {
          flex: 1;
          min-width: 0;
          margin-left: 10 * @rem;
          .game-name {
            font-size: 16 * @rem;
            font-weight: bold;
            color: #000000;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
          }
          .use-card {
            font-size: 12 * @rem;
            color: #f72e2e;
            margin-top: 10 * @rem;
            &.can {
              color: #36b150;
            }
          }
        }
      }
    }
  }
}
</style>
