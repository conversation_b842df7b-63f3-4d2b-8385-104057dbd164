<template>
  <div class="shuangshier-rule">
    <nav-bar-2
      ref="topNavBar"
      bgStyle="transparent-white"
      :azShow="true"
      :title="'活动规则'"
      :placeholder="false"
      v-if="navBgTransparent"
    ></nav-bar-2>
    <nav-bar-2 :title="'活动规则'" :placeholder="false" :border="true" v-else>
    </nav-bar-2>
    <div class="section">
      <div class="title"><span>活动时间：</span></div>
      <div class="text">2022年12月31日0点0分 - 2023年1月02日23点59分</div>
    </div>
    <div class="section">
      <div class="title"><span>活动内容：</span></div>
      <div class="container">
        <div class="big-text">一.游戏现金充值得积分</div>
        <div class="text">
          每次抽卡可在2、0、2、3、元、旦、快、乐、3733好运卡，8张卡牌中随机获得一张。
        </div>
      </div>
      <div class="container">
        <div class="big-text">二．抽卡机会获取</div>
        <div class="text">
          SVIP大于30天的玩家可免费获得一次抽卡机会（每日仅一次）<br />
          当日游戏单笔订单现金支付满10元获得一次抽卡机会（每日仅三次）<br />
          当日游戏现金支付每满100元可额外获取一次机会（每日仅三次）<br />
          当日游戏现金支付满500元可任选一张卡牌获得（每日仅一次，好运卡除外）<br />
          温馨提示：仅限<span class="yellow">游戏内使用微信/支付宝充值</span
          >，如有使用金币/平台币/代金券抵扣的，则只计算抵扣后的现金充值金额。
        </div>
      </div>
      <div class="container">
        <div class="big-text">三．消耗卡牌兑奖</div>
        <div class="yellow margin10">普通奖励</div>
        <div class="text"><span class="green">2+0</span>兑换：1天SVIP</div>
        <div class="text"><span class="green">2+3</span>兑换：88金币</div>
        <div class="text"><span class="green">2+0+2+3</span>兑换：588金币</div>
        <div class="text"><span class="green">元+旦</span>兑换：1天SVIP</div>
        <div class="text"><span class="green">快+乐</span>兑换：88金币</div>
        <div class="text">
          <span class="green">元+旦+快+乐</span>兑换：3天SVIP
        </div>
        <div class="yellow margin10">最终奖励</div>
        <div class="text">
          2+0+2+3+元+旦+快+乐 兑换：1888金币+7天SVIP+瓜分大奖
          机会（活动期间仅一次）
        </div>
      </div>
      <div class="container">
        <div class="big-text">四．瓜分千万红包</div>
        <div class="text">
          集齐卡牌的玩家可获得一次瓜分2023万金币红包的机会，金额随机。
        </div>
      </div>
      <div class="container">
        <div class="big-text">五．天选之人</div>
        <div class="text">
          前30个集齐卡牌（2023元旦快乐）并兑换最终奖励的玩家还将额外
          获得2023金币（以后台显示兑换的时间为准），活动结束后1-2个工
          作日公示获奖名单并进行奖励发放。
        </div>
      </div>
      <div class="container">
        <div class="big-text">六．规则说明</div>
        <div class="text">
          1.卡牌抽卡概率公示<br />
          2/0/3/元/旦/快/乐 抽卡概率均为14%<br />3733好运卡 抽卡概率为4%
        </div>
        <div class="text yellow">
          2.好运卡奖励：凭卡可在活动期间联系客服申请一张5折代金券（仅限BT游戏）。
        </div>
        <div class="text">
          3.活动期间充值完成后请返回本活动页面点击<span class="green"
            >【刷新】</span
          >查看抽卡次数进行抽卡并及时兑换奖励，活动结束后将清空所有抽卡次数和卡牌。
          4.温馨提示：由于游戏厂商的要求，部分游戏内充值暂不支持使用金币支付，请先确定需要用金币充值的游戏在不在可使用名单内。<br />
          <span @click="game_dialog_show = true" class="yellow btn"
            >查看名单></span
          >
        </div>
      </div>
    </div>
    <!-- 查看名单 -->
    <van-dialog
      v-model="game_dialog_show"
      :lock-scroll="false"
      :show-confirm-button="false"
      :close-on-click-overlay="true"
      class="no-gold-game-popup"
    >
      <div class="search-container">
        <div class="close-search" @click="game_dialog_show = false"></div>
        <div class="search-bar">
          <div class="input-text">
            <form @submit.prevent="searchGame">
              <input
                type="text"
                v-model.trim="input_game"
                placeholder="输入游戏名"
              />
            </form>
          </div>
          <div class="search-btn" @click="searchGame">搜索</div>
        </div>
        <yy-list
          class="yy-list game-list"
          v-model="loading_obj"
          :finished="finished"
          @refresh="onRefresh"
          @loadMore="loadMore"
          :empty="!game_list.length"
          tips="请输入您想搜索的游戏"
          :check="false"
        >
          <div
            class="game-item btn"
            v-for="item in game_list"
            :key="item.id"
            @click="toGame(item)"
          >
            <div class="game-icon">
              <img :src="item.titlepic" alt="" />
            </div>
            <div class="right">
              <div class="game-name">{{ item.title }}</div>
              <div class="use-card" :class="{ can: item.use_gold_pay == 1 }">
                {{ item.use_gold_pay == 1 ? '' : '不' }}支持使用金币支付
              </div>
            </div>
          </div>
        </yy-list>
      </div>
    </van-dialog>
  </div>
</template>
<script>
import { ApiGameCardSearchGame } from '@/api/views/recharge.js';
import { BOX_goToGame } from '@/utils/box.uni.js';

export default {
  name: 'Rule',
  data() {
    return {
      game_list: [], //查看的游戏名单
      loading_obj: {
        loading: false,
        reloading: false,
      }, //loading对象
      input_game: '', //search的input框
      page: 1, //查看名单的页码
      listRows: 20, //查看名单的大小
      empty: false, //查看名单的空
      game_dialog_show: false, //查看名单弹窗
      finished: false, //防抖
      navBgTransparent: true,
    };
  },
  mounted() {
    // 获取顶部导航栏的高度
    this.stickyOffsetTop =
      document.querySelector('.van-nav-bar').offsetHeight + 'px';
    window.addEventListener('scroll', this.handleScroll);
  },
  methods: {
    // 滚动处理
    handleScroll(e) {
      let scrollTop = e.target.scrollingElement.scrollTop;
      if (scrollTop > 100) {
        this.navBgTransparent = false;
      } else {
        this.navBgTransparent = true;
      }
    },
    // 搜索不可使用金币的游戏
    async searchGame() {
      if (!this.input_game) {
        this.$toast('请输入游戏名');
        return false;
      }
      this.$toast({
        type: 'loading',
        duration: 0,
        message: '拼命加载中...',
      });
      this.game_list = [];
      await this.getGameList();
    },
    async getGameList(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      let res = await ApiGameCardSearchGame({
        keyword: this.input_game,
        listRows: 20,
        page: this.page,
      });
      this.$toast.clear();
      let { list } = res.data;
      if (action === 1 || this.page === 1) {
        this.game_list = [];
      }
      if (!list.length) {
        this.empty = true;
      }
      this.game_list.push(...list);
      if (list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
    },
    async onRefresh() {
      try {
        await this.getGameList();
      } finally {
        this.loading_obj.reloading = false;
      }
    },
    async loadMore() {
      try {
        await this.getGameList(2);
      } finally {
        this.loading_obj.loading = false;
      }
    },
    toGame(item) {
      BOX_goToGame(
        {
          params: {
            id: item.id,
          },
        },
        { id: item.id },
      );
    },
  },
};
</script>
<style lang="less" scoped>
.shuangshier-rule {
  font-family: PingFang SC-Regular, PingFang SC;
  .image-bg('~@/assets/images/0101/0101_bg3.png');
  background-color: #2f295c;
  padding: 50 * @rem 22 * @rem 22 * @rem;
  overflow: hidden;
  .title {
    margin: 20 * @rem 0 15 * @rem;
    font-size: 16 * @rem;
    font-family: PingFang HK-Semibold, PingFang HK;
    font-weight: 600;
    color: #ffffff;
    span {
      background-image: url('~@/assets/images/0101/0101_bg2.png');
      background-size: 100%;
      background-repeat: no-repeat;
      background-position: 0 16 * @rem;
    }
  }
  .big-text {
    margin-bottom: 6 * @rem;
    font-size: 14 * @rem;
    font-family: PingFang SC-Semibold, PingFang SC;
    font-weight: 600;
    color: #ffffff;
    line-height: 21 * @rem;
  }
  .text {
    font-size: 11 * @rem;
    color: #ffffff;
    line-height: 18 * @rem;
  }
  .yellow {
    color: #ffe500;
  }
  .green {
    color: #0ff4b0;
  }
  .margin10 {
    margin: 10 * @rem 0;
  }
  .container {
    margin-top: 15 * @rem;
  }
}
.no-gold-game-popup {
  overflow: unset;
  width: 320 * @rem;
  .search-container {
    box-sizing: border-box;
    width: 320 * @rem;
    height: 450 * @rem;
    padding: 24 * @rem 19 * @rem 10 * @rem;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: unset;
    .close-search {
      width: 24 * @rem;
      height: 24 * @rem;
      background: url(~@/assets/images/close-search.png) center center no-repeat;
      background-size: 24 * @rem 24 * @rem;
      position: absolute;
      right: -10 * @rem;
      top: -10 * @rem;
    }
    .search-bar {
      display: flex;
      align-items: center;
      .input-text {
        width: 240 * @rem;
        height: 35 * @rem;
        border: 1 * @rem solid #e5e5e5;
        border-radius: 18 * @rem;
        flex: 1;
        overflow: hidden;
        form {
          display: block;
          width: 100%;
          height: 100%;
        }
        input {
          box-sizing: border-box;
          display: block;
          width: 100%;
          height: 100%;
          padding: 0 18 * @rem;
          font-size: 15 * @rem;
          color: #333333;
          background-color: #f6f6f6;
        }
      }
      .search-btn {
        font-size: 15 * @rem;
        color: #666666;
        padding-left: 13 * @rem;
        height: 35 * @rem;
        line-height: 35 * @rem;
      }
    }
    .game-list {
      flex: 1;
      overflow: auto;
      margin-top: 10 * @rem;
      .game-item {
        display: flex;
        align-items: center;
        padding: 21 * @rem 0;
        border-bottom: 1 * @rem solid #eeeeee;
        .game-icon {
          width: 50 * @rem;
          height: 50 * @rem;
          border-radius: 10 * @rem;
          background-color: #b5b5b5;
        }
        .right {
          flex: 1;
          min-width: 0;
          margin-left: 10 * @rem;
          .game-name {
            font-size: 16 * @rem;
            font-weight: bold;
            color: #000000;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
          }
          .use-card {
            font-size: 12 * @rem;
            color: #f72e2e;
            margin-top: 10 * @rem;
            &.can {
              color: #36b150;
            }
          }
        }
      }
    }
  }
}
</style>
