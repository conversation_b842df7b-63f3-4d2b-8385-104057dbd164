<template>
  <div class="april-activity-rule">
    <nav-bar-2
      ref="topNavBar"
      :bgStyle="'transparent-white'"
      :azShow="true"
      :placeholder="false"
    ></nav-bar-2>
    <div class="bg1"></div>
    <div class="bg2"></div>
    <main>
      <div class="section">
        <div class="title"><span>活动介绍：</span></div>
        <div class="text color">
          活动时间: 2023-4-14 00:00:00 ~ 2023-4-16 23:59:59
        </div>
      </div>
      <div class="container">
        <div class="big-text">1.充值得积分</div>
        <div class="text">
          活动期间，每天前5笔游戏现金充值订单可获得等额积分奖励<span
            class="color"
            >（仅限游戏内使用微信/支付宝充值）</span
          >RMB对积分的比例为1：10，即充值1元获得10积分。如有使用金币/平台币/代金券抵扣的，则只计算抵扣后的现金充值金额。
        </div>
      </div>
      <div class="container">
        <div class="big-text">2.积分抽奖</div>
        <div class="text">
          活动期间可使用积分进行抽奖每次消耗100积分。每个用户每天可抽奖三次。每次可在以下奖品列表中随机获得一样。
        </div>
      </div>
      <table>
        <tr>
          <th>奖品</th>
        </tr>
        <tr>
          <td>20000<span class="color">积分</span></td>
        </tr>
        <tr>
          <td>888平台币</td>
        </tr>
        <tr>
          <td>8888<span class="color">积分</span></td>
        </tr>
        <tr>
          <td>3733金币</td>
        </tr>
        <tr>
          <td>888金币</td>
        </tr>
        <tr>
          <td>288<span class="color">积分</span></td>
        </tr>
        <tr>
          <td>88金币</td>
        </tr>
        <tr>
          <td>68<span class="color">积分</span></td>
        </tr>
      </table>
      <div class="container">
        <div class="big-text">3.额外积分奖励</div>
        <div class="text">
          活动期间参与抽奖，可额外获得积分奖励。<br />
          活动期间每日登录盒子可免费领取20积分（每日只能领取一次）<br />
          累计参与抽奖3次，可免费领取50积分（活动期间只能领取一次）<br />
          累计参与抽奖5次，可免费领取100积分（活动期间只能领取一次）<br />
          累计参与抽奖8次，可免费领取200积分（活动期间只能领取一次）
        </div>
      </div>
      <div class="container">
        <div class="big-text">4.积分兑好礼</div>
        <div class="text">
          活动期间可消耗积分兑换礼品<br />
          消耗150积分可兑换88金币（<span class="color">每个用户可兑换3次</span
          >）<br />
          消耗300积分可兑换288金币（<span class="color">每个用户可兑换3次</span
          >）<br />
          消耗500积分可兑换488金币+3天SVIP（<span class="color"
            >每个用户可兑换1次</span
          >）<br />
          消耗1000积分可兑换988金币+7天SVIP（<span class="color"
            >每个用户可兑换1次</span
          >）<br />
          消耗3000积分可兑换3733金币（<span class="color"
            >每个用户可兑换2次</span
          >）<br />
          消耗7000积分可兑换400平台币（<span class="color"
            >每个用户可兑换1次</span
          >）<br />
          终极奖励：消耗20000积分可兑换1200平台币（<span class="color"
            >每个用户可兑换1次</span
          >）<br />
        </div>
      </div>
      <div class="container">
        <div class="big-text">5.额外奖励</div>
        <div class="text">
          前30个兑换终极奖励的玩家还将额外获得8888金币（以后台显示兑换的时间为准），活动结束后1-2个工作日公示获奖名单并进行奖励发放。<br />
        </div>
      </div>
      <div class="container">
        <div class="big-text">活动规则</div>
        <div class="text">
          1.温馨提示：由于游戏厂商的要求，部分游戏内充值暂不支持使用金币支付，请先确定需要用金币充值的游戏在不在可使用名单内。<span
            @click="game_dialog_show = true"
            class="color2 btn"
            >查看名单></span
          >
        </div>
        <div class="text">
          2.活动期间充值完成后请返回本活动页面点击<span class="color2"
            >【刷新】</span
          >领取积分，并及时兑换奖励，活动结束后将清空所有积分和奖励领取次数。<br />
          3.抽奖概率公示
        </div>
      </div>
      <table>
        <tr>
          <th>奖品</th>
          <th>概率</th>
        </tr>
        <tr>
          <td>20000<span class="color">积分</span></td>
          <td>0.5%</td>
        </tr>
        <tr>
          <td>888平台币</td>
          <td>1.5%</td>
        </tr>
        <tr>
          <td>8888<span class="color">积分</span></td>
          <td>4%</td>
        </tr>
        <tr>
          <td>3733金币</td>
          <td>6%</td>
        </tr>
        <tr>
          <td>888金币</td>
          <td>8%</td>
        </tr>
        <tr>
          <td>288<span class="color">积分</span></td>
          <td>10%</td>
        </tr>
        <tr>
          <td>88金币</td>
          <td>20%</td>
        </tr>
        <tr>
          <td>68<span class="color">积分</span></td>
          <td>50%</td>
        </tr>
      </table>
    </main>
    <div class="bg3"></div>
    <div class="bottom-container">
      <div class="bottom-left"></div>
      <div class="bottom-right"></div>
    </div>
    <!-- 查看名单 -->
    <van-dialog
      v-model="game_dialog_show"
      :lock-scroll="false"
      :show-confirm-button="false"
      :close-on-click-overlay="true"
      class="no-gold-game-popup"
    >
      <div class="search-container">
        <div class="close-search" @click="game_dialog_show = false"></div>
        <div class="search-bar">
          <div class="input-text">
            <form @submit.prevent="searchGame">
              <input
                type="text"
                v-model.trim="input_game"
                placeholder="输入游戏名"
              />
            </form>
          </div>
          <div class="search-btn" @click="searchGame">搜索</div>
        </div>
        <yy-list
          class="yy-list game-list"
          v-model="loading_obj"
          :finished="finished"
          @refresh="onRefresh"
          @loadMore="loadMore"
          :empty="!game_list.length"
          tips="请输入您想搜索的游戏"
          :check="false"
        >
          <div
            class="game-item btn"
            v-for="item in game_list"
            :key="item.id"
            @click="toGame(item)"
          >
            <div class="game-icon">
              <img :src="item.titlepic" alt="" />
            </div>
            <div class="right">
              <div class="game-name">{{ item.title }}</div>
              <div class="use-card" :class="{ can: item.use_gold_pay == 1 }">
                {{ item.use_gold_pay == 1 ? '' : '不' }}支持使用金币支付
              </div>
            </div>
          </div>
        </yy-list>
      </div>
    </van-dialog>
  </div>
</template>
<script>
import { ApiGameCardSearchGame } from '@/api/views/recharge.js';
import { BOX_goToGame, BOX_openInNewWindow } from '@/utils/box.uni.js';
import { envFun } from '@/utils/function';

export default {
  name: 'Rule',
  data() {
    return {
      game_list: [], //查看的游戏名单
      loading_obj: {
        loading: false,
        reloading: false,
      }, //loading对象
      input_game: '', //search的input框
      page: 1, //查看名单的页码
      listRows: 20, //查看名单的大小
      empty: false, //查看名单的空
      game_dialog_show: false, //查看名单弹窗
      finished: false, //防抖
      navBgTransparent: 0,
    };
  },
  mounted() {
    // 获取顶部导航栏的高度
    this.stickyOffsetTop =
      document.querySelector('.van-nav-bar').offsetHeight + 'px';
    window.addEventListener('scroll', this.handleScroll);
  },
  methods: {
    // 滚动处理
    handleScroll(e) {
      let scrollTop = e.target.scrollingElement.scrollTop;
      if (scrollTop >= 100) {
        this.navBgTransparent = 1;
      } else {
        this.navBgTransparent = scrollTop / 100;
      }
    },
    // 搜索不可使用金币的游戏
    async searchGame() {
      if (!this.input_game) {
        this.$toast('请输入游戏名');
        return false;
      }
      this.$toast({
        type: 'loading',
        duration: 0,
        message: '拼命加载中...',
      });
      this.game_list = [];
      await this.getGameList();
    },
    async getGameList(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      let res = await ApiGameCardSearchGame({
        keyword: this.input_game,
        listRows: 20,
        page: this.page,
      });
      this.$toast.clear();
      let { list } = res.data;
      if (action === 1 || this.page === 1) {
        this.game_list = [];
      }
      if (!list.length) {
        this.empty = true;
      }
      this.game_list.push(...list);
      if (list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
    },
    async onRefresh() {
      try {
        await this.getGameList();
      } finally {
        this.loading_obj.reloading = false;
      }
    },
    async loadMore() {
      try {
        await this.getGameList(2);
      } finally {
        this.loading_obj.loading = false;
      }
    },
    toGame(item) {
      BOX_goToGame(
        {
          params: {
            id: item.id,
          },
        },
        { id: item.id },
      );
    },
    toCashGift() {
      BOX_openInNewWindow(
        { name: 'MyCashGift' },
        { url: `https://${envFun()}game.3733.com/#/my_cashgift` },
      );
    },
  },
};
</script>
<style lang="less" scoped>
.april-activity-rule {
  position: relative;
  background: #2d1513;
  overflow: hidden;
  .bg1 {
    width: 100%;
    height: 112 * @rem;
    .image-bg('~@/assets/images/april-activity/ac_bg16.png');
  }
  .bg2 {
    width: 100%;
    height: 74 * @rem;
    .image-bg('~@/assets/images/april-activity/ac_bg14.png');
    background-position: -0.5 * @rem 0;
  }
  .bottom-container {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
  }
  main {
    background: #f0dcc4;
    overflow: hidden;
    .section {
      padding: 0 20 * @rem;
      .title {
        margin: 26 * @rem 0 15 * @rem;
        span {
          background-image: url('~@/assets/images/april-activity/ac_bg17.png');
          background-size: 100%;
          background-repeat: no-repeat;
          background-position: 0 16 * @rem;
          font-size: 16 * @rem;
          font-family: PingFang HK-Semibold, PingFang HK;
          font-weight: 600;
          color: #7c343f;
        }
      }
    }
    .container {
      margin: 25 * @rem 24 * @rem 0;
      .big-text {
        font-size: 14 * @rem;
        font-family: PingFang SC-Semibold, PingFang SC;
        font-weight: 600;
        color: #7c343f;
        line-height: 18 * @rem;
        margin-bottom: 7 * @rem;
      }
      .text {
        color: #97603d;
        line-height: 16 * @rem;
      }
    }
    table {
      width: 335 * @rem;
      margin: 24 * @rem auto;
      th,
      td {
        height: 25 * @rem;
        color: #97603d;
        border: 1px solid #7c343f;
        text-align: center;
        line-height: 25 * @rem;
      }
      th {
        background: rgba(124, 52, 63, 0.1);
        border: 1px solid #7c343f;
      }
    }
    .color {
      color: #ff0707;
    }
    .color2 {
      color: #ff601c;
    }
  }
  .bg3 {
    width: 100%;
    height: 60 * @rem;
    margin-bottom: 18 * @rem;
    .image-bg('~@/assets/images/april-activity/ac_bg2.png');
  }
  .bottom-container {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    .bottom-left {
      width: 60 * @rem;
      height: 60 * @rem;
      .image-bg('~@/assets/images/april-activity/ac_bg7.png');
    }
    .bottom-right {
      position: relative;
      top: 2 * @rem;
      width: 54 * @rem;
      height: 46 * @rem;
      .image-bg('~@/assets/images/april-activity/ac_bg8.png');
    }
  }
}
.no-gold-game-popup {
  overflow: unset;
  width: 320 * @rem;
  .search-container {
    box-sizing: border-box;
    width: 320 * @rem;
    height: 450 * @rem;
    padding: 24 * @rem 19 * @rem 10 * @rem;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: unset;
    .close-search {
      width: 24 * @rem;
      height: 24 * @rem;
      background: url(~@/assets/images/close-search.png) center center no-repeat;
      background-size: 24 * @rem 24 * @rem;
      position: absolute;
      right: -10 * @rem;
      top: -10 * @rem;
    }
    .search-bar {
      display: flex;
      align-items: center;
      .input-text {
        width: 240 * @rem;
        height: 35 * @rem;
        border: 1 * @rem solid #e5e5e5;
        border-radius: 18 * @rem;
        flex: 1;
        overflow: hidden;
        form {
          display: block;
          width: 100%;
          height: 100%;
        }
        input {
          box-sizing: border-box;
          display: block;
          width: 100%;
          height: 100%;
          padding: 0 18 * @rem;
          font-size: 15 * @rem;
          color: #333333;
          background-color: #f6f6f6;
        }
      }
      .search-btn {
        font-size: 15 * @rem;
        color: #666666;
        padding-left: 13 * @rem;
        height: 35 * @rem;
        line-height: 35 * @rem;
      }
    }
    .game-list {
      flex: 1;
      overflow: auto;
      margin-top: 10 * @rem;
      .game-item {
        display: flex;
        align-items: center;
        padding: 21 * @rem 0;
        border-bottom: 1 * @rem solid #eeeeee;
        .game-icon {
          width: 50 * @rem;
          height: 50 * @rem;
          border-radius: 10 * @rem;
          background-color: #b5b5b5;
        }
        .right {
          flex: 1;
          min-width: 0;
          margin-left: 10 * @rem;
          .game-name {
            font-size: 16 * @rem;
            font-weight: bold;
            color: #000000;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
          }
          .use-card {
            font-size: 12 * @rem;
            color: #f72e2e;
            margin-top: 10 * @rem;
            &.can {
              color: #36b150;
            }
          }
        }
      }
    }
  }
}
</style>
