import { request } from '../index';

/**
 * 活动页4月活动
 */

export function ApiAprilIndex(params = {}) {
  return request('/activity/april/index', params);
}

export function ApiAprilRankList(params = {}) {
  return request('/activity/april/maxTop30', params);
}

export function ApiAprilExchangeRecord(params = {}) {
  return request('/activity/april/aprilLog', params);
}

export function ApiAprilLottery(params = {}) {
  return request('/activity/april/lottery', params);
}

export function ApiAprilTakeExchange(params = {}) {
  return request('/activity/april/takeExchange', params);
}

export function ApiAprilRefresh(params = {}) {
  return request('/activity/april/getRemainNum', params);
}

export function ApiAprilTakeExtra(params = {}) {
  return request('/activity/april/takeExtra', params);
}
