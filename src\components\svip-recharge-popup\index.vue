<template>
  <van-popup
    v-model="popup"
    :close-on-click-overlay="true"
    position="bottom"
    round
    class="svip-recharge-popup"
    :lock-scroll="false"
  >
    <div class="close" @click="setShowSvipRechargePopup(false)"></div>
    <div class="popup-title">开通SVIP会员</div>
    <div class="swiper">
      <swiper
        id="svipSwiper"
        class="svip-swiper"
        ref="svipSwiper"
        :options="swiperOptions"
        :auto-update="true"
        style="width: 100%; margin: 0 auto"
        v-if="selectList.length > 0"
      >
        <swiper-slide
          class="select-item"
          v-for="(item, index) in selectList"
          :class="{
            on: selectedMeal.amount == item.amount,
          }"
          :key="index"
        >
          <div class="select-title">{{ item.title }}会员</div>
          <div class="money">
            ¥<span>{{ item.amount }}</span>
          </div>
          <div class="tip">
            开通立返<span>{{ item.rebate_gold }}</span
            >金币<br />每日签到额外领<span>88</span>金币
          </div>
        </swiper-slide>
      </swiper>
      <div class="svip-scrollbar"></div>
    </div>
    <div class="payway-title">请选择支付方式</div>
    <ul class="pay-list">
      <li
        class="pay-item"
        :class="{ on: payWay == item.key }"
        v-for="(item, index) in payWayList"
        :key="index"
        @click="payWay = item.key"
      >
        <i class="icon" :style="{ backgroundImage: `url(${item.icon})` }"></i>
        <span class="text">{{ item.name }}</span>
        <div class="select-icon"></div>
      </li>
    </ul>
    <div class="recharge btn" @click="handlePay">
      支付<span>{{ selectedMeal.amount }}</span
      >元
    </div>
    <bottom-safe-area></bottom-safe-area>
  </van-popup>
</template>
<script>
import { mapMutations, mapGetters } from 'vuex';
import {
  ApiSvipIndex,
  ApiGetPayUrl,
  ApiCreateOrderSvip,
} from '@/api/views/recharge.js';
export default {
  data() {
    let that = this;
    return {
      swiperOptions: {
        observer: true, //开启动态检查器，监测swiper和slide
        observeSlideChildren: true, //监测Swiper的子元素wrapper、pagination、navigation、scrollbar或其他一级子元素
        slidesPerView: 'auto',
        freeMode: true,
        freeModeMomentumRatio: 0.3, // 运动惯量
        scrollbar: {
          el: '.svip-scrollbar',
        },
        on: {
          click: function () {
            setTimeout(() => {
              that.selectedMeal = that.selectList[this.clickedIndex];
            }, 0);
          },
        },
      },
      payWay: '', // 支付方式
      selectedMeal: {}, // 选中的套餐
      selectList: [], // 套餐列表
      payWayList: [], // 支付方式
    };
  },
  computed: {
    popup: {
      get() {
        return this.showSvipRechargePopup;
      },
      set(value) {
        this.setShowSvipRechargePopup(value);
      },
    },
    ...mapGetters({
      showSvipRechargePopup: 'recharge/showSvipRechargePopup',
    }),
  },
  async created() {
    await this.getSvipList();
  },
  methods: {
    ...mapMutations({
      setShowSvipRechargePopup: 'recharge/setShowSvipRechargePopup',
    }),
    // 支付逻辑
    handlePay() {
      this.setShowSvipRechargePopup(false);
      const orderParams = {
        day: this.selectedMeal.day,
        amount: this.selectedMeal.amount,
        rebate_gold: this.selectedMeal.rebate_gold,
        payWay: this.payWay,
        is_cycle: 0,
      };
      ApiCreateOrderSvip(orderParams).then(async orderRes => {
        ApiGetPayUrl({
          orderId: orderRes.data.orderId,
          orderType: 103,
          payWay: this.payWay,
          packageName: '',
        }).finally(() => {
          this.$emit('success');
          this.getSvipList();
        });
      });
    },
    // 获取套餐列表和支付方式
    async getSvipList() {
      const res = await ApiSvipIndex();
      let { payArr, svipList } = res.data;
      this.selectList = svipList;
      this.selectedMeal = this.selectList[1]; // 2022年7月7日10:10:36，zyq说默认选中第二个套餐
      this.payWayList = payArr;
      this.payWay = this.payWayList[0].key;
    },
  },
};
</script>
<style lang="less" scoped>
.svip-recharge-popup {
  width: 100%;
  .close {
    position: absolute;
    right: 9 * @rem;
    top: 9 * @rem;
    width: 22 * @rem;
    height: 22 * @rem;
    .image-bg('~@/assets/images/recharge/recharge-popup-close.png');
  }
  .popup-title {
    font-size: 18 * @rem;
    line-height: 23 * @rem;
    font-weight: bold;
    color: #333333;
    text-align: center;
    margin-top: 19 * @rem;
  }
  .swiper {
    width: 100%;
    height: 112 * @rem;
    margin-top: 28 * @rem;
    .svip-scrollbar {
      margin: 8 * @rem auto 0;
      width: 60 * @rem;
      height: 4 * @rem;
      background-color: #e0e0e0;
      border-radius: 2 * @rem;
      overflow: hidden;
    }
    /deep/ .swiper-scrollbar-drag {
      background: @themeBg;
    }
    .svip-swiper {
      .select-item {
        box-sizing: border-box;
        width: 130 * @rem;
        height: 100 * @rem;
        border-radius: 8 * @rem;
        border: 1 * @rem solid #e7e7e7;
        margin-right: 12 * @rem;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        &:first-of-type {
          margin-left: 22 * @rem;
        }
        .select-title {
          font-size: 12 * @rem;
          color: #333333;
          line-height: 15 * @rem;
        }
        .money {
          font-size: 12 * @rem;
          line-height: 15 * @rem;
          color: #666666;
          margin-top: 3 * @rem;
          span {
            font-size: 28 * @rem;
            color: #333333;
            line-height: 30 * @rem;
            font-weight: bold;
            margin-left: 2 * @rem;
          }
        }
        .tip {
          text-align: center;
          font-size: 10 * @rem;
          color: #666666;
          line-height: 13 * @rem;
          margin-top: 8 * @rem;
          span {
            color: #ff591f;
          }
        }
        &.on {
          .image-bg('~@/assets/images/recharge/svip-selected.png');
          border-color: transparent;
        }
      }
    }
  }
  .input-container {
    margin: 16 * @rem 23 * @rem 0;
    border-radius: 19 * @rem;
    height: 36 * @rem;
    background-color: #f4f4f4;
    display: flex;
    align-items: center;
    padding: 0 14 * @rem;
    .text-input {
      flex: 1;
      display: block;
      height: 100%;
      background-color: transparent;
      font-size: 18 * @rem;
      color: #333333;
      font-weight: bold;
      &::-webkit-input-placeholder {
        color: #999999;
        font-size: 14px;
        font-weight: normal;
      }
    }
    .text-right {
      font-size: 14 * @rem;
      color: #333333;
      font-weight: bold;
    }
  }
  .payway-title {
    font-size: 16 * @rem;
    color: #333333;
    font-weight: 600;
    line-height: 20 * @rem;
    padding: 0 23 * @rem;
    margin-top: 20 * @rem;
  }
  .pay-list {
    overflow: hidden;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    margin-top: 12 * @rem;
    padding: 0 23 * @rem;
  }
  .pay-item {
    box-sizing: border-box;
    width: 48%;
    height: 52 * @rem;
    padding-top: 10 * @rem;
    text-align: center;
    border: 0.5 * @rem solid #cbcbcb;
    border-radius: 6 * @rem;
    font-size: 0;
    display: flex;
    align-items: center;
    padding: 0 12 * @rem;
    &:not(:nth-of-type(-n + 2)) {
      margin-top: 10 * @rem;
    }
    &.on {
      border-color: #21b98a;
      border-width: 1 * @rem;
    }
    .icon {
      display: block;
      width: 25 * @rem;
      height: 25 * @rem;
      background-repeat: no-repeat;
      background-size: 25 * @rem 25 * @rem;
    }
    .text {
      display: block;
      font-size: 14 * @rem;
      font-weight: 400;
      color: #333;
      flex: 1;
      min-width: 0;
      margin-left: 6 * @rem;
      text-align: left;
    }
    .select-icon {
      width: 16 * @rem;
      height: 16 * @rem;
      background: url(~@/assets/images/recharge/pay-no.png) no-repeat;
      background-size: 16 * @rem 16 * @rem;
    }
    &.on .select-icon {
      background-image: url(~@/assets/images/recharge/pay-yes.png);
    }
  }
  .recharge {
    width: 273 * @rem;
    height: 42 * @rem;
    .image-bg('~@/assets/images/recharge/recharge-btn-bg.png');
    margin: 23 * @rem auto;
    font-size: 18 * @rem;
    font-weight: bold;
    color: #ffffff;
    line-height: 21 * @rem;
    display: flex;
    align-items: center;
    justify-content: center;
    span {
      font-weight: bold;
    }
  }
}
</style>
