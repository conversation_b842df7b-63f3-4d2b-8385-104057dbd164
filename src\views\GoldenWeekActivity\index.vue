<template>
  <div class="golden-week-activity">
    <nav-bar-2
      ref="topNavBar"
      bgStyle="transparent-white"
      :azShow="true"
      :placeholder="false"
    >
      <template #left>
        <div class="back" @click="back"></div>
      </template>
    </nav-bar-2>
    <div class="main">
      <div class="top-bg">
        <div class="user-container">
          <div v-if="userInfo.token" class="user">
            <user-avatar class="avatar"></user-avatar>
            <div class="nickname">{{ userInfo.nickname }}</div>
          </div>
          <div @click="login" v-else class="user btn">
            <div class="avatar img"></div>
            <div class="text">未登录</div>
          </div>
        </div>

        <div @click="toPage('GoldenWeekActivityRule')" class="rule btn"></div>
        <div class="exchange-record btn" @click="openRecordPopup"></div>
        <div class="activity-time">{{ start_time }} - {{ end_time }}</div>
      </div>
      <!-- 活动主页面 -->
      <div class="content-container">
        <div class="section-1">
          <div class="task-container">
            <div class="task-list">
              <div
                class="task-item"
                v-for="(item, index) in dailyTasksInfo.slice(0, 3)"
                :key="index"
              >
                <div class="task-icon" :class="{ on: item.status != 0 }"></div>
                <div class="task-title">{{ item.title }}</div>
              </div>
            </div>
            <div
              class="get-btn get-can"
              v-if="dailyTasksInfo.at(-1)?.status == 1"
              @click="takePrize(dailyTasksInfo.at(-1)?.prize_id)"
            ></div>
            <div
              class="get-btn get-had"
              v-else-if="dailyTasksInfo.at(-1)?.status == 2"
              @click="takePrize(dailyTasksInfo.at(-1)?.prize_id)"
            ></div>
            <div
              class="get-btn get-no"
              v-else
              @click="takePrize(dailyTasksInfo.at(-1)?.prize_id)"
            ></div>
            <div class="task-tip">24点刷新，请记得领取当日奖励</div>
          </div>
          <div class="exchange-container">
            <div class="coupon-left">兑换券：{{ memInfo.ticket }}</div>
            <div class="coupon-list">
              <div
                class="coupon-item"
                v-for="(item, index) in ticketStore"
                :key="index"
              >
                <div class="icon">
                  <img :src="item.img" :alt="item.title" />
                </div>
                <div class="cost">{{ item.title }}</div>
                <div
                  class="exchange-btn get-no"
                  v-if="item.status == 2"
                  @click="takePrize(item.prize_id)"
                >
                  已兑换
                </div>
                <div
                  class="exchange-btn"
                  :class="[`${item.status == 1 ? 'get-can' : 'get-no'}`]"
                  v-else
                  @click="takePrize(item.prize_id)"
                >
                  兑换 {{ item.count }}/{{ item.max }}
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="section-2">
          <div class="broadcast">
            <van-swipe
              class="broadcast-swiper"
              vertical
              :autoplay="2000"
              :touchable="false"
              :show-indicators="false"
            >
              <van-swipe-item v-for="(item, index) in awardNews" :key="index">
                <div class="broadcast-item">
                  {{ item }}
                </div>
              </van-swipe-item>
            </van-swipe>
          </div>
          <div class="turn-table">
            <div
              class="turn-btn"
              :class="[`rotate${result}`]"
              @click="handleTurn(1)"
            ></div>
            <div class="scores">积分：{{ memInfo.integral }}</div>
            <div class="turn-btn-10" @click="handleTurn(10)"></div>
            <div class="turn-tip">
              玩家实付充值可获得积分，RMB对积分的比例为1:10，
              即充值1元获得10积分。
            </div>
          </div>
        </div>
        <div class="section-3">
          <div class="box-list">
            <div
              class="box-item"
              v-for="(item, index) in raffleAmassPrizeInfo"
              :key="index"
            >
              <div class="box-icon">
                <img :src="item.img" :alt="item.title" />
              </div>
              <div class="box-title">
                {{ item.title }}({{
                  memInfo.raffleAmass > item.need
                    ? item.need
                    : memInfo.raffleAmass
                }}/{{ item.need }})
              </div>
              <div
                class="box-btn box-btn-had"
                v-if="item.status == 2"
                @click="takePrize(item.prize_id)"
              >
                已领取
              </div>
              <div
                class="box-btn box-btn-can"
                v-else-if="item.status == 1"
                @click="takePrize(item.prize_id)"
              >
                领取
              </div>
              <div
                class="box-btn box-btn-no"
                v-else
                @click="takePrize(item.prize_id)"
              >
                未达成
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 奖励记录 -->
    <van-popup
      v-model="recordPopup"
      :lock-scroll="false"
      :close-on-click-overlay="false"
      class="popup"
    >
      <div v-if="recordPopup" class="record-popup">
        <div class="record-popup-close" @click="closeRecordPopup"></div>
        <div class="record-popup-title"></div>
        <yy-list
          class="yy-list record-list"
          v-model="loadingObj"
          :finished="finished"
          @refresh="onRefresh"
          @loadMore="loadMore"
          :empty="!record_list.length"
          tips="暂无兑奖记录"
          :check="false"
        >
          <div
            v-for="(item, index) in record_list"
            :key="index"
            class="record-item"
          >
            <div class="title">{{ item.title }}</div>
            <div class="desc">{{ item.prize_info }}</div>
          </div>
        </yy-list>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { platform, boxInit } from '@/utils/box.uni.js';
import { BOX_login } from '@/utils/box.uni.js';
import userAvatar from '@/components/user-avatar';
import { mapActions } from 'vuex';
import {
  ApiActivityNationalDayIndex,
  ApiActivityNationalDayReceivePrize,
  ApiActivityNationalDayGetPrizeLog,
} from '@/api/views/golden_week_activity.js';
export default {
  components: {
    userAvatar,
  },
  data() {
    return {
      start_time: '09月28日00:00',
      end_time: '10月06日23:59',
      activity_status: 3, // 活动状态 1活动已开始 2 活动不存在 3活动未开始 4活动已结束

      dailyTasksInfo: [],
      raffleAmassPrizeBtn: [],
      raffleAmassPrizeInfo: [],
      rafflePrize: [],
      ticketStore: [],
      awardNews: [],
      memInfo: {
        ticket: 0, // 兑换券数量
        integral: 0, // 积分
        raffleAmass: 0, // 摇奖次数
      },

      recordPopup: false,
      record_list: [],
      result: 0, // 转盘结果
      resultMsg: '', // 转盘结果消息
      isTurning: false, // 转盘转动状态

      loadingObj: {
        loading: false,
        reloading: false,
      },
      finished: false,
      page: 1,
      listRows: 20,
      empty: false,
    };
  },
  computed: {
    activity_status_text() {
      const arr = [
        '活动已开始！',
        '活动不存在！',
        '活动未开始！',
        '活动已结束！',
      ];
      return arr[this.activity_status - 1];
    },
  },
  async mounted() {
    // 官包触发页面刷新（将onResume挂载在window上）
    if (platform == 'android') {
      window.onResume = this.onResume;
    }
    await this.init();
  },
  methods: {
    ...mapActions({
      SET_USER_INFO: 'user/SET_USER_INFO',
    }),
    async onResume() {
      await boxInit();
      await this.SET_USER_INFO(true);
      await this.init();
    },
    async init() {
      await this.getIndexData();
    },
    async refresh() {
      await this.getIndexData();
    },

    async getIndexData() {
      const res = await ApiActivityNationalDayIndex();
      let {
        activityStatus,
        dailyTasksInfo,
        raffleAmassPrizeBtn,
        raffleAmassPrizeInfo,
        rafflePrize,
        ticketStore,
        memInfo,
        awardNews,
      } = res.data;
      this.activity_status = activityStatus;
      this.dailyTasksInfo = dailyTasksInfo;
      this.raffleAmassPrizeBtn = raffleAmassPrizeBtn;
      this.raffleAmassPrizeInfo = raffleAmassPrizeInfo;
      this.rafflePrize = rafflePrize;
      this.ticketStore = ticketStore;
      this.memInfo = memInfo;
      this.awardNews = awardNews;
    },
    async takePrize(id) {
      if (this.activity_status != 1) {
        this.$toast(this.activity_status_text);
        return false;
      }
      if (!this.userInfo.token) {
        this.$toast('请登录');
        this.login();
        return false;
      }
      this.$toast({
        type: 'loading',
        duration: 0,
      });
      try {
        const res = await ApiActivityNationalDayReceivePrize({ id });
        let { prize_id, msg } = res.data;

        if (msg) {
          if (prize_id) {
            // 转盘
            this.$toast.clear();
            this.result = this.resultFormat(prize_id);
            this.resultMsg = msg;
          } else {
            this.$toast(msg);
          }
        }
      } finally {
        await this.refresh();
      }
    },
    login() {
      BOX_login();
    },
    async getPrizeLog(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      const res = await ApiActivityNationalDayGetPrizeLog({
        page: this.page,
        listRows: this.listRows,
      });
      this.$toast.clear();
      let { list } = res.data;
      if (action === 1 || this.page === 1) {
        this.record_list = [];
      }
      if (!list.length) {
        this.empty = true;
      }
      this.record_list.push(...list);
      if (list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
    },

    async onRefresh() {
      try {
        await this.getPrizeLog();
      } finally {
        this.loadingObj.reloading = false;
      }
    },
    async loadMore() {
      try {
        await this.getPrizeLog(2);
      } finally {
        this.loadingObj.loading = false;
      }
    },
    async openRecordPopup() {
      if (this.activity_status == 2 || this.activity_status == 3) {
        this.$toast(this.activity_status_text);
        return false;
      }
      this.recordPopup = true;
      await this.getPrizeLog();
    },
    // 关闭记录列表
    closeRecordPopup() {
      this.recordPopup = false;
    },
    async handleTurn(times = 1) {
      if (this.activity_status != 1) {
        this.$toast(this.activity_status_text);
        return false;
      }
      if (!this.userInfo.token) {
        this.$toast('请登录');
        this.login();
        return false;
      }
      if (this.isTurning) {
        return false;
      }
      let prize_id;
      if (times == 10) {
        prize_id = this.raffleAmassPrizeBtn[1].prize_id;
      } else {
        prize_id = this.raffleAmassPrizeBtn[0].prize_id;
      }
      this.isTurning = true;
      this.result = 0;
      try {
        await this.takePrize(prize_id);
        setTimeout(() => {
          this.$toast({
            message: this.resultMsg,
            duration: 3000,
          });
          this.isTurning = false;
        }, 10000);
      } catch (e) {
        this.$nextTick(() => {
          console.log(e);
          this.isTurning = false;
        });
      }
    },
    resultFormat(id) {
      let index = this.rafflePrize.findIndex(item => item.prize_id == id);
      return index > -1 ? index + 1 : 0;
    },
  },
};
</script>

<style lang="less" scoped>
.golden-week-activity {
  width: 100%;
  overflow: hidden;
  .back {
    width: 28 * @rem;
    height: 28 * @rem;
    border-radius: 15 * @rem;
    background: rgba(0, 0, 0, 0.3) url(~@/assets/images/nav-bar-back-white.png)
      center center no-repeat;
    background-size: 8 * @rem 14 * @rem;
  }
  .main {
    .top-bg {
      width: 100%;
      height: 523 * @rem;
      .image-bg('~@/assets/images/golden-week-activity/golden-bg-1.png');
      background-size: 100% 523 * @rem;
      position: relative;

      .user-container {
        position: absolute;
        top: calc(15 * @rem + @safeAreaTop);
        top: calc(15 * @rem + @safeAreaTopEnv);
        left: 70 * @rem;
        z-index: 1000;
        height: 22 * @rem;
        background-color: rgba(0, 0, 0, 0.3);
        color: #fff;
        line-height: 22 * @rem;
        border-radius: 20 * @rem;
        .user {
          padding: 0 12 * @rem 0 30 * @rem;
          .text {
            white-space: nowrap;
          }
        }
        .avatar {
          position: absolute;
          top: -3.5 * @rem;
          left: -3.5 * @rem;
          width: 28 * @rem;
          height: 28 * @rem;
          border-radius: 50%;
          &.img {
            .image-bg('~@/assets/images/golden-week-activity/avatar_default.png');
          }
        }
      }
      .exchange-record {
        position: absolute;
        right: 2 * @rem;
        top: 85 * @rem;
        width: 26 * @rem;
        height: 110 * @rem;
        .image-bg('~@/assets/images/golden-week-activity/record-icon.png');
        background-size: 26 * @rem 110 * @rem;
      }
      .rule {
        position: absolute;
        right: 2 * @rem;
        top: 195 * @rem;
        width: 26 * @rem;
        height: 110 * @rem;
        .image-bg('~@/assets/images/golden-week-activity/rule-icon.png');
        background-size: 26 * @rem 110 * @rem;
      }
      .activity-time {
        box-sizing: border-box;
        position: absolute;
        left: 50%;
        top: 144 * @rem;
        transform: translateX(-50%);
        background-color: rgba(89, 11, 11, 0.2);
        width: 205 * @rem;
        height: 22 * @rem;
        line-height: 22 * @rem;
        font-size: 12 * @rem;
        color: #ffffff;
        text-align: center;
        border-radius: 12 * @rem;
      }
    }
    .content-container {
      .section-1 {
        position: relative;
        width: 100%;
        height: 512 * @rem;
        .image-bg('~@/assets/images/golden-week-activity/golden-bg-2.png');
        background-size: 100% 512 * @rem;
        .task-container {
          padding-bottom: 8 * @rem;
          .task-list {
            display: flex;
            justify-content: center;
            position: relative;
            height: 69 * @rem;
            &::before {
              content: '';
              position: absolute;
              left: 50%;
              transform: translateX(-50%);
              height: 6 * @rem;
              width: 242 * @rem;
              background-color: #e27334;
              top: 20 * @rem;
            }
            .task-item {
              position: relative;
              width: 118 * @rem;
              z-index: 2;
              .task-icon {
                width: 45 * @rem;
                height: 45 * @rem;
                margin: 0 auto;
                .image-bg('~@/assets/images/golden-week-activity/task-icon.png');
                background-size: 45 * @rem 45 * @rem;
                &.on {
                  .image-bg('~@/assets/images/golden-week-activity/task-icon-success.png');
                  background-size: 45 * @rem 45 * @rem;
                }
              }
              .task-title {
                font-size: 12 * @rem;
                color: #ffdba6;
                margin-top: 9 * @rem;
                text-align: center;
                line-height: 15 * @rem;
              }
            }
          }
          .get-btn {
            width: 229 * @rem;
            height: 70 * @rem;
            margin-top: 22 * @rem;
            margin-left: 89 * @rem;
            &.get-can {
              .image-bg('~@/assets/images/golden-week-activity/task-get-can.png');
              background-size: 229 * @rem 70 * @rem;
            }
            &.get-no {
              .image-bg('~@/assets/images/golden-week-activity/task-get-no.png');
              background-size: 229 * @rem 70 * @rem;
            }
            &.get-had {
              .image-bg('~@/assets/images/golden-week-activity/task-get-had.png');
              background-size: 229 * @rem 70 * @rem;
            }
          }
          .task-tip {
            text-align: center;
            font-size: 12 * @rem;
            margin-top: 10 * @rem;
            color: #d9866a;
            line-height: 15 * @rem;
          }
        }
        .exchange-container {
          margin-top: 94 * @rem;
          padding: 0 30 * @rem;
          .coupon-left {
            font-size: 14 * @rem;
            color: #7d0220;
            line-height: 18 * @rem;
            font-weight: 600;
          }
          .coupon-list {
            display: flex;
            justify-content: space-between;
            margin-top: 12 * @rem;
            .coupon-item {
              width: 65 * @rem;
              .icon {
                width: 65 * @rem;
                height: 78 * @rem;
                &.icon-1 {
                  .image-bg('~@/assets/images/golden-week-activity/exchange-icon-1.png');
                  background-size: 65 * @rem 78 * @rem;
                }
                &.icon-2 {
                  .image-bg('~@/assets/images/golden-week-activity/exchange-icon-2.png');
                  background-size: 65 * @rem 78 * @rem;
                }
                &.icon-3 {
                  .image-bg('~@/assets/images/golden-week-activity/exchange-icon-3.png');
                  background-size: 65 * @rem 78 * @rem;
                }
                &.icon-4 {
                  .image-bg('~@/assets/images/golden-week-activity/exchange-icon-4.png');
                  background-size: 65 * @rem 78 * @rem;
                }
              }
              .cost {
                font-size: 11 * @rem;
                color: #7d0220;
                line-height: 14 * @rem;
                margin-top: 5 * @rem;
                white-space: nowrap;
                text-align: center;
              }
              .exchange-btn {
                width: 60 * @rem;
                height: 23 * @rem;
                font-size: 12 * @rem;
                color: #fff;
                font-weight: 600;
                margin-top: 8 * @rem;
                display: flex;
                align-items: center;
                justify-content: center;
                &.get-can {
                  .image-bg('~@/assets/images/golden-week-activity/get-can.png');
                  background-size: 60 * @rem 23 * @rem;
                }
                &.get-no {
                  .image-bg('~@/assets/images/golden-week-activity/get-no.png');
                  background-size: 60 * @rem 23 * @rem;
                }
              }
            }
          }
        }
      }
      .section-2 {
        position: relative;
        width: 100%;
        height: 512 * @rem;
        .image-bg('~@/assets/images/golden-week-activity/golden-bg-3.png');
        background-size: 100% 512 * @rem;
        padding-top: 1 * @rem;
        .broadcast {
          margin: 59 * @rem auto 0;
          width: 285 * @rem;
          height: 20 * @rem;
          .image-bg('~@/assets/images/golden-week-activity/broadcast-bg.png');
          background-size: 285 * @rem 20 * @rem;
          .broadcast-swiper {
            height: 20 * @rem;
            .broadcast-item {
              height: 20 * @rem;
              white-space: nowrap;
              text-align: center;
              line-height: 20 * @rem;
              font-size: 11 * @rem;
              color: #ffffff;
              font-weight: 500;
            }
          }
        }
        .turn-table {
          .turn-btn {
            position: relative;
            width: 150 * @rem;
            height: 150 * @rem;
            margin: 111 * @rem auto;

            &.rotate0 {
              &::before {
                transform: rotate(0deg);
              }
            }
            &.rotate1 {
              &::before {
                transform: rotate(3660deg);
                transition: all 10s ease;
              }
            }
            &.rotate2 {
              &::before {
                transform: rotate(3720deg);
                transition: all 10s ease;
              }
            }
            &.rotate3 {
              &::before {
                transform: rotate(3780deg);
                transition: all 10s ease;
              }
            }
            &.rotate4 {
              &::before {
                transform: rotate(3840deg);
                transition: all 10s ease;
              }
            }
            &.rotate5 {
              &::before {
                transform: rotate(3900deg);
                transition: all 10s ease;
              }
            }
            &.rotate6 {
              &::before {
                transform: rotate(3960deg);
                transition: all 10s ease;
              }
            }
            &::before {
              content: '';
              position: absolute;
              left: 0;
              top: 0;
              width: 150 * @rem;
              height: 150 * @rem;
              .image-bg('~@/assets/images/golden-week-activity/turn-btn.png');
            }
            &::after {
              content: '';
              width: 56 * @rem;
              height: 47 * @rem;
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
              .image-bg('~@/assets/images/golden-week-activity/turn-text.png');
              background-size: 56 * @rem 47 * @rem;
            }
          }
          .scores {
            text-align: center;
            font-size: 16 * @rem;
            color: #7d0220;
            line-height: 20 * @rem;
            font-weight: 600;
            margin-top: 118 * @rem;
          }
          .turn-btn-10 {
            position: relative;
            z-index: 2;
            width: 218 * @rem;
            height: 60 * @rem;
            .image-bg('~@/assets/images/golden-week-activity/turn-btn-10.png');
            background-size: 218 * @rem 60 * @rem;
            margin: 5 * @rem auto 0;
          }
          .turn-tip {
            position: relative;
            z-index: 2;
            font-size: 11 * @rem;
            color: #7d0220;
            text-align: center;
            width: 280 * @rem;
            height: 28 * @rem;
            line-height: 14 * @rem;
            margin: 10 * @rem auto 0;
          }
        }
      }
      .section-3 {
        position: relative;
        width: 100%;
        height: 403 * @rem;
        .image-bg('~@/assets/images/golden-week-activity/golden-bg-4.png');
        background-size: 100% 403 * @rem;
        padding-top: 1 * @rem;
        margin-top: -1 * @rem;
        .box-list {
          display: flex;
          flex-wrap: wrap;
          justify-content: center;
          margin: 90 * @rem auto 0;
          .box-item {
            width: 30%;
            margin-bottom: 18 * @rem;
            .box-icon {
              width: 60 * @rem;
              height: 60 * @rem;
              margin: 0 auto;
              &.icon1 {
                .image-bg('~@/assets/images/golden-week-activity/box-icon-1.png');
                background-size: 60 * @rem 60 * @rem;
              }
              &.icon2 {
                .image-bg('~@/assets/images/golden-week-activity/box-icon-2.png');
                background-size: 60 * @rem 60 * @rem;
              }
              &.icon3 {
                .image-bg('~@/assets/images/golden-week-activity/box-icon-3.png');
                background-size: 60 * @rem 60 * @rem;
              }
              &.icon4 {
                .image-bg('~@/assets/images/golden-week-activity/box-icon-4.png');
                background-size: 60 * @rem 60 * @rem;
              }
              &.icon5 {
                .image-bg('~@/assets/images/golden-week-activity/box-icon-5.png');
                background-size: 60 * @rem 60 * @rem;
              }
              &.icon6 {
                .image-bg('~@/assets/images/golden-week-activity/box-icon-6.png');
                background-size: 60 * @rem 60 * @rem;
              }
            }
            .box-title {
              font-size: 10 * @rem;
              color: #7d0220;
              margin-top: 0;
              text-align: center;
              line-height: 13 * @rem;
            }
            .box-btn {
              width: 60 * @rem;
              height: 25 * @rem;
              margin: 8 * @rem auto 0;
              display: flex;
              justify-content: center;
              font-size: 12 * @rem;
              color: #ffffff;
              font-weight: 600;
              line-height: 24 * @rem;
              &.box-btn-can {
                .image-bg('~@/assets/images/golden-week-activity/get-can.png');
                background-size: 60 * @rem 25 * @rem;
              }
              &.box-btn-no {
                .image-bg('~@/assets/images/golden-week-activity/get-no.png');
                background-size: 60 * @rem 25 * @rem;
              }
              &.box-btn-had {
                .image-bg('~@/assets/images/golden-week-activity/get-no.png');
                background-size: 60 * @rem 25 * @rem;
              }
            }
          }
        }
      }
    }
  }
}
.popup {
  overflow: visible;
  background: transparent;
}
.record-popup {
  box-sizing: border-box;
  width: 356 * @rem;
  padding: 15 * @rem 12 * @rem 12 * @rem;
  background: url('~@/assets/images/golden-week-activity/popup-bg-1.png') center
      top / 356 * @rem auto no-repeat,
    url('~@/assets/images/golden-week-activity/popup-bg-2.png') center bottom /
      356 * @rem auto no-repeat;
  .record-popup-close {
    width: 36 * @rem;
    height: 36 * @rem;
    .image-bg('~@/assets/images/golden-week-activity/popup-close.png');
    background-size: 36 * @rem 36 * @rem;
    position: absolute;
    right: -1 * @rem;
    top: 10 * @rem;
  }
  .record-popup-title {
    width: 170 * @rem;
    height: 24 * @rem;
    margin: 0 auto;
  }
  .record-list {
    box-sizing: border-box;
    border-radius: 18 * @rem;
    background: #fceed8;
    padding: 14 * @rem 0 * @rem 18 * @rem 0 * @rem;
    margin-top: 18 * @rem;
    height: 378 * @rem;
    overflow-y: auto;
    .record-item {
      display: flex;
      align-items: flex-start;
      line-height: 16 * @rem;
      margin-top: 12 * @rem;
      &:first-of-type {
        margin-top: 0;
      }
      .title {
        width: 170 * @rem;
        font-size: 12 * @rem;
        color: #934028;
        padding-left: 18 * @rem;
      }
      .desc {
        flex: 1;
        min-width: 0;
        font-size: 12 * @rem;
        color: #ff6235;
        margin-left: 10 * @rem;
      }
    }
  }
  .empty {
    box-sizing: border-box;
    border-radius: 18 * @rem;
    background: #fceed8;
    padding: 14 * @rem 14 * @rem 18 * @rem;
    margin-top: 18 * @rem;
    height: 378 * @rem;
    font-size: 14 * @rem;
    color: rgba(147, 64, 40, 0.4);
    text-align: center;
    line-height: 300 * @rem;
  }
}
</style>
