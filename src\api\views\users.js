import { request } from '../index';
import h5Page from '@/utils/h5Page';

export function ApiLogin(params = {}) {
  return request('/api/user/login', params);
}

// export function ApiLogin(params = {}) {
//   return request('/api/user/login', params, true, h5Page.api_url3)
// }

export function ApiUserInfo(params = {}) {
  return request('/api/user/info', params);
}

export function ApiUserInfoEx(params = {}) {
  return request('/api/user/infoEx', params);
}

export function ApiAuthCode(params = {}) {
  //type: "1=通用认证 2=登录（登录接口已废弃），3=登录，4=重置密码，5=绑定，6=解绑，7=小号回收，8=小号交易 9=账号注销",
  return request('/api/sms/send', params);
}

/**
 * 设置密码
 * phone 手机号
 * code 验证码
 * password 密码
 *  */
export function ApiResetPassword(params = {}) {
  return request('/api/user/resetPassword', params);
}

export function ApiChangeInfo(params = {}) {
  return request('/api/user/changeInfo', params);
}

export function ApiChangeInfoEx(params = {}) {
  // 修改性别、生日等
  return request('/api/user/changeInfoEx', params);
}

export function ApiUserCertification(params = {}) {
  return request('/api/user/certification', params);
}

export function ApibindPhone(params = {}) {
  return request('/api/user/bindPhone', params);
}

export function ApiUnBindPhone(params = {}) {
  return request('/api/user/unbindPhone', params);
}

export function ApiChangeAvatar(params = {}) {
  return request('/api/user/changeAvatar', params);
}

/**
 * 设置密码
 * phone 手机号
 * code 验证码
 * password 密码
 *  */

/**
 * 获取我的反馈列表
 *  */
export function ApiMyFeedback(params = {}) {
  return request('/web/feedback/myFeedback', params);
}

/**
 * 获取我的投诉列表
 *  */
export function ApiMyComplaint(params = {}) {
  return request('/web/feedback/myComplain', params);
}

/**
 * 签到
 */
export function ApiUserClockIn(params = {}) {
  return request('/api/user/clockIn', params);
}

/**
 * 点击签到
 */
export function ApiUserActiveSign(params = {}) {
  return request('/api/user/activeSign', params);
}

/**
 * 签到获取宝箱奖励
 * @param box_num 天数 3   7  14   28  领取最后两个宝箱，需要助力。SVIP可以不用
 */
export function ApiUserGetGoldBox(params = {}) {
  return request('/api/user/getGoldBox', params);
}

/**
 * 补签所需金币数
 */
export function ApiUserRepairDeductGold(params = {}) {
  return request('/api/user/repairDeductGold', params);
}

/**
 * 补签
 * @param signDate 2021-08-11 补签时间
 */
export function ApiUserRepairSign(params = {}) {
  return request('/api/user/repairSign', params);
}

/**
 * 金币转盘列表
 */
export function ApiGoldDialList(params = {}) {
  return request('/api/gold_dial/goldDialList', params);
}

/**
 * 我的中奖列表
 */
export function ApiGoldDialMyWonList(params = {}) {
  return request('/api/gold_dial/myWonList', params);
}

/**
 * 抽奖
 * @param type 1 1连抽  2 10连抽  3 免费
 */
export function ApiGoldDialRaffle(params = {}) {
  return request('/api/gold_dial/raffle', params);
}

/**
 * 获取个人签
 * @param game_id
 * @param uuid
 * @param udid
 * req
 * status 1成功、2不是SVIP、3未绑定设备、4已经在队列中、5已经签名成功、6svip天数小于七天
 */
export function ApiPersonalSign(params = {}) {
  return request('/index/api/addPersonalSign', params, false, h5Page.apigrq);
}

/**
 * 检查用户是否绑定成功
 */
export function ApiCheckUdid(params = {}) {
  return request('/index/api/realBindUdid', params, false, h5Page.apigrq);
}

/**
 * 检测是否回归
 * @return {is_regression}  // true = 可以领取回归奖励  false = 不符合回归奖励
 */
export function ApiUserIsRegression(params = {}) {
  return request('/api/user/isRegression', params);
}

/**
 * 领取回归奖励
 */
export function ApiUserTakeRegression(params = {}) {
  return request('/api/user/takeRegression', params);
}

//检测个人签是否实名认证，并返回相应链接（用于第三方个人签）
/**
 * @param {number} game_id 游戏ID
 * @param {number} grq_status 个人签状态
 */
export function ApiUserCheckGrqAuthStatus(params = {}) {
  return request('/index/api/checkGrqAuthStatus', params, false, h5Page.apigrq);
}

// 获取用户经验等级
export function ApiUserExpRank(params = {}) {
  return request('/api/user/expRank', params);
}

// 获取用户财富等级
export function ApiUserPayRank(params = {}) {
  return request('/api/user/payRank', params);
}

// 邀请好友数据
export function ApiUserInviteCount(params = {}) {
  return request('/api/user/inviteCount', params);
}

// 微信提醒
export function ApiNewsWeixin(params = {}) {
  return request('/api/news/weixin', params);
}

// 微信绑定
export function ApiNewsWxBind(params = {}) {
  return request('/api/news/wxBind', params);
}

/**
 * @param {string} packageName 包名
 * */
export function ApiNewUser(params = {}) {
  return request('/api/user/newUser', params);
}

/**
 * 通知
 * */
export function ApiUserInform(params = {}) {
  return request('/api/user/inform', params);
}

/**
 * 添加通知阅读记录(消除小红点)
 * @param pushRecordId inform中id
 * */
export function ApiUserPushMsgRead(params = {}) {
  return request('/api/user/pushMsgRead', params);
}

/**
 * 我的开服提醒列表
 * */
export function ApiServerMyList(params = {}) {
  return request('/api/server/myList', params);
}

/**
 * 我的 - 综合
 * @return {action}
 * #  ACTION_REQUITE           = 1;//六倍返还
#  ACTION_RECYCLE           = 2;//小号回收
#  ACTION_REBATE            = 3;//返利申请
#  ACTION_CARD              = 4;//我的礼包
#  ACTION_TURN_TABLE        = 5;//金币转盘
#  ACTION_SPREADER          = 6; //邀请赚佣金
#  ACTION_GOLD_SHOP         = 7;//金币商城
#  ACTION_ZHUANYOU          = 8;//转游中心
#  ACTION_MY_GAME           = 9;//我的游戏
#  ACTION_XIAOHAO           = 10;//小号管理
#  ACTION_KEFU              = 11;//联系客服
#  ACTION_MY_COLLECTION     = 12;//我的收藏
#  ACTION_USE_GUIDE         = 13;//使用指南
#  ACTION_QUESTIONS_ANSWERS = 14;//我的问答
#  ACTION_MY_SUBMIT         = 15;//我的发布
#  ACTION_FEEDBACK          = 16;//投诉反馈
#  ACTION_WX                = 17;//微信提醒
#   ACTION_TRADE             = 18;//交易中心
 * */
export function ApiUserMyModular(params = {}) {
  return request('/api/user/myModular', params);
}

export function ApiUserMyModularV2(params = {}) {
  return request('/api/user/myModularV2', params);
}

// 接下来4个接口是上报接口 ios马甲包专用 uuid从壳获取
/**
 * 上报安装信息-iOS使用 只有首次安装后登录才进行上报
 * */
export function ApiUserSubmitInstallInfo(params = {}) {
  return request('/api/user/submitInstallInfo', params);
}
/**
 * 上报登录信息-iOS使用
 * */
export function ApiUserSubmitLoginInfo(params = {}) {
  return request('/api/user/submitLoginInfo', params);
}
/**
 * 上报注册信息-iOS使用
 * */
export function ApiUserSubmitRegisterInfo(params = {}) {
  return request('/api/user/submitRegisterInfo', params);
}
/**
 * 上传玩家绑定信息，后续上报投放平台使用
 * @param uuid iOS使用 使用iOS壳的uuid
 * @param plat 平台类型： ios,android,h5
 * */
export function ApiUserSubmitUserInfo(params = {}) {
  return request('/api/user/submitUserInfo', params);
}

export function ApiUserImgCertification(params = {}) {
  return request('/api/user/imgCertification', params);
}
