<template>
  <div class="page-250101-activity">
    <nav-bar-2
      ref="topNavBar"
      bgStyle="transparent-white"
      :azShow="true"
      :placeholder="false"
    >
      <template #left>
        <div class="back" @click="back"></div>
      </template>
      <template #right>
        <div class="rule" @click="clickRule">规则</div>
      </template>
    </nav-bar-2>

    <div class="fixed-btns">
      <div class="fixed-share-btn" @click="handleShare(false)">分享</div>
    </div>
    <div class="fixed-btns right-fixed">
      <div class="fixed-prize-btn" @click="prizePopup = true">
        我的<br />奖品
      </div>
    </div>

    <div class="main">
      <div class="activity-top">
        <!-- 顶部内容 -->
        <div class="activity-title"></div>
        <div class="activity-sub-title" @click="hbCoverShow = true">
          得定制 <span class="sub-title">微信红包封面</span> (数量有限)
        </div>
        <div class="activity-time">活动时间 2025.1.27 0点 ~ 2025.2.5 24点</div>
      </div>
      <div class="activity-content">
        <div class="activity-content-dl">
          <div
            class="content-dl-item"
            :class="getDlClass(item)"
            v-for="item in dlInfoList"
            :key="item.id"
            @click="handleClickDl(item)"
          >
            <div class="yy-item"></div>
            <div v-if="isItemSelected(item)" class="active-item"></div>
            <div v-if="isItemSelected(item)" class="active-snake"></div>
          </div>
        </div>
        <div class="activity-content-xl">
          <div class="xl-title">下联：</div>
          <div class="xl-content">
            <div class="input-box">
              <div
                :class="['input-item']"
                v-for="(item, index) in inputBlocks"
                :key="index"
                @click="newHandleClick(item, index)"
              >
                <div
                  @click.stop="clearInput(item, index)"
                  :class="{ 'clear-icon': item !== '' }"
                ></div>
                <input
                  type="text"
                  :class="[
                    `input-content${index}`,
                    {
                      'click-item': inputClick == index && item !== '',
                    },
                  ]"
                  v-model.trim="inputBlocks[index]"
                  :ref="'input' + index"
                  maxlength="1"
                  @keyup="handleKeyup($event, index)"
                />
              </div>
            </div>
          </div>
        </div>
        <div class="activity-content-text">
          <div class="text-title">请使用已有文字或自由题写完成下联</div>
          <div class="text-content">
            <div
              class="text-column"
              v-for="(group, groupIndex) in groupedSelectCoupletsTxtInfo"
              :key="groupIndex"
            >
              <div
                class="text-item"
                :class="{ active: item.isSelect }"
                v-for="item in group"
                :key="item.id"
                @click="selectCoupletsTxt(item)"
              >
                <span class="text">{{ item.text }}</span>
              </div>
            </div>
          </div>
        </div>
        <div class="activity-content-btn">
          <div
            class="btn btn-default"
            @click="handleSelectDl()"
            :class="{ confirm: inputBlocksLength == dlLength }"
          ></div>
        </div>
      </div>
      <div class="activity-bottom">
        <div class="more-xl-logo"></div>
        <div class="more-xl-btn">
          <div class="more-btn-box" @click="clickMoreDl">
            <div>更多</div>
            <span></span>
          </div>
        </div>
        <div class="more-xl-content" v-if="isLoading">
          <content-empty
            style="height: 60%"
            v-if="!moreCoupletsInfo.length"
            tips="暂无数据"
          ></content-empty>
          <template v-else>
            <div
              class="more-xl-content-item"
              v-for="item in moreCoupletsInfo"
              :key="item.id"
            >
              <div class="left-content">
                <div class="user-info">
                  <div class="avatar">
                    <img
                      v-if="item.userinfo?.avatar"
                      class="img"
                      :src="item.userinfo?.avatar"
                    />
                    <img v-else class="img" :src="defaultAvatar" />
                  </div>
                  <span class="user-name">{{
                    formatNickname(item.userinfo.nickname)
                  }}</span>
                </div>
                <div class="dl-info">
                  <div class="sl-title">
                    <div class="sl-content">{{ getDlSl(item.text.dl_id) }}</div>
                    <div
                      class="me"
                      v-if="item.userinfo.user_id == userInfo.user_id"
                    >
                      我
                    </div>
                  </div>
                  <div class="xl-title">{{ item.text.text }}</div>
                </div>
              </div>
              <div class="likes" @click="handleClickDm(item)">
                <div
                  class="like"
                  :class="{ active: item.text.click_status }"
                ></div>
                <span class="num">{{ item.text.click_count }}</span>
              </div>
            </div>
          </template>
        </div>
      </div>
    </div>

    <div
      class="return-top"
      v-show="showReturnTop"
      @click="handleReturnTop"
    ></div>
    <!-- 我的奖品弹窗 -->
    <prize-popup :show.sync="prizePopup" :is_task="true"></prize-popup>

    <!-- 红包封面弹窗 -->
    <van-dialog
      v-model="hbCoverShow"
      :show-confirm-button="false"
      :lock-scroll="false"
      class="hb-dialog"
    >
      <div class="dialog-content"></div>
      <div class="btn-close" @click="hbCoverShow = false"></div>
    </van-dialog>

    <!-- 定制红包弹窗 -->
    <van-dialog
      v-model="customizedHbShow"
      :show-confirm-button="false"
      :lock-scroll="false"
      class="customized-hb-dialog"
    >
      <div class="dialog-content">
        <div class="dialog-title">
          <div class="title">恭喜获得定制微信红包封面</div>
          <div class="close-btn" @click="closeHbShow"></div>
        </div>
        <div class="dialog-img"></div>
        <div class="dialog-info">
          <div class="time">
            <span> 使用有效期:</span>
            自领取后可在3个月（自然月）内无限次使用，有效期截止日当天24点前均可使用。超过有效期后，已领取的封面将自动失效，无法再次使用。
          </div>
          <div class="info">
            <div class="serialNumber">
              <span>序列号：</span>{{ serialNumber }}
            </div>
            <div class="copy" @click="clickCopy">点击复制</div>
          </div>
        </div>
        <div class="check-look-btn" @click="lookUseFunc">查看使用方法</div>
      </div>
    </van-dialog>

    <!-- 我的对联弹窗 -->
    <my-dl-dialog
      :show.sync="myDlPopUp"
      :myDlInfo="myDlInfo"
      :isExistHbCover="isExistHbCover"
      :hbCoverQuantity="hbCoverQuantity"
      @handleShare="handleShare"
    ></my-dl-dialog>
  </div>
</template>

<script>
import { mapGetters, mapActions, mapMutations } from 'vuex';
import { platform, boxInit, BOX_login, iframeCopy } from '@/utils/box.uni.js';
import { ApiCommonShareInfo } from '@/api/views/system.js';
import {
  ApiDoingsChunjieCouplet,
  ApiDoingsChunjieDowncouplet,
  ApiDoingsChunjieThumb,
  ApiDoingsChunjieCover,
} from '@/api/views/25_new_year_activity.js';

export default {
  name: 'Couplets',
  components: {
    prizePopup: () => import('../components/prize-popup.vue'),
    myDlDialog: () => import('../components/my-dl-dialog.vue'),
  },
  data() {
    let _self = this;
    return {
      luckyBagOpened: false, //福袋是否开启
      activity_status: 3, // 活动状态 1活动已开始 2 活动不存在  3活动未开始 4活动已结束
      info: {}, // 活动信息
      shareInfo: {}, //分享信息
      prizePopup: false, // 规则弹窗
      hbCoverShow: false, // 红包封面弹窗
      customizedHbShow: false, // 定制红包弹窗
      myDlPopUp: false, // 我的对联弹窗
      rulePopup: false,
      showReturnTop: false,
      selectDlInfo: {
        dl_id: 1,
        text: '丰年盛景龙蛇舞',
      },
      myDlInfo: {
        sl: '',
        xl: '',
      },
      dlInfoList: [
        {
          dl_id: 1,
          text: '丰年盛景龙蛇舞',
        },
        {
          dl_id: 2,
          text: '春风入喜财入户',
        },
        {
          dl_id: 3,
          text: '国色方兴普天开景运',
        },
      ], //对联信息
      selectCoupletsTxtInfo: [
        {
          // 新、锦、岁、绣、春、盛、光、世、彩、舞、蝶、凤、飞、龙
          dl_id: 1,
          dlInfo: [
            { id: 1, text: '新', isSelect: false },
            { id: 2, text: '锦', isSelect: false },
            { id: 3, text: '岁', isSelect: false },
            { id: 4, text: '绣', isSelect: false },
            { id: 5, text: '春', isSelect: false },
            { id: 6, text: '盛', isSelect: false },
            { id: 7, text: '光', isSelect: false },
            { id: 8, text: '世', isSelect: false },
            { id: 9, text: '彩', isSelect: false },
            { id: 10, text: '舞', isSelect: false },
            { id: 11, text: '碟', isSelect: false },
            { id: 12, text: '凤', isSelect: false },
            { id: 13, text: '飞', isSelect: false },
            { id: 14, text: '龙', isSelect: false },
          ],
        },
        {
          // 岁、秋、月、瑞、更、景、新、祥、福、泰、满、乐、门、安
          dl_id: 2,
          dlInfo: [
            { id: 1, text: '岁', isSelect: false },
            { id: 2, text: '秋', isSelect: false },
            { id: 3, text: '月', isSelect: false },
            { id: 4, text: '瑞', isSelect: false },
            { id: 5, text: '更', isSelect: false },
            { id: 6, text: '景', isSelect: false },
            { id: 7, text: '新', isSelect: false },
            { id: 8, text: '祥', isSelect: false },
            { id: 9, text: '福', isSelect: false },
            { id: 10, text: '泰', isSelect: false },
            { id: 11, text: '满', isSelect: false },
            { id: 12, text: '乐', isSelect: false },
            { id: 13, text: '门', isSelect: false },
            { id: 14, text: '安', isSelect: false },
          ],
        },
        {
          // 春、秋、潮、收、早、富、涨、裕、大、喜、地、庆、发、丰、生、登、机、泰
          dl_id: 3,
          dlInfo: [
            { id: 1, text: '春', isSelect: false },
            { id: 2, text: '秋', isSelect: false },
            { id: 3, text: '潮', isSelect: false },
            { id: 4, text: '收', isSelect: false },
            { id: 5, text: '早', isSelect: false },
            { id: 6, text: '富', isSelect: false },
            { id: 7, text: '涨', isSelect: false },
            { id: 8, text: '裕', isSelect: false },
            { id: 9, text: '大', isSelect: false },
            { id: 10, text: '喜', isSelect: false },
            { id: 11, text: '地', isSelect: false },
            { id: 12, text: '庆', isSelect: false },
            { id: 13, text: '发', isSelect: false },
            { id: 14, text: '丰', isSelect: false },
            { id: 15, text: '生', isSelect: false },
            { id: 16, text: '登', isSelect: false },
            { id: 17, text: '机', isSelect: false },
            { id: 18, text: '泰', isSelect: false },
          ],
        },
      ],
      moreCoupletsInfo: [],
      inputClick: null, // 点击的input框的索引
      inputFocus: null, // 点击的input框的高亮
      inputBlocks: ['', '', '', '', '', '', ''], // 方块的值
      dlLength: 7,
      operationLoading: false,
      visibilityChange: '',
      isLoading: false,
      JSBridge: {
        /**
         * @function 安卓原生分享弹窗回调
         * @param shareType 分享方式 0、复制链接（不需要判断shareStatus的状态成功与否） 1、QQ好友 2、QQ空间 3、微信好友 4、微信朋友圈 5、微信收藏
         * @param shareStatus 分享结果的状态 0、仅仅只是点击了分享，并未获取到是否成功或者失败 1、分享成功 2、分享失败 3、分享已取消
         */
        getMobShareCallback: function (type, status) {},
      },
      is_share_type: 11,
      isExistHbCover: false,
      hbCoverQuantity: 0,
    };
  },
  computed: {
    ...mapGetters({
      serialNumber: 'activity/serialNumber',
      userInfo: 'user/userInfo',
      initData: 'system/initData',
    }),
    selectedDlInfo() {
      // 找到对应 dl_id 的项
      const selectedDl = this.selectCoupletsTxtInfo.find(
        item => item.dl_id === this.selectDlInfo.dl_id,
      );

      // 如果找到了对应的 dl_id，则返回其 dlInfo，否则返回空数组
      return selectedDl ? selectedDl.dlInfo : [];
    },
    groupedSelectCoupletsTxtInfo() {
      return this.chunkArray(this.selectedDlInfo, 7);
    },
    inputBlocksLength() {
      return this.inputBlocks.join('').length;
    },
  },
  async created() {
    this.addScrollEvent();
  },
  async mounted() {
    // 官包触发页面刷新（将onResume挂载在window上）
    if (platform == 'android') {
      window.onResume = this.onResume;
      window.JSBridge = this.JSBridge;
    }
    this.shuffleTextOrder();
    document.body.addEventListener('touchstart', function () {});
  },
  beforeDestroy() {
    this.removeScrollEvent();
    document.body.removeEventListener('touchstart', function () {});
    document.body.removeEventListener(this.visibilityChange, function () {});
    if (this.timeClock) {
      clearInterval(this.timeClock);
      this.timeClock = null;
    }
  },
  async activated() {
    await this.resetDlInfo();
    await this.getInitData();
  },
  methods: {
    ...mapMutations({
      setSerialNumber: 'activity/setSerialNumber',
    }),
    ...mapActions({
      SET_USER_INFO: 'user/SET_USER_INFO',
    }),
    async onResume() {
      await boxInit();
      await this.SET_USER_INFO(true);
      await this.getInitData();
    },
    addScrollEvent() {
      window.addEventListener('scroll', this.handleScroll);
    },
    removeScrollEvent() {
      window.removeEventListener('scroll', this.handleScroll);
    },
    handleScroll() {
      let windowScrollTop =
        document.documentElement.scrollTop || document.body.scrollTop;

      if (!this.showReturnTop && windowScrollTop > 400) {
        this.showReturnTop = true;
      } else if (this.showReturnTop && windowScrollTop <= 400) {
        this.showReturnTop = false;
      }
    },
    handleReturnTop() {
      window.scrollTo({
        top: 0,
        behavior: 'smooth',
      });
    },
    formatNickname(nickname) {
      if (nickname.length > 5) {
        return nickname.slice(0, 2) + '**' + nickname.slice(-2);
      }
      return nickname;
    },
    // 处理键盘事件
    handleKeyup(event, index) {
      const key = event.keyCode;
      // const value = event.target.value;

      // 删除键：只删除当前输入框内容
      if (key === 8) {
        if (index == this.inputClick) {
          this.inputClick = null;
        }
        if (this.inputBlocks[index] !== '') {
          // this.inputBlocks[index] = ""; // 仅清空当前输入框
          this.$set(this.inputBlocks, index, '');
        } else if (index > 0) {
          this.$refs['input' + (index - 1)][0].focus(); // 如果为空，跳到前一个
          this.$nextTick(() => {
            // 把光标移到最后
            const prevInput = this.$refs['input' + (index - 1)][0];
            prevInput.selectionStart = prevInput.selectionEnd =
              prevInput.value.length;
          });
        }
        return;
      }

      // 输入后自动跳转到下一个输入框（如果为空才跳转）
      if (
        index < this.inputBlocks.length - 1 &&
        this.inputBlocks[index] !== ''
      ) {
        if (this.inputBlocks[index + 1] === '') {
          this.$refs['input' + (index + 1)][0].focus(); // 下一个为空才跳
        }
      }
    },
    // 确定对联
    handleSelectDl() {
      if (!this.userInfo.token) {
        this.$toast('请登录');
        this.login();
        return false;
      }
      if (this.inputBlocksLength < this.dlLength) return;
      this.savaDl();
    },
    async savaDl() {
      await ApiDoingsChunjieDowncouplet({
        dl_id: this.selectDlInfo.dl_id,
        text: this.inputBlocks.join(''),
      });
      await this.getInitData();
      this.myDlInfo.sl = this.selectDlInfo.text;
      this.myDlInfo.xl = this.inputBlocks.join('');
      this.myDlPopUp = true;
      this.$nextTick(() => {
        this.resetDlInfo();
        this.resetSelection();
      });
    },
    resetSelection() {
      this.selectCoupletsTxtInfo.forEach(group => {
        group.dlInfo.forEach(item => {
          item.isSelect = false;
        });
      });
    },
    shuffleTextOrder() {
      this.selectCoupletsTxtInfo.forEach(group => {
        const dlInfo = group.dlInfo;
        for (let i = dlInfo.length - 1; i > 0; i--) {
          const j = Math.floor(Math.random() * (i + 1));
          // 交换元素
          [dlInfo[i], dlInfo[j]] = [dlInfo[j], dlInfo[i]];
        }
      });
    },
    getDlSl(dl_id) {
      const item = this.dlInfoList.find(item => item.dl_id == dl_id);
      return item ? item.text : null;
    },
    clickCopy() {
      if (platform == 'android') {
        this.$copyText(`${this.serialNumber}`).then(
          async res => {
            this.$toast('复制成功');
          },
          err => {
            this.$dialog.alert({
              message: '复制失败',
              lockScroll: false,
            });
          },
        );
      } else {
        iframeCopy(`${this.serialNumber}`, '复制成功');
      }
    },
    closeHbShow() {
      this.customizedHbShow = false;
      document.body.removeEventListener(this.visibilityChange, function () {});
    },
    // 查看使用方法
    lookUseFunc() {
      this.customizedHbShow = false;
      document.body.removeEventListener(this.visibilityChange, function () {});
      this.$nextTick(() => {
        this.toPage('25NewYearActivityHbCoverStep');
      });
    },
    handleClick(item, index) {
      if (this.inputClick == index) {
        this.inputClick = null;
        this.handleFocus();
      } else {
        if (item) {
          this.inputClick = index;
        } else {
          this.handleFocus();
          this.inputFocus = index;
          this.inputClick = index;
          this.$nextTick(() => {
            const inputElement = this.$refs.hiddenInput;
            if (inputElement) {
              inputElement.setSelectionRange(index, index);
              inputElement.focus();
            }
          });
        }
      }
    },
    newHandleClick(item, index) {
      if (this.inputClick == index) {
        this.inputClick = null;
        this.handleFocus();
      } else {
        if (item) {
          this.inputClick = index;
        } else {
          this.handleFocus();
          this.inputFocus = index;
          this.inputClick = null;
        }
      }
    },
    clearInput(item, index) {
      if (this.inputClick == index) {
        this.inputClick = null;
      }
      this.selectCoupletsTxtInfo
        .find(item => item.dl_id === this.selectDlInfo.dl_id)
        .dlInfo.forEach(select => {
          if (select.text == item) {
            select.isSelect = !select.isSelect;
          }
        });
      this.$set(this.inputBlocks, index, '');
    },
    handleInput(event) {
      this.inputClick = null;
      const value = event.target.value;
      const remainingLength = Math.max(this.dlLength - value.length, 0);
      if (value.length > this.dlLength) return;
      this.inputBlocks = value
        .split('')
        .concat(new Array(remainingLength).fill(''));
      if (this.dlLength !== value.length) {
        for (let i = this.inputFocus + 1; i < this.inputBlocks.length; i++) {
          this.$set(this.inputBlocks, i, '');
        }
      }

      const nextEmptyIndex = this.inputBlocks.findIndex(block => block === '');
      this.inputFocus =
        nextEmptyIndex !== -1 ? nextEmptyIndex : this.inputFocus;
    },
    handleBlur() {
      this.inputFocus = null;
    },
    handleFocus() {
      this.$nextTick(() => {
        setTimeout(() => {
          const inputBox = document.querySelector('.input-box');
          if (inputBox) {
            inputBox.scrollIntoView({
              block: 'center',
              // behavior: "smooth",
              inline: 'nearest',
            });
          }
        }, 0);
      });
    },
    fillInput(char) {
      let firstEmptyIndex = this.inputBlocks.findIndex(item => item === '');
      if (firstEmptyIndex !== -1) {
        this.$set(this.inputBlocks, firstEmptyIndex, char);
      }
    },
    // 选中对联
    async handleClickDl(item) {
      if (this.selectDlInfo.dl_id === item.dl_id) {
        return;
      }
      this.selectDlInfo = item;
      this.resetDlInfo();
      this.resetSelection();
    },
    // 重置对联
    resetDlInfo() {
      this.inputClick = null;
      this.inputFocus = null;
      if (this.selectDlInfo.dl_id == 3) {
        this.dlLength = 9;
        this.inputBlocks = ['', '', '', '', '', '', '', '', ''];
      } else {
        this.dlLength = 7;
        this.inputBlocks = ['', '', '', '', '', '', ''];
      }
    },
    selectCoupletsTxt(item) {
      if (
        this.inputBlocksLength >= this.dlLength &&
        this.inputClick == null &&
        !item.isSelect
      ) {
        return;
      }
      if (this.inputClick !== null) {
        let firstEmptyIndex = this.inputBlocks.findIndex(
          block => block == item.text,
        );
        if (firstEmptyIndex !== -1) {
          this.$set(this.inputBlocks, firstEmptyIndex, '');
        }
        this.$set(this.inputBlocks, this.inputClick, item.text);
        return;
      }
      this.selectCoupletsTxtInfo
        .find(item => item.dl_id === this.selectDlInfo.dl_id)
        .dlInfo.forEach(select => {
          if (select.id === item.id) {
            select.isSelect = !select.isSelect;
            if (select.isSelect) {
              this.fillInput(item.text);
            } else {
              let firstEmptyIndex = this.inputBlocks.findIndex(
                block => block == item.text,
              );
              if (firstEmptyIndex !== -1) {
                this.$set(this.inputBlocks, firstEmptyIndex, '');
              }
            }
          }
        });
    },
    // 活动规则
    clickRule() {
      this.toPage('25NewYearActivityRulePage');
    },
    login() {
      BOX_login();
    },
    // 更多对联
    clickMoreDl() {
      this.toPage('25NewYearActivityMoreCouplets');
    },
    // 初始化信息
    async getInitData() {
      try {
        const res = await ApiDoingsChunjieCouplet();
        this.moreCoupletsInfo = res.data.offset;
        this.isExistHbCover = res.data.isExistHbCover;
        this.hbCoverQuantity = res.data.hbCoverQuantity;
      } catch (error) {
      } finally {
        this.isLoading = true;
      }
    },
    // 获取分享信息
    // 当is_share为1时，表示上报分享成功
    async getShareInfo(is_share = 0) {
      let params = {
        type: this.is_share_type,
        id: this.userInfo.user_id ? this.userInfo.user_id : 1,
      };
      if (is_share) {
        params.is_share = 1;
      }
      const res = await ApiCommonShareInfo(params);
      if (!is_share) {
        this.shareInfo = res.data;
      }
    },
    async handleShare(is_share = false) {
      // if ([2, 3, 4].includes(this.activity_status)) {
      //   this.$toast(this.activity_status_text);
      //   return false;
      // }
      if (!is_share) {
        this.is_share_type = 11;
      } else {
        this.is_share_type = 12;
      }
      await this.getShareInfo();
      if (this.initData?.share_info?.length) {
        if (this.operationLoading) {
          return false;
        }
        this.operationLoading = true;
        setTimeout(() => {
          this.operationLoading = false;
        }, 1000);
        if (is_share && platform == 'android') {
          if (typeof document.hidden !== 'undefined') {
            // Opera 12.10 and Firefox 18 and later support
            this.visibilityChange = 'visibilitychange';
          } else if (typeof document.msHidden !== 'undefined') {
            this.visibilityChange = 'msvisibilitychange';
          } else if (typeof document.webkitHidden !== 'undefined') {
            this.visibilityChange = 'webkitvisibilitychange';
          }
          document.addEventListener(this.visibilityChange, e => {
            if (e.target.visibilityState == 'visible') {
              this.$nextTick(() => {
                setTimeout(() => {
                  this.getChunjieCover();
                }, 300);
              });
            }
          });
        }
        window.BOX.mobShare(this.is_share_type, this.userInfo.user_id);
      } else {
        // web的iframe安全策略导致无法复制，故需使用postMessage转移至父级窗口中复制
        if (platform !== 'android') {
          await this.getShareInfo(1);
        }
        iframeCopy(this.shareInfo.share_text + this.shareInfo.url);
      }
      setTimeout(async () => {
        if (is_share && platform !== 'android') {
          this.getChunjieCover();
        }
        await this.getInitData();
      }, 1000);
    },
    async getChunjieCover() {
      const res = await ApiDoingsChunjieCover();
      if (res.data && res.data.is_tc && res.code == 3) {
        this.setSerialNumber(res.data.xlh);
        this.customizedHbShow = true;
      }
    },
    debounce(fn, delay) {
      let timer = null;
      return function (value) {
        clearTimeout(timer);
        timer = setTimeout(() => {
          fn.call(this, value);
        }, delay);
      };
    },
    getDlClass(item) {
      const baseClass = `dl${item.dl_id}-default`;
      const activeClass = `dl${item.dl_id}-active`;
      return item.dl_id === this.selectDlInfo.dl_id ? activeClass : baseClass;
    },
    isItemSelected(item) {
      return item.dl_id === this.selectDlInfo.dl_id;
    },
    chunkArray(array, chunkSize) {
      const result = [];
      for (let i = 0; i < array.length; i += chunkSize) {
        result.push(array.slice(i, i + chunkSize));
      }
      return result;
    },
    async handleClickDm(item) {
      if (!this.userInfo.token) {
        this.$toast('请登录');
        this.login();
        return false;
      }
      await ApiDoingsChunjieThumb({
        id: item.id,
        action: !item.text.click_status ? 'yes' : 'no',
      });
      item.text.click_status = !item.text.click_status;
      item.text.click_count = item.text.click_status
        ? item.text.click_count + 1
        : item.text.click_count - 1;
    },
  },
  watch: {
    inputBlocks(newBlocks) {
      this.selectCoupletsTxtInfo
        .find(item => item.dl_id === this.selectDlInfo.dl_id)
        .dlInfo.forEach(item => {
          item.isSelect = newBlocks.includes(item.text);
        });
    },
  },
};
</script>

<style lang="less" scoped>
.page-250101-activity {
  font-family: PingFang SC, PingFang SC;
  display: flex;
  flex-direction: column;
  width: 100%;
  overflow: hidden;
  min-height: 100vh;
  background: #fcf4e3;
  .back,
  .rule {
    width: 40 * @rem;
    height: 40 * @rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(109, 24, 39, 0.3);
    border-radius: 14 * @rem;
  }
  .back {
    background: rgba(109, 24, 39, 0.3)
      url(~@/assets/images/25newyear/left-back.png) center center no-repeat;
    background-size: 9 * @rem 14 * @rem;
  }
  .rule {
    font-weight: 500;
    font-size: 12 * @rem;
    color: #ffffff;
  }
  .fixed-btns {
    position: fixed;
    top: 106 * @rem;
    z-index: 999;
    padding: 0 16 * @rem;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    &.right-fixed {
      right: 0;
    }
    .fixed-share-btn,
    .fixed-prize-btn {
      box-sizing: border-box;
      padding: 0 7 * @rem;
      min-width: 40 * @rem;
      height: 40 * @rem;
      background: rgba(109, 24, 39, 0.3);
      border-radius: 14 * @rem;
      font-size: 12 * @rem;
      color: #ffffff;
      flex-wrap: wrap;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
  .main {
    width: 100%;
    position: relative;
    height: 100vh;
    flex: 1;
    min-height: 0;
    background: url(~@/assets/images/25newyear/25newyear-bg5.png) center top
      no-repeat;
    background-size: 100% 466 * @rem;
    padding-bottom: 47 * @rem;
    .activity-top {
      width: 100%;
      //   height: 205 * @rem;
      overflow: hidden;
      .activity-title {
        width: 189 * @rem;
        height: 56 * @rem;
        margin: 70 * @rem auto 0;
        background: url(~@/assets/images/25newyear/25newyear-title4.png) center
          top no-repeat;
        background-size: 189 * @rem 56 * @rem;
      }
      .activity-sub-title {
        font-size: 16 * @rem;
        color: #fff7e0;
        text-align: center;
        margin-top: 11 * @rem;
        height: 22 * @rem;
        line-height: 19 * @rem;
        .sub-title {
          color: #f12232;
          // text-decoration: underline;
          border-bottom: 1px solid #f12232;
        }
      }
      .activity-time {
        width: fit-content;
        height: 24 * @rem;
        border-radius: 20 * @rem;
        background: rgba(255, 255, 255, 0.2);
        font-size: 13 * @rem;
        color: #fff7e0;
        display: flex;
        padding: 0 17 * @rem;
        margin: 8 * @rem auto 0;
        line-height: 24 * @rem;
      }
    }
    .activity-content {
      position: relative;
      margin: 28 * @rem auto 0;
      display: flex;
      flex-direction: column;
      // align-items: center;
      .activity-content-dl {
        padding: 0 19 * @rem;
        box-sizing: border-box;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .content-dl-item {
          position: relative;
          width: 94 * @rem;
          height: 260 * @rem;
          .yy-item {
            position: absolute;
            bottom: -5 * @rem;
            width: 96 * @rem;
            height: 11 * @rem;
            background: url(~@/assets/images/25newyear/25newyear-icon11.png)
              center center no-repeat;
            background-size: 96 * @rem 11 * @rem;
          }
          .active-item {
            position: absolute;
            right: -7 * @rem;
            top: -5 * @rem;
            width: 18 * @rem;
            height: 18 * @rem;
            background: url(~@/assets/images/25newyear/btn-check1.png) center
              center no-repeat;
            background-size: 18 * @rem 18 * @rem;
          }
          .active-snake {
            position: absolute;
            bottom: -5 * @rem;
            right: -24 * @rem;
            width: 62 * @rem;
            height: 62 * @rem;
            background: url(~@/assets/images/25newyear/25newyear-logo3.png)
              center top no-repeat;
            background-size: 62 * @rem 62 * @rem;
          }
          &.dl1-default {
            background: url(~@/assets/images/25newyear/25newyear-dl1-default.png)
              center top no-repeat;
            background-size: 94 * @rem 260 * @rem;
          }
          &.dl1-active {
            background: url(~@/assets/images/25newyear/25newyear-dl1-active.png)
              center top no-repeat;
            background-size: 94 * @rem 260 * @rem;
          }
          &.dl2-default {
            background: url(~@/assets/images/25newyear/25newyear-dl2-default.png)
              center top no-repeat;
            background-size: 94 * @rem 260 * @rem;
          }
          &.dl2-active {
            background: url(~@/assets/images/25newyear/25newyear-dl2-active.png)
              center top no-repeat;
            background-size: 94 * @rem 260 * @rem;
          }
          &.dl3-default {
            background: url(~@/assets/images/25newyear/25newyear-dl3-default.png)
              center top no-repeat;
            background-size: 94 * @rem 260 * @rem;
          }
          &.dl3-active {
            background: url(~@/assets/images/25newyear/25newyear-dl3-active.png)
              center top no-repeat;
            background-size: 94 * @rem 260 * @rem;
          }
        }
      }
      .activity-content-xl {
        margin-top: 28 * @rem;
        padding: 0 12 * @rem;
        .xl-title {
          display: inline-block;
          height: 19 * @rem;
          font-weight: bold;
          font-size: 15 * @rem;
          color: #5a2a2a;
          line-height: 19 * @rem;
          text-align: left;
        }
        .xl-content {
          .input-box {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            flex-wrap: wrap;
            box-sizing: border-box;
            width: calc((40 * @rem + 12 * @rem) * 7);
            .input-item {
              position: relative;
              margin-top: 12 * @rem;
              box-sizing: border-box;
              flex-shrink: 0;
              width: 40 * @rem;
              height: 48 * @rem;
              background: #fafafa;
              border-radius: 6 * @rem;
              background: url(~@/assets/images/25newyear/zidi-default.png)
                center top no-repeat;
              background-size: 40 * @rem 48 * @rem;
              display: flex;
              align-items: center;
              justify-content: center;
              font-weight: 600;
              font-size: 16 * @rem;
              color: #5a2a2a;
              input {
                width: 40 * @rem;
                height: 48 * @rem;
                background: #fafafa;
                border-radius: 6 * @rem;
                background: url(~@/assets/images/25newyear/zidi-default.png)
                  center top no-repeat;
                background-size: 40 * @rem 48 * @rem;
                border: none;
                text-align: center;
                &.click-item {
                  width: 40 * @rem;
                  height: 48 * @rem;
                  background: url(~@/assets/images/25newyear/zidi-check.png) top
                    center no-repeat;
                  background-size: 40 * @rem 48 * @rem;
                }
              }
              &:not(:first-child) {
                margin-left: 12 * @rem;
              }
              &:nth-of-type(8) {
                margin-left: 0;
              }
              .clear-icon {
                position: absolute;
                top: -5 * @rem;
                right: -5 * @rem;
                width: 18 * @rem;
                height: 18 * @rem;
                background: url(~@/assets/images/25newyear/btn-close1.png) top
                  center no-repeat;
                background-size: 18 * @rem 18 * @rem;
              }
              &.click-default-item {
                width: 40 * @rem;
                height: 48 * @rem;
                background: url(~@/assets/images/25newyear/zidi-default.png) top
                  center no-repeat;
                background-size: 40 * @rem 48 * @rem;
              }
              &.click-item {
                width: 40 * @rem;
                height: 48 * @rem;
                background: url(~@/assets/images/25newyear/zidi-check.png) top
                  center no-repeat;
                background-size: 40 * @rem 48 * @rem;
              }
            }
            .highlight {
              display: inline-block;
              width: 1 * @rem;
              height: 18 * @rem;
              background-color: #333;
              animation: blink 1s step-end infinite;
            }
          }
        }
      }
      .activity-content-text {
        margin-top: 22 * @rem;
        padding: 0 12 * @rem;
        .text-title {
          display: inline-block;
          height: 19 * @rem;
          font-weight: bold;
          font-size: 15 * @rem;
          color: #5a2a2a;
          line-height: 19 * @rem;
          text-align: left;
        }
        .text-content {
          margin-top: 10 * @rem;
          display: flex;
          flex-direction: column;
          .text-column {
            display: flex;
            align-items: center;
            .text-item {
              box-sizing: border-box;
              width: 40 * @rem;
              height: 48 * @rem;
              background: #fafafa;
              border-radius: 6 * @rem;
              display: flex;
              align-items: center;
              justify-content: center;
              border: 1 * @rem solid #eac6c3;
              .text {
                font-weight: 500;
                font-size: 16 * @rem;
                color: #5a2a2a;
              }
              &.active {
                background: #ffdbd4;
                border: 1 * @rem solid #f12232;
                color: #5a2a2a;
              }
              &:not(:first-child) {
                margin-left: 12 * @rem;
              }
            }
            &:not(:first-child) {
              margin-top: 16 * @rem;
            }
          }
        }
      }
      .activity-content-btn {
        margin: 24 * @rem auto 0;
        display: flex;
        align-items: center;
        justify-content: center;
        .btn-default {
          width: 256 * @rem;
          height: 74 * @rem;
          background: url(~@/assets/images/25newyear/25newyear-btn1-default.png)
            center top no-repeat;

          background-size: 256 * @rem 74 * @rem;
          &.confirm {
            width: 256 * @rem;
            height: 74 * @rem;
            background: url(~@/assets/images/25newyear/25newyear-btn1-active.png)
              center top no-repeat;
            background-size: 256 * @rem 74 * @rem;
          }
        }
      }
    }
    .activity-bottom {
      position: relative;
      margin: 18 * @rem auto 0;
      width: 351 * @rem;
      // height: 468 * @rem;
      background: #fafafa;
      border-radius: 12 * @rem;
      border: 2 * @rem solid #ffbf76;
      .more-xl-logo {
        position: absolute;
        top: -6 * @rem;
        left: 50%;
        transform: translateX(-50%);
        width: 164 * @rem;
        height: 46 * @rem;
        background: url(~@/assets/images/25newyear/25newyear-title5.png) center
          top no-repeat;
        background-size: 164 * @rem 46 * @rem;
      }
      .more-xl-btn {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        margin: 10 * @rem 13 * @rem 0 0;
        .more-btn-box {
          display: flex;
          align-items: center;
          > div {
            display: inline-block;
            height: 15 * @rem;
            font-weight: 500;
            font-size: 12 * @rem;
            color: #ad8585;
            margin-right: 5 * @rem;
          }
          > span {
            width: 7 * @rem;
            height: 11 * @rem;
            background: url(~@/assets/images/25newyear/btn-right-arrow.png)
              center top no-repeat;
            background-size: 7 * @rem 11 * @rem;
          }
        }
      }
      .more-xl-content {
        margin: 25 * @rem 12 * @rem 24 * @rem;
        .more-xl-content-item {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-top: 14 * @rem;
          .left-content {
            display: flex;
            align-items: center;
            .user-info {
              display: flex;
              align-items: center;
              .avatar {
                width: 36 * @rem;
                height: 36 * @rem;
                border-radius: 50%;
                overflow: hidden;
                background: #ccc;
              }
              .user-name {
                width: 63 * @rem;
                margin-left: 8 * @rem;
                font-weight: 400;
                font-size: 13 * @rem;
                color: #5a2a2a;
                height: 15 * @rem;
                line-height: 15 * @rem;
                // text-overflow: ellipsis;
                overflow: hidden;
                white-space: nowrap;
              }
            }
            .dl-info {
              margin-left: 20 * @rem;
              display: flex;
              flex-direction: column;

              .sl-title {
                display: flex;
                align-items: center;
                .sl-content {
                  max-width: 140 * @rem;
                  height: 20 * @rem;
                  font-weight: 500;
                  font-size: 14 * @rem;
                  color: #a17272;
                  line-height: 20 * @rem;
                  text-align: left;
                  font-style: normal;
                  text-transform: none;
                  text-overflow: ellipsis;
                  overflow: hidden;
                  white-space: nowrap;
                }
                .me {
                  margin-left: 3 * @rem;
                  width: 20 * @rem;
                  height: 14 * @rem;
                  background: #ffd1d1;
                  border-radius: 3 * @rem;
                  font-weight: 500;
                  font-size: 11 * @rem;
                  color: #a71111;
                  line-height: 14 * @rem;
                  text-align: center;
                }
              }
              .xl-title {
                max-width: 140 * @rem;
                margin-top: 3 * @rem;
                height: 20 * @rem;
                font-weight: 500;
                font-size: 14 * @rem;
                color: #5a2a2a;
                text-overflow: ellipsis;
                overflow: hidden;
                white-space: nowrap;
              }
            }
          }

          .likes {
            display: flex;
            align-items: center;
            .like {
              width: 16 * @rem;
              height: 16 * @rem;
              background: url(~@/assets/images/25newyear/btn-likes.png) center
                top no-repeat;
              background-size: 16 * @rem 16 * @rem;
              &.active {
                background: url(~@/assets/images/25newyear/btn-likes-active.png)
                  center top no-repeat;
                background-size: 16 * @rem 16 * @rem;
              }
            }
            .num {
              margin-left: 4 * @rem;
              font-weight: 400;
              font-size: 12 * @rem;
              color: #a17272;
            }
          }
          &:not(:last-child) {
            padding-bottom: 16 * @rem;
            border-bottom: 1 * @rem solid #efebeb;
          }
        }
      }
    }
  }
}
.hb-dialog {
  width: 240 * @rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: transparent;
  overflow: visible;
  .dialog-content {
    box-sizing: border-box;
    position: relative;
    z-index: 2;
    width: 240 * @rem;
    height: 397 * @rem;
    background: url(~@/assets/images/25newyear/25newyear-bg6.png) center top
      no-repeat;
    background-size: 240 * @rem 397 * @rem;
  }
  .btn-close {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    bottom: -49 * @rem;
    width: 30 * @rem;
    height: 30 * @rem;
    background: url(~@/assets/images/25newyear/btn-close2.png) center top
      no-repeat;
    background-size: 30 * @rem 30 * @rem;
  }
}
.customized-hb-dialog {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: transparent;
  overflow: visible;
  .dialog-content {
    width: 300 * @rem;
    height: 527 * @rem;
    border-radius: 16 * @rem;
    background: #ffffff;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 2;
    .dialog-title {
      margin-top: 20 * @rem;
      width: 100%;
      position: relative;
      .title {
        height: 23 * @rem;
        font-family: PingFang SC, PingFang SC;
        font-weight: 600;
        font-size: 18 * @rem;
        color: #5a2a2a;
        line-height: 23 * @rem;
        text-align: center;
      }
      .close-btn {
        position: absolute;
        top: 2 * @rem;
        right: 15 * @rem;
        width: 13 * @rem;
        height: 13 * @rem;
        background: url(~@/assets/images/25newyear/btn-close.png) center top
          no-repeat;
        background-size: 13 * @rem 13 * @rem;
      }
    }
    .dialog-img {
      margin-top: 26 * @rem;
      width: 160 * @rem;
      height: 265 * @rem;
      background: url(~@/assets/images/25newyear/25newyear-bg8.png) center top
        no-repeat;
      background-size: 160 * @rem 265 * @rem;
      border-radius: 16 * @rem;
      overflow: hidden;
    }
    .dialog-info {
      margin-top: 17 * @rem;
      padding: 0 22 * @rem;
      .time {
        font-weight: 500;
        font-size: 13 * @rem;
        color: #7a5252;
        line-height: 20 * @rem;
        span {
          font-weight: bold;
        }
      }
      .info {
        margin-top: 6 * @rem;
        display: flex;
        align-items: center;
        .serialNumber {
          font-weight: 400;
          font-size: 13 * @rem;
          color: #7a5252;
          user-select: text;
          span {
            font-weight: bold;
          }
        }
        .copy {
          margin-left: 5 * @rem;
          font-weight: bold;
          font-size: 13 * @rem;
          color: #e75555;
        }
      }
    }
    .check-look-btn {
      margin-top: 22 * @rem;
      margin-bottom: 15 * @rem;
      width: 238 * @rem;
      height: 40 * @rem;
      background: linear-gradient(90deg, #f64b4b 0%, #ffb07c 100%), #d9d9d9;
      border-radius: 20 * @rem;
      font-weight: 500;
      font-size: 15 * @rem;
      color: #fffdf1;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
.return-top {
  position: fixed;
  right: 5 * @rem;
  bottom: 100 * @rem;
  width: 50 * @rem;
  height: 50 * @rem;
  background: url(~@/assets/images/return-top.png) center center no-repeat;
  background-size: 50 * @rem 50 * @rem;
  z-index: 100;
}
@keyframes blink {
  50% {
    opacity: 0;
  }
}
@keyframes tipShow {
  0% {
    opacity: 0;
    top: 0;
  }
  100% {
    opacity: 1;
    top: -12 * @rem;
  }
}
@keyframes tipShowLast {
  0% {
    opacity: 0;
    top: 0;
  }
  100% {
    opacity: 1;
    top: -30 * @rem;
  }
}
</style>
