<template>
  <div class="danmaku-page">
    <nav-bar-2
      ref="topNavBar"
      bgStyle="transparent-white"
      :azShow="true"
      :placeholder="false"
    >
      <template #left>
        <div class="back" @click="back"></div>
      </template>
    </nav-bar-2>

    <div class="main">
      <div class="activity-title"></div>
      <div class="danmaku-content">
        <DanmakuItem
          v-if="isShow"
          ref="danmaku"
          class="demo"
          v-model="danmus"
          isSuspend
          v-bind="config"
        >
          <!-- 容器slot -->
          <div></div>
          <!-- 弹幕slot -->
          <template v-slot:dm="{ index, danmu }">
            <!-- <div class="danmu-item"> -->
            <div
              class="danmu-item"
              :class="{ 'btn-item-me': danmu.id == danmuId }"
            >
              <img
                v-if="danmu.user?.avatar"
                class="img"
                :src="danmu.user?.avatar"
              />
              <img v-else class="img" :src="defaultAvatar" />
              <!-- <span>{{ danmu.name }}：</span> -->
              <span>{{ danmu.content }}</span>
              <div class="btn-box">
                <div
                  @click="handleClickDm(index, danmu)"
                  class="thumbs-up"
                  :class="{ 'thumbs-up-active': danmu.click_status }"
                ></div>
                <div
                  class="num"
                  v-show="danmu.click_count > 0"
                  :class="{ 'num-active': danmu.click_status }"
                >
                  {{ danmu.click_count }}
                </div>
              </div>
            </div>
          </template>
        </DanmakuItem>
      </div>
      <div class="danmaku-bg"></div>

      <!-- <div class="main1">
        <div class="intro">
          <h1>vue-danmaku</h1>
          <p>基于 Vue.js 的弹幕交互组件</p>
        </div>
        <div class="action">
          <p>
            播放：
            <button class="btn" @click="play('play')">播放</button>
            <button class="btn" @click="play('pause')">暂停</button>
            <button class="btn" @click="play('stop')">停止</button>
          </p>
          <p>
            模式：
            <button class="btn" @click="switchSlot(true)">弹幕 slot</button>
            <button class="btn" @click="switchSlot(false)">普通文本</button>
          </p>
          <p>
            循环：
            <button class="btn" @click="play('show')">开启</button>
            <button class="btn" @click="play('hide')">关闭</button>
          </p>
          <p>
            显示：
            <button class="btn" @click="play('show')">显示</button>
            <button class="btn" @click="play('hide')">隐藏</button>
          </p>
          <p>
            速度：
            <button class="btn" @click="speedsChange(-10)">减速</button>
            <button class="btn" @click="speedsChange(10)">增速</button>
            <span>当前速度：{{ config.speeds }}像素/s</span>
          </p>
          <p>
            轨道：
            <button class="btn" @click="channelChange(-1)">-1</button>
            <button class="btn" @click="channelChange(1)">+1</button>
            <button class="btn" @click="channelChange(-config.channels)">
              填满
            </button>
            <span>当前轨道：{{ config.channels }}</span>
          </p>
          <p>
            发送：
            <input class="ipt" type="text" v-model="danmuMsg" />
            <button class="btn" @click="handleAddDanmu">发送</button>
          </p>
        </div>
      </div> -->

      <div class="danmaku-input">
        <div class="user-info">
          <div class="avatar">
            <user-avatar></user-avatar>
          </div>
          <div class="nickname">{{ userInfo.nickname }}</div>
        </div>
        <div class="danmaku-text">
          <van-field
            class="danmaku-field"
            v-model="danmuMsg"
            autosize
            type="textarea"
            maxlength="50"
            placeholder="请留下你的新年愿望"
          />
        </div>
      </div>
      <div class="bottom-container">
        <div class="bottom-fixed">
          <div class="bottom-btn" @click="handleAddDanmu">
            <div class="bottom-bg-icon"></div>
            <div class="operation-btn btn">放飞心愿</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
// import { danmus, getDanmuData } from "./danmu.js";
import DanmakuItem from '../../../components/danmaku-item';
import { platform, boxInit, BOX_login } from '@/utils/box.uni.js';
import {
  ApiDoingsYuandanMessage,
  ApiDoingsYuandanThumb,
  ApiDoingsYuandanMessageList,
} from '@/api/views/250101';
export default {
  components: { DanmakuItem },
  data() {
    return {
      // danmus: getDanmuData(),
      danmus: [],
      config: {
        channels: 5, // 轨道数量，为0则弹幕轨道数会撑满容器
        useSlot: true, // 是否开启slot
        loop: true, // 是否开启弹幕循环
        speeds: 30, // 弹幕速度，实际为每秒弹幕走过的像素距离
        fontSize: 20, // 文本模式下的字号
        top: 30, // 弹幕轨道间的垂直间距
        right: 0, // 同一轨道弹幕的水平间距
        debounce: 100, // 弹幕刷新频率（多少毫秒插入一条弹幕，建议不小于50）
        randomChannel: true,
      },
      danmuMsg: '',
      danmuId: '',
      isShow: false,
    };
  },
  created() {},
  mounted() {
    // 官包触发页面刷新（将onResume挂载在window上）
    if (platform == 'android') {
      window.onResume = this.onResume;
    }
    window.onresize = () => this.resizeHandler();
  },
  beforeDestroy() {
    window.onresize = null;
  },
  async activated() {
    await this.getDoingsYuandanMessageList();
  },
  methods: {
    login() {
      BOX_login();
    },
    async getDoingsYuandanMessageList() {
      const res = await ApiDoingsYuandanMessageList();
      this.danmus = res.data;
      this.isShow = true;
    },
    async onResume() {
      await boxInit();
      await this.SET_USER_INFO(true);
      await this.getDoingsYuandanMessageList();
    },
    async postYuandanMessage() {
      const res = await ApiDoingsYuandanMessage({
        content: this.danmuMsg,
      });
      const danmuMsg = this.config.useSlot ? res.data : this.danmuMsg;
      this.danmuId = res.data.id;
      this.$refs.danmaku.add(danmuMsg);
      this.danmuMsg = '';
    },
    async handleClickDm(index, dm) {
      if (!this.userInfo.token) {
        this.$toast('请登录');
        this.login();
        return false;
      }

      await ApiDoingsYuandanThumb({
        id: dm.id,
        action: !dm.click_status ? 'yes' : 'no',
      });
      dm.click_status = !dm.click_status;
      if (dm.click_count >= 1) {
        dm.click_count = dm.click_status
          ? dm.click_count + 1
          : dm.click_count - 1;
      }
      // console.log("当前点击的弹幕:>> ", index, dm);
    },
    play(type) {
      switch (type) {
        case 'play':
          this.$refs.danmaku.play();
          break;
        case 'pause':
          this.$refs.danmaku.pause();
          break;
        case 'stop':
          this.$refs.danmaku.stop();
          break;
        case 'show':
          this.$refs.danmaku.show();
          break;
        case 'hide':
          this.$refs.danmaku.hide();
          break;
        case 'reset':
          this.$refs.danmaku.reset();
          break;
        default:
          break;
      }
    },
    switchSlot(slot) {
      this.config.useSlot = slot;
      this.danmus = slot ? getDanmuData() : danmus;
      setTimeout(() => {
        this.$refs.danmaku.reset();
        this.$refs.danmaku.play();
      });
    },
    setPerformance(type) {
      stats.dom.style.display = type;
    },
    speedsChange(val) {
      if (this.config.speeds <= 10 && val === -10) {
        return;
      }
      this.config.speeds += val;
      this.$refs.danmaku.reset();
    },
    fontChange(val) {
      this.config.fontSize += val;
      this.$refs.danmaku.reset();
    },
    channelChange(val) {
      if (!this.config.channels && val === -1) {
        return;
      }
      this.config.channels += val;
    },
    resizeHandler() {
      if (this.timer) clearTimeout(this.timer);
      this.timer = setTimeout(() => {
        this.$refs.danmaku.resize();
      });
    },
    async handleAddDanmu() {
      if (!this.userInfo.token) {
        this.$toast('请登录');
        this.login();
        return false;
      }
      if (!this.danmuMsg) return;
      await this.postYuandanMessage();
    },
  },
};
</script>

<style lang="less" scoped>
.danmaku-page {
  font-family: PingFang SC, PingFang SC;
  display: flex;
  flex-direction: column;
  width: 100%;
  overflow: hidden;
  min-height: 100vh;
  background: #ffcfba;
  .back {
    width: 30 * @rem;
    height: 30 * @rem;
    border-radius: 15 * @rem;
    background: rgba(0, 0, 0, 0.3) url(~@/assets/images/nav-bar-back-white.png)
      center center no-repeat;
    background-size: 8 * @rem 14 * @rem;
  }
  .main {
    width: 100%;
    position: relative;
    flex: 1;
    min-height: 0;
    background: url(~@/assets/images/250101/250101_bg1.png) center top no-repeat;
    background-size: 100% 227 * @rem;
    width: 100%;
    height: 223 * @rem;

    .activity-title {
      margin: 49 * @rem auto 0;
      background: url(~@/assets/images/250101/lottery-title1.png) center top
        no-repeat;
      background-size: 288 * @rem 62 * @rem;
      width: 288 * @rem;
      height: 62 * @rem;
    }
    .danmaku-content {
      position: relative;
      min-height: 300 * @rem;
      height: 300 * @rem;
      padding: 35 * @rem 0;
      z-index: 999;
      .demo {
        width: 100%;
        height: 100%;
        z-index: 0;
        .danmu-item {
          height: 32 * @rem;
          padding: 3 * @rem;
          box-sizing: border-box;
          display: flex;
          align-items: center;
          background: rgba(255, 255, 255, 0.3);
          border-radius: 22 * @rem;
          border: 1px solid rgba(255, 255, 255, 0.28);
          .img {
            height: 26 * @rem;
            width: 26 * @rem;
            border-radius: 50%;
            margin-right: 6 * @rem;
          }
          span {
            font-weight: 500;
            height: 18 * @rem;
            line-height: 18 * @rem;
            font-size: 13 * @rem;
            color: #c56639;
          }
          .btn-box {
            display: flex;
            align-items: center;
            .thumbs-up {
              margin-left: 6 * @rem;
              background: url('~@/assets/images/250101/like_icon.png') center
                top no-repeat;
              background-size: 16 * @rem 16 * @rem;
              width: 16 * @rem;
              height: 16 * @rem;
              &.thumbs-up-active {
                background: url('~@/assets/images/250101/like_active_icon.png')
                  center top no-repeat;
                background-size: 16 * @rem 16 * @rem;
                width: 16 * @rem;
                height: 16 * @rem;
              }
            }
            .num {
              height: 18 * @rem;
              line-height: 18 * @rem;
              font-weight: 500;
              font-size: 13 * @rem;
              color: #c56639;
              &.num-active {
                color: #f43c27;
              }
            }
          }

          &.btn-item-me {
            border: 1px solid #888;
            background: rgba(255, 255, 255, 0.2);
          }
        }
      }
    }
    .danmaku-bg {
      position: absolute;
      top: 73 * @rem;
      background: url(~@/assets/images/250101/lottery-bg1.png) no-repeat -10 * @rem -25 *
        @rem;
      background-size: 100% 545 * @rem;
      width: 100%;
      height: 545 * @rem;
      z-index: 1;
    }

    .main1 {
      position: absolute;
      z-index: 1;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      .intro {
        display: inline-block;
        color: #fff;
        text-align: center;
        text-shadow: 2 * @rem 4 * @rem 6 * @rem rgba(0, 0, 0, 0.4);
        h1 {
          font-size: 48 * @rem;
          line-height: 32 * @rem;
        }
      }
      .action {
        margin-top: 20 * @rem;
        color: #fff;
        min-width: 360 * @rem;
        .btn {
          color: #000;
          background: #fff;
          border: none;
          padding: 6 * @rem 16 * @rem;
          margin-right: 8 * @rem;
          border-radius: 5 * @rem;
          min-height: 31 * @rem;
          outline: none;
          cursor: pointer;
          transition: all 0.3s;
          &:hover {
            background-color: #f3f7fa;
          }
          &:active {
            background-color: #fff;
          }
        }
        .ipt {
          width: 130 * @rem;
          padding: 8 * @rem 16 * @rem;
          border-radius: 5 * @rem;
          outline: none;
          border: none;
          margin-right: 8 * @rem;
        }
      }
    }

    .danmaku-input {
      position: relative;
      padding: 0 27 * @rem;
      box-sizing: border-box;
      z-index: 999;
      .user-info {
        display: flex;
        align-items: center;
        margin-left: 3 * @rem;
        .avatar {
          width: 36 * @rem;
          height: 36 * @rem;
          flex-shrink: 0;
        }
        .nickname {
          margin-left: 5 * @rem;
          font-weight: 500;
          font-size: 15 * @rem;
          font-weight: bold;
          color: #c56639;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
      .danmaku-text {
        width: 100%;
        margin-top: 10 * @rem;
        .danmaku-field {
          width: 100%;
          height: 149 * @rem;
          background: rgba(255, 252, 242, 0.6);
          border-radius: 12 * @rem;
        }
        /deep/.van-field__control::placeholder {
          font-weight: 400;
          font-size: 14 * @rem;
          color: rgba(197, 102, 57, 0.52);
        }
      }
    }
    .bottom-container {
      flex-shrink: 0;
      width: 100%;
      // height: calc(70 * @rem + @safeAreaBottom);
      // height: calc(70 * @rem + @safeAreaBottomEnv);
      margin-top: 45 * @rem;
      margin-bottom: 60 * @rem;
      .bottom-fixed {
        box-sizing: border-box;
        // position: fixed;
        // bottom: 60 * @rem;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        .bottom-btn {
          position: relative;
          .bottom-bg-icon {
            position: absolute;
            z-index: 9;
            left: -19 * @rem;
            top: -35 * @rem;
            background: url('~@/assets/images/250101/danmaku_dl_icon.png')
              no-repeat -5 * @rem -5 * @rem;
            background-size: 100 * @rem 118 * @rem;
            width: 70 * @rem;
            height: 102 * @rem;
          }
          .operation-btn {
            width: 238 * @rem;
            height: 40 * @rem;
            border-radius: 20 * @rem;
            background: linear-gradient(221deg, #fd9083 0%, #ff495c 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 15 * @rem;
            font-weight: 500;
            color: #ffffff;
            background-color: #e1e1e1;
          }
        }
      }
    }
  }
  .github-corner:hover .octo-arm {
    animation: octocat-wave 560ms ease-in-out;
  }

  @keyframes octocat-wave {
    0%,
    100% {
      transform: rotate(0);
    }
    20%,
    60% {
      transform: rotate(-25deg);
    }
    40%,
    80% {
      transform: rotate(10deg);
    }
  }

  @media (max-width: 500px) {
    .github-corner:hover .octo-arm {
      animation: none;
    }
    .github-corner .octo-arm {
      animation: octocat-wave 560ms ease-in-out;
    }
  }
}</style
>>
