<template>
  <div class="page-250101-activity">
    <nav-bar-2
      ref="topNavBar"
      bgStyle="transparent-white"
      :azShow="true"
      :placeholder="false"
    >
      <template #left>
        <div class="back" @click="back"></div>
      </template>
    </nav-bar-2>
    <div class="rule-btn" @click="rulePopup = true">规则</div>
    <!-- 主体内容 -->
    <div class="main">
      <div class="activity-title-text">—— 完成会场一第5个任务参与秒杀 ——</div>
      <div class="activity-title"></div>
      <div class="activity-sub-title">限时秒杀 惊喜不断</div>
      <div class="activity-time">活动时间：2025.01.01</div>
      <div class="activity-grab-it" v-if="seckillList.length">
        <div
          class="grab-it-item btn"
          v-for="(item, index) in seckillList"
          :key="index"
          @click="handleClickSeckill(index)"
        >
          <div class="time">{{ item.start_time }}</div>
          <div class="status" :class="{ loading: current_index == index }">
            <div class="status-item" v-if="item.is_started == 0">等待开抢</div>
            <div class="status-item" v-else-if="item.is_started == 1">
              仅剩{{ countdownSeckill }}
            </div>
            <div class="status-item" v-else-if="item.is_started == 2">
              已结束
            </div>
            <div class="status-item" v-else-if="item.is_started == 3">
              即将开抢
            </div>
          </div>
        </div>
      </div>
      <!-- 预约秒杀提醒 -->
      <div class="grab-it-remind" v-if="seckillList.length && isStartedValid">
        <div class="left-remind">
          <div class="icon">
            <img src="~@/assets/images/250101/seckill-icon1.png" alt="" />
          </div>
          <div class="remind-content">
            <div class="remind-title">
              <div class="title">预约秒杀提醒</div>
              <div class="reservation" v-if="isRemind">
                <div class="icon"></div>
                <div class="text">已预约</div>
              </div>
            </div>
            <div class="remind-text">秒杀前5分钟通过微信提醒你</div>
          </div>
        </div>
        <div
          class="remind-btn"
          :class="{ cancel: isRemind }"
          @click="appointmentRemind"
        >
          <div v-if="!isRemind">预约提醒</div>
          <div v-else>取消预约</div>
        </div>
      </div>
      <!-- 秒杀开抢列表 -->
      <div class="grab-it-list">
        <div
          class="grab-it-item"
          v-for="item in SeckillCommodityList"
          :key="item.id"
        >
          <div class="grad-card">
            <div class="left-remind">
              <div class="icon">
                <img :src="item.icon" alt="" />
              </div>
              <div class="remind-content">
                <div class="remind-title">
                  <div class="title">{{ item.desc }}</div>
                </div>
                <div class="remind-text">{{ item.title }}</div>
              </div>
            </div>
            <div
              class="remind-btn"
              :class="{
                finished: item.status == 1 || item.status == 2,
                immediately: item.status == 0,
              }"
              @click="handleSeckill(item)"
            >
              <div v-if="item.status == 0">立即抢</div>
              <div v-else-if="item.status == 1">已抢到</div>
              <div v-else-if="item.status == 2">已抢完</div>
              <div v-else-if="item.status == 3">等待开抢</div>
            </div>
          </div>
          <div class="dashed-line"></div>
          <div class="grad-describe">
            <div class="grad-num">限量{{ item.num }}</div>
            <div class="grad-limit">每人限抢1件</div>
          </div>
        </div>
      </div>
    </div>

    <!-- svip购买 -->
    <div class="svip-container">
      <div class="svip-card">
        <div class="svip-border">
          <div class="svip-content">
            <div class="svip-title"></div>
            <div class="svip-desc">每项每人仅可购买一次</div>
            <div class="meal-list">
              <div
                class="meal-item"
                :class="{
                  active: meal.amount == selectedMeal.amount,
                }"
                v-for="(meal, index) in vipList"
                :key="index"
                @click="handleSelectMeal(meal)"
              >
                <!-- 0不可购买  1可以购买 -->
                <div class="tag" v-if="meal.buy_status != 1">已购买</div>
                <div class="title">{{ meal.title }}</div>
                <div class="price-line">
                  <div class="price"><span>¥</span>{{ meal.amount }}</div>
                  <div class="old-price no-through" v-if="meal.show_title">
                    {{ meal.show_title }}
                  </div>
                  <div class="old-price" v-else-if="meal.show_amount">
                    ¥{{ meal.show_amount }}
                  </div>
                </div>
                <div class="desc">{{ meal.desc }}</div>
              </div>
            </div>

            <div class="btn svip-btn" @click="handleSvipBtn">
              立即购买
              <template v-if="selectedMeal.amount"
                >¥{{ selectedMeal.amount }}</template
              >
            </div>
            <div class="time-left">{{ activityTimeLeft }}</div>
          </div>
        </div>
      </div>
    </div>
    <!-- 支付方式弹窗 -->
    <pay-type-popup
      :show.sync="payPopupShow"
      :list="payList"
      @choosePayType="choosePayType"
      :money="Number(selectedMeal.amount)"
      unit="¥"
    ></pay-type-popup>

    <!-- 秒杀弹窗 -->
    <van-dialog
      v-model="SeckillPopUp"
      :show-confirm-button="false"
      :lock-scroll="false"
      class="seckill-dialog"
    >
      <div class="logo-icon"></div>
      <div class="dialog-content">
        <div class="title">暂无秒杀资格</div>
        <div class="msg">
          <span>完成“向新出发 步步为赢”第5个任务即可参与秒杀</span>
        </div>
        <div class="btn-bottom">
          <div class="btn" @click="goToTask">去完成</div>
          <div class="text">
            <span @click="SeckillPopUp = false">我知道了</span>
          </div>
        </div>
      </div>
    </van-dialog>

    <!-- 规则弹窗 -->
    <rule-popup :show.sync="rulePopup"></rule-popup>
  </div>
</template>

<script>
import { platform, boxInit, BOX_openInNewWindow } from '@/utils/box.uni.js';
import { BOX_login } from '@/utils/box.uni.js';
import { envFun } from '@/utils/function';
import {
  ApiDoingsYuandanIndex,
  ApiDoingsYuandanSeckill,
  ApiDoingsYuandanSeckillList,
  ApiDoingsYuandanReservation,
} from '@/api/views/250101.js';
import dayjs from 'dayjs';
import { mapGetters, mapActions } from 'vuex';
import {
  ApiCreateOrderSvip,
  ApiGetPayUrl,
  ApiGetOrderStatus,
  ApiGetPaymentMethod,
  ApiCreateOrderPtbNew,
} from '@/api/views/recharge.js';

export default {
  components: {
    rulePopup: () => import('../components/rule-popup.vue'),
  },
  data() {
    return {
      activity_status: 3, // 活动状态 1活动已开始 2 活动不存在 3活动未开始 4活动已结束
      rulePopup: false,
      isRemind: false,
      timeClock: null, // 倒计时定时器
      timeClock1: null, // 倒计时定时器
      isOpenTimeClock: false, //是否开启倒计时
      SeckillPopUp: false, // 秒杀弹窗
      SeckillCommodityList: [], // 秒杀开抢列表
      vipList: [],
      currentSession: null, //当前场次
      seckillList: [], //秒杀活动列表
      info: {}, // 活动信息
      nowTime: Date.now(),
      selectedMeal: {},
      payPopupShow: false,
      payList: [], // 支付方式列表
      selectedPayType: 'wx', // 支付方式
      current_index: 0,
    };
  },

  async created() {
    await this.getPayMethod(); // 获取支付方式
  },
  async mounted() {
    // 官包触发页面刷新（将onResume挂载在window上）
    if (platform == 'android') {
      window.onResume = this.onResume;
    }
    var visibilityChange;
    if (typeof document.hidden !== 'undefined') {
      // Opera 12.10 and Firefox 18 and later support
      visibilityChange = 'visibilitychange';
    } else if (typeof document.msHidden !== 'undefined') {
      visibilityChange = 'msvisibilitychange';
    } else if (typeof document.webkitHidden !== 'undefined') {
      visibilityChange = 'webkitvisibilitychange';
    }
    document.addEventListener(visibilityChange, e => {
      if (e.target.visibilityState == 'visible') {
        this.updateSeckillList();
      }
    });
  },
  deactivated() {
    clearInterval(this.timeClock);
    this.timeClock = null;
  },
  beforeDestroy() {
    clearInterval(this.timeClock);
    this.timeClock = null;
  },
  async activated() {
    this.SeckillPopUp = false;
    await this.updateSeckillList();
    await this.getIndexData();
  },
  methods: {
    async handleClickSeckill(index) {
      if (this.current_index == index) return;
      this.current_index = index;
      await this.updateSeckillList(index);
    },
    // 是否预约
    async doingsYuandanReservation(status) {
      await ApiDoingsYuandanReservation({
        status,
      });
    },
    // 秒杀
    async handleSeckill(item) {
      try {
        // 获取秒杀列表
        // await this.updateSeckillList(this.current_index);

        // const seckillItem = this.SeckillCommodityList.find((seckill) => {
        //   return seckill.id === item.id;
        // });
        // if (
        //   seckillItem.status === 1 ||
        //   seckillItem.status === 2 ||
        //   seckillItem.status === 3
        // ) {
        //   await this.updateSeckillList(this.current_index);
        //   return;
        // }
        // if (
        //   this.currentSession === -1 ||
        //   this.seckillList[this.currentSession].starttime -
        //     this.seckillList[this.currentSession].nowtime >
        //     0
        // ) {
        //   return;
        // }

        // 进行抽奖请求
        const res = await ApiDoingsYuandanSeckill({ id: item.id });

        if (res.code == 3) {
          this.SeckillPopUp = true;
          return;
        }
      } catch (error) {
      } finally {
        await this.updateSeckillList(this.current_index);
      }
    },

    async updateSeckillList(ids) {
      try {
        // 请求秒杀列表接口
        const params =
          ids !== undefined && ids !== null && ids !== -1 ? { ids } : {};

        // 请求秒杀列表接口
        const res = await ApiDoingsYuandanSeckillList(params);

        if (ids == undefined || ids == null) {
          this.SeckillCommodityList = res.data.list;
          this.seckillList = res.data.time;
          this.isRemind = res.data.isyy == 2 ? true : false;
          this.current_index = this.seckillList.findIndex(
            item => item.currentStarted === true,
          );
        } else {
          this.SeckillCommodityList = res.data.list;
          res.data.time.forEach(newItem => {
            const index = this.seckillList.findIndex(
              item => item.start_time == newItem.start_time,
            );
            if (index !== -1) {
              this.$set(this.seckillList, index, newItem);
            }
          });
          this.isRemind = res.data.isyy == 2 ? true : false;
        }
        // 获取当前秒杀场次
        this.currentSession = this.seckillList.findIndex(
          item => item.is_started === 1,
        );

        // 如果不存在当前场次 不能秒杀 返回
        if (this.currentSession === -1) return;

        // 设置定时器
        this.setupTimer();
      } catch (error) {}
    },

    setupTimer() {
      // 清除已存在的定时器
      if (this.timeClock1) {
        clearInterval(this.timeClock1);
        this.timeClock1 = null;
      }

      // 设置新的定时器
      this.timeClock1 = setInterval(() => {
        this.seckillList[this.currentSession].nowtime += 1;
        if (
          this.seckillList[this.currentSession].end_time -
            this.seckillList[this.currentSession].nowtime <=
          0
        ) {
          clearInterval(this.timeClock1);
          this.timeClock1 = null;
          this.updateSeckillList();
        }
      }, 1000);
    },
    goToTask() {
      this.toPage('250101Activity');
    },
    // 选择套餐
    handleSelectMeal(item) {
      if (!item.buy_status) {
        // 不可购买 buy_status: 0 不可购买 1 可购买
        return;
      }
      if (this.selectedMeal.amount === item.amount) {
        this.selectedMeal = {};
        return;
      }
      this.selectedMeal = item;
    },
    // 获取活动首页数据
    async getIndexData() {
      const res = await ApiDoingsYuandanIndex();
      let { info, vip_list } = res.data;
      this.activity_status = 1;
      this.info = info;
      this.vipList = vip_list;
      this.selectedMeal =
        this.vipList.find(item => item.buy_status === 1) ?? {};

      if (this.timeClock) {
        clearInterval(this.timeClock);
        this.timeClock = null;
      }
      this.timeClock = setInterval(() => {
        this.nowTime = new Date().getTime();
      }, 1000);
    },
    // 点击购买svip
    handleSvipBtn() {
      if ([2, 3, 4].includes(this.activity_status)) {
        this.$toast(this.activity_status_text);
        return false;
      }
      if (!this.userInfo.token) {
        this.$toast('请登录');
        this.login();
        return false;
      }

      if (this.vipList.every(item => item.buy_status === 0)) {
        this.$toast('您已全部开通，每项每人仅可购买一次');
        return false;
      }

      if (!this.selectedMeal.amount) {
        this.$toast('请先选择套餐');
        return false;
      }
      this.payPopupShow = true;
    },
    // 获取支付方式
    async getPayMethod() {
      let res = await ApiGetPaymentMethod({
        orderType: 103,
      });
      this.payList = res.data;
    },

    // 选择支付方式
    choosePayType(selectedPayType) {
      this.selectedPayType = selectedPayType.symbol;
      this.handlePay();
    },

    // 支付逻辑
    handlePay() {
      this.payPopupShow = false;
      if (this.selectedMeal.position_id) {
        // 平台币充值
        const orderParams = {
          isNew: 1,
          money: this.selectedMeal.amount,
          payWay: this.selectedPayType,
          positionId: this.selectedMeal.position_id,
        };
        ApiCreateOrderPtbNew(orderParams).then(async orderRes => {
          await ApiGetPayUrl({
            orderId: orderRes.data.orderId,
            orderType: 102,
            payWay: this.selectedPayType,
            packageName: '',
          }).finally(() => {
            ApiGetOrderStatus({
              order_id: orderRes.data.orderId,
              order_type: 102,
            });
          });

          await this.getIndexData();
        });
      } else {
        // svip充值
        const orderParams = {
          day: this.selectedMeal.day,
          amount: this.selectedMeal.amount,
          rebate_gold: this.selectedMeal.rebate_gold,
          payWay: this.selectedPayType,
          is_cycle: 0,
        };
        ApiCreateOrderSvip(orderParams).then(async orderRes => {
          await ApiGetPayUrl({
            orderId: orderRes.data.orderId,
            orderType: 103,
            payWay: this.selectedPayType,
            packageName: '',
          }).finally(() => {
            ApiGetOrderStatus({
              order_id: orderRes.data.orderId,
              order_type: 103,
            });
          });
          await this.getIndexData();
        });
      }
    },

    async appointmentRemind() {
      if (!this.userInfo.token) {
        this.login();
        return false;
      }
      if (!this.isRemind) {
        await this.doingsYuandanReservation(1);
        BOX_openInNewWindow(
          { name: 'BindWeChat' },
          { url: `https://${envFun()}game.3733.com/#/bind_we_chat` },
        );
      } else {
        await this.doingsYuandanReservation(2);
        await this.updateSeckillList(this.current_index);
      }
    },
    formatTime(timeStamp) {
      timeStamp = Number(timeStamp);
      let day = this.addZero(Math.floor(timeStamp / 3600 / 24));
      let hour = this.addZero(Math.floor(timeStamp / 3600) % 24);
      let minute = this.addZero(Math.floor((timeStamp % 3600) / 60));
      let second = this.addZero((timeStamp % 3600) % 60);
      return {
        day,
        hour,
        minute,
        second,
      };
    },
    addZero(num) {
      num = parseInt(num);
      return num < 10 ? '0' + num : num.toString();
    },
    async onResume() {
      await boxInit();
      await this.SET_USER_INFO(true);
      await this.updateSeckillList();
      await this.getIndexData();
    },
    login() {
      BOX_login();
    },
    fromSecondToHour(secondStemp) {
      if (secondStemp <= 0) {
        if (this.timeClock) {
          clearInterval(this.timeClock);
          this.timeClock = null;
        }
        return `${this.activity_status_text}`;
      }
      let day = Math.floor(secondStemp / (60 * 60 * 24));

      let hour = Math.floor((secondStemp - day * 60 * 60 * 24) / (60 * 60));
      let minute = Math.floor(
        (secondStemp - day * 60 * 60 * 24 - hour * 60 * 60) / 60,
      );
      let second = Math.floor(
        secondStemp - day * 60 * 60 * 24 - hour * 60 * 60 - minute * 60,
      );
      if (hour < 10) {
        hour = '0' + hour;
      }
      if (minute < 10) {
        minute = '0' + minute;
      }
      if (second < 10) {
        second = '0' + second;
      }
      if (Number(day) === 0) {
        return `仅剩余${hour}:${minute}:${second}`;
      }
      return `仅剩余${day}天 ${hour}:${minute}:${second}`;
    },
  },
  computed: {
    isStartedValid() {
      const selectedItem = this.seckillList[this.current_index];
      if (selectedItem) {
        return selectedItem.is_started !== 2;
      }
      return true;
    },
    countdownSeckill() {
      if (this.currentSession === null || this.currentSession === -1) return;
      const { day, hour, minute, second } = this.formatTime(
        this.seckillList[this.currentSession].end_time -
          this.seckillList[this.currentSession].nowtime,
      );
      return hour + ':' + minute + ':' + second;
    },

    activity_status_text() {
      const arr = [
        '活动已开始！',
        '活动不存在！',
        '活动未开始！',
        '活动已结束！',
      ];
      return arr[this.activity_status - 1];
    }, // 活动剩余时间
    activityTimeLeft() {
      return this.fromSecondToHour(
        this.info.end_time - Math.floor(this.nowTime / 1000),
      );
    },
  },
};
</script>

<style lang="less" scoped>
.page-250101-activity {
  font-family: PingFang SC, PingFang SC;
  display: flex;
  flex-direction: column;
  width: 100%;
  overflow: hidden;
  min-height: 100vh;
  background: #ffcfba;
  .back {
    width: 30 * @rem;
    height: 30 * @rem;
    border-radius: 15 * @rem;
    background: rgba(0, 0, 0, 0.3) url(~@/assets/images/nav-bar-back-white.png)
      center center no-repeat;
    background-size: 8 * @rem 14 * @rem;
  }
  .rule-btn {
    box-sizing: border-box;
    position: fixed;
    width: 28 * @rem;
    height: 48 * @rem;
    top: 70 * @rem;
    right: 0;
    z-index: 999;
    background: rgba(133, 0, 0, 0.26);
    font-size: 13 * @rem;
    color: #fff;
    line-height: 18 * @rem;
    border-radius: 12 * @rem 0 0 12 * @rem;
    text-align: center;
    padding: 0 5 * @rem;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .main {
    width: 100%;
    position: relative;
    flex: 1;
    min-height: 0;
    background: url(~@/assets/images/250101/250101_bg1.png) center top no-repeat;
    background-size: 100% 227 * @rem;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    box-sizing: border-box;
    .activity-title-text {
      margin: 49 * @rem auto 0;
      height: 17 * @rem;
      font-size: 14 * @rem;
      color: #ffffff;
      line-height: 17 * @rem;
      text-align: center;
    }
    .activity-title {
      background: url(~@/assets/images/250101/seckill-title.png) center center
        no-repeat;
      background-size: 282 * @rem 34 * @rem;
      width: 282 * @rem;
      height: 68 * @rem;
    }
    .activity-sub-title {
      margin-top: -12 * @rem;
      font-size: 14 * @rem;
      color: rgba(255, 255, 255, 0.9);
      text-align: center;
      line-height: 17 * @rem;
    }
    .activity-time {
      margin-top: 7 * @rem;
      width: 146 * @rem;
      white-space: nowrap;
      height: 23 * @rem;
      line-height: 23 * @rem;
      text-align: center;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 13 * @rem;
      font-size: 11 * @rem;
      color: #ffffff;
    }
    .activity-grab-it {
      display: flex;
      align-items: center;
      justify-content: space-evenly;
      width: 339 * @rem;
      height: 72 * @rem;
      background: rgba(255, 255, 255, 0.3);
      border-radius: 8 * @rem;
      margin-top: 24 * @rem;
      .grab-it-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        flex: 1;
        .time {
          height: 20 * @rem;
          font-weight: 600;
          font-size: 14 * @rem;
          color: #902d17;
          line-height: 20 * @rem;
        }
        .status {
          margin-top: 4 * @rem;
          font-weight: 400;
          font-size: 11 * @rem;
          text-align: center;
          color: #c56639;
          height: 20 * @rem;
          line-height: 20 * @rem;
          padding: 0 6 * @rem;
          .status-item {
            width: 100%;
          }
          &.loading {
            height: 20 * @rem;
            background: rgba(255, 255, 255, 0.5);
            border-radius: 9 * @rem;
            font-weight: 500;
          }
        }
      }
    }
    .grab-it-remind {
      margin-top: 18 * @rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 339 * @rem;
      height: 74 * @rem;
      box-sizing: border-box;
      padding: 16 * @rem 14 * @rem 15 * @rem 18 * @rem;
      background: rgba(255, 255, 255, 0.3);
      border-radius: 8 * @rem;
      .left-remind {
        display: flex;
        align-items: center;
        .icon {
          width: 42 * @rem;
          height: 43 * @rem;
        }
        .remind-content {
          height: 42 * @rem;
          margin-left: 12 * @rem;
          display: flex;
          flex-direction: column;
          justify-content: space-evenly;
          .remind-title {
            display: flex;
            align-items: center;
            .title {
              height: 20 * @rem;
              font-weight: 600;
              font-size: 14 * @rem;
              color: #902d17;
              line-height: 20 * @rem;
            }
            .reservation {
              margin-left: 8 * @rem;
              display: flex;
              align-items: center;
              .icon {
                background: url(~@/assets/images/250101/seckill-icon4.png);
                background-size: 12 * @rem 12 * @rem;
                width: 12 * @rem;
                height: 12 * @rem;
              }
              .text {
                margin-left: 2 * @rem;
                height: 15 * @rem;
                line-height: 15 * @rem;
                font-weight: 600;
                font-size: 12 * @rem;
                color: #ff495c;
              }
            }
          }
          .remind-text {
            height: 15 * @rem;
            font-weight: 400;
            font-size: 12 * @rem;
            color: #c56639;
            line-height: 15 * @rem;
          }
        }
      }

      .remind-btn {
        min-width: 64 * @rem;
        height: 28 * @rem;
        line-height: 28 * @rem;
        text-align: center;
        background: linear-gradient(221deg, #fd9083 0%, #ff495c 100%);
        border-radius: 20 * @rem;
        font-weight: 500;
        font-size: 11 * @rem;
        color: #fff;
        &.cancel {
          background: rgba(255, 207, 186, 1);
          color: #c56639;
        }
      }
    }
    .grab-it-list {
      .grab-it-item {
        margin-top: 14 * @rem;
        width: 339 * @rem;
        height: 108 * @rem;
        box-sizing: border-box;
        padding: 16 * @rem 14 * @rem 10 * @rem;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 8 * @rem;
        .grad-card {
          display: flex;
          align-items: center;
          justify-content: space-between;
          .left-remind {
            display: flex;
            align-items: center;
            .icon {
              width: 42 * @rem;
              height: 43 * @rem;
            }
            .remind-content {
              height: 42 * @rem;
              margin-left: 12 * @rem;
              display: flex;
              flex-direction: column;
              justify-content: space-evenly;
              .remind-title {
                display: flex;
                align-items: center;
                .title {
                  height: 20 * @rem;
                  font-weight: 600;
                  font-size: 14 * @rem;
                  color: #902d17;
                  line-height: 20 * @rem;
                }
              }
              .remind-text {
                height: 15 * @rem;
                font-weight: 400;
                font-size: 12 * @rem;
                color: #c56639;
                line-height: 15 * @rem;
              }
            }
          }

          .remind-btn {
            min-width: 64 * @rem;
            height: 28 * @rem;
            line-height: 28 * @rem;
            text-align: center;
            background: rgba(255, 255, 255, 0.5);
            border-radius: 20 * @rem;
            font-weight: 500;
            font-size: 11 * @rem;
            color: #c56639;
            &.immediately {
              background: linear-gradient(221deg, #fd9083 0%, #ff495c 100%);
              color: #fff;
            }
            &.finished {
              background: rgba(255, 207, 186, 1);
              color: #fff;
            }
          }
        }
        .dashed-line {
          margin: 16 * @rem 0 10 * @rem 0;
          width: 100%;
          height: 1 * @rem;
          background-color: #ffc2a7;
          mask-image: linear-gradient(90deg, #ffc2a7 50%, transparent 0%);
          mask-size: 8 * @rem 100%;
        }
        .grad-describe {
          display: flex;
          align-items: center;
          justify-content: space-between;
          > div {
            height: 14 * @rem;
            font-weight: 400;
            font-size: 11 * @rem;
            color: #c56639;
            line-height: 14 * @rem;
          }
        }
      }
    }
  }
  .svip-container {
    box-sizing: border-box;
    position: relative;
    width: 351 * @rem;
    height: 293 * @rem;
    margin: 26 * @rem auto 38 * @rem;
    &::before {
      content: '';
      position: relative;
      box-sizing: border-box;
      display: block;
      width: 351 * @rem;
      height: 36 * @rem;
      overflow: hidden;
      background: url(~@/assets/images/250101/activity-bg-top.png) center center
        no-repeat;
      background-size: 351 * @rem 36 * @rem;
      z-index: 1;
    }
    &::after {
      content: '';
      position: relative;
      box-sizing: border-box;
      display: block;
      width: 351 * @rem;
      height: 36 * @rem;
      overflow: hidden;
      background: url(~@/assets/images/250101/activity-bg-bottom.png) center top
        no-repeat;
      background-size: 351 * @rem 36 * @rem;
      z-index: 1;
    }
    .svip-card {
      box-sizing: border-box;
      background: rgba(255, 255, 255, 0.3);
      width: 351 * @rem;
      padding: 0 6 * @rem;
      position: relative;
      z-index: 99;
      .svip-border {
        border-left: 1 * @rem solid #ffcfba;
        border-right: 1 * @rem solid #ffcfba;
        position: relative;
        z-index: 2;
        .svip-content {
          position: relative;
          padding-top: 1 * @rem;
          padding-bottom: 1 * @rem;
          .svip-title {
            width: 167 * @rem;
            height: 24 * @rem;
            background: url(~@/assets/images/250101/svip-title.png) center top
              no-repeat;
            background-size: 167 * @rem 24 * @rem;
            margin: 0 auto;
            margin-top: -16 * @rem;
          }
          .svip-desc {
            width: 304 * @rem;
            height: 15 * @rem;
            margin: 6 * @rem auto 0;
            line-height: 15 * @rem;
            font-size: 12 * @rem;
            color: rgba(197, 102, 57, 1);
            text-align: center;
            position: relative;
            &::before {
              content: '';
              width: 84 * @rem;
              height: 10 * @rem;
              display: block;
              background: url(~@/assets/images/250101/svip-decoration.png)
                center center no-repeat;
              background-size: 84 * @rem 10 * @rem;
              position: absolute;
              left: 0;
              top: 50%;
              transform: translateY(-50%);
            }
            &::after {
              content: '';
              width: 84 * @rem;
              height: 10 * @rem;
              display: block;
              background: url(~@/assets/images/250101/svip-decoration.png)
                center center no-repeat;
              background-size: 84 * @rem 10 * @rem;
              position: absolute;
              right: 0;
              top: 50%;
              transform: translateY(-50%) rotateY(180deg);
            }
          }
          .meal-list {
            display: flex;
            align-items: flex-start;
            overflow-x: auto;
            scroll-snap-type: x mandatory;
            padding-right: 10 * @rem;
            padding-left: 10 * @rem;
            margin-top: 16 * @rem;
            &::-webkit-scrollbar {
              width: 0;
              display: none;
            }
            .meal-item {
              position: relative;
              box-sizing: border-box;
              flex-shrink: 0;
              width: 119 * @rem;
              height: 107 * @rem;
              border: 1 * @rem solid rgba(255, 255, 255, 0.2);
              background: rgba(255, 246, 245, 0.2);
              border-radius: 8 * @rem;
              scroll-snap-align: center;
              scroll-snap-stop: always;
              padding-top: 12 * @rem;
              padding-bottom: 4 * @rem;
              transition: all 0.3s;
              display: flex;
              flex-direction: column;
              justify-content: space-between;
              &:not(:last-of-type) {
                margin-right: 10 * @rem;
              }

              .tag {
                width: 36 * @rem;
                height: 17 * @rem;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 9 * @rem;
                color: rgba(197, 102, 57, 1);
                font-weight: bold;
                border-radius: 0 8 * @rem 0 8 * @rem;
                background-color: rgba(255, 207, 186, 1);
                position: absolute;
                right: 0;
                top: 0;
              }

              .title {
                font-size: 14 * @rem;
                font-weight: bold;
                color: rgba(197, 102, 57, 0.8);
                padding-left: 15 * @rem;
                line-height: 20 * @rem;
              }
              .price-line {
                display: flex;
                align-items: flex-end;
                padding-left: 15 * @rem;
                margin-top: 10 * @rem;
                .price {
                  font-size: 22 * @rem;
                  line-height: 22 * @rem;
                  font-weight: bold;
                  color: rgba(197, 102, 57, 0.8);
                  span {
                    font-size: 14 * @rem;
                    font-weight: bold;
                  }
                }
                .old-price {
                  font-size: 12 * @rem;
                  line-height: 17 * @rem;
                  color: rgba(183, 147, 63, 0.5);
                  margin-left: 2 * @rem;
                  text-decoration: line-through;
                  &.no-through {
                    text-decoration: none;
                  }
                }
              }
              .desc {
                height: 23 * @rem;
                width: 111 * @rem;
                border-radius: 8 * @rem;
                font-size: 11 * @rem;
                color: rgba(197, 102, 57, 0.8);
                background: rgba(255, 255, 255, 0.25);
                display: flex;
                align-items: center;
                justify-content: center;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                margin: 0 auto;
                flex-shrink: 0;
              }
              &.active {
                border: 1 * @rem solid rgba(255, 255, 255, 0.3);
                background: rgba(255, 246, 245, 0.5);
                .title {
                  color: #902d17;
                }
                .price-line {
                  .price {
                    color: #902d17;
                  }
                  .old-price {
                    color: rgba(144, 45, 23, 0.5);
                  }
                }
                .desc {
                  color: #9e4f0f;
                  background: #ffddcf;
                }
              }
            }
          }
          .svip-btn {
            width: 210 * @rem;
            height: 40 * @rem;
            background: linear-gradient(221deg, #fd9083 0%, #ff495c 100%);
            border-radius: 20 * @rem;
            margin: 24 * @rem auto 0;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 17 * @rem;
            color: #ffffff;
            font-weight: bold;
          }
          .time-left {
            font-size: 11 * @rem;
            color: rgba(144, 45, 23, 1);
            margin-top: 8 * @rem;
            line-height: 14 * @rem;
            text-align: center;
            margin-bottom: -10 * @rem;
          }
        }
      }
    }
  }
  .seckill-dialog {
    width: 300 * @rem;
    background: transparent;
    overflow: visible;
    .dialog-content {
      box-sizing: border-box;
      position: relative;
      border-radius: 16 * @rem;
      z-index: 2;
      padding: 28 * @rem 20 * @rem 0;
      width: 300 * @rem;
      height: 227 * @rem;
      background-color: #fff;
      text-align: center;
      display: flex;
      flex-direction: column;
      align-items: center;
      .title {
        white-space: nowrap;
        font-weight: bold;
        height: 20 * @rem;
        font-weight: 600;
        font-size: 16 * @rem;
        color: #333333;
        line-height: 20 * @rem;
        text-align: center;
      }

      .msg {
        height: 42 * @rem;
        font-weight: 400;
        font-size: 15 * @rem;
        color: #777777;
        line-height: 18 * @rem;
        text-align: center;
        margin: 20 * @rem 0 0 0;
      }
      .btn-bottom {
        position: absolute;
        bottom: 16 * @rem;
        .btn {
          width: 240 * @rem;
          height: 40 * @rem;
          line-height: 40 * @rem;
          background: linear-gradient(235deg, #6ddc8c 0%, #21b98a 100%);
          border-radius: 29 * @rem;
          font-weight: 500;
          font-size: 15 * @rem;
          color: #ffffff;
          text-align: center;
        }
        .text {
          margin-top: 14 * @rem;
          span {
            display: inline-block;
            width: 100%;
            font-weight: 400;
            font-size: 15 * @rem;
            color: #777777;
          }
        }
      }
    }
  }
  .title {
    height: 26 * @rem;
    font-weight: 600;
    font-size: 18 * @rem;
    color: #902d17;
    line-height: 26 * @rem;
  }
  @keyframes ripple {
    0% {
      box-shadow: 0 0 0 0px #ffffff;
      opacity: 0.3;
    }
    100% {
      box-shadow: 0 0 0 20px #ffffff;
      opacity: 0;
    }
  }
}
</style>
