import Vue from 'vue';
import LoginPopup from './index.vue';
import router from '@/router';
import store from '@/store';

const LoginPopupConstructor = Vue.extend(LoginPopup);

/**
 * 登录弹窗构造器
 * @param {Object} options 配置选项
 * @param {string} options.title 弹窗标题，默认：'登录提示'
 * @param {string} options.desc 描述文本
 * @param {string} options.content 内容文本，默认：'请先登录后再进行操作'
 * @param {string} options.tips 提示文本
 * @param {string} options.icon 图标地址
 * @param {string} options.cancelText 取消按钮文本，默认：'取消'
 * @param {string} options.loginText 登录按钮文本，默认：'登录'
 * @param {string} options.cancelBgColor 取消按钮背景色
 * @param {string} options.cancelTextColor 取消按钮文字颜色
 * @param {string} options.loginBgColor 登录按钮背景色
 * @param {string} options.loginTextColor 登录按钮文字颜色
 * @param {boolean} options.showCancel 是否显示取消按钮，默认：true
 * @param {boolean} options.showLogin 是否显示登录按钮，默认：true
 * @param {boolean} options.showClose 是否显示关闭按钮，默认：false
 * @param {boolean} options.closeOnClickOverlay 点击遮罩是否关闭，默认：false
 * @param {boolean} options.lockScroll 是否锁定滚动，默认：true
 * @param {Function} options.onCancel 取消回调函数
 * @param {Function} options.onLogin 登录回调函数
 * @param {Function} options.onClose 关闭回调函数
 * @returns {Promise} 返回Promise对象
 */
function useLoginPopup(options = {}) {
  return new Promise((resolve, reject) => {
    const popup = new LoginPopupConstructor({
      router,
      store,
    });

    popup.$mount(document.createElement('div'));
    document.body.appendChild(popup.$el);

    // 监听动画结束事件，销毁组件
    popup.$el.addEventListener(
      'animationend',
      () => {
        if (popup.show === false) {
          popup.$destroy();
          if (popup.$el.parentNode) {
            popup.$el.parentNode.removeChild(popup.$el);
          }
        }
      },
      false,
    );

    // 设置配置项
    popup.show = true;
    popup.title = options.title || '请登录账号';
    popup.cancelText = options.cancelText || '取消';
    popup.confirmText = options.loginText || '去登录';

    // 绑定事件处理函数
    if (options.onCancel) {
      popup.onCancel = () => {
        const result = options.onCancel();
        popup.show = false;
        resolve({ action: 'cancel', result });
      };
    } else {
      popup.onCancel = () => {
        popup.show = false;
        resolve({ action: 'cancel' });
      };
    }

    if (options.onLogin) {
      popup.onLogin = () => {
        const result = options.onLogin();
        popup.show = false;
        resolve({ action: 'login', result });
      };
    } else {
      popup.onLogin = () => {
        popup.show = false;
        resolve({ action: 'login' });
      };
    }

    if (options.onClose) {
      popup.onClose = () => {
        const result = options.onClose();
        popup.show = false;
        resolve({ action: 'close', result });
      };
    } else {
      popup.onClose = () => {
        popup.show = false;
        resolve({ action: 'close' });
      };
    }

    // 如果没有提供任何回调，设置默认的reject处理
    const originalCancel = popup.onCancel;
    const originalLogin = popup.onLogin;
    const originalClose = popup.onClose;

    // 超时处理（可选）
    if (options.timeout) {
      setTimeout(() => {
        if (popup.show) {
          popup.show = false;
          reject(new Error('Login popup timeout'));
        }
      }, options.timeout);
    }
  });
}

// 快捷方法：显示登录弹窗
useLoginPopup.show = (options = {}) => {
  return useLoginPopup(options);
};

// 快捷方法：显示简单的登录提示
useLoginPopup.alert = (content, title = '登录提示') => {
  return useLoginPopup({
    title,
    content,
    showCancel: false,
  });
};

// 快捷方法：显示确认登录弹窗
useLoginPopup.confirm = (content, title = '登录确认') => {
  return useLoginPopup({
    title,
    content,
    showCancel: true,
    showLogin: true,
  });
};

export default useLoginPopup;
