<template>
  <div class="activity0501">
    <nav-bar-2
      ref="topNavBar"
      bgStyle="transparent-white"
      class="top-nav-bar"
      :azShow="true"
      :placeholder="false"
    ></nav-bar-2>
    <div @click="toPage('0501ActivityRule')" class="rule btn"></div>
    <div @click="openCoordinateList" class="code btn"></div>
    <div @click="getNameList" class="rank btn"></div>
    <div @click="getRecordList" class="exchange-record btn"></div>
    <div class="active-time">04月29日00:00 - 05月3日23:59</div>
    <div class="user-container">
      <div v-if="userInfo.token" class="user">
        <UserAvatar class="avatar" />
        <div class="nickname">{{ userInfo.nickname }}</div>
      </div>
      <div @click="login" v-else class="user no-login btn">
        <div class="text">未登录</div>
      </div>
    </div>
    <div class="point-container">
      <div @click="showTips" class="question"></div>
      <div class="point">夺宝币：{{ point }}</div>
    </div>
    <div @click="handleStartButton" class="start-button"></div>
    <div class="explain-text">
      奖品说明：活动期间，玩家可消耗夺宝币进行夺宝，获取抽奖码，在所有抽奖码里将随机产生一个获奖者。有机会获得30天SVIP、1000平台币、8888金币、14天SVIP、1888金币。详情可在活动规则里查看。
    </div>
    <div class="list-container">
      <div
        v-for="(item, index) in exchange_list"
        :key="index"
        :class="`list${index + 1}`"
        class="list"
      >
        <div class="list-header"></div>
        <div class="list-body">
          <div v-for="item2 in item.list" :key="item2.type_id" class="item">
            <div class="item-left">
              <div class="big-text">{{ item2.title }}</div>
              <div class="small-text">{{ item2.desc }}</div>
            </div>
            <div
              :class="{ empty: item2.status == 0, already: item2.status == 2 }"
              @click="handleExchangeListTake(item2)"
              class="item-right btn"
            ></div>
          </div>
        </div>
      </div>
      <div class="bottom-container"></div>
    </div>
    <div class="bg1"></div>
    <div class="bg2"></div>
    <!-- 兑奖记录 -->
    <van-popup
      v-model="record_list_popup"
      :lock-scroll="false"
      :close-on-click-overlay="false"
      class="popup record-list-popup"
    >
      <div class="popup-close" @click="closeRecordPopup"></div>
      <div class="title">兑奖记录</div>
      <div v-if="record_list.length > 0" class="list">
        <div v-for="(item, index) in record_list" :key="index" class="item">
          <div class="left">{{ item.title }}</div>
          <div v-html="item.desc" class="right"></div>
        </div>
      </div>
      <div v-else class="empty">暂无名单</div>
    </van-popup>
    <!-- 夺宝类型 -->
    <van-popup
      v-model="db_popup"
      :lock-scroll="false"
      :close-on-click-overlay="false"
      class="popup2 db-popup"
    >
      <div class="bg"></div>
      <div class="popup-close" @click="db_popup = false"></div>
      <div class="container">
        <div class="db-list">
          <div
            v-for="item in db_list"
            :key="item.db_id"
            :class="{ on: db_current.db_id == item.db_id }"
            @click="handleDbCount(3, item)"
            class="item"
          >
            {{ item.title }}
          </div>
          <div class="item space"></div>
        </div>
        <div class="big-bold-text">购买数量</div>
        <div class="content">
          <div class="small-bold-text">合计：{{ db_sum }}夺宝币</div>
          <div class="right">
            <div
              :class="{ empty: db_count == 0 }"
              @click="handleDbCount(1)"
              class="circle reduce btn"
            >
              -
            </div>
            <div class="count">{{ db_count }}</div>
            <div
              :class="{ empty: point < db_current.need_num }"
              @click="handleDbCount(2)"
              class="circle add btn"
            >
              +
            </div>
          </div>
        </div>
        <div class="small-text">剩余夺宝币：{{ point - db_sum }}</div>
        <div
          :class="{ empty: db_count == 0 }"
          @click="handleBuyCode"
          class="bottom-button"
        ></div>
      </div>
    </van-popup>
    <!-- 获奖名单 -->
    <van-popup
      v-model="name_list_popup"
      :lock-scroll="false"
      :close-on-click-overlay="false"
      class="popup2 name-list-popup"
    >
      <div class="bg"></div>
      <div class="popup-close" @click="name_list_popup = false"></div>
      <div class="container">
        <div class="item">
          <div class="th td">日期</div>
          <div class="th td">获奖用户</div>
          <div class="th td">幸运号码</div>
          <div class="th td">奖品</div>
        </div>
        <div v-if="name_list.length > 0" class="list">
          <div v-for="(item, index) in name_list" :key="index" class="item">
            <div class="td">{{ item.buy_time }}</div>
            <div class="td">{{ item.user.username }}</div>
            <div class="td">{{ item.code }}</div>
            <div class="td">{{ item.db_title }}</div>
          </div>
        </div>
        <div v-else class="empty">暂无兑奖记录</div>
        <div class="text">
          注：早上一期的开奖时间为当日12点后，下午一期的开奖时间为当日0点后，奖励将自动发放给获奖账号
        </div>
      </div>
    </van-popup>
    <!-- 夺宝坐标 -->
    <van-popup
      v-model="db_coordinate_popup"
      :lock-scroll="false"
      :close-on-click-overlay="false"
      class="popup2 db-coordinate-popup"
    >
      <div class="bg"></div>
      <div class="popup-close" @click="db_coordinate_popup = false"></div>
      <div class="container">
        <div class="tab">
          <div
            v-for="item in db_list"
            :key="item.db_id"
            :class="{ current: db_current_tab == item.db_id }"
            @click="handleDbCurrentTab(item.db_id)"
            class="tab-item"
          >
            {{ item.title }}
          </div>
        </div>
        <div v-if="db_current_list.length > 0" class="list">
          <div
            v-for="(item, index) in db_current_list"
            :key="index"
            class="item"
          >
            {{ item.code }}
          </div>
        </div>
        <div v-else class="empty">暂无夺宝记录</div>
        <div @click="db_coordinate_popup = false" class="bottom-button"></div>
      </div>
    </van-popup>
  </div>
</template>
<script>
import { platform, boxInit } from '@/utils/box.uni.js';
import UserAvatar from '@/components/user-avatar';
import { BOX_login } from '@/utils/box.uni.js';
import {
  Api0501Index,
  Api0501TakeExchange,
  Api0501ExchangeRecord,
  Api0501MyCode,
  Api0501BuyCode,
  Api0501NameList,
} from '@/api/views/0501';
import { mapGetters } from 'vuex';

export default {
  data() {
    return {
      activity_status: 3, //1活动已开始 2 活动不存在 3活动未开始 4活动已结束
      svip_popup: false, //svip引导弹窗
      record_list_popup: false, //记录弹窗
      name_list_popup: false, //获奖弹窗
      explain_popup: false, //说明弹窗
      record_list: [], //记录列表
      name_list: [], //名单列表
      point: 0, //当前积分
      finished: false, //ajax防卡
      exchange_list: [], //兑换列表
      db_list: [], // 夺宝列表
      db_popup: false, //夺宝弹窗
      db_current: {}, //当前选中夺宝
      db_count: 0, //购买数量
      db_sum: 0, //合计数量
      db_coordinate_popup: false, //夺宝坐标
      db_current_tab: 1, //夺宝坐标当前tab
      db_current_list: [], //夺宝坐标当前list
    };
  },
  computed: {
    active_status_text() {
      const arr = ['活动已开始', '活动不存在', '活动未开始', '活动已结束'];
      return arr[this.activity_status - 1];
    },
    ...mapGetters({
      userInfo: 'user/userInfo',
    }),
  },
  async mounted() {
    // 官包触发页面刷新（将onResume挂载在window上）
    if (platform == 'android') {
      window.onResume = this.onResume;
    }
    await this.getActivityInfo();
  },
  methods: {
    async onResume() {
      await boxInit();
      await this.getActivityInfo();
      this.SET_USER_INFO(true);
    },
    async getActivityInfo() {
      const res = await Api0501Index();
      this.activity_status = res.data.activity_status;
      this.exchange_list = res.data.task_list;
      this.db_list = res.data.db_list;
      this.db_current = res.data.db_list[0];
      this.db_current_tab = res.data.db_list[0].db_id;
      this.point = res.data.remain_num;
      // this.today_lottery_status = res.data.lottery_status;
    },
    login() {
      BOX_login();
    },
    showTips() {
      this.$toast(
        '活动期间，玩家使用现金充值游戏可获得等额夺宝币奖励（仅限游戏内使用微信/支付宝充值）',
      );
    },
    // 关闭记录弹窗
    closeRecordPopup() {
      this.record_list_popup = false;
    },
    // 处理购买夺宝码
    handleBuyCode() {
      if (this.db_count == 0) {
        return false;
      }
      this.$dialog
        .confirm({
          message: `是否消耗${this.db_sum}夺宝币进行夺宝抽奖`,
        })
        .then(async () => {
          // on confirm
          try {
            const res = await Api0501BuyCode({
              db_id: this.db_current.db_id,
              num: this.db_count,
            });
            this.db_popup = false;
            this.$toast(res.data);
            await this.getActivityInfo();
          } finally {
            this.db_current = this.db_list[0];
            this.db_count = 0;
            this.db_sum = 0;
          }
        })
        .catch(() => {
          // on cancel
        });
    },
    // 处理开始按钮
    handleStartButton() {
      if (!this.userInfo.token) {
        this.login();
        return false;
      }
      if (this.activity_status != 1) {
        this.$toast(this.active_status_text);
        return false;
      }
      this.db_popup = true;
    },
    // 获取记录列表
    async getRecordList() {
      if (!this.userInfo.token) {
        this.login();
        return false;
      }
      if (this.activity_status == 2 || this.activity_status == 3) {
        this.$toast(this.active_status_text);
        return false;
      }
      this.$toast({
        type: 'loading',
        duration: 0,
        message: '拼命加载中...',
        forbidClick: true,
      });
      try {
        const res = await Api0501ExchangeRecord();
        this.record_list = res.data.list;
        this.record_list_popup = true;
      } finally {
        this.$toast.clear();
      }
    },
    // 处理夺宝计数
    handleDbCount(type, item = {}) {
      if (type == 1) {
        if (this.db_count <= 0) {
          return false;
        }
        this.db_count--;
      } else if (type == 2) {
        if (this.point - this.db_sum < this.db_current.need_num) {
          this.$dialog.alert({
            message: `当前夺宝币不足，请前往游戏内充值获取夺宝币`,
          });
          return false;
        }
        this.db_count++;
      } else if (type == 3) {
        this.db_current = item;
        this.db_count = 0;
      }
      this.db_sum = this.db_count * this.db_current.need_num;
    },
    // 改变夺宝当前tab
    async handleDbCurrentTab(id) {
      await this.getCoordinateList(id);
      this.db_current_tab = id;
    },
    // 获取获奖名单
    async getNameList() {
      if (!this.userInfo.token) {
        this.login();
        return false;
      }
      if (this.activity_status == 2 || this.activity_status == 3) {
        this.$toast(this.active_status_text);
        return false;
      }
      this.$toast({
        type: 'loading',
        duration: 0,
        message: '拼命加载中...',
        forbidClick: true,
      });
      try {
        const res = await Api0501NameList();
        this.name_list = res.data.list;
        this.name_list_popup = true;
      } finally {
        this.$toast.clear();
      }
    },
    // 打开坐标列表
    async openCoordinateList() {
      if (!this.userInfo.token) {
        this.login();
        return false;
      }
      if (this.activity_status == 2 || this.activity_status == 3) {
        this.$toast(this.active_status_text);
        return false;
      }
      await this.getCoordinateList(this.db_current_tab);
      this.db_coordinate_popup = true;
    },
    // 获取坐标列表
    async getCoordinateList(id) {
      this.$toast({
        type: 'loading',
        duration: 0,
        message: '拼命加载中...',
        forbidClick: true,
      });
      try {
        const res = await Api0501MyCode({
          db_id: id,
        });
        this.db_current_list.splice(0, this.db_current_list.length);
        this.db_current_list.push(...res.data.list);
      } finally {
        this.$toast.clear();
      }
    },
    // 兑换任务奖励
    async handleExchangeListTake(item) {
      if (!this.userInfo.token) {
        this.login();
        return false;
      }
      if (this.activity_status != 1) {
        this.$toast(this.active_status_text);
        return false;
      }
      if (item.status == 0) {
        this.$toast('您未达到领取条件哦~');
        return false;
      }
      if (item.status == 2) {
        this.$toast('您已经领取过此奖励啦~');
        return false;
      }
      const res = await Api0501TakeExchange({ type_id: item.type_id });
      await this.getActivityInfo();
    },
  },
  components: {
    UserAvatar,
  },
};
</script>
<style lang="less" scoped>
.activity0501 {
  position: relative;
  min-height: 150vh;
  .image-bg('~@/assets/images/0501/51_bg10.png');
  background-color: RGBA(38, 27, 40, 1);
  .top-nav-bar {
    position: relative;
    z-index: 5;
  }
  .user-container {
    position: absolute;
    top: calc(9 * @rem + constant(safe-area-inset-top));
    top: calc(9 * @rem + env(safe-area-inset-top));
    left: 70 * @rem;
    z-index: 6;
    width: 115 * @rem;
    height: 31 * @rem;
    .image-bg('~@/assets/images/0501/51_bg13.png');
    font-size: 14 * @rem;
    font-weight: bold;
    color: #ffffff;
    .user {
      display: flex;
      align-items: center;
      .avatar {
        flex: 0 0 30 * @rem;
        width: 30 * @rem;
        height: 30 * @rem;
        border-radius: 50%;
        // background: #000;
      }
      .nickname {
        flex: 1;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        margin-left: 8 * @rem;
      }
    }
    .no-login {
      .text {
        text-indent: 3em;
        line-height: 31 * @rem;
      }
    }
  }
  .rule {
    position: absolute;
    top: calc(75 * @rem + constant(safe-area-inset-top));
    top: calc(75 * @rem + env(safe-area-inset-top));
    right: 0 * @rem;
    width: 60 * @rem;
    height: 25 * @rem;
    .image-bg('~@/assets/images/0501/51_button13.png');
  }
  .exchange-record {
    position: absolute;
    top: 406 * @rem;
    right: 9 * @rem;
    z-index: 1;
    width: 60 * @rem;
    height: 67 * @rem;
    .image-bg('~@/assets/images/0501/51_button11.png');
  }
  .rank {
    position: absolute;
    top: 313 * @rem;
    right: 9 * @rem;
    z-index: 1;
    width: 60 * @rem;
    height: 62 * @rem;
    .image-bg('~@/assets/images/0501/51_button15.png');
  }
  .code {
    position: absolute;
    top: 216 * @rem;
    right: 9 * @rem;
    z-index: 1;
    width: 60 * @rem;
    height: 65 * @rem;
    .image-bg('~@/assets/images/0501/51_button12.png');
  }
  .active-time {
    position: absolute;
    top: 205 * @rem;
    left: 50%;
    transform: translate(-50%, 0);
    width: 205 * @rem;
    height: 30 * @rem;
    font-weight: 600;
    color: #ffffff;
    background: rgba(38, 20, 20, 0.7);
    border: 1px solid rgba(166, 131, 131, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 20px;
  }
  .explain-text {
    position: absolute;
    top: 694 * @rem;
    left: 0;
    z-index: 1;
    margin: 0 22 * @rem 15 * @rem;
    font-size: 10 * @rem;
    line-height: 15 * @rem;
    color: #ffffff;
  }
  .point-container {
    position: absolute;
    top: calc(8 * @rem + constant(safe-area-inset-top));
    top: calc(8 * @rem + env(safe-area-inset-top));
    right: 0;
    z-index: 6;
    display: flex;
    align-items: center;
    .point {
      width: 123 * @rem;
      height: 32 * @rem;
      .image-bg('~@/assets/images/0501/51_bg14.png');
      white-space: nowrap;
      font-size: 14 * @rem;
      text-align: left;
      box-sizing: border-box;
      padding-left: 10 * @rem;
      line-height: 34 * @rem;
      font-weight: bold;
      color: #ffffff;
    }
    .question {
      width: 21 * @rem;
      height: 21 * @rem;
      margin-right: 10 * @rem;
      .image-bg('~@/assets/images/0501/51_button14.png');
    }
  }
  .start-button {
    position: absolute;
    top: 593 * @rem;
    left: 50%;
    transform: translate(-50%, 0);
    z-index: 1;
    width: 204 * @rem;
    height: 69 * @rem;
    .image-bg('~@/assets/images/0501/51_button4.png');
    &.empty {
      .image-bg('~@/assets/images/0501/51_button3.png');
    }
    &.already {
      // .image-bg("~@/assets/images/0501/51_button13.png");
    }
  }
  .list-container {
    position: absolute;
    width: 100%;
    left: 0;
    top: 748 * @rem;
    z-index: 2;
    padding-top: 21 * @rem;
    .image-bg('~@/assets/images/0501/51_bg1.png');
    background-color: rgba(124, 52, 63, 1);
    overflow: hidden;
    .bottom-container {
      border-top: 7 * @rem solid rgba(212, 142, 85, 1);
      height: 11 * @rem;
      border-bottom: 13 * @rem solid rgba(30, 23, 34, 1);
    }
    .list {
      width: 100%;
      background: rgba(241, 204, 160, 1);
      .list-header {
        height: 38 * @rem;
        // margin: 0 12*@rem;
        box-sizing: border-box;
      }
      .list-body {
        background: #f0dcc4;
        overflow: hidden;
        .item {
          display: flex;
          height: 80 * @rem;
          justify-content: space-between;
          align-items: center;
          padding: 0 21 * @rem;
          .image-bg('~@/assets/images/0501/51_bg2.png');
          background-repeat: repeat;
          .item-left {
            .big-text {
              font-size: 15 * @rem;
              color: #7c343f;
              font-weight: 600;
              line-height: 19 * @rem;
            }
            .small-text {
              font-size: 13 * @rem;
              margin-top: 5 * @rem;
              line-height: 15 * @rem;
              color: #a16b48;
            }
          }
          .item-right {
            width: 68 * @rem;
            height: 29 * @rem;
            .image-bg('~@/assets/images/0501/51_button5.png');
            &.already {
              .image-bg('~@/assets/images/0501/51_button6.png');
            }
            &.empty {
              .image-bg('~@/assets/images/0501/51_button9.png');
            }
          }
        }
      }
      &.list1 {
        .list-header {
          .image-bg('~@/assets/images/0501/51_img1.png');
          background-color: RGBA(241, 212, 178, 1);
        }
      }
      &.list2 {
        .list-header {
          .image-bg('~@/assets/images/0501/51_img2.png');
        }
      }
      &.list3 {
        padding-bottom: 13 * @rem;
        .list-header {
          .image-bg('~@/assets/images/0501/51_img3.png');
        }
      }
    }
  }
  .bg1 {
    position: absolute;
    top: 437 * @rem;
    left: 0;
    z-index: 0;
    width: 100%;
    height: 300 * @rem;
    .image-bg('~@/assets/images/0501/51_bg11.png');
    // background-color: #000;
  }
  .popup {
    width: 290 * @rem;
    padding: 18 * @rem;
    border-radius: 20 * @rem;
    overflow: hidden;
    background: rgba(255, 234, 209, 1);
    box-shadow: 0 * @rem 2 * @rem 15 * @rem 0 * @rem rgba(248, 0, 0, 0.1);
    border: 2 * @rem solid rgba(170, 104, 67, 1);
    .text {
      font-size: 14 * @rem;
      color: rgba(168, 98, 69, 1);
    }
    .popup-close {
      width: 33 * @rem;
      height: 27 * @rem;
      background: rgba(170, 104, 67, 1) url(~@/assets/images/popup-close.png)
        center center no-repeat;
      background-size: 22 * @rem 22 * @rem;
      position: absolute;
      right: -1 * @rem;
      top: -1 * @rem;
      border-radius: 0 12 * @rem 0 12 * @rem;
    }
    .title {
      font-size: 16 * @rem;
      font-family: PingFang SC-Semibold, PingFang SC;
      font-weight: 600;
      color: rgba(124, 52, 63, 1);
      line-height: 20 * @rem;
      text-align: center;
      margin-bottom: 30 * @rem;
    }
    &.message-popup {
      .text {
        width: 254 * @rem;
        margin: 0 auto;
        padding: 20 * @rem 0;
        font-size: 14 * @rem;
        font-family: PingFang SC-Semibold, PingFang SC;
        font-weight: 600;
        color: #7b74aa;
        line-height: 18 * @rem;
        text-align: center;
      }
    }
  }
  .record-list-popup {
    padding: 18 * @rem 0;
    height: 245 * @rem;
    .list {
      height: 210 * @rem;
      overflow-y: scroll;
      padding: 0 18 * @rem;
      .item {
        display: flex;
        justify-content: space-between;
        height: 30 * @rem;
        align-items: center;
        font-size: 14 * @rem;
        color: #7c343f;
        .left {
          flex: 0 0 90 * @rem;
          white-space: nowrap;
        }
        .right {
          flex: 1;
          text-align: right;
        }
        /deep/ span {
          color: #f8582e;
        }
      }
    }
    .empty {
      width: 100%;
      height: 210 * @rem;
      display: flex;
      justify-content: center;
      align-items: center;
      color: rgba(124, 52, 63, 1);
    }
  }
  .popup2 {
    width: 351 * @rem;
    height: 200 * @rem;
    background: transparent;
    .bg {
      position: absolute;
      top: 103 * @rem;
      left: 0;
      width: 100%;
      background-image: url('~@/assets/images/0501/51_bg3.png'),
        url('~@/assets/images/0501/51_bg2.png');
      background-position: bottom center, center center;
      background-repeat: no-repeat, repeat;
      background-size: 351 * @rem 80 * @rem, 351 * @rem 30 * @rem;
      &::before {
        position: absolute;
        top: -102 * @rem;
        left: 0;
        z-index: 9999;
        content: '';
        width: 351 * @rem;
        height: 103 * @rem;
      }
    }
    .popup-close {
      position: absolute;
      top: 0;
      right: 0;
      z-index: 99999;
      width: 29 * @rem;
      height: 29 * @rem;
      .image-bg('~@/assets/images/0501/51_button10.png');
    }
    overflow: unset;
    .container {
      width: 351 * @rem;
    }
    &.name-list-popup {
      height: 491 * @rem;
      .bg {
        height: 388 * @rem;
        &::before {
          .image-bg('~@/assets/images/0501/51_bg4.png');
        }
      }
      .container {
        position: absolute;
        top: 60 * @rem;
        left: 0;
        padding: 0 30 * @rem;
        z-index: 9999;
        width: 100%;
        box-sizing: border-box;
        .list {
          height: 280 * @rem;
          overflow-y: scroll;
        }
        .empty {
          display: flex;
          justify-content: center;
          align-items: center;
          height: 280 * @rem;
          color: #a86245;
          font-size: 16 * @rem;
          font-weight: 600;
        }
        .item {
          display: flex;
          .td {
            flex: 0 0 25%;
            height: 40 * @rem;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 12 * @rem;
            color: #a86245;
          }
          .th {
            height: 50 * @rem;
            display: flex;
            justify-content: center;
            align-items: center;
            font-weight: 600;
            font-weight: 600;
            color: #7c343f;
            font-size: 16 * @rem;
          }
        }
        .text {
          margin-top: 12 * @rem;
          font-size: 11 * @rem;
          color: #a86245;
          line-height: 17 * @rem;
        }
      }
    }
    &.db-popup {
      height: 412 * @rem;
      .bg {
        height: 309 * @rem;
        &::before {
          .image-bg('~@/assets/images/0501/51_bg7.png');
        }
      }
      .container {
        position: absolute;
        top: 60 * @rem;
        left: 0;
        padding: 0 30 * @rem;
        z-index: 9999;
        width: 100%;
        box-sizing: border-box;
        .db-list {
          display: flex;
          flex-wrap: wrap;
          justify-content: space-between;
          .item {
            width: 90 * @rem;
            height: 70 * @rem;
            margin-bottom: 9 * @rem;
            background: #ffdfb9;
            border-radius: 3 * @rem;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 16 * @rem;
            font-weight: 600;
            color: #a86245;
            &.on {
              .image-bg('~@/assets/images/0501/51_bg18.png');
            }
            &.space {
              background: transparent;
            }
          }
        }
        .big-bold-text {
          color: #7c343f;
          font-size: 16 * @rem;
          font-weight: 600;
        }
        .content {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-top: 5 * @rem;
          .right {
            display: flex;
            .circle {
              width: 16 * @rem;
              height: 16 * @rem;
              border: 2 * @rem solid RGBA(124, 52, 63, 1);
              border-radius: 50%;
              font-size: 18 * @rem;
              font-weight: bold;
              color: rgba(124, 52, 63, 1);
              text-align: center;
              line-height: 16 * @rem;
              &.empty {
                color: RGBA(190, 143, 136, 1);
                border-color: RGBA(190, 143, 136, 1);
              }
            }
            .count {
              margin: 0 13 * @rem;
              font-size: 16 * @rem;
              font-weight: 600;
              color: #7c343f;
            }
          }
        }
        .small-bold-text {
          color: #7c343f;
          font-size: 14 * @rem;
          font-weight: 600;
        }
        .small-text {
          margin-top: 7 * @rem;
          font-size: 14 * @rem;
          color: #7c343f;
        }
        .bottom-button {
          width: 113 * @rem;
          height: 44 * @rem;
          margin: 18 * @rem auto;
          .image-bg('~@/assets/images/0501/51_button1.png');
          &.empty {
            .image-bg('~@/assets/images/0501/51_button7.png');
          }
        }
      }
    }
    &.db-coordinate-popup {
      height: 296 * @rem;
      .bg {
        height: 193 * @rem;
        &::before {
          .image-bg('~@/assets/images/0501/51_bg8.png');
        }
      }
      .container {
        position: absolute;
        top: 43 * @rem;
        left: 0;
        padding: 0 16 * @rem;
        z-index: 9999;
        width: 100%;
        box-sizing: border-box;
        .tab {
          display: flex;
          justify-content: space-between;
          align-items: flex-end;
          .tab-item {
            flex: 0 0 61 * @rem;
            height: 32 * @rem;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 10 * @rem;
            color: #7c343f;
            .image-bg('~@/assets/images/0501/51_bg16.png');
            &.current {
              flex: 0 0 71 * @rem;
              height: 34 * @rem;
              .image-bg('~@/assets/images/0501/51_bg17.png');
            }
          }
        }
        .list {
          margin-top: 10 * @rem;
          height: 105 * @rem;
          overflow-y: scroll;
          margin-bottom: 10 * @rem;
          font-size: 0;
          .item {
            width: 25%;
            display: inline-block;
            height: 25 * @rem;
            margin-bottom: 13 * @rem;
            text-align: center;
            line-height: 25 * @rem;
            font-size: 12 * @rem;
            color: #7c343f;
            font-weight: bold;
          }
        }
        .empty {
          display: flex;
          justify-content: center;
          align-items: center;
          height: 125 * @rem;
          color: #a86245;
          font-size: 16 * @rem;
          font-weight: 600;
        }
        .bottom-button {
          width: 113 * @rem;
          height: 44 * @rem;
          margin: 0 * @rem auto;
          .image-bg('~@/assets/images/0501/51_button2.png');
        }
      }
    }
  }
}
</style>
<style lang="less">
.van-dialog,
.van-button--default {
  background: #fff7ed;
  color: rgba(124, 52, 63, 1);
}
[class*='van-hairline']::after {
  border-color: #7c343f;
}
</style>
