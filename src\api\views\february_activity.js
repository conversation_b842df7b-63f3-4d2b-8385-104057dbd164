import { request } from '../index';

/**
 * 活动页/龙抬头 - 2月2首页接口
 */
export function ApiFBIndex(params = {}) {
  return request('/activity/february_two/index', params);
}

/**
 * 活动页/龙抬头 - svip回馈领取
 */
export function ApiFBTakeSvip(params = {}) {
  return request('/activity/february_two/takeSvip', params);
}

/**
 * 活动页/龙抬头 - 投入金币
 */
export function ApiFBTakeInvestment(params = {}) {
  return request('/activity/february_two/takeInvestment', params);
}

/**
 * 活动页/龙抬头 - 投入列表
 */
export function ApiFBInvestmentList(params = {}) {
  return request('/activity/february_two/investmentList', params);
}

/**
 * 活动页/龙抬头 - 龙抬头兑换记录
 */
export function ApiFBLotteryList(params = {}) {
  return request('/activity/february_two/lotteryList', params);
}

/**
 * 活动页/龙抬头 - 龙抬头刮奖
 */
export function ApiFBScratchLottery(params = {}) {
  return request('/activity/february_two/scratchLottery', params);
}

/**
 * 活动页/龙抬头 - 龙抬头刷新用户剩余次数
 */
export function ApiFBRemainLotteryCount(params = {}) {
  return request('/activity/february_two/remainLotteryCount', params);
}

/**
 * 活动页/龙抬头 - 龙抬头用户信息接口
 */
export function ApiFBGetSelfInfo(params = {}) {
  return request('/activity/february_two/getSelfInfo', params);
}
