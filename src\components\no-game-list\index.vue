<template>
  <van-dialog
    v-model="show"
    :lock-scroll="false"
    :show-confirm-button="false"
    :close-on-click-overlay="true"
    class="no-gold-game-popup"
  >
    <div class="search-container">
      <div class="close-search" @click="show = false"></div>
      <div class="search-bar">
        <div class="input-text">
          <form @submit.prevent="searchGame">
            <input
              type="text"
              v-model.trim="input_game"
              placeholder="输入游戏名"
            />
          </form>
        </div>
        <div class="search-btn" @click="searchGame">搜索</div>
      </div>
      <yy-list
        class="yy-list game-list"
        v-model="loading_obj"
        :finished="finished"
        @refresh="onRefresh"
        @loadMore="loadMore"
        :empty="!game_list.length"
        tips="请输入您想搜索的游戏"
        :check="false"
      >
        <div
          class="game-item btn"
          v-for="item in game_list"
          :key="item.id"
          @click="toGame(item)"
        >
          <div class="game-icon">
            <img :src="item.titlepic" alt="" />
          </div>
          <div class="right">
            <div class="game-name">{{ item.title }}</div>
            <div class="use-card" :class="{ can: item.use_gold_pay == 1 }">
              {{ item.use_gold_pay == 1 ? '' : '不' }}支持使用金币支付
            </div>
          </div>
        </div>
      </yy-list>
    </div>
  </van-dialog>
</template>
<script>
import { ApiGameCardSearchGame } from '@/api/views/recharge.js';
import { BOX_goToGame, BOX_openInNewWindow } from '@/utils/box.uni.js';
import { envFun } from '@/utils/function';

export default {
  props: {
    game_dialog_show: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      game_list: [], //查看的游戏名单
      loading_obj: {
        loading: false,
        reloading: false,
      }, //loading对象
      input_game: '', //search的input框
      page: 1, //查看名单的页码
      listRows: 20, //查看名单的大小
      empty: false, //查看名单的空
      finished: false, //防抖
    };
  },
  computed: {
    show: {
      set(show) {
        this.$emit('changeGameDialogShow', show);
      },
      get() {
        return this.game_dialog_show;
      },
    },
  },
  methods: {
    // 搜索不可使用金币的游戏
    async searchGame() {
      if (!this.input_game) {
        this.$toast('请输入游戏名');
        return false;
      }
      this.$toast({
        type: 'loading',
        duration: 0,
        message: '拼命加载中...',
      });
      this.game_list = [];
      await this.getGameList();
    },
    async getGameList(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      let res = await ApiGameCardSearchGame({
        keyword: this.input_game,
        listRows: 20,
        page: this.page,
      });
      this.$toast.clear();
      let { list } = res.data;
      if (action === 1 || this.page === 1) {
        this.game_list = [];
      }
      if (!list.length) {
        this.empty = true;
      }
      this.game_list.push(...list);
      if (list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
    },
    async onRefresh() {
      try {
        await this.getGameList();
      } finally {
        this.loading_obj.reloading = false;
      }
    },
    async loadMore() {
      try {
        await this.getGameList(2);
      } finally {
        this.loading_obj.loading = false;
      }
    },
    toGame(item) {
      BOX_goToGame(
        {
          params: {
            id: item.id,
          },
        },
        { id: item.id },
      );
    },
    toCashGift() {
      BOX_openInNewWindow(
        { name: 'MyCashGift' },
        { url: `https://${envFun()}game.3733.com/#/my_cashgift` },
      );
    },
  },
};
</script>
<style lang="less" scoped>
.no-gold-game-popup {
  overflow: unset;
  width: 320 * @rem;

  .search-container {
    box-sizing: border-box;
    width: 320 * @rem;
    height: 450 * @rem;
    padding: 24 * @rem 19 * @rem 10 * @rem;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: unset;

    .close-search {
      width: 24 * @rem;
      height: 24 * @rem;
      background: url(~@/assets/images/close-search.png) center center no-repeat;
      background-size: 24 * @rem 24 * @rem;
      position: absolute;
      right: -10 * @rem;
      top: -10 * @rem;
    }

    .search-bar {
      display: flex;
      align-items: center;

      .input-text {
        width: 240 * @rem;
        height: 35 * @rem;
        border: 1 * @rem solid #e5e5e5;
        border-radius: 18 * @rem;
        flex: 1;
        overflow: hidden;

        form {
          display: block;
          width: 100%;
          height: 100%;
        }

        input {
          box-sizing: border-box;
          display: block;
          width: 100%;
          height: 100%;
          padding: 0 18 * @rem;
          font-size: 15 * @rem;
          color: #333333;
          background-color: #f6f6f6;
        }
      }

      .search-btn {
        font-size: 15 * @rem;
        color: #666666;
        padding-left: 13 * @rem;
        height: 35 * @rem;
        line-height: 35 * @rem;
      }
    }

    .game-list {
      flex: 1;
      overflow: auto;
      margin-top: 10 * @rem;

      .game-item {
        display: flex;
        align-items: center;
        padding: 21 * @rem 0;
        border-bottom: 1 * @rem solid #eeeeee;

        .game-icon {
          width: 50 * @rem;
          height: 50 * @rem;
          border-radius: 10 * @rem;
          background-color: #b5b5b5;
        }

        .right {
          flex: 1;
          min-width: 0;
          margin-left: 10 * @rem;

          .game-name {
            font-size: 16 * @rem;
            font-weight: bold;
            color: #000000;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
          }

          .use-card {
            font-size: 12 * @rem;
            color: #f72e2e;
            margin-top: 10 * @rem;

            &.can {
              color: #36b150;
            }
          }
        }
      }
    }
  }
}
</style>
