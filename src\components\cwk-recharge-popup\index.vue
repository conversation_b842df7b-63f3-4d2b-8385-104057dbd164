<template>
  <van-popup
    v-model="popup"
    :close-on-click-overlay="true"
    position="bottom"
    round
    class="cwk-recharge-popup"
    :lock-scroll="false"
  >
    <div class="close" @click="setShowCwkRechargePopup(false)"></div>
    <div class="popup-title">开通畅玩卡</div>
    <div class="swiper">
      <swiper
        id="cwkSwiper"
        class="cwk-swiper"
        ref="cwkSwiper"
        :options="swiperOptions"
        :auto-update="true"
        style="width: 100%; margin: 0 auto"
        v-if="selectList.length > 0"
      >
        <swiper-slide
          class="select-item"
          v-for="(item, index) in selectList"
          :class="{
            on: selectedMeal.type == item.type,
          }"
          :key="index"
        >
          <div class="title">畅玩{{ item.title }}</div>
          <div class="day">{{ item.amount_desc.day }}<span>天</span></div>
          <div class="desc">
            ({{ item.amount_desc.price }}元 x {{ item.amount_desc.day }}张 =
            {{ item.amount_desc.total_price }}元)
          </div>
          <div class="amount-container">
            <div class="amount">
              ¥ <span>{{ item.amount }}</span>
            </div>
            <div class="old-amount" v-if="item.old_amount > 0">
              原价¥{{ item.old_amount }}
            </div>
          </div>
        </swiper-slide>
      </swiper>
    </div>
    <div class="payway-title">请选择支付方式</div>
    <ul class="pay-list">
      <li
        class="pay-item"
        :class="{ on: payWay == item.key }"
        v-for="(item, index) in payWayList"
        :key="index"
        @click="payWay = item.key"
      >
        <i class="icon" :style="{ backgroundImage: `url(${item.icon})` }"></i>
        <span class="text">{{ item.name }}</span>
        <div class="select-icon"></div>
      </li>
    </ul>
    <div class="recharge btn" @click="handlePay">立即开通</div>
    <bottom-safe-area></bottom-safe-area>
  </van-popup>
</template>
<script>
import { mapMutations, mapGetters } from 'vuex';
import {
  ApiGameCardIndex,
  ApiGetPayUrl,
  ApiGameCardCreateOrder,
} from '@/api/views/recharge.js';
export default {
  data() {
    let that = this;
    return {
      swiperOptions: {
        observer: true, //开启动态检查器，监测swiper和slide
        observeSlideChildren: true, //监测Swiper的子元素wrapper、pagination、navigation、scrollbar或其他一级子元素
        slidesPerView: 'auto',
        freeMode: true,
        freeModeMomentumRatio: 0.3, // 运动惯量
        on: {
          click: function () {
            setTimeout(() => {
              if (
                that.selectedMeal.type !=
                that.selectList[this.clickedIndex].type
              ) {
                that.selectedMeal = that.selectList[this.clickedIndex];
              }
            }, 0);
          },
        },
      },
      payWay: '', // 支付方式
      selectedMeal: {}, // 选中的套餐
      selectList: [], // 套餐列表
      payWayList: [], // 支付方式
    };
  },
  computed: {
    popup: {
      get() {
        return this.showCwkRechargePopup;
      },
      set(value) {
        this.setShowCwkRechargePopup(value);
      },
    },
    ...mapGetters({
      showCwkRechargePopup: 'recharge/showCwkRechargePopup',
    }),
  },
  async created() {
    await this.getChangwanList();
  },
  methods: {
    ...mapMutations({
      setShowCwkRechargePopup: 'recharge/setShowCwkRechargePopup',
    }),
    // 支付逻辑
    handlePay() {
      this.setShowCwkRechargePopup(false);
      ApiGameCardCreateOrder({
        type: this.selectedMeal.type,
        payWay: this.payWay,
      }).then(async orderRes => {
        ApiGetPayUrl({
          orderId: orderRes.data.orderId,
          orderType: 104,
          payWay: this.payWay,
          packageName: '',
        }).finally(() => {
          this.$emit('success');
          this.getChangwanList();
        });
      });
    },
    // 获取套餐列表和支付方式
    async getChangwanList() {
      const res = await ApiGameCardIndex();
      let { payArr, card_list } = res.data;
      this.selectList = card_list;
      this.payWayList = payArr;
      this.payWay = this.payWayList[0].key;
      this.selectedMeal = this.selectList[0];
    },
  },
};
</script>
<style lang="less" scoped>
.cwk-recharge-popup {
  width: 100%;
  .close {
    position: absolute;
    right: 9 * @rem;
    top: 9 * @rem;
    width: 22 * @rem;
    height: 22 * @rem;
    .image-bg('~@/assets/images/recharge/recharge-popup-close.png');
  }
  .popup-title {
    font-size: 18 * @rem;
    line-height: 23 * @rem;
    font-weight: bold;
    color: #333333;
    text-align: center;
    margin-top: 19 * @rem;
  }
  .swiper {
    width: 100%;
    height: 122 * @rem;
    margin-top: 28 * @rem;
    .cwk-swiper {
      .select-item {
        box-sizing: border-box;
        width: 160 * @rem;
        height: 122 * @rem;
        margin-right: 10 * @rem;
        .image-bg('~@/assets/images/recharge/cwk-select.png');
        position: relative;
        &:first-of-type {
          margin-left: 23 * @rem;
        }
        .title {
          position: absolute;
          right: 0;
          top: 0;
          width: 73 * @rem;
          height: 24 * @rem;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 12 * @rem;
          color: #ffffff;
        }
        .day {
          line-height: 40 * @rem;
          font-size: 32 * @rem;
          font-weight: bold;
          color: #333333;
          margin-top: 12 * @rem;
          margin-left: 17 * @rem;
          height: 40 * @rem;
          span {
            color: #666666;
            font-size: 12 * @rem;
            margin-left: 2 * @rem;
            font-weight: bold;
          }
        }
        .desc {
          font-size: 11 * @rem;
          line-height: 14 * @rem;
          color: #999999;
          margin-top: 0 * @rem;
          margin-left: 17 * @rem;
        }
        .amount-container {
          height: 40 * @rem;
          display: flex;
          align-items: center;
          margin-top: 15 * @rem;
          padding-left: 13 * @rem;
          .amount {
            font-size: 12 * @rem;
            font-weight: bold;
            color: #fd6a33;
            display: flex;
            align-items: center;
            span {
              color: #fd6a33;
              line-height: 35 * @rem;
              font-size: 26 * @rem;
              margin-left: 2 * @rem;
            }
          }
          .old-amount {
            font-size: 12 * @rem;
            line-height: 15 * @rem;
            color: #666666;
            margin-left: 6 * @rem;
            margin-top: 5 * @rem;
            text-decoration: line-through;
          }
        }
        &.on {
          .image-bg('~@/assets/images/recharge/cwk-selected.png');
          .amount-container {
            .amount {
              color: #fff;
              span {
                color: #fff;
              }
            }
            .old-amount {
              color: #fff;
            }
          }
        }
      }
    }
  }
  .input-container {
    margin: 16 * @rem 23 * @rem 0;
    border-radius: 19 * @rem;
    height: 36 * @rem;
    background-color: #f4f4f4;
    display: flex;
    align-items: center;
    padding: 0 14 * @rem;
    .text-input {
      flex: 1;
      display: block;
      height: 100%;
      background-color: transparent;
      font-size: 18 * @rem;
      color: #333333;
      font-weight: bold;
      &::-webkit-input-placeholder {
        color: #999999;
        font-size: 14px;
        font-weight: normal;
      }
    }
    .text-right {
      font-size: 14 * @rem;
      color: #333333;
      font-weight: bold;
    }
  }
  .payway-title {
    font-size: 16 * @rem;
    color: #333333;
    font-weight: 600;
    line-height: 20 * @rem;
    padding: 0 23 * @rem;
    margin-top: 20 * @rem;
  }
  .pay-list {
    overflow: hidden;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    margin-top: 12 * @rem;
    padding: 0 23 * @rem;
  }
  .pay-item {
    box-sizing: border-box;
    width: 48%;
    height: 52 * @rem;
    padding-top: 10 * @rem;
    text-align: center;
    border: 0.5 * @rem solid #cbcbcb;
    border-radius: 6 * @rem;
    font-size: 0;
    display: flex;
    align-items: center;
    padding: 0 12 * @rem;
    &:not(:nth-of-type(-n + 2)) {
      margin-top: 10 * @rem;
    }
    &.on {
      border-color: #21b98a;
      border-width: 1 * @rem;
    }
    .icon {
      display: block;
      width: 25 * @rem;
      height: 25 * @rem;
      background-repeat: no-repeat;
      background-size: 25 * @rem 25 * @rem;
    }
    .text {
      display: block;
      font-size: 14 * @rem;
      font-weight: 400;
      color: #333;
      flex: 1;
      min-width: 0;
      margin-left: 6 * @rem;
      text-align: left;
    }
    .select-icon {
      width: 16 * @rem;
      height: 16 * @rem;
      background: url(~@/assets/images/recharge/pay-no.png) no-repeat;
      background-size: 16 * @rem 16 * @rem;
    }
    &.on .select-icon {
      background-image: url(~@/assets/images/recharge/pay-yes.png);
    }
  }
  .recharge {
    width: 273 * @rem;
    height: 42 * @rem;
    .image-bg('~@/assets/images/recharge/recharge-btn-bg.png');
    margin: 23 * @rem auto;
    font-size: 18 * @rem;
    font-weight: bold;
    color: #ffffff;
    line-height: 21 * @rem;
    display: flex;
    align-items: center;
    justify-content: center;
    span {
      font-weight: bold;
    }
  }
}
</style>
