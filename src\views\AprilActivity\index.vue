<template>
  <div class="april-activity">
    <nav-bar-2
      ref="topNavBar"
      bgStyle="transparent-white"
      :azShow="true"
      :placeholder="false"
    ></nav-bar-2>
    <div @click="toPage('AprilActivityRule')" class="rule btn"></div>
    <div @click="getRecordList" class="exchange-record btn"></div>
    <div class="active-time">04月14日00:00 - 04月16日23:59</div>
    <div class="user-container">
      <div v-if="userInfo.token" class="user">
        <UserAvatar class="avatar" />
        <div class="nickname">{{ userInfo.nickname }}</div>
      </div>
      <div @click="login" v-else class="user no-login btn">
        <div class="text">未登录</div>
      </div>
    </div>
    <div @click="explain_popup = true" class="prize-button"></div>
    <div class="point-container">
      <div class="point">当前积分：{{ point }}</div>
      <div @click="showTips" class="question"></div>
    </div>
    <div
      :class="{
        already: today_lottery_status == 2,
        empty: today_lottery_status == 0,
      }"
      class="start-button-container"
    >
      <div @click="handleLottery" class="start-button"></div>
    </div>
    <div class="list-container">
      <div class="list list1">
        <div class="list-header"></div>
        <div class="list-body">
          <div v-for="item in extra_list" :key="item.type" class="item">
            <div class="item-left">
              <div class="big-text">{{ item.title }}</div>
              <div class="small-text">{{ item.desc }}</div>
            </div>
            <div
              :class="{ empty: item.status == 0, already: item.status == 2 }"
              @click="handleExtraListTake(item)"
              class="item-right btn"
            ></div>
          </div>
        </div>
        <div class="list-footer"></div>
      </div>
      <div class="list list2">
        <div class="list-header"></div>
        <div class="list-body">
          <div v-for="item in exchange_list" :key="item.type" class="item">
            <div class="item-left">
              <div :class="{ final: item.is_max }" class="big-text">
                {{ item.title }}{{ item.num_text }}
              </div>
              <div class="small-text">{{ item.desc }}</div>
            </div>
            <div
              :class="{ empty: item.status == 0, already: item.status == 2 }"
              @click="handleExchangeListTake(item)"
              class="item-right btn"
            ></div>
          </div>
        </div>
        <div class="list-footer"></div>
      </div>
      <div class="list list3">
        <div class="list-header"></div>
        <div class="list-body">
          <div class="item">
            <div class="item-left">
              <div class="big-text">8888金币</div>
              <div class="small-text">前30个兑换终极奖励的玩家</div>
            </div>
            <div
              @click="toPage('AprilActivityRank')"
              class="item-right btn"
            ></div>
          </div>
        </div>
        <div class="list-footer"></div>
      </div>
      <div class="bottom-container">
        <div class="bottom-left"></div>
        <div class="bottom-right"></div>
      </div>
    </div>
    <div class="bg1"></div>
    <div class="bg2"></div>
    <!-- 兑奖记录 -->
    <van-popup
      v-model="record_list_popup"
      :lock-scroll="false"
      :close-on-click-overlay="false"
      class="popup record-list-popup"
    >
      <div class="popup-close" @click="closeRecordPopup"></div>
      <div class="title">兑奖记录</div>
      <div v-if="record_list.length > 0" class="list">
        <div v-for="(item, index) in record_list" :key="index" class="item">
          <div class="left">{{ item.title }}</div>
          <div v-html="item.desc" class="right"></div>
        </div>
      </div>
      <div v-else class="empty">暂无兑奖记录</div>
    </van-popup>
    <!-- 说明弹窗 -->
    <van-popup
      v-model="explain_popup"
      :lock-scroll="false"
      :close-on-click-overlay="false"
      class="popup explain-popup"
    >
      <div class="popup-close" @click="explain_popup = false"></div>
      <div class="title">活动期间可使用积分进行抽奖</div>
      <div class="text">
        每次消耗100积分。每个用户每天可抽奖三次。每次可在以下奖品列表中随机获得一样：
      </div>
      <div class="color-text">
        20000积分、888平台币、8888积分、3733金币、888金币、288积分、88金币、68积分。
      </div>
      <div class="bottom-text">抽奖概率公示可在活动规则处查看</div>
    </van-popup>
  </div>
</template>
<script>
import { platform, boxInit } from '@/utils/box.uni.js';
import UserAvatar from '@/components/user-avatar';
import { BOX_login } from '@/utils/box.uni.js';
import {
  ApiAprilIndex,
  ApiAprilTakeExtra,
  ApiAprilTakeExchange,
  ApiAprilExchangeRecord,
  ApiAprilLottery,
} from '@/api/views/april_activity';
import { mapGetters, mapMutations } from 'vuex';
import ptbRechargePopup from '@/components/ptb-recharge-popup';
import svipRechargePopup from '@/components/svip-recharge-popup';

export default {
  data() {
    return {
      activity_status: 3, //1活动已开始 2 活动不存在 3活动未开始 4活动已结束
      svip_popup: false, //svip引导弹窗
      record_list_popup: false, //记录弹窗
      explain_popup: false, //说明弹窗
      record_list: [], //记录列表
      point: 0, //当前积分
      finished: false, //ajax防卡
      exchange_list: [], //兑换列表
      extra_list: [], //额外列表
      today_lottery_status: 0, //今日抽奖状态，0 不可领取 1可领取 2已领取
    };
  },
  computed: {
    active_status_text() {
      const arr = ['活动已开始', '活动不存在', '活动未开始', '活动已结束'];
      return arr[this.activity_status - 1];
    },
    ...mapGetters({
      userInfo: 'user/userInfo',
    }),
  },
  async mounted() {
    // 官包触发页面刷新（将onResume挂载在window上）
    if (platform == 'android') {
      window.onResume = this.onResume;
    }
    await this.getActivityInfo();
  },
  methods: {
    async onResume() {
      await boxInit();
      await this.getActivityInfo();
      this.SET_USER_INFO(true);
    },
    async getActivityInfo() {
      const res = await ApiAprilIndex();
      this.activity_status = res.data.activity_status;
      this.exchange_list = res.data.exchange_list;
      this.extra_list = res.data.extra_list;
      this.point = res.data.remain_num;
      this.today_lottery_status = res.data.lottery_status;
    },
    login() {
      BOX_login();
    },
    handleLottery() {
      if (!this.userInfo.token) {
        this.login();
        return false;
      }
      if (this.activity_status != 1) {
        this.$toast(this.active_status_text);
        return false;
      }
      if (this.today_lottery_status == 2) {
        this.$toast('今日抽奖次数已用完');
        return false;
      }
      if (this.today_lottery_status == 0) {
        this.$dialog.alert({
          message: '当前积分不足100，请前往游戏内充值获取积分',
          lockScroll: false,
          confirmButtonColor: '#7C343F',
        });
        return false;
      }
      this.$dialog
        .confirm({
          message: '是否要消耗100积分进行积分抽奖？',
          lockScroll: false,
          confirmButtonColor: '#7C343F',
          cancelButtonColor: '#7C343F',
        })
        .then(async () => {
          try {
            this.$toast({
              type: 'loading',
              duration: 0,
              message: '拼命加载中...',
              forbidClick: true,
              lockScroll: false,
            });
            const res = await ApiAprilLottery();
            await this.getActivityInfo();
          } catch {}
        });
    },
    showTips() {
      this.$toast(
        '活动期间，每天前5笔游戏现金充值订单可获得等额积分奖励（仅限游戏内使用微信/支付宝充值）',
      );
    },
    toRecharge() {
      if (!this.userInfo.token) {
        this.login();
        return false;
      }
      if (this.activity_status != 1) {
        this.$toast(this.active_status_text);
        return false;
      }
      this.scratch_empty_popup = false;
      this.setShowPtbRechargePopup(true);
    },
    toRechargeSvip() {
      if (!this.userInfo.token) {
        this.login();
        return false;
      }
      if (this.activity_status != 1) {
        this.$toast(this.active_status_text);
        return false;
      }
      this.svip_popup = false;
      this.setShowSvipRechargePopup(true);
    },
    // 关闭记录弹窗
    closeRecordPopup() {
      this.record_list_popup = false;
    },
    // 获取记录列表
    async getRecordList() {
      if (!this.userInfo.token) {
        this.login();
        return false;
      }
      if (this.activity_status != 1) {
        this.$toast(this.active_status_text);
        return false;
      }
      this.$toast({
        type: 'loading',
        duration: 0,
        message: '拼命加载中...',
        forbidClick: true,
      });
      try {
        const res = await ApiAprilExchangeRecord();
        this.record_list = res.data.list;
        this.record_list_popup = true;
      } finally {
        this.$toast.clear();
      }
    },
    // 兑换任务奖励
    async handleExchangeListTake(item) {
      if (!this.userInfo.token) {
        this.login();
        return false;
      }
      if (this.activity_status != 1) {
        this.$toast(this.active_status_text);
        return false;
      }
      if (item.status == 0) {
        this.$toast('您未达到兑换条件哦~');
        return false;
      }
      if (item.status == 2) {
        this.$toast('您已经兑换过此奖励啦~');
        return false;
      }
      const res = await ApiAprilTakeExchange({ exchange_type: item.type });
      await this.getActivityInfo();
    },
    // 领取额外任务
    async handleExtraListTake(item) {
      if (!this.userInfo.token) {
        this.login();
        return false;
      }
      if (this.activity_status != 1) {
        this.$toast(this.active_status_text);
        return false;
      }
      // if (item.status == 0) {
      //   this.$toast("您未达到领取条件哦~");
      //   return false;
      // }
      // if (item.status == 2) {
      //   this.$toast("您已经领取过此奖励啦~");
      //   return false;
      // }
      const res = await ApiAprilTakeExtra({ extra_type: item.type });
      await this.getActivityInfo();
    },
    ...mapMutations({
      setShowPtbRechargePopup: 'recharge/setShowPtbRechargePopup',
      setShowSvipRechargePopup: 'recharge/setShowSvipRechargePopup',
    }),
  },
  components: {
    ptbRechargePopup,
    svipRechargePopup,
    UserAvatar,
  },
};
</script>
<style lang="less" scoped>
.april-activity {
  position: relative;
  background-color: #2d1513;
  min-height: 100vh;
}
.bg1 {
  width: 100%;
  height: 177 * @rem;
  .image-bg('~@/assets/images/april-activity/ac_bg13.png');
}
.bg2 {
  width: 100%;
  height: 528 * @rem;
  .image-bg('~@/assets/images/april-activity/ac_bg1.png');
}
.rule {
  position: absolute;
  top: 80 * @rem;
  right: 5 * @rem;
  width: 52 * @rem;
  height: 50 * @rem;
  .image-bg('~@/assets/images/april-activity/ac_button9.png');
}
.exchange-record {
  position: absolute;
  top: 146 * @rem;
  right: 5 * @rem;
  width: 52 * @rem;
  height: 50 * @rem;
  .image-bg('~@/assets/images/april-activity/ac_button8.png');
}
.active-time {
  position: absolute;
  top: 109 * @rem;
  left: 50%;
  transform: translate(-50%, 0);
  width: 205 * @rem;
  height: 30 * @rem;
  border: 1px solid rgba(166, 131, 131, 0.5);
  color: #fff;
  background: rgba(38, 20, 20, 1);
  text-align: center;
  line-height: 30 * @rem;
  border-radius: 20 * @rem;
}
.user-container {
  position: absolute;
  top: 151 * @rem;
  left: 17 * @rem;
  width: 115 * @rem;
  height: 31 * @rem;
  .image-bg('~@/assets/images/april-activity/ac_bg10.png');
  font-size: 14 * @rem;
  font-weight: bold;
  color: #ffffff;
  .user {
    display: flex;
    align-items: center;
    .avatar {
      flex: 0 0 30 * @rem;
      width: 30 * @rem;
      height: 30 * @rem;
      border-radius: 50%;
      // background: #000;
    }
    .nickname {
      flex: 1;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      margin-left: 8 * @rem;
    }
  }
  .no-login {
    .text {
      text-indent: 3em;
      line-height: 31 * @rem;
    }
  }
}
.prize-button {
  width: 61 * @rem;
  height: 62 * @rem;
  position: absolute;
  top: 502 * @rem;
  right: 20 * @rem;
  .image-bg('~@/assets/images/april-activity/ac_button15.png');
}
.point-container {
  position: absolute;
  top: 490 * @rem;
  left: 20 * @rem;
  display: flex;
  align-items: center;
  .point {
    width: 128 * @rem;
    height: 34 * @rem;
    .image-bg('~@/assets/images/april-activity/ac_bg11.png');
    white-space: nowrap;
    font-size: 15 * @rem;
    text-align: center;
    line-height: 34 * @rem;
    font-weight: bold;
    color: #ffffff;
  }
  .question {
    width: 21 * @rem;
    height: 21 * @rem;
    margin-left: 7 * @rem;
    .image-bg('~@/assets/images/april-activity/ac_button10.png');
  }
}
.start-button-container {
  position: absolute;
  top: 565 * @rem;
  left: 50%;
  transform: translate(-50%, 0);
  width: 357 * @rem;
  height: 128 * @rem;
  .image-bg('~@/assets/images/april-activity/ac_button6.png');
  &.empty {
    .image-bg('~@/assets/images/april-activity/ac_button14.png');
  }
  &.already {
    .image-bg('~@/assets/images/april-activity/ac_button13.png');
  }
  .start-button {
    width: 193 * @rem;
    height: 54 * @rem;
    // background-color: #fff;
    margin: 40 * @rem auto 0;
  }
}
.list-container {
  position: absolute;
  width: 100%;
  left: 0;
  top: 700 * @rem;
  padding-bottom: 18 * @rem;
  background-color: #2d1513;
  overflow: hidden;
  .bottom-container {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    .bottom-left {
      width: 60 * @rem;
      height: 60 * @rem;
      .image-bg('~@/assets/images/april-activity/ac_bg7.png');
    }
    .bottom-right {
      position: relative;
      top: 2 * @rem;
      width: 54 * @rem;
      height: 46 * @rem;
      .image-bg('~@/assets/images/april-activity/ac_bg8.png');
    }
  }
  .list {
    width: 100%;
    .list-header {
      width: 100%;
      height: 74 * @rem;
    }
    .list-footer {
      width: 100%;
      height: 60 * @rem;
      .image-bg('~@/assets/images/april-activity/ac_bg2.png');
    }
    .list-body {
      background: #f0dcc4;
      overflow: hidden;
      .item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 20 * @rem;
        margin-top: 15 * @rem;
        .item-left {
          .big-text {
            font-size: 14 * @rem;
            color: #7c343f;
            font-weight: 600;
            line-height: 18 * @rem;
          }
          .small-text {
            margin-top: 5 * @rem;
            line-height: 15 * @rem;
            color: #a16b48;
          }
        }
        .item-right {
          width: 62 * @rem;
          height: 26 * @rem;
          .image-bg('~@/assets/images/april-activity/ac_button1.png');
          &.already {
            .image-bg('~@/assets/images/april-activity/ac_button3.png');
          }
          &.empty {
            .image-bg('~@/assets/images/april-activity/ac_button12.png');
          }
        }
      }
    }
    &.list1 {
      .list-header {
        .image-bg('~@/assets/images/april-activity/ac_bg4.png');
        background-size: 380 * @rem auto;
        background-position: -5 * @rem 0;
      }
    }
    &.list2 {
      position: relative;
      top: -27 * @rem;
      .list-header {
        .image-bg('~@/assets/images/april-activity/ac_bg9.png');
        height: 125 * @rem;
      }
      .list-body {
        .item {
          .item-left {
            .big-text {
              position: relative;
              &.final::before {
                content: '';
                position: absolute;
                top: 0;
                right: -53 * @rem;
                width: 47 * @rem;
                height: 15 * @rem;
                .image-bg('~@/assets/images/april-activity/ac_button7.png');
              }
            }
          }
          .item-right {
            width: 62 * @rem;
            height: 26 * @rem;
            .image-bg('~@/assets/images/april-activity/ac_button2.png');
            &.already {
              .image-bg('~@/assets/images/april-activity/ac_button4.png');
            }
            &.empty {
              .image-bg('~@/assets/images/april-activity/ac_button11.png');
            }
          }
        }
      }
    }
    &.list3 {
      .list-header {
        .image-bg('~@/assets/images/april-activity/ac_bg6.png');
        background-size: 380 * @rem auto;
        background-position: -5 * @rem 0;
      }
      .list-body {
        .item {
          .item-right {
            width: 72 * @rem;
            height: 27 * @rem;
            .image-bg('~@/assets/images/april-activity/ac_button5.png');
          }
        }
      }
    }
  }
}
.popup {
  width: 290 * @rem;
  padding: 18 * @rem;
  border-radius: 20 * @rem;
  overflow: hidden;
  background: #f8fffc;
  box-shadow: 0 * @rem 2 * @rem 15 * @rem 0 * @rem rgba(248, 0, 0, 0.1);
  border: 2 * @rem solid #7c343f;
  .text {
    font-size: 14 * @rem;
    color: #7c343f;
  }
  .popup-close {
    width: 33 * @rem;
    height: 27 * @rem;
    background: #7c343f url(~@/assets/images/popup-close.png) center center
      no-repeat;
    background-size: 22 * @rem 22 * @rem;
    position: absolute;
    right: -1 * @rem;
    top: -1 * @rem;
    border-radius: 0 12 * @rem 0 12 * @rem;
  }
  .title {
    font-size: 16 * @rem;
    font-family: PingFang SC-Semibold, PingFang SC;
    font-weight: 600;
    color: #7c343f;
    line-height: 20 * @rem;
    text-align: center;
    margin-bottom: 30 * @rem;
  }
  .bottom-button {
    width: 255 * @rem;
    height: 47 * @rem;
    margin: 15 * @rem auto 0;
    .image-bg('~@/assets/images/february-activity/fa_button13.png');
  }
  &.message-popup {
    .text {
      width: 254 * @rem;
      margin: 0 auto;
      padding: 20 * @rem 0;
      font-size: 14 * @rem;
      font-family: PingFang SC-Semibold, PingFang SC;
      font-weight: 600;
      color: #7b74aa;
      line-height: 18 * @rem;
      text-align: center;
    }
  }
}
.record-list-popup {
  padding: 18 * @rem 0;
  height: 245 * @rem;
  .list {
    height: 210 * @rem;
    overflow-y: scroll;
    padding: 0 18 * @rem;
    .item {
      display: flex;
      justify-content: space-between;
      height: 30 * @rem;
      align-items: center;
      font-size: 14 * @rem;
      color: #7c343f;
      .left {
        flex: 0 0 90 * @rem;
      }
      .right {
        flex: 1;
        text-align: right;
      }
      /deep/ span {
        color: #f8582e;
      }
    }
  }
  .empty {
    width: 100%;
    height: 210 * @rem;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #478671;
  }
}
.explain-popup {
  padding: 0;
  background: #fff7ed;
  .title {
    height: 46 * @rem;
    line-height: 46 * @rem;
    color: #97603d;
    font-size: 16 * @rem;
    background: rgba(124, 52, 63, 0.1);
  }
  .text {
    margin: 21 * @rem 16 * @rem 12 * @rem;
    color: #976037;
  }
  .color-text {
    margin: 0 16 * @rem 16 * @rem;
    font-size: 13 * @rem;
    font-weight: 600;
    color: #ff5146;
  }
  .bottom-text {
    font-size: 13 * @rem;
    font-weight: 600;
    color: #ff5146;
    text-align: right;
    margin: 0 16 * @rem 15 * @rem;
  }
}
</style>
<style lang="less">
.van-dialog,
.van-button--default {
  background: #fff7ed;
  color: #97603d;
}
[class*='van-hairline']::after {
  border-color: #7c343f;
}
</style>
