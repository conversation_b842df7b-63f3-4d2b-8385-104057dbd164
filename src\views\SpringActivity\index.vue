<template>
  <div class="laba-activity">
    <div class="main">
      <div class="top-section">
        <div
          class="top-line"
          :style="{ backgroundColor: `rgba(247, 109, 72, ${navbarOpacity})` }"
        >
          <div class="back" @click="back"></div>
          <div class="user-container">
            <div v-if="userInfo.nickname" class="user">
              <user-avatar class="avatar"></user-avatar>
              <div class="nickname">{{ userInfo.nickname }}</div>
            </div>
            <div @click="login" v-else class="user">
              <div class="avatar img">
                <img
                  src="@/assets/images/spring-activity/spring-avatar-default.png"
                  alt=""
                />
              </div>
              <div class="nickname">未登录</div>
            </div>
          </div>

          <div class="fudai-fixed">
            {{ lucky_num }}
          </div>
        </div>
        <div class="rule-btn" @click="rulePopup = true"></div>
        <div class="activity-time"> 2024年2月9日 - 2024年2月17日 </div>
      </div>
      <div class="top-section-2">
        <div
          class="main-btn had"
          v-if="remain_count == 0"
          @click="clickFudaiBtn"
        ></div>
        <div
          class="main-btn open"
          v-else-if="bag_info.lucky_status == 1"
          @click="clickFudaiBtn"
        ></div>
        <div class="main-btn" v-else @click="clickFudaiBtn"></div>
      </div>
      <!-- 春节福袋 -->
      <div class="fudai-container">
        <div class="section-content">
          <div class="content-container">
            <div class="empty" v-if="remain_count == 0">
              今日福袋全部领完啦，明日再来
            </div>
            <template v-else>
              <div class="title" v-if="bag_info.title">
                {{ bag_info.title }}
              </div>
              <div class="content" v-html="bag_info.sub_title"></div>
              <div class="tips">{{ bag_info.tips }}</div>
            </template>
          </div>
        </div>
        <div
          class="refresh-btn"
          @click="clickRefresh"
          v-if="remain_count > 1 && remain_count != 5"
        >
          <img
            src="@/assets/images/spring-activity/spring-refresh-btn.png"
            alt=""
          />
          <span>刷新福袋</span>
        </div>
      </div>
      <!-- 开宝箱 -->
      <div class="kbx-container">
        <div class="section-content">
          <div class="content-container">
            <div class="top-bar">
              <div class="section-title"></div>
              <div class="lantern-num" @click="openRecordPopup">
                {{ lantern_num }}
              </div>
            </div>
            <div class="title-bar">第 {{ round }} 轮</div>
            <div class="title-tips">
              每轮宝箱仅可开启一个，开箱后将清空灯笼，进入下一轮
            </div>
            <div class="kbx-list">
              <div
                class="kbx-item"
                v-for="(item, index) in box_list"
                :key="index"
              >
                <div class="box-top">
                  <div class="box-icon">
                    <img :src="item.icon" alt="" />
                  </div>
                  <div class="box-name">{{ item.title }}</div>
                </div>
                <div class="box-bottom">
                  <div class="cost-title">消耗灯笼</div>
                  <div class="cost-bar">
                    <span class="cost-num">{{ item.num }}</span>
                    <div
                      class="box-btn"
                      v-if="item.status"
                      @click="clickOpenBox(item)"
                    ></div>
                    <div
                      class="box-btn no"
                      v-else
                      @click="clickOpenBox(item)"
                    ></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="more-container">
        <div class="more-title"></div>
        <div class="more-section gift">
          <div class="section-title"></div>
          <!-- 648礼盒 -->
          <van-swipe
            class="swiper-container"
            :loop="false"
            :width="
              card_list.length > 2 ? `${(305 * windowWidth) / 375}` : 'auto'
            "
          >
            <van-swipe-item
              class="swiper-slide"
              v-for="(slide, slideIndex) in cardList"
              :key="slideIndex"
            >
              <div class="slide-list">
                <div
                  class="slide-item"
                  v-for="(item, index) in slide"
                  :key="index"
                >
                  <img class="item-pic" :src="item.game.titlepic" alt="" />
                  <div class="info">
                    <div class="name">
                      <span>{{ item.game.title }}</span>
                      <div class="discount">{{ item.game.subtitle }}</div>
                    </div>
                    <div class="desc">
                      <div class="star" v-if="item.game">
                        <i></i>
                        <span>{{ item.game.rating.rating }}</span>
                      </div>
                      <div class="type">{{ item.game.type[0] }}</div>
                    </div>
                    <div class="tags">
                      <span
                        v-for="(tag, tagIndex) in item.game.app_tag"
                        :key="tagIndex"
                        >{{ tag.name }}</span
                      >
                    </div>
                  </div>
                  <div class="item-right">
                    <div class="get-btn" @click.stop="goToGame(item)">
                      领取
                    </div>
                    <div class="residue">
                      剩余：<span>{{ item.remain }}%</span>
                    </div>
                  </div>
                </div>
              </div>
            </van-swipe-item>
          </van-swipe>
        </div>
        <div class="more-section coupon">
          <div class="section-title"></div>
          <!-- 648代金券 -->
          <van-swipe
            class="swiper-container"
            :loop="false"
            :width="
              card_list.length > 2 ? `${(305 * windowWidth) / 375}` : 'auto'
            "
          >
            <van-swipe-item
              class="swiper-slide"
              v-for="(item, index) in coupon_list"
              :key="index"
            >
              <div class="slide-item-container">
                <div class="slide-item">
                  <img class="item-pic" :src="item.titlepic" alt="" />
                  <div class="info">
                    <div class="name">
                      <span>{{ item.title }}</span>
                      <div class="discount">{{ item.subtitle }}</div>
                    </div>
                    <div class="desc">
                      <div class="star">
                        <i></i>
                        <span>{{ item.rating.rating }}</span>
                      </div>
                      <div class="type">{{ item.type[0] }}</div>
                    </div>
                    <div class="tags">
                      <span
                        v-for="(tag, tagIndex) in item.app_tag"
                        :key="tagIndex"
                        >{{ tag.name }}</span
                      >
                    </div>
                  </div>
                </div>
                <div class="slide-bottom">
                  <div class="residue">
                    可省
                    <span><em>￥</em>{{ Number(item.coupon_money) }}</span>
                    <div class="coupon-count">({{ item.coupon_total }}张)</div>
                  </div>
                  <div class="get-btn" @click.stop="takeCoupon(item)">
                    免费领取
                  </div>
                </div>
              </div>
            </van-swipe-item>
          </van-swipe>
        </div>
        <div class="more-section activity">
          <div class="section-title"></div>
          <!-- 春节限时活动 -->
          <van-swipe
            class="swiper-container"
            :loop="false"
            :width="
              card_list.length > 2 ? `${(305 * windowWidth) / 375}` : 'auto'
            "
          >
            <van-swipe-item
              class="swiper-slide"
              v-for="(item, index) in game_news"
              :key="index"
            >
              <div class="slide-item-container">
                <div class="slide-item">
                  <img class="item-pic" :src="item.titlepic" alt="" />
                  <div class="info">
                    <div class="name">
                      <span>{{ item.main_title }}</span>
                      <div class="discount">{{ item.subtitle }}</div>
                    </div>
                    <div class="desc">
                      <div class="star">
                        <i></i>
                        <span>{{ item.rating.rating }}</span>
                      </div>
                      <div class="type">{{ item.type[0] }}</div>
                    </div>
                    <div class="tags">
                      <span
                        v-for="(tag, tagIndex) in item.app_tag"
                        :key="tagIndex"
                        >{{ tag.name }}</span
                      >
                    </div>
                  </div>
                </div>
                <div class="slide-bottom" v-if="item.news">
                  <div
                    class="fanli-content"
                    @click.stop="goToFanliDetail(item)"
                  >
                    <div class="fanli-title">
                      {{ item.news.title }}
                    </div>
                    <div class="fanli-desc">
                      {{ item.news.time_text }}
                    </div>
                  </div>
                  <div class="get-btn" @click.stop="clickMoreFanli(item)">
                    查看更多
                  </div>
                </div>
              </div>
            </van-swipe-item>
          </van-swipe>
        </div>
      </div>
      <div class="copy-right">本活动最终解释权归官方所有</div>
    </div>

    <!-- 福袋弹窗 -->
    <van-popup
      v-model="fudaiPopup"
      :lock-scroll="false"
      :close-on-click-overlay="false"
      class="popup"
      @closed="onFudaiPopupClosed"
    >
      <div class="popup-close" @click="fudaiPopup = false"></div>
      <div class="fudai-popup-container">
        <div class="open-container">
          <template v-if="bag_result_info">
            <div class="open-title">
              <img :src="bag_result_info.open_icon" alt="" />
            </div>
            <div class="result-line" v-if="bag_result_info.award_info">
              <div class="lantern" v-if="bag_result_info.award_info.num">
                <div class="lantern-icon">
                  <img
                    src="@/assets/images/spring-activity/fudai-lantern-icon.png"
                    alt=""
                  />
                </div>
                <img
                  class="fudai-x"
                  src="@/assets/images/spring-activity/fudai-num-x.png"
                  alt=""
                />
                <div class="fudai-num">
                  <img
                    :src="item"
                    alt=""
                    v-for="(item, index) in numberToArray(
                      bag_result_info.award_info.num,
                    )"
                    :key="index"
                  />
                </div>
              </div>
              <div class="gold" v-if="bag_result_info.award_info.gold">
                <div class="gold-icon">
                  <img
                    src="@/assets/images/spring-activity/fudai-gold-icon.png"
                    alt=""
                  />
                </div>
                <img
                  class="fudai-x"
                  src="@/assets/images/spring-activity/fudai-num-x.png"
                  alt=""
                />
                <div class="fudai-num">
                  <img
                    :src="item"
                    alt=""
                    v-for="(item, index) in numberToArray(
                      bag_result_info.award_info.gold,
                    )"
                    :key="index"
                  />
                </div>
              </div>
            </div>
            <div class="ad-line">{{ bag_result_info.award_desc }}</div>
          </template>
          <div class="fudai-card">
            <div class="empty" v-if="remain_count == 0">
              <img
                class="empty-icon"
                src="@/assets/images/spring-activity/fudai-empty.png"
                alt=""
              />
              <div class="empty-text">恭喜您，已开启今日所有福袋啦~</div>
            </div>
            <template v-else>
              <div
                class="refresh-btn"
                @click="clickRefresh"
                v-if="remain_count > 1 && remain_count != 5"
              >
                刷新福袋
              </div>
              <div class="refresh-btn hide" v-else></div>
              <div class="title">{{ fudaiCardInfo.condition }}</div>
              <div class="share" v-if="fudaiCardInfo.is_share">
                需好友浏览活动后才可获得奖励
              </div>
              <div class="fudai-icon">
                <img :src="fudaiCardInfo.icon" alt="" />
              </div>
              <div class="fudai-name">
                <img :src="fudaiCardInfo.title_icon" alt="" />
              </div>
              <div class="desc">{{ fudaiCardInfo.award_desc }}</div>
            </template>
            <div
              class="btn-desc"
              v-if="
                fudaiCardInfo.lucky_status != 1 && fudaiCardInfo.is_share != 1
              "
            >
              活动期间充值平台币,1平台币=1福气值
            </div>
            <div
              class="fudai-btn tomorrow"
              v-if="remain_count == 0"
              @click="fudaiPopup = false"
            ></div>
            <div
              class="fudai-btn open"
              v-else-if="fudaiCardInfo.lucky_status == 1"
              @click="clickOpenFudai"
            ></div>
            <div
              class="fudai-btn share"
              v-else-if="fudaiCardInfo.is_share == 1"
              @click="handleShare"
            ></div>
            <div
              class="fudai-btn recharge"
              v-else
              @click="clickRechargePtb"
            ></div>
          </div>
        </div>
      </div>
    </van-popup>

    <!-- 宝箱确认弹窗 -->
    <van-popup
      v-model="boxConfirmPopup"
      :lock-scroll="false"
      :close-on-click-overlay="false"
      class="popup"
    >
      <div class="popup-close" @click="boxConfirmPopup = false"></div>
      <div class="popup-container">
        <div class="popup-title">
          <img src="@/assets/images/spring-activity/wenxintishi.png" alt="" />
        </div>
        <div class="desc">
          开启{{ selectedBox.title }}可随机获得{{ selectedBox.desc }}个金币哦~
        </div>
        <div class="box-tips" v-if="selectedBox.status">
          注：开启后将清空剩余灯笼，进入下一轮宝箱兑换
        </div>
        <div class="bottom-operation">
          <template v-if="selectedBox.status">
            <div class="cancel" @click="boxConfirmPopup = false"></div>
            <div class="confirm" @click="confirmOpenBox"></div>
          </template>
          <div v-else class="ok" @click="boxConfirmPopup = false"></div>
        </div>
      </div>
    </van-popup>

    <!-- 宝箱结果弹窗 -->
    <van-popup
      v-model="boxResultPopup"
      :lock-scroll="false"
      :close-on-click-overlay="false"
      class="popup"
    >
      <div class="popup-close" @click="boxResultPopup = false"></div>
      <div class="popup-container">
        <div class="box-icon">
          <img :src="resultBoxInfo.icon" alt="" />
        </div>
        <div class="box-desc">恭喜开启{{ resultBoxInfo.title }}</div>
        <div class="box-gold">
          获得 <span>{{ resultBoxInfo.gold }}</span> 金币
        </div>
        <div class="bottom-operation">
          <div class="ok" @click="boxResultPopup = false"></div>
        </div>
      </div>
    </van-popup>

    <!-- 已开箱记录 -->
    <van-popup
      v-model="recordPopup"
      :lock-scroll="false"
      :close-on-click-overlay="false"
      class="popup"
    >
      <div class="popup-close" @click="recordPopup = false"></div>
      <div class="rule-container">
        <div class="section-container record-container">
          <div class="content-container">
            <div class="record-title">
              <img
                src="@/assets/images/spring-activity/spring-record-title.png"
                alt=""
              />
            </div>
            <div class="record-empty" v-if="!recordList.length">
              暂无开箱记录
            </div>
            <div class="record-list" v-else>
              <div
                class="record-item"
                v-for="(item, index) in recordList"
                :key="index"
              >
                <div class="first-line">
                  <div class="left">{{ item.title }}</div>
                  <div class="right">{{ item.num }}</div>
                </div>
                <div class="second-line">
                  <div class="date">{{ item.date }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </van-popup>

    <!-- 活动规则 -->
    <van-popup
      v-model="rulePopup"
      :lock-scroll="false"
      :close-on-click-overlay="false"
      class="popup"
    >
      <div class="popup-close" @click="rulePopup = false"></div>
      <div class="rule-container">
        <div class="section-container">
          <div class="content-container">
            <div class="rule-title"></div>
            <div class="rule-content">
              <div class="h">1.活动时间</div>
              <p> 活动开启时间：2024年02月09日0时0分0秒 </p>
              <p>活动结束时间：2024年02月17日23时59分59秒</p>
              <div class="h">2.如何开启福袋</div>
              <p>
                活动期间内：充值平台币即可1:1获得福气值（1平台币=1福气值），使用福气值即可开启各种福袋获取奖励<span>（福气值每日清空）</span>。
              </p>
              <div class="h">3.刷新福袋</div>
              <p>
                活动期间每日可获得3个新春福袋，可通过刷新福袋获取稀有的福袋来获取更高的奖励，玩家每天均可直接获取一个每日签到福袋（普通），及活动分享福袋（普通）。
              </p>
              <div class="h">4.宝箱兑换</div>
              <p>
                在兑换宝箱后会清空当前剩余灯笼数并且开启新一轮的宝箱兑换。
              </p>
              <div class="h">5.注意事项</div>
              <p>
                活动期间充值完成后请返回本活动页面领取奖励，请及时兑换奖励，活动结束后将清空所有灯笼。
              </p>
              <p>
                温馨提示:由于游戏厂商的要求，部分游戏内充值暂不支持使用金币支付，请先确定需要用金币充值的游戏在不在可使用名单内。<span
                  class="bold"
                  @click="game_dialog_popup = true"
                  >查看名单></span
                >
              </p>
            </div>
          </div>
        </div>
      </div>
    </van-popup>

    <noGameList
      :game_dialog_show="game_dialog_popup"
      @changeGameDialogShow="changeGameDialogShow"
    />
    <ptb-new-popup @success="getIndexData"></ptb-new-popup>
  </div>
</template>

<script>
import { remNumberLess } from '@/common/styles/_variable.less';
import { mapMutations, mapGetters } from 'vuex';
import {
  platform,
  boxInit,
  BOX_login,
  BOX_goToGame,
  BOX_goToGameCoupon,
  BOX_goToGameFanli,
  BOX_goToFanliDetails,
} from '@/utils/box.uni.js';
import { mapActions } from 'vuex';
import noGameList from '@/components/no-game-list';
import {
  ApiDragonYearIndex,
  ApiDragonYearRefreshBagId,
  ApiDragonYearTaskBox,
  ApiDragonYearTaskLuckyBag,
  ApiDragonYearBoxLog,
} from '@/api/views/spring_activity.js';
import { ApiCommonShareInfo } from '@/api/views/system.js';
import ptbNewPopup from '@/components/ptb-new-popup';

import fudaiNum1 from '@/assets/images/spring-activity/fudai-num-1.png';
import fudaiNum2 from '@/assets/images/spring-activity/fudai-num-2.png';
import fudaiNum3 from '@/assets/images/spring-activity/fudai-num-3.png';
import fudaiNum4 from '@/assets/images/spring-activity/fudai-num-4.png';
import fudaiNum5 from '@/assets/images/spring-activity/fudai-num-5.png';
import fudaiNum6 from '@/assets/images/spring-activity/fudai-num-6.png';
import fudaiNum7 from '@/assets/images/spring-activity/fudai-num-7.png';
import fudaiNum8 from '@/assets/images/spring-activity/fudai-num-8.png';
import fudaiNum9 from '@/assets/images/spring-activity/fudai-num-9.png';
import fudaiNum0 from '@/assets/images/spring-activity/fudai-num-0.png';

export default {
  components: {
    ptbNewPopup,
    noGameList,
  },
  data() {
    return {
      fudaiNum1,
      fudaiNum2,
      fudaiNum3,
      fudaiNum4,
      fudaiNum5,
      fudaiNum6,
      fudaiNum7,
      fudaiNum8,
      fudaiNum9,
      fudaiNum0,
      remNumberLess,
      navbarOpacity: 0,

      activity_status: 3, // 活动状态 1活动已开始 2 活动不存在 3活动未开始 4活动已结束

      bag_info: {}, // 福袋信息
      bag_result_info: null, // 福袋开启的结果

      box_list: [],
      card_list: [],
      coupon_list: [],
      game_news: [],
      lantern_num: 0, // 灯笼数量
      lucky_num: 0, // 福利值
      remain_count: 5, // 剩余可领福袋的数量
      round: 1, // 第几轮

      fudaiPopup: false, // 福袋弹窗

      boxConfirmPopup: false, // 宝箱确认弹窗
      selectedBox: {}, // 选择预开启的宝箱
      confirmLaternPopup: false, // 确认开启宝箱的弹窗
      boxResultPopup: false, // 开启宝箱结果弹窗
      resultBoxInfo: {}, // 宝箱结果信息

      rulePopup: false,
      game_dialog_popup: false,

      shareInfo: {},

      recordPopup: false,
      recordList: [],
    };
  },
  computed: {
    ...mapGetters({
      initData: 'system/initData',
    }),
    windowWidth() {
      return Math.min(document.body.clientWidth, 450);
    },
    activity_status_text() {
      const arr = [
        '活动已开始！',
        '活动不存在！',
        '活动未开始！',
        '活动已结束！',
      ];
      return arr[this.activity_status - 1];
    },
    fudaiCardInfo() {
      return this.bag_info;
    },
    cardList() {
      // 将card_list分成每组2项的二维数组
      return this.card_list.reduce((acc, item, index) => {
        if (index % 2 === 0) {
          acc.push(this.card_list.slice(index, index + 2));
        }
        return acc;
      }, []);
    },
  },
  created() {
    window.addEventListener('scroll', this.handleScroll);
  },
  async mounted() {
    // 官包触发页面刷新（将onResume挂载在window上）
    if (platform == 'android') {
      window.onResume = this.onResume;
    }
    this.$toast.loading('加载中');
    // 页面数据初始化
    await this.$onAppFinished;
    try {
      await this.init();
    } finally {
      this.$toast.clear();
    }
  },

  beforeDestroy() {
    // 清除滚动事件监听
    window.removeEventListener('scroll', this.handleScroll);
  },

  methods: {
    ...mapActions({
      SET_USER_INFO: 'user/SET_USER_INFO',
    }),
    handleScroll() {
      let scrollTop =
        document.documentElement.scrollTop ||
        window.pageYOffset ||
        document.body.scrollTop;
      if (Math.abs(scrollTop) > 10) {
        this.navbarOpacity = Math.abs(scrollTop) / 120;
      } else {
        this.navbarOpacity = 0;
      }
    },
    async onResume() {
      this.$toast.loading('加载中');
      await boxInit();
      await this.SET_USER_INFO(true);
      await this.init();
      this.$toast.clear();
    },
    async init() {
      await this.getIndexData();
      await this.getShareInfo();
    },
    // 点击main-btn 福袋按钮
    async clickFudaiBtn() {
      if (!this.userInfo.token) {
        this.login();
        return false;
      }
      if (this.activity_status != 1) {
        this.$toast(this.activity_status_text);
        return false;
      }
      if (
        [11, 12].includes(this.bag_info.bag_id) &&
        this.bag_info.lucky_status == 1
      ) {
        // 签到和分享直接开启福袋
        await this.handleFudaiOpen();
      }

      this.fudaiPopup = true; // 开启福袋弹窗
    },
    // 点击弹窗内的开启福袋
    async clickOpenFudai() {
      this.fudaiPopup = false; // 关闭福袋弹窗
      await this.handleFudaiOpen(); // 开启福袋
      this.fudaiPopup = true; // 再次开启福袋弹窗
    },
    async handleFudaiOpen() {
      this.$toast.loading('刷新中...');
      try {
        const res = await ApiDragonYearTaskLuckyBag({
          bag_id: this.bag_info.bag_id,
          round: this.round,
        });
        this.bag_info = res.data.next_info;
        this.bag_result_info = {
          ...res.data.info,
          award_desc: res.data.award_desc,
          award_info: res.data.award_info,
        };
      } finally {
        this.$toast.clear();
        await this.getIndexData();
      }
    },
    onFudaiPopupClosed(e) {
      this.bag_result_info = null;
    },
    // 点击开宝箱
    clickOpenBox(item) {
      if (!this.userInfo.token) {
        this.login();
        return false;
      }
      if (this.activity_status != 1) {
        this.$toast(this.activity_status_text);
        return false;
      }
      this.selectedBox = item;
      this.boxConfirmPopup = true;
    },
    // 确认开启宝箱
    async confirmOpenBox() {
      try {
        this.$toast.loading('开启中...');
        const res = await ApiDragonYearTaskBox({
          round: this.round,
          box_id: this.selectedBox.box_id,
        });
        this.boxConfirmPopup = false;
        this.resultBoxInfo = res.data.box_info;
        this.boxResultPopup = true;
      } finally {
        this.$toast.clear();
        this.getIndexData();
      }
    },
    // 点击刷新按钮
    async clickRefresh() {
      this.$toast.loading('刷新中...');
      await this.handleRefresh();
      this.$toast('福袋刷新成功');
    },
    async handleRefresh() {
      const res = await ApiDragonYearRefreshBagId({
        bag_id: this.bag_info.bag_id,
      });
      this.bag_info = res.data.bag_info;
    },
    // 获取首页数据
    async getIndexData() {
      const res = await ApiDragonYearIndex();
      this.activity_status = res.data.activity_status;
      this.bag_info = res.data.bag_info;
      this.box_list = res.data.box_list;
      this.card_list = res.data.card_list;
      this.coupon_list = res.data.coupon_list;
      this.game_news = res.data.game_news;
      this.lantern_num = res.data.lantern_num;
      this.lucky_num = res.data.lucky_num;
      this.remain_count = res.data.remain_count;
      this.round = res.data.round;
    },
    clickRechargePtb() {
      if (!this.userInfo.token) {
        this.login();
        return false;
      }
      if (this.activity_status != 1) {
        this.$toast(this.activity_status_text);
        return false;
      }
      this.setShowPtbRechargePopup(true);
    },
    async getRecordLog() {
      const res = await ApiDragonYearBoxLog();
      this.recordList = res.data.list;
    },
    // 登录
    login() {
      BOX_login();
    },
    // 点击领取游戏648礼包
    goToGame(item) {
      BOX_goToGame(
        {
          params: {
            id: item.game.id,
            gameInfo: item.game,
          },
        },
        { id: item.game.id },
      );
    },
    // 点击领取游戏648代金券
    takeCoupon(item) {
      BOX_goToGameCoupon(
        {
          params: {
            game_id: item.id,
          },
        },
        {
          game_id: item.id,
          class_id: item.classid,
        },
      );
    },
    // 点击返利详情
    goToFanliDetail(item) {
      BOX_goToFanliDetails(
        {
          params: {
            title: item.news.title,
            url: item.news.titleurl,
          },
        },
        {
          url: item.news.titleurl,
          news_id: item.news.id,
          title: item.news.title,
          apply_type: item.news.apply_type.type,
        },
      );
    },
    // 点击更多返利
    clickMoreFanli(item) {
      BOX_goToGameFanli(
        {
          params: {
            game_id: item.id,
            game_info: {
              ...item,
            },
          },
        },
        {
          game_id: item.id,
          class_id: item.classid,
        },
      );
    },
    async openRecordPopup() {
      if (this.activity_status == 2 || this.activity_status == 3) {
        this.$toast(this.activity_status_text);
        return false;
      }
      if (!this.userInfo.token) {
        this.login();
        return false;
      }
      this.recordPopup = true;
      await this.getRecordLog();
    },
    changeGameDialogShow(show) {
      this.game_dialog_popup = show;
    },
    async getShareInfo() {
      if (this.userInfo.user_id) {
        const res = await ApiCommonShareInfo({
          type: 5,
          id: this.userInfo.user_id,
        });
        this.shareInfo = res.data;
      }
    },
    handleShare() {
      if (this.initData?.share_info?.length) {
        window.BOX.mobShare(5, this.userInfo.user_id);
      } else {
        this.$copyText(this.shareInfo.share_text + this.shareInfo.url).then(
          res => {
            this.$toast('链接已复制到剪贴板，快去邀请好友吧~');
          },
          err => {
            this.$dialog.alert({
              message: '复制失败',
              lockScroll: false,
            });
          },
        );
      }
    },
    numberToArray(num) {
      // 将一个多位数字，分割成数组
      return (num + '').split('').map(item => {
        switch (item) {
          case '0':
            return this.fudaiNum0;
          case '1':
            return this.fudaiNum1;
          case '2':
            return this.fudaiNum2;
          case '3':
            return this.fudaiNum3; // 这里使用的是字符串，所以需要用""包裹
          case '4':
            return this.fudaiNum4;
          case '5':
            return this.fudaiNum5;
          case '6':
            return this.fudaiNum6;
          case '7':
            return this.fudaiNum7;
          case '8':
            return this.fudaiNum8;
          case '9':
            return this.fudaiNum9;
        }
      });
    },
    ...mapMutations({
      setShowSvipRechargePopup: 'recharge/setShowSvipRechargePopup',
      setShowPtbRechargePopup: 'recharge/setShowPtbRechargePopup',
    }),
  },
};
</script>

<style lang="less" scoped>
.laba-activity {
  width: 100%;
  min-height: 100vh;
  background-color: #f76d48;
  padding-bottom: 38 * @rem;
  .back {
    width: 28 * @rem;
    height: 28 * @rem;
    margin-left: 20 * @rem;
    border-radius: 15 * @rem;
    background: rgba(0, 0, 0, 0.3) url(~@/assets/images/nav-bar-back-white.png)
      center center no-repeat;
    background-size: 8 * @rem 14 * @rem;
  }
  .main {
    .top-section {
      overflow: hidden;
      width: 100%;
      height: 435 * @rem;
      background: url(~@/assets/images/spring-activity/spring-top-bg-1.png)
        center center no-repeat;
      background-size: 100% 435 * @rem;
      .top-line {
        display: flex;
        box-sizing: border-box;
        padding: 0 15 * @rem 0 0;
        width: 100%;
        height: 50 * @rem;
        position: fixed;
        max-width: 450px;
        z-index: 999;
        top: @safeAreaTop;
        top: @safeAreaTopEnv;
        left: 50%;
        transform: translateX(-50%);
        display: flex;
        align-items: center;
        .user-container {
          box-sizing: border-box;
          height: 28 * @rem;
          margin-left: 11 * @rem;
          color: #fff;
          line-height: 28 * @rem;
          background: rgba(255, 255, 255, 0.2);
          border-radius: 15 * @rem;
          overflow: hidden;
          .user {
            display: flex;
            .nickname {
              box-sizing: border-box;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              padding: 0 15 * @rem 0 10 * @rem;
              text-align: center;
              line-height: 28 * @rem;
            }
          }
          .avatar {
            width: 28 * @rem;
            height: 28 * @rem;
            border-radius: 50%;
          }
        }
        .fudai-fixed {
          box-sizing: border-box;
          margin-left: auto;
          width: 112 * @rem;
          height: 32 * @rem;
          background: url(~@/assets/images/spring-activity/fudai-fixed-bg.png)
            center center no-repeat;
          background-size: 112 * @rem 32 * @rem;
          line-height: 32 * @rem;
          font-size: 12 * @rem;
          color: #ffffff;
          padding-left: 30 * @rem;
          padding-right: 5 * @rem;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          text-align: center;
        }
      }
      .rule-btn {
        box-sizing: border-box;
        width: 26 * @rem;
        height: 86 * @rem;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-wrap: wrap;
        background: url(~@/assets/images/spring-activity/spring-rule-btn.png)
          center center no-repeat;
        background-size: 26 * @rem 86 * @rem;
        font-size: 13 * @rem;
        color: #ffffff;
        padding: 4 * @rem;
        border-radius: 6 * @rem 0 0 6 * @rem;
        position: absolute;
        right: 0;
        top: 92 * @rem;
        text-align: center;
      }
      .activity-time {
        width: fit-content;
        height: 27 * @rem;
        line-height: 27 * @rem;
        text-align: center;
        font-size: 13 * @rem;
        color: #ffffff;
        border-radius: 14 * @rem;
        background-color: rgba(0, 0, 0, 0.35);
        margin: 208 * @rem auto 0;
        padding: 0 8 * @rem;
      }
    }
  }
  .top-section-2 {
    overflow: hidden;
    width: 100%;
    height: 288 * @rem;
    background: url(~@/assets/images/spring-activity/spring-top-bg-2.png) center
      center no-repeat;
    background-size: 100% 288 * @rem;
    margin-top: -1 * @rem;
    .main-btn {
      width: 220 * @rem;
      height: 70 * @rem;
      margin: 188 * @rem auto 0;
      background: url(~@/assets/images/spring-activity/main-btn.png) center
        center no-repeat;
      background-size: 220 * @rem 70 * @rem;
      &.open {
        background-image: url(~@/assets/images/spring-activity/main-btn-open.png);
      }
      &.had {
        background-image: url(~@/assets/images/spring-activity/main-btn-had.png);
      }
    }
  }
  .fudai-container {
    box-sizing: border-box;
    width: 347 * @rem;
    margin: 0 auto;
    position: relative;
    .refresh-btn {
      height: 20 * @rem;
      width: fit-content;
      position: absolute;
      bottom: 16 * @rem;
      left: 50%;
      transform: translateX(-50%);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 4;
      cursor: pointer;
      img {
        display: block;
        width: 20 * @rem;
        height: 20 * @rem;
      }
      span {
        font-size: 14 * @rem;
        font-weight: 600;
        color: #f76d48;
        margin-left: 11 * @rem;
        line-height: 20 * @rem;
      }
    }
    &::before {
      content: '';
      position: relative;
      z-index: 2;
      display: block;
      width: 347 * @rem;
      height: 92 * @rem;
      background: url(~@/assets/images/spring-activity/spring-fudai-bar-1.png)
        center top no-repeat;
      background-size: 347 * @rem 92 * @rem;
    }
    &::after {
      content: '';
      position: relative;
      z-index: 2;
      display: block;
      width: 347 * @rem;
      height: 78 * @rem;
      background: url(~@/assets/images/spring-activity/spring-fudai-bar-3.png)
        center bottom no-repeat;
      background-size: 347 * @rem 78 * @rem;
      z-index: 1;
    }
    .section-content {
      position: relative;
      background: url(~@/assets/images/spring-activity/spring-fudai-bar-2.png)
        center center repeat;
      background-size: 347 * @rem 25 * @rem;
      position: relative;
      padding: 0.5 * @rem 20 * @rem;
      margin-top: -8 * @rem;
      margin-bottom: -16 * @rem;
      .content-container {
        position: relative;
        z-index: 99;
        .empty {
          height: 60 * @rem;
          text-align: center;
          line-height: 50 * @rem;
          font-size: 14 * @rem;
          color: #ffffff;
          font-weight: 600;
        }
        .title {
          padding: 0 30 * @rem;
          width: fit-content;
          height: 20 * @rem;
          font-size: 16 * @rem;
          color: #ffffff;
          font-weight: 600;
          margin: 0 auto;
          position: relative;
          margin-bottom: 15 * @rem;
          &::before {
            content: '';
            position: absolute;
            width: 9 * @rem;
            height: 9 * @rem;
            background: url(~@/assets/images/spring-activity/fudai-dot.png)
              center center no-repeat;
            background-size: 9 * @rem 9 * @rem;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
          }
          &::after {
            content: '';
            position: absolute;
            width: 9 * @rem;
            height: 9 * @rem;
            background: url(~@/assets/images/spring-activity/fudai-dot.png)
              center center no-repeat;
            background-size: 9 * @rem 9 * @rem;
            right: 0;
            top: 50%;
            transform: translateY(-50%);
          }
        }
        .content {
          font-size: 11 * @rem;
          color: #ffffff;
          line-height: 22 * @rem;
        }
        .tips {
          padding: 0 7 * @rem 0 22 * @rem;
          height: 22 * @rem;
          width: fit-content;
          border-radius: 11 * @rem;
          background: #ffdfb9
            url(~@/assets/images/spring-activity/fudai-tip.png) 7 * @rem center
            no-repeat;
          background-size: 10 * @rem 10 * @rem;
          margin: 9 * @rem auto 0;
          line-height: 22 * @rem;
          font-size: 11 * @rem;
          color: #de4848;
        }
      }
    }
  }

  .kbx-container {
    box-sizing: border-box;
    width: 347 * @rem;
    margin: 24 * @rem auto 0;
    position: relative;
    &::before {
      content: '';
      position: relative;
      z-index: 2;
      display: block;
      width: 347 * @rem;
      height: 55 * @rem;
      background: url(~@/assets/images/spring-activity/spring-kbx-1.png) center
        top no-repeat;
      background-size: 347 * @rem 55 * @rem;
    }
    &::after {
      content: '';
      position: relative;
      z-index: 2;
      display: block;
      width: 347 * @rem;
      height: 28 * @rem;
      background: url(~@/assets/images/spring-activity/spring-kbx-3.png) center
        bottom no-repeat;
      background-size: 347 * @rem 28 * @rem;
      z-index: 1;
    }
    .section-content {
      position: relative;
      background: url(~@/assets/images/spring-activity/spring-kbx-2.png) center
        center repeat;
      background-size: 347 * @rem 19 * @rem;
      position: relative;
      padding: 0.5 * @rem 0;
      margin-top: -46 * @rem;
      margin-bottom: -1 * @rem;
      .content-container {
        position: relative;
        z-index: 99;
        .top-bar {
          display: flex;
          align-items: center;
          .section-title {
            width: 220 * @rem;
            height: 38 * @rem;
            background: url(~@/assets/images/spring-activity/kbx-title-bg.png)
              center top no-repeat;
            background-size: 220 * @rem 38 * @rem;
            margin-left: 12 * @rem;
          }
          .lantern-num {
            box-sizing: border-box;
            width: 88 * @rem;
            height: 29 * @rem;
            background: url(~@/assets/images/spring-activity/lantern-icon.png)
              center top no-repeat;
            background-size: 88 * @rem 29 * @rem;
            margin-left: 4 * @rem;
            line-height: 29 * @rem;
            padding: 0 5 * @rem 0 30 * @rem;
            text-align: center;
            white-space: nowrap;
            font-size: 14 * @rem;
            color: #c54624;
            font-weight: 600;
          }
        }
        .title-bar {
          width: 302 * @rem;
          height: 30 * @rem;
          background: url(~@/assets/images/spring-activity/kbx-list-title.png)
            center top no-repeat;
          background-size: 302 * @rem 30 * @rem;
          font-size: 14 * @rem;
          color: #ffffff;
          font-weight: 600;
          text-align: center;
          line-height: 30 * @rem;
          margin: 0 auto;
          position: relative;
          margin: 21 * @rem auto 0;
        }
        .title-tips {
          font-size: 11 * @rem;
          color: #a15858;
          text-align: center;
          line-height: 14 * @rem;
          margin-top: 10 * @rem;
          padding: 0 20 * @rem;
        }
        .kbx-list {
          display: flex;
          flex-wrap: wrap;
          justify-content: space-between;
          margin-top: 10 * @rem;
          padding: 0 9 * @rem;
          .kbx-item {
            box-sizing: border-box;
            width: 105 * @rem;
            height: 120 * @rem;
            background-color: #fef8f1;
            margin-top: 10 * @rem;
            border-radius: 6 * @rem;
            overflow: hidden;
            .box-top {
              box-sizing: border-box;
              height: 73 * @rem;
              padding-top: 7 * @rem;
              background: linear-gradient(
                180deg,
                rgba(255, 215, 144, 0.3) 0%,
                rgba(255, 200, 102, 0.3) 100%
              );
              .box-icon {
                width: 56 * @rem;
                height: 42 * @rem;
                margin: 0 auto;
              }
              .box-name {
                font-size: 11 * @rem;
                color: #a15858;
                line-height: 14 * @rem;
                font-weight: 600;
                text-align: center;
                white-space: nowrap;
                margin-top: 4 * @rem;
              }
            }
            .box-bottom {
              box-sizing: border-box;
              padding: 5 * @rem;
              .cost-title {
                font-size: 12 * @rem;
                color: #a15858;
                line-height: 15 * @rem;
              }
              .cost-bar {
                display: flex;
                justify-content: space-between;
                align-items: center;
                .cost-num {
                  font-size: 12 * @rem;
                  color: #f76d48;
                }
                .box-btn {
                  width: 50 * @rem;
                  height: 20 * @rem;
                  background: url(~@/assets/images/spring-activity/spring-box-btn.png)
                    center top no-repeat;
                  background-size: 50 * @rem 20 * @rem;
                  &.no {
                    background-image: url(~@/assets/images/spring-activity/spring-box-btn-no.png);
                  }
                }
              }
            }
          }
        }
        .content {
          font-size: 11 * @rem;
          color: #ffffff;
          line-height: 22 * @rem;
          margin-top: 15 * @rem;
        }
        .tips {
          padding: 0 7 * @rem 0 22 * @rem;
          height: 22 * @rem;
          width: fit-content;
          border-radius: 11 * @rem;
          background: #ffdfb9
            url(~@/assets/images/spring-activity/fudai-tip.png) 7 * @rem center
            no-repeat;
          background-size: 10 * @rem 10 * @rem;
          margin: 9 * @rem auto 0;
          line-height: 22 * @rem;
          font-size: 11 * @rem;
          color: #de4848;
        }
      }
    }
  }

  .more-container {
    .more-title {
      width: 313 * @rem;
      height: 25 * @rem;
      margin: 24 * @rem auto 25 * @rem;
      background: url(~@/assets/images/spring-activity/more-welfare-title.png)
        center center no-repeat;
      background-size: 313 * @rem 25 * @rem;
    }
    .more-section {
      background: linear-gradient(180deg, #fefffa 14%, #fefffa 100%);
      border: 1 * @rem solid #f8d4a1;
      border-radius: 20 * @rem;
      min-height: 100 * @rem;
      width: 345 * @rem;
      margin: 20 * @rem auto 0;
      padding-top: 9 * @rem;
      .section-title {
        height: 38 * @rem;
        background-size: auto 38 * @rem;
        background-position: left center;
        background-repeat: no-repeat;
        margin-left: 12 * @rem;
      }
      &.gift {
        .section-title {
          background-image: url(~@/assets/images/spring-activity/gift-title-bg.png);
        }
      }
      &.coupon {
        .section-title {
          background-image: url(~@/assets/images/spring-activity/coupon-title-bg.png);
        }
      }
      &.activity {
        .section-title {
          background-image: url(~@/assets/images/spring-activity/activity-title-bg.png);
        }
      }
      .swiper-container {
        padding-bottom: 28 * @rem;
        margin-top: 11 * @rem;
        /deep/ .van-swipe__indicator {
          background-color: #d9d9d9;
          width: 5 * @rem;
          height: 5 * @rem;
          border-radius: 5 * @rem;
          transition: all 0.3s;
        }
        /deep/ .van-swipe__indicator--active {
          background-color: #ff6649;
          width: 15 * @rem;
        }
      }
      .swiper-slide {
        &:last-of-type {
          box-sizing: border-box;
          padding-right: 10 * @rem;
        }
        .slide-list {
          box-sizing: border-box;
          padding-left: 8 * @rem;
          .slide-item {
            display: flex;
            align-items: center;
            width: 100%;
            height: 84 * @rem;
            background: #fff5ec;
            border-radius: 8 * @rem;
            padding: 10 * @rem;
            margin-bottom: 10 * @rem;
            box-sizing: border-box;

            &:last-of-type {
              margin-bottom: 0;
            }

            .item-pic {
              display: block;
              flex-shrink: 0;
              width: 56 * @rem;
              height: 56 * @rem;
              margin-right: 8 * @rem;
            }

            .info {
              flex: 1;
              min-width: 0;

              .name {
                display: flex;
                align-items: center;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;

                span {
                  height: 20 * @rem;
                  font-size: 14 * @rem;
                  font-weight: 500;
                  color: #333333;
                  line-height: 20 * @rem;
                }

                .discount {
                  flex-shrink: 0;
                  height: 17 * @rem;
                  line-height: 17 * @rem;
                  background: #ffffff;
                  border-radius: 4 * @rem;
                  border: 1 * @rem solid rgba(0, 0, 0, 0.05);
                  padding: 0 4 * @rem;
                  color: #808080;
                  text-align: center;
                  font-size: 10 * @rem;
                  margin-left: 8 * @rem;
                  white-space: nowrap;
                  overflow: hidden;
                }
              }

              .desc {
                display: flex;
                align-items: center;
                height: 15 * @rem;
                margin-top: 6 * @rem;
                font-size: 10 * @rem;
                font-weight: 400;
                color: #808080;
                line-height: 15 * @rem;
                overflow: hidden;

                .star {
                  display: flex;
                  align-items: center;
                  flex-shrink: 0;
                  height: 15 * @rem;
                  font-size: 12 * @rem;
                  font-weight: 600;
                  color: #fe6600;
                  line-height: 15 * @rem;
                  margin-right: 8 * @rem;

                  i {
                    display: block;
                    width: 10 * @rem;
                    height: 10 * @rem;
                    background: url(~@/assets/images/star.png) no-repeat;
                    background-size: 10 * @rem 10 * @rem;
                    margin-right: 4 * @rem;
                    flex-shrink: 0;
                    margin-top: -2 * @rem;
                  }
                }
                .type {
                  flex-shrink: 0;
                }
                .play-count {
                  margin-left: 6 * @rem;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                }
              }

              .tags {
                display: flex;
                flex-wrap: wrap;
                align-items: center;
                margin-top: 6 * @rem;
                height: 19 * @rem;
                overflow: hidden;

                span {
                  flex-shrink: 0;
                  height: 17 * @rem;
                  line-height: 17 * @rem;
                  background: #ffffff;
                  border-radius: 4 * @rem;
                  border: 1 * @rem solid rgba(0, 0, 0, 0.05);
                  padding: 0 4 * @rem;
                  font-size: 10 * @rem;
                  font-weight: 400;
                  color: #808080;
                  margin-right: 8 * @rem;

                  &:first-of-type {
                    background-color: #ffffff;
                    border-color: #ff7474;
                    color: #ff6649;
                  }
                }
              }
            }

            .item-right {
              flex-shrink: 0;
              margin-left: 8 * @rem;

              .get-btn {
                display: block;
                width: 58 * @rem;
                height: 28 * @rem;
                line-height: 28 * @rem;
                text-align: center;
                background: #fe6600;
                color: #fff;
                border-radius: 6 * @rem;
                margin: 0 auto;
                position: relative;
                z-index: 1;
              }

              .residue {
                width: 100%;
                height: 17 * @rem;
                font-size: 12 * @rem;
                font-weight: 400;
                color: #777777;
                line-height: 14 * @rem;
                text-align: center;
                margin-top: 14 * @rem;

                span {
                  color: #fe6600;
                }
              }
            }
          }
        }
        .slide-item-container {
          box-sizing: border-box;
          margin-left: 8 * @rem;
          border-radius: 8 * @rem;
          overflow: hidden;
          .slide-item {
            display: flex;
            align-items: center;
            width: 100%;
            height: 84 * @rem;
            background: #fff5ec;
            padding: 10 * @rem;
            box-sizing: border-box;

            &:last-of-type {
              margin-bottom: 0;
            }

            .item-pic {
              display: block;
              flex-shrink: 0;
              width: 56 * @rem;
              height: 56 * @rem;
              margin-right: 8 * @rem;
            }

            .info {
              flex: 1;
              min-width: 0;

              .name {
                display: flex;
                align-items: center;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;

                span {
                  height: 20 * @rem;
                  font-size: 14 * @rem;
                  font-weight: 500;
                  color: #333333;
                  line-height: 20 * @rem;
                }

                .discount {
                  flex-shrink: 0;
                  height: 17 * @rem;
                  line-height: 17 * @rem;
                  background: #ffffff;
                  border-radius: 4 * @rem;
                  border: 1 * @rem solid rgba(0, 0, 0, 0.05);
                  padding: 0 4 * @rem;
                  color: #808080;
                  text-align: center;
                  font-size: 10 * @rem;
                  margin-left: 8 * @rem;
                  white-space: nowrap;
                  overflow: hidden;
                }
              }

              .desc {
                display: flex;
                align-items: center;
                height: 15 * @rem;
                margin-top: 6 * @rem;
                font-size: 10 * @rem;
                font-weight: 400;
                color: #808080;
                line-height: 15 * @rem;
                overflow: hidden;

                .star {
                  display: flex;
                  align-items: center;
                  flex-shrink: 0;
                  height: 15 * @rem;
                  font-size: 12 * @rem;
                  font-weight: 600;
                  color: #fe6600;
                  line-height: 15 * @rem;
                  margin-right: 8 * @rem;

                  i {
                    display: block;
                    width: 10 * @rem;
                    height: 10 * @rem;
                    background: url(~@/assets/images/star.png) no-repeat;
                    background-size: 10 * @rem 10 * @rem;
                    margin-right: 4 * @rem;
                    flex-shrink: 0;
                    margin-top: -2 * @rem;
                  }
                }
                .type {
                  flex-shrink: 0;
                }
                .play-count {
                  margin-left: 6 * @rem;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                }
              }

              .tags {
                display: flex;
                flex-wrap: wrap;
                align-items: center;
                margin-top: 6 * @rem;
                height: 19 * @rem;
                overflow: hidden;

                span {
                  flex-shrink: 0;
                  height: 17 * @rem;
                  line-height: 17 * @rem;
                  background: #ffffff;
                  border-radius: 4 * @rem;
                  border: 1 * @rem solid rgba(0, 0, 0, 0.05);
                  padding: 0 4 * @rem;
                  font-size: 10 * @rem;
                  font-weight: 400;
                  color: #808080;
                  margin-right: 8 * @rem;

                  &:first-of-type {
                    background-color: #ffffff;
                    border-color: #ff7474;
                    color: #ff6649;
                  }
                }
              }
            }

            .item-right {
              flex-shrink: 0;
              margin-left: 8 * @rem;

              .get-btn {
                display: block;
                width: 58 * @rem;
                height: 28 * @rem;
                line-height: 28 * @rem;
                text-align: center;
                background: #fe6600;
                color: #fff;
                border-radius: 6 * @rem;
                margin: 0 auto;
                position: relative;
                z-index: 1;
              }

              .residue {
                width: 100%;
                height: 17 * @rem;
                font-size: 12 * @rem;
                font-weight: 400;
                color: #777777;
                line-height: 14 * @rem;
                text-align: center;
                margin-top: 14 * @rem;

                span {
                  color: #fe6600;
                }
              }
            }
          }
          .slide-bottom {
            box-sizing: border-box;
            width: 100%;
            height: 42 * @rem;
            background: #ffecda;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 10 * @rem 0 16 * @rem;

            .get-btn {
              display: block;
              width: 70 * @rem;
              height: 28 * @rem;
              line-height: 28 * @rem;
              text-align: center;
              background: #fe6600;
              color: #fff;
              border-radius: 6 * @rem;
              margin: 0 auto;
            }
            .fanli-content {
              flex: 1;
              min-width: 0;
              .fanli-title {
                font-size: 12 * @rem;
                color: #444444;
                font-weight: 600;
                line-height: 17 * @rem;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
              }
              .fanli-desc {
                font-size: 11 * @rem;
                line-height: 13 * @rem;
                margin-top: 3 * @rem;
                color: #666666;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
              }
            }
            .residue {
              display: flex;
              align-items: center;
              flex: 1;
              min-width: 0;
              width: 100%;
              height: 42 * @rem;
              font-size: 13 * @rem;
              font-weight: 600;
              color: #32b768;
              line-height: 42 * @rem;
              text-align: center;

              span {
                color: #ff6649;
                margin-left: 6 * @rem;
                font-size: 18 * @rem;
                font-weight: 600;

                em {
                  font-size: 12 * @rem;
                  font-weight: 600;
                  color: #ff6649;
                }
              }

              .coupon-count {
                display: block;
                flex-shrink: 0;
                font-size: 11 * @rem;
                font-weight: 400;
                color: #666666;
                line-height: 15 * @rem;
                margin-left: 4 * @rem;
              }
            }
          }
        }
      }
    }
  }
  .copy-right {
    text-align: center;
    margin: 19 * @rem auto 0;
    font-size: 12 * @rem;
    color: rgba(255, 255, 255, 0.6);
    line-height: 15 * @rem;
  }
}
.popup {
  box-sizing: border-box;
  background: transparent;
  overflow: visible;
  width: 345 * @rem;
  .popup-close {
    width: 27 * @rem;
    height: 27 * @rem;
    margin-left: auto;
    background: url(~@/assets/images/spring-activity/spring-popup-close.png)
      center center no-repeat;
    background-size: 27 * @rem 27 * @rem;
  }
  .fudai-popup-container {
    .open-container {
      .open-title {
        height: 67 * @rem;
        margin: 0 auto;
        img {
          object-fit: contain;
        }
      }
      .result-line {
        height: 42 * @rem;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 20 * @rem;
        .lantern,
        .gold {
          display: flex;
          align-items: center;
          margin: 0 15 * @rem;
          justify-content: center;
          .lantern-icon,
          .gold-icon {
            width: 42 * @rem;
            height: 42 * @rem;
            flex-shrink: 0;
          }
          .fudai-x {
            width: 16 * @rem;
            height: 25 * @rem;
            margin-left: 10 * @rem;
            margin-top: 5 * @rem;
            margin-right: 3 * @rem;
          }
          .fudai-num {
            height: 41 * @rem;
            display: flex;
            align-items: center;
            justify-content: center;
            img {
              flex-grow: 0;
              height: 41 * @rem;
              width: auto;
              object-fit: contain;
            }
          }
        }
      }
      .ad-line {
        text-align: center;
        font-size: 14 * @rem;
        color: #ffda75;
        font-weight: 600;
        margin-top: 20 * @rem;
        margin-bottom: 10 * @rem;
      }
    }
    .fudai-card {
      width: 270 * @rem;
      height: 326 * @rem;
      background: url(~@/assets/images/spring-activity/fudai-card-bg.png) center
        center no-repeat;
      background-size: 270 * @rem 326 * @rem;
      margin: 0 auto;
      position: relative;
      overflow: hidden;
      .empty {
        margin: 57 * @rem auto 0;
        .empty-icon {
          width: 101 * @rem;
          height: 101 * @rem;
          margin: 0 auto;
        }
        .empty-text {
          font-size: 12 * @rem;
          color: #a15858;
          text-align: center;
          line-height: 15 * @rem;
          margin-top: 9 * @rem;
        }
      }
      .refresh-btn {
        width: 50 * @rem;
        height: 23 * @rem;
        background-color: #fc5a27;
        border-radius: 12 * @rem 0 0 12 * @rem;
        line-height: 23 * @rem;
        margin-left: auto;
        font-size: 10 * @rem;
        color: #ffffff;
        margin-top: 26 * @rem;
        margin-right: 8 * @rem;
        text-align: center;
        &.hide {
          opacity: 0;
        }
      }
      .title {
        font-size: 14 * @rem;
        color: #f95724;
        font-weight: 600;
        text-align: center;
      }
      .share {
        font-size: 11 * @rem;
        color: #a15858;
        line-height: 14 * @rem;
        margin-top: 3 * @rem;
        text-align: center;
      }
      .fudai-icon {
        width: 68 * @rem;
        height: 68 * @rem;
        margin: 10 * @rem auto 0;
      }
      .fudai-name {
        height: 14 * @rem;
        margin-top: 6 * @rem;
        img {
          object-fit: contain;
        }
      }
      .desc {
        text-align: center;
        font-size: 11 * @rem;
        color: #a15858;
        line-height: 14 * @rem;
        margin: 9 * @rem 30 * @rem 0;
      }

      .btn-desc {
        width: 100%;
        height: 14 * @rem;
        font-size: 11 * @rem;
        color: #ffffff;
        line-height: 14 * @rem;
        text-align: center;
        position: absolute;
        bottom: 66 * @rem;
        left: 50%;
        transform: translateX(-50%);
        font-weight: 600;
      }
      .fudai-btn {
        width: 156 * @rem;
        height: 35 * @rem;
        background: url(~@/assets/images/spring-activity/fudai-card-btn-open.png)
          center center no-repeat;
        background-size: 156 * @rem 35 * @rem;
        position: absolute;
        bottom: 24 * @rem;
        left: 50%;
        transform: translateX(-50%);
        &.share {
          background-image: url(~@/assets/images/spring-activity/fudai-card-btn-share.png);
        }
        &.recharge {
          background-image: url(~@/assets/images/spring-activity/fudai-card-btn-recharge.png);
        }
        &.tomorrow {
          background-image: url(~@/assets/images/spring-activity/fudai-card-btn-tomorrow.png);
        }
      }
    }
  }
  .rule-container {
    margin-top: 20 * @rem;
    &::before {
      content: '';
      position: relative;
      z-index: 2;
      display: block;
      width: 347 * @rem;
      height: 40 * @rem;
      background: url(~@/assets/images/spring-activity/spring-rule-1.png) center
        top no-repeat;
      background-size: 347 * @rem 40 * @rem;
    }
    &::after {
      content: '';
      position: relative;
      z-index: 2;
      display: block;
      width: 347 * @rem;
      height: 80 * @rem;
      background: url(~@/assets/images/spring-activity/spring-rule-3.png) center
        bottom no-repeat;
      background-size: 347 * @rem 80 * @rem;
      z-index: 1;
    }
    .section-container {
      position: relative;
      width: 347 * @rem;
      background: url(~@/assets/images/spring-activity/spring-rule-2.png) center
        top;
      background-size: 347 * @rem 50 * @rem;
      margin-top: -13 * @rem;
      margin-bottom: -32 * @rem;
      &.record-container {
        min-height: 250 * @rem;
        .content-container {
          padding: 0 15 * @rem;
          .record-title {
            width: 212 * @rem;
            height: 25 * @rem;
            margin: 0 auto;
          }
          .record-list {
            box-sizing: border-box;
            height: 250 * @rem;
            overflow: auto;
            padding: 0 10 * @rem;
            .record-item {
              margin-top: 16 * @rem;
              .first-line {
                display: flex;
                align-items: center;
                justify-content: space-between;
                .left {
                  font-size: 14 * @rem;
                  color: #7c343f;
                  font-weight: 600;
                  flex: 1;
                  min-width: 0;
                  text-overflow: ellipsis;
                  overflow: hidden;
                  white-space: nowrap;
                }
                .right {
                  font-size: 12 * @rem;
                  color: #ff0000;
                  font-weight: 600;
                }
              }
              .second-line {
                margin-top: 2 * @rem;
                display: flex;
                align-items: center;
                .date {
                  margin-left: auto;
                  font-size: 12 * @rem;
                  color: #a15858;
                }
              }
            }
          }
          .record-empty {
            height: 250 * @rem;
            display: flex;
            justify-content: center;
            align-items: center;
            text-align: center;
            font-size: 16 * @rem;
            color: #ba3918;
            line-height: 22 * @rem;
          }
        }
      }
      .content-container {
        position: relative;
        z-index: 99;
        padding: 0 26 * @rem;
        .rule-title {
          width: 212 * @rem;
          height: 25 * @rem;
          background: url(~@/assets/images/spring-activity/spring-rule-title.png)
            center bottom no-repeat;
          background-size: 212 * @rem 25 * @rem;
          margin: 0 * @rem auto;
        }
        .rule-content {
          .h {
            font-size: 14 * @rem;
            font-weight: 600;
            line-height: 18 * @rem;
            color: #7c343f;
            margin-top: 17 * @rem;
          }
          p {
            font-size: 12 * @rem;
            line-height: 16 * @rem;
            color: #a15858;
            margin-top: 10 * @rem;
            span {
              color: #f76d48;
              &.bold {
                font-weight: 600;
              }
            }
          }
        }
      }
    }
  }
  .popup-container {
    box-sizing: border-box;
    width: 329 * @rem;
    height: 210 * @rem;
    background: url(~@/assets/images/spring-activity/spring-popup.png) center
      top no-repeat;
    background-size: 329 * @rem 210 * @rem;
    margin: 18 * @rem auto 0;
    padding: 27 * @rem 0 26 * @rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    .popup-title {
      width: 207 * @rem;
      height: 25 * @rem;
      margin: 0 auto;
    }
    .desc {
      text-align: center;
      font-size: 14 * @rem;
      color: #a15858;
      margin-top: 10 * @rem;
      padding: 0 30 * @rem;
      line-height: 18 * @rem;
    }
    .box-tips {
      font-size: 11 * @rem;
      color: #f6743d;
      margin-top: 11 * @rem;
    }
    .box-icon {
      width: 60 * @rem;
      height: 60 * @rem;
    }
    .box-desc {
      font-size: 20 * @rem;
      color: #c54624;
      text-align: center;
      font-weight: 600;
      margin-top: 10 * @rem;
    }
    .box-gold {
      font-size: 14 * @rem;
      color: #a15858;
      text-align: center;
      margin-top: 6 * @rem;
      span {
        font-size: 20 * @rem;
        color: #f6743d;
      }
    }
    .bottom-operation {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 22 * @rem;

      .ok {
        width: 118 * @rem;
        height: 31 * @rem;
        background: url(~@/assets/images/spring-activity/spring-box-ok.png)
          center top no-repeat;
        background-size: 118 * @rem 31 * @rem;
        margin: 0 5 * @rem 0;
      }
      .cancel {
        width: 103 * @rem;
        height: 31 * @rem;
        background: url(~@/assets/images/spring-activity/spring-box-cancel.png)
          center top no-repeat;
        background-size: 103 * @rem 31 * @rem;
        margin: 0 5 * @rem 0;
      }
      .confirm {
        width: 103 * @rem;
        height: 31 * @rem;
        background: url(~@/assets/images/spring-activity/spring-box-confirm.png)
          center top no-repeat;
        background-size: 103 * @rem 31 * @rem;
        margin: 0 5 * @rem 0;
      }
    }
  }
}
</style>
