<template>
  <div class="page-250101-activity">
    <nav-bar-2
      ref="topNavBar"
      bgStyle="transparent-white"
      :azShow="true"
      :placeholder="false"
    >
      <template #left>
        <div class="back" @click="back"></div>
      </template>
    </nav-bar-2>
    <div class="rule-btn" @click="rulePopup = true">规则</div>
    <!-- 主体内容 -->
    <div class="main">
      <div class="activity-title"></div>
      <div class="activity-sub-title" v-if="lotteryStatus !== 2">
        2025.01.01 22点整自动开奖
      </div>
      <div class="activity-sub-title" v-else>已开奖</div>
      <div class="banner">
        <img
          class="bg-banner"
          src="@/assets/images/250101/seckill-banner.png"
          alt=""
        />
      </div>
      <div class="prize-info">
        <div class="prize-item" v-for="item in prizeInfo" :key="item.id">
          <div class="prize-name">
            <div class="title">{{ item.title }}</div>
            <div class="number">x {{ item.num }}份</div>
          </div>
          <div class="prize-desc">
            {{ item.desc }}
          </div>
        </div>
      </div>
      <div
        class="lottery-box"
        v-if="lotteryStatus !== 2 && countdownObj && isLoading"
      >
        <div
          class="lottery-btn"
          :class="{
            'no-ripple':
              lotteryStatus !== 0 ||
              activity_status === 2 ||
              activity_status === 3,
          }"
          @click="handleLottery"
        >
          <span
            class="lottery-btn-text"
            :class="{
              'text-status':
                lotteryStatus !== 0 ||
                activity_status === 2 ||
                activity_status === 3,
            }"
            >{{ activity_status_text }}</span
          >
        </div>
        <template v-if="countdown && countdown.endTime">
          <div class="lottery-text">开奖时间</div>
          <div class="lottery-time">
            <div class="countdown-clock">
              <span>{{ countdownObj.hour }}</span
              >&nbsp;:&nbsp;<span>{{ countdownObj.minute }}</span
              >&nbsp;:&nbsp;
              <span>{{ countdownObj.second }}</span>
            </div>
          </div>
        </template>
      </div>
      <!-- 奖品信息 -->
      <template v-if="lotteryStatus == 2 && isPrizeDraw">
        <div
          class="lottery-info margin-t39"
          v-for="(item, index) in filteredLotteryInfo"
          :key="index"
        >
          <div class="lottery-card">
            <div class="lottery-border">
              <div class="lottery-content">
                <div
                  class="jl-title"
                  :class="{
                    'jl-title-bg1': item.id === 1,
                    'jl-title-bg2': item.id === 2,
                  }"
                ></div>
                <div class="jl-content">
                  <div class="title">{{ item.title }}</div>
                  <div class="desc" v-if="item.desc">{{ item.desc }}</div>
                  <ul>
                    <li v-for="(li, index1) in item.list" :key="index1">
                      {{ li }}
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>

      <!-- 中奖者名单 -->
      <div v-if="lotteryStatus == 2" class="lottery-info lottery-user-roster">
        <div class="lottery-card">
          <div class="lottery-border">
            <div class="lottery-content">
              <div class="jl-title jl-title-bg3"></div>
              <div class="roster-list">
                <div class="roster-item1" v-if="hyjlList[0]">
                  <div class="title">好运锦鲤</div>
                  <div class="user-info1">
                    <div class="user-img">
                      <img
                        v-if="hyjlList[0]?.avatar"
                        class="img"
                        :src="hyjlList[0]?.avatar"
                      />
                      <img v-else class="img" :src="defaultAvatar" />
                    </div>
                    <div class="nickname">{{ hyjlList[0]?.nickname }}</div>
                  </div>
                </div>
                <div class="roster-item2">
                  <div class="title">新年礼</div>
                  <swiper
                    v-if="isKeep && xnlList1.length > 10"
                    :options="getSwiperOption"
                  >
                    <swiper-slide
                      class="user-info2"
                      v-for="(item, index) in xnlList"
                      :key="index"
                    >
                      <div
                        class="user-item"
                        v-for="info in item"
                        :key="info.id"
                      >
                        <div class="user-img">
                          <img
                            v-if="info?.avatar"
                            class="img"
                            :src="info?.avatar"
                          />
                          <img v-else class="img" :src="defaultAvatar" />
                        </div>
                        <div class="nickname">{{ info.nickname }}</div>
                      </div>
                    </swiper-slide>
                  </swiper>
                  <swiper v-else :options="getSwiperOption1">
                    <swiper-slide
                      class="user-info2"
                      v-for="(item, index) in xnlList"
                      :key="index"
                    >
                      <div
                        class="user-item"
                        v-for="info in item"
                        :key="info.id"
                      >
                        <div class="user-img">
                          <img
                            v-if="info?.avatar"
                            class="img"
                            :src="info?.avatar"
                          />
                          <img v-else class="img" :src="defaultAvatar" />
                        </div>
                        <div class="nickname">{{ info.nickname }}</div>
                      </div>
                    </swiper-slide>
                  </swiper>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 抽奖弹窗 -->
    <van-dialog
      v-model="LotteryPopUp"
      :show-confirm-button="false"
      :lock-scroll="false"
      class="lottery-dialog"
    >
      <div class="logo-icon"></div>
      <div class="dialog-content">
        <div class="title" v-if="Number(isNewYearGift) == 2">
          哇！2025新年锦鲤就是你
        </div>
        <div class="title" v-if="Number(isNewYearGift) == 1">
          3733 祝您元旦快乐！
        </div>
        <div class="msg">
          <span v-if="Number(isNewYearGift) == 2"
            >SVIP年卡(1张) + 云挂机年卡(1张) + 2025 金币</span
          >
          <span v-if="Number(isNewYearGift) == 1">588 金币</span>
        </div>
        <div class="btn" @click="closeLotteryPopUp">开心收下</div>
      </div>
    </van-dialog>

    <!-- 规则弹窗 -->
    <rule-popup :show.sync="rulePopup"></rule-popup>
  </div>
</template>

<script>
import { platform, boxInit } from '@/utils/box.uni.js';
import { BOX_login } from '@/utils/box.uni.js';
import {
  ApiDoingsYuandanRaffle,
  ApiDoingsYuandanCompleteRewardPage,
} from '@/api/views/250101.js';
import { mapGetters } from 'vuex';
export default {
  components: {
    rulePopup: () => import('../components/rule-popup.vue'),
  },
  data() {
    return {
      rulePopup: false,
      prizeInfo: [
        {
          id: 1,
          title: '好运锦鲤',
          desc: 'SVIP年卡(1张) + 云挂机年卡(1张) + 2025 金币',
          num: 1,
        },
        {
          id: 2,
          title: '新年礼',
          desc: '588 金币',
          num: 9999,
        },
      ],
      countdown: {}, // 显示倒计时对象
      timeClock: null, // 倒计时定时器
      isLoadingLottery: false, //正在参于抽奖中
      activity_status: 0, //参与抽奖 0待开奖 1等待 2已开奖 3已结束
      lotteryStatus: 0, //0 未参与抽奖 1待开奖 2已开奖
      LotteryPopUp: false, //抽奖弹窗
      isNewYearGift: 0, //1新年礼 2好运锦鲤，0改成没抽奖返回
      isPrizeDraw: false, // 是否参与抽奖
      isLoading: false,
      lotteryInfo: [
        {
          id: 1,
          title: '好运锦鲤',
          desc: 'SVIP年卡(1张) + 云挂机年卡(1张) + 2025 金币',
          list: [
            'SVIP年卡于开奖日自动生效，生效时间为2025.01.01 ~ 2025.12.31',
            '云挂机年卡于开奖日自动生效，生效时间为2025.01.01 ~ 2025.12.31',
            '2025 金币已存入账户',
          ],
        },
        {
          id: 2,
          title: '新年礼物',
          desc: '',
          list: ['588 金币已存入账户'],
        },
      ],
      getSwiperOption: {
        slidesPerView: 1,
        direction: 'vertical',
        loop: true,
        autoplay: {
          delay: 3000,
        },
        allowTouchMove: false,
      },
      getSwiperOption1: {
        slidesPerView: 1,
        direction: 'vertical',
        allowTouchMove: false,
      },
      hyjlList: [], //好运锦鲤名单
      xnlList: [], //新年礼名单
      xnlList1: [],
      activityStatusArr: ['参与抽奖', '参与中...', '待开奖', '已结束'],
      isKeep: false,
    };
  },

  async created() {},
  async mounted() {
    // 官包触发页面刷新（将onResume挂载在window上）
    if (platform == 'android') {
      window.onResume = this.onResume;
    }
  },
  deactivated() {
    this.isKeep = false;
    clearInterval(this.timeClock);
    this.timeClock = null;
  },
  beforeDestroy() {
    clearInterval(this.timeClock);
    this.timeClock = null;
  },
  async activated() {
    this.isKeep = true;
    await this.$onAppFinished;
    await this.getInitData();
  },
  methods: {
    // 获取抽奖初始化信息
    async getInitData() {
      try {
        const res = await ApiDoingsYuandanCompleteRewardPage();
        this.hyjlList = res.data.hyjl;
        this.xnlList = this.chunkArray(res.data.xnl, 10);
        this.xnlList1 = res.data.xnl;
        this.lotteryStatus = res.data.status;
        this.isNewYearGift = res.data.isxnl;
        this.isPrizeDraw = res.data.iscj === 1 ? true : false;
        const popupData =
          JSON.parse(localStorage.getItem('ACTIVITY_250101_POPUP')) || {};
        let userPopupData = popupData[this.userInfo.user_id] || null;
        if (
          res.data.status === 2 &&
          this.isPrizeDraw &&
          (!userPopupData || !userPopupData.isClose)
        ) {
          this.LotteryPopUp = true;
        }

        this.countdown = {
          nowTime: res.data.time,
          endTime: res.data.start_time + 3, //往后延时3秒开奖
        };
        this.$toast.clear();
        clearInterval(this.timeClock);
        this.timeClock = null;
        if (res.data.status === 2) return;
        this.timeClock = setInterval(async () => {
          this.countdown.nowTime += 1;
          if (this.countdown.nowTime == this.countdown.endTime) {
            // if (this.countdown.endTime - this.countdown.nowTime <= 0) {
            clearInterval(this.timeClock);
            this.timeClock = null;
            //获取初始化信息
            this.$toast.loading('开奖中...');
            this.activity_status = 3;
            await this.getInitData();
          }
          if (this.countdown.endTime - this.countdown.nowTime <= 0) {
            clearInterval(this.timeClock);
            this.timeClock = null;
            return;
          }
        }, 1000);
      } catch (error) {
      } finally {
        this.isLoading = true;
        this.$toast.clear();
      }
    },
    chunkArray(arr, size) {
      const result = [];
      for (let i = 0; i < arr.length; i += size) {
        result.push(arr.slice(i, i + size));
      }
      return result;
    },
    closeLotteryPopUp() {
      this.LotteryPopUp = false;

      let popupData =
        JSON.parse(localStorage.getItem('ACTIVITY_250101_POPUP')) || {};
      const newUserData = {
        user_id: this.userInfo.user_id,
        isClose: true,
      };
      popupData[this.userInfo.user_id] = newUserData;
      localStorage.setItem('ACTIVITY_250101_POPUP', JSON.stringify(popupData));
    },
    // 参与抽奖
    async handleLottery() {
      if (!this.userInfo.token) {
        this.$toast('请登录');
        this.login();
        return false;
      }
      if (
        this.isLoadingLottery ||
        [2, 3].includes(this.activity_status) ||
        [1, 2].includes(this.lotteryStatus)
      ) {
        return;
      }

      this.activity_status = 1;
      this.isLoadingLottery = true;
      try {
        await ApiDoingsYuandanRaffle();
        this.$toast('参与成功');
        this.activity_status = 2;
        this.isPrizeDraw = true;
      } catch (error) {
        this.activity_status = 0;
      } finally {
        this.isLoadingLottery = false;
      }
    },
    // 格式化时间戳为小时、分、秒
    formatTime(timeStamp) {
      timeStamp = Number(timeStamp);
      let hour = this.addZero(Math.floor(timeStamp / 3600)); // 总小时数
      let minute = this.addZero(Math.floor((timeStamp % 3600) / 60)); // 分钟数
      let second = this.addZero((timeStamp % 3600) % 60); // 秒数
      return {
        hour,
        minute,
        second,
      };
    },
    addZero(num) {
      num = parseInt(num);
      return num < 10 ? '0' + num : num.toString();
    },
    async onResume() {
      await boxInit();
      await this.getInitData();
    },
    login() {
      BOX_login();
    },
  },
  computed: {
    ...mapGetters({
      userInfo: 'user/userInfo',
    }),
    filteredLotteryInfo() {
      if (Number(this.isNewYearGift) == 1) {
        return this.lotteryInfo.filter(item => item.id === 2);
      }

      if (Number(this.isNewYearGift) == 2) {
        return this.lotteryInfo.filter(item => item.id === 1);
      }
    },
    countdownObj() {
      if (this.countdown && this.countdown.endTime) {
        if (this.countdown.nowTime > this.countdown.endTime) {
          // 已结束
          return this.formatTime(0);
        }
        return this.formatTime(this.countdown.endTime - this.countdown.nowTime);
      } else {
        return {};
      }
    },

    activity_status_text() {
      if (this.lotteryStatus == 1) {
        return this.activityStatusArr[2];
      }
      if (this.lotteryStatus == 2) {
        return this.activityStatusArr[3];
      }
      return this.activityStatusArr[this.activity_status];
    },
  },
};
</script>

<style lang="less" scoped>
.page-250101-activity {
  font-family: PingFang SC, PingFang SC;
  display: flex;
  flex-direction: column;
  width: 100%;
  overflow: hidden;
  min-height: 100vh;
  background: #ffcfba;
  .back {
    width: 30 * @rem;
    height: 30 * @rem;
    border-radius: 15 * @rem;
    background: rgba(0, 0, 0, 0.3) url(~@/assets/images/nav-bar-back-white.png)
      center center no-repeat;
    background-size: 8 * @rem 14 * @rem;
  }
  .rule-btn {
    box-sizing: border-box;
    position: fixed;
    width: 28 * @rem;
    height: 48 * @rem;
    top: 70 * @rem;
    right: 0;
    z-index: 999;
    background: rgba(133, 0, 0, 0.26);
    font-size: 13 * @rem;
    color: #fff;
    line-height: 18 * @rem;
    border-radius: 12 * @rem 0 0 12 * @rem;
    text-align: center;
    padding: 0 5 * @rem;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .main {
    width: 100%;
    position: relative;
    flex: 1;
    min-height: 0;
    background: url(~@/assets/images/250101/250101_bg1.png) center top no-repeat;
    background-size: 100% 227 * @rem;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    box-sizing: border-box;
    .activity-title {
      margin: 49 * @rem auto 0;
      background: url(~@/assets/images/250101/lottery-title.png) center top
        no-repeat;
      background-size: 228 * @rem 62 * @rem;
      width: 228 * @rem;
      height: 62 * @rem;
    }
    .activity-sub-title {
      font-size: 14 * @rem;
      color: rgba(255, 255, 255, 0.9);
      text-align: center;
      margin-top: -13 * @rem;
      line-height: 17 * @rem;
    }
    .banner {
      margin-top: 24 * @rem;
      width: 335 * @rem;
      height: 152 * @rem;
      background: #ffffff;
      border-radius: 8 * @rem;
    }
    .prize-info {
      margin-top: 18 * @rem;
      width: 335 * @rem;
      .prize-item {
        display: flex;
        flex-direction: column;
        .prize-name {
          display: flex;
          align-items: center;
          .number {
            margin-left: 6 * @rem;
            height: 15 * @rem;
            font-weight: 400;
            font-size: 12 * @rem;
            color: #c56639;
            line-height: 15 * @rem;
          }
        }
        .prize-desc {
          margin-top: 3 * @rem;
          font-weight: 400;
          font-size: 14 * @rem;
          color: #c56639;
        }
        &:not(:first-child) {
          margin-top: 16 * @rem;
        }
      }
    }
    .lottery-box {
      margin: 94 * @rem 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      .lottery-btn {
        position: relative;
        width: 112 * @rem;
        height: 112 * @rem;
        background: linear-gradient(180deg, #fd9083 0%, #ff495c 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1;
        transition: all 0.1s linear;
        .lottery-btn-text {
          font-weight: 600;
          font-size: 20 * @rem;
          color: #ffffff;
          &.text-status {
            color: rgba(255, 255, 255, 0.4);
            width: 104 * @rem;
            height: 104 * @rem;
            line-height: 104 * @rem;
            text-align: center;
            border-radius: 22 * @rem;
            border-radius: 50%;
            box-sizing: border-box;
            border: 2px solid rgba(255, 255, 255, 0.15);
          }
        }
        &::before,
        &::after {
          position: absolute;
          content: '';
          width: 112 * @rem;
          height: 112 * @rem;
          border-radius: 50%;
          animation: ripple 1.4s linear infinite;
        }

        &:active {
          transform: scale(0.96);
        }
        &::after {
          animation-delay: 0.7s;
        }

        &.no-ripple::before,
        &.no-ripple::after {
          display: none;
        }
        &.no-ripple:active {
          transform: scale(1);
        }
      }

      .lottery-text {
        margin-top: 26 * @rem;
        font-weight: 600;
        font-size: 16 * @rem;
        height: 23 * @rem;
        line-height: 23 * @rem;
        color: #902d17;
      }
      .lottery-time {
        margin-top: 8 * @rem;
        .countdown-clock {
          display: flex;
          align-items: center;
          height: 30 * @rem;
          line-height: 30 * @rem;
          font-weight: 600;
          font-size: 16 * @rem;
          color: #902d17;
          span {
            display: block;
            font-weight: 600;
            padding: 0 7 * @rem;
            height: 30 * @rem;
            line-height: 30 * @rem;
            text-align: center;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 4 * @rem;
          }
        }
      }
    }
    .lottery-info {
      position: relative;
      width: 351 * @rem;
      display: flex;
      flex-direction: column;
      align-items: center;
      &::before {
        position: relative;
        content: '';
        box-sizing: border-box;
        display: block;
        width: 351 * @rem;
        height: 36 * @rem;
        overflow: hidden;
        background: url(~@/assets/images/250101/activity-bg-top.png) center
          center no-repeat;
        background-size: 351 * @rem 36 * @rem;
        z-index: 1;
      }
      &::after {
        content: '';
        position: relative;
        box-sizing: border-box;
        display: block;
        width: 351 * @rem;
        height: 36 * @rem;
        overflow: hidden;
        background: url(~@/assets/images/250101/activity-bg-bottom.png) center
          top no-repeat;
        background-size: 351 * @rem 36 * @rem;
        z-index: 1;
      }
      .lottery-card {
        box-sizing: border-box;
        background: rgba(255, 255, 255, 0.3);
        width: 351 * @rem;
        padding: 0 6 * @rem;
        position: relative;
        z-index: 99;
        .lottery-border {
          border-left: 1 * @rem solid #ffcfba;
          border-right: 1 * @rem solid #ffcfba;
          position: relative;
          z-index: 2;
          .lottery-content {
            position: relative;
            padding-top: 1 * @rem;
            padding-bottom: 1 * @rem;
            .jl-title {
              margin: 0 auto;
              margin-top: -16 * @rem;
              &.jl-title-bg1 {
                background: url(~@/assets/images/250101/lottery-jl-title.png)
                  center top no-repeat;
                background-size: 274 * @rem 24 * @rem;
                height: 24 * @rem;
                width: 274 * @rem;
              }
              &.jl-title-bg2 {
                background: url(~@/assets/images/250101/lottery-ydkl-title.png)
                  center top no-repeat;
                background-size: 274 * @rem 24 * @rem;
                height: 24 * @rem;
                width: 274 * @rem;
              }
              &.jl-title-bg3 {
                background: url(~@/assets/images/250101/lottery-zjz-title.png)
                  center top no-repeat;
                background-size: 288 * @rem 45 * @rem;
                height: 45 * @rem;
                width: 288 * @rem;
              }
            }
            .jl-content {
              width: 100%;
              .title {
                padding: 0 18 * @rem;
                margin-top: 14 * @rem;
              }
              .desc {
                padding: 0 18 * @rem;
                height: 18 * @rem;
                font-weight: 400;
                font-size: 14 * @rem;
                color: #c56639;
                line-height: 18 * @rem;
                margin: 3 * @rem 0 10 * @rem 0;
              }
              ul {
                padding: 10 * @rem 30 * @rem;
                li {
                  font-weight: 400;
                  font-size: 12 * @rem;
                  color: #c56639;
                  line-height: 17 * @rem;
                  list-style-type: disc;
                  &:not(:first-child) {
                    margin-top: 8 * @rem;
                  }
                }
              }
            }
          }
        }
      }
      &.margin-t39 {
        margin-top: 39 * @rem;
      }
    }
    .lottery-user-roster {
      margin-top: 26 * @rem;
      margin-bottom: 34 * @rem;
      width: 351 * @rem;
      // height: 445 * @rem;
      // background: #ffffff;
      display: flex;
      flex-direction: column;
      //   align-items: center;
      .jl-title {
        // height: 24 * @rem;
        // width: 288 * @rem;
        // margin: 20 * @rem auto 0;
      }
      .roster-list {
        padding: 0 18 * @rem;
        margin-bottom: -10 * @rem;
        box-sizing: border-box;
        .roster-item1 {
          .title {
            margin-top: 14 * @rem;
          }
          .user-info1 {
            display: flex;
            align-items: center;
            padding: 10 * @rem 10 * @rem 20 * @rem 10 * @rem;
          }
        }
        .roster-item2 {
          width: 100%;
          .swiper-container {
            height: 244 * @rem;
            margin-top: 10 * @rem;
            margin-left: 10 * @rem;
            .user-info2 {
              display: grid;
              grid-template-columns: repeat(2, 1fr);
              // gap: 16 * @rem;
              align-content: flex-start;
              .user-item {
                height: 34 * @rem;
                display: flex;
                align-items: center;
                margin-bottom: 16 * @rem;
                &:nth-child(2n) {
                  margin-right: 0;
                }
                &:not(:last-child) {
                  margin-bottom: 16 * @rem;
                }
              }
            }
          }
        }
        .user-img {
          flex-shrink: 0;
          width: 34 * @rem;
          height: 34 * @rem;
          border-radius: 50%;
          overflow: hidden;
        }
        .nickname {
          margin-left: 8 * @rem;
          height: 20 * @rem;
          font-weight: 400;
          font-size: 14 * @rem;
          color: #c56639;
          line-height: 20 * @rem;
          width: 100 * @rem;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
  }

  .lottery-dialog {
    width: 300 * @rem;
    background: transparent;
    overflow: visible;
    .logo-icon {
      background: url(~@/assets/images/250101/lottery-logo.png) no-repeat 0 0;
      background-size: 164 * @rem 124 * @rem;
      width: 164 * @rem;
      height: 124 * @rem;
      margin: 0 auto;
      position: relative;
      z-index: 3;
    }
    .dialog-content {
      box-sizing: border-box;
      position: relative;
      border-radius: 16 * @rem;
      margin-top: -74 * @rem;
      z-index: 2;
      padding: 84 * @rem 27 * @rem 0;
      width: 300 * @rem;
      height: 259 * @rem;
      background: url(~@/assets/images/250101//lottery-bg.png) top center
        no-repeat;
      background-color: #fff;
      text-align: center;
      .title {
        white-space: nowrap;
        width: 241 * @rem;
        height: 25 * @rem;
        font-family: 'Dream Han Sans CN', 'Dream Han Sans CN';
        font-weight: bold;
        font-size: 21 * @rem;
        line-height: 25 * @rem;
        text-align: center;
        font-style: normal;
        text-transform: none;
        background: linear-gradient(to bottom, #ff691d 50%, #c03a00 50%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      .msg {
        height: 40 * @rem;
        font-weight: 400;
        font-size: 15 * @rem;
        color: #c56639;
        line-height: 19 * @rem;
        text-align: center;
        margin: 15 * @rem 0 0 0;
      }
      .btn {
        position: absolute;
        bottom: 30 * @rem;
        width: 238 * @rem;
        height: 40 * @rem;
        line-height: 40 * @rem;
        background: linear-gradient(221deg, #fd9083 0%, #ff495c 100%);
        border-radius: 40 * @rem;
        font-family: PingFang SC, PingFang SC;
        font-weight: 500;
        font-size: 15 * @rem;
        color: #ffffff;
        text-align: center;
        font-style: normal;
        text-transform: none;
      }
    }
  }
  .title {
    height: 26 * @rem;
    font-weight: 600;
    font-size: 18 * @rem;
    color: #902d17;
    line-height: 26 * @rem;
  }
  @keyframes ripple {
    0% {
      box-shadow: 0 0 0 0px #ffffff;
      opacity: 0.3;
    }
    50% {
      box-shadow: 0 0 0 10px rgba(255, 255, 255, 0.5);
      opacity: 0.6;
    }
    100% {
      box-shadow: 0 0 0 20px rgba(255, 255, 255, 0);
      opacity: 0;
    }
  }
}
</style>
