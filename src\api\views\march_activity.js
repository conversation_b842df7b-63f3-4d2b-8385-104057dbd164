import { request } from '../index';

/**
 * 活动页3月暖春活动
 */

export function ApiMARIndex(params = {}) {
  return request('/activity/warm_spring/index', params);
}

export function ApiMARTaskTake(params = {}) {
  return request('/activity/warm_spring/take', params);
}

export function ApiMARExchangeRecord(params = {}) {
  return request('/activity/warm_spring/prizeList', params);
}

export function ApiMARRefresh(params = {}) {
  return request('/activity/warm_spring/getRemainCount', params);
}

export function ApiMARTakePrize(params = {}) {
  return request('/activity/warm_spring/takePrize', params);
}
