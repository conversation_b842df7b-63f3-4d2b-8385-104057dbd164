import { request } from '../index';

export function ApiHeishenhuaIndex(params = {}) {
  return request('/activity/wukong/index', params);
}

export function ApiHeishenhuaTakeTask(params = {}) {
  return request('/activity/wukong/take', params);
}

export function ApiHeishenhuaExchange(params = {}) {
  return request('/activity/wukong/exchange', params);
}

export function ApiHeishenhuaSendMessage(params = {}) {
  return request('/activity/wukong/message', params);
}

export function ApiHeishenhuaLotteryLog(params = {}) {
  return request('/activity/wukong/lotteryLog', params);
}

export function ApiHeishenhuaLottery(params = {}) {
  return request('/activity/wukong/lottery', params);
}

export function ApiHeishenhuaPointLog(params = {}) {
  return request('/activity/wukong/exchangeLog', params);
}

export function ApiHeishenhuaGameList(params = {}) {
  return request('/activity/wukong/gameList', params);
}

export function ApiDownloadAdded(params = {}) {
  return request('/api/game/downloadAdded', params);
}
