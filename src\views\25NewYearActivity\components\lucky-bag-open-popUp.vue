<template>
  <div>
    <van-dialog
      v-model="popup"
      :show-confirm-button="false"
      :lock-scroll="false"
      class="lucky-bag-open-dialog"
    >
      <div class="logo-icon"></div>
      <div class="dialog-content">
        <div class="title">{{ luckyBagInfo.title }}</div>
        <div class="msg">
          {{ luckyBagInfo.desc }}
        </div>
        <div
          class="tips"
          v-if="
            luckyBagInfo.prize_id == 6 ||
            luckyBagInfo.prize_id == 7 ||
            luckyBagInfo.prize_id == 8
          "
        >
          <span>* 过期未联系客服者视为自动放弃 ~</span>
        </div>
        <div
          class="btn-close btn"
          v-if="
            luckyBagInfo.prize_id !== 6 &&
            luckyBagInfo.prize_id !== 7 &&
            luckyBagInfo.prize_id !== 8
          "
          @click="closeBtn()"
        >
          开心收下
        </div>
        <div
          class="btn-close btn"
          v-if="
            luckyBagInfo.prize_id == 6 ||
            luckyBagInfo.prize_id == 7 ||
            luckyBagInfo.prize_id == 8
          "
          @click="goToKefu()"
        >
          联系客服
        </div>
        <div
          class="btn-open btn"
          v-if="luckyBagInfo.prize_id == 9"
          @click="goTOFillInAddress()"
        >
          去填写收货地址
        </div>
      </div>
    </van-dialog>
  </div>
</template>

<script>
import { BOX_openInNewWindow, platform } from '@/utils/box.uni.js';
import { envFun } from '@/utils/function.js';
export default {
  name: 'luckyBagOpenDialog',
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    luckyBagInfo: {
      type: Object,
      default: () => {
        return {
          title: '',
          msg: '',
          isShowTips: false,
          isShowKefu: false,
          isShowAddress: false,
        };
      },
      required: true,
    },
  },
  data() {
    return {};
  },
  computed: {
    popup: {
      get() {
        return this.show;
      },
      set(value) {
        this.$emit('update:show', value);
      },
    },
  },

  methods: {
    closePopup() {
      this.$emit('update:show', false);
    },
    closeBtn() {
      this.closePopup();
    },
    goToKefu() {
      this.closePopup();
      this.$nextTick(() => {
        if (platform == 'android') {
          BOX_openInNewWindow(
            { name: 'Kefu' },
            { url: `https://${envFun()}game.3733.com/#/kefu` },
          );
        } else {
          BOX_openInNewWindow(
            { name: 'KefuChat' },
            { url: `https://${envFun()}game.3733.com/#/kefu_chat` },
          );
        }
      });
    },
    goTOFillInAddress() {
      this.closePopup();
      this.$nextTick(() => {
        this.toPage('25NewYearActivityFillInAddress');
      });
    },
  },
};
</script>

<style lang="less" scoped>
.lucky-bag-open-dialog {
  width: 300 * @rem;
  background: transparent;
  overflow: visible;
  .logo-icon {
    background: url(~@/assets/images/25newyear/25newyear-logo2.png) top center
      no-repeat;
    background-size: 157 * @rem 167 * @rem;
    width: 157 * @rem;
    height: 167 * @rem;
    margin: 0 auto;
    position: relative;
    z-index: 3;
  }
  .dialog-content {
    box-sizing: border-box;
    position: relative;
    border-radius: 16 * @rem;
    margin-top: -74 * @rem;
    z-index: 2;
    padding: 67 * @rem 27 * @rem 0;
    width: 300 * @rem;
    background: url(~@/assets/images/25newyear/25newyear-bg1.png) center top
        no-repeat,
      #ffffff;
    background-size: 300 * @rem 120 * @rem;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    .title {
      font-family: 'Dream Han Sans CN', 'Dream Han Sans CN';
      font-weight: bold;
      font-size: 20 * @rem;
      line-height: 28 * @rem;
      text-align: center;
      font-style: normal;
      text-transform: none;
      color: #e75555;
    }

    .msg {
      min-height: 77 * @rem;
      box-sizing: border-box;
      width: 100%;
      font-weight: 400;
      font-size: 15 * @rem;
      color: #7a5252;
      line-height: 23 * @rem;
      text-align: left;
      font-style: normal;
      text-transform: none;
      padding: 17 * @rem 0 16 * @rem 0;
    }
    .tips {
      margin: 10 * @rem 0 16 * @rem 0;
      height: 16 * @rem;
      font-weight: 400;
      font-size: 13 * @rem;
      color: #7a5252;
      line-height: 16 * @rem;
      white-space: nowrap;
    }
    .btn-close,
    .btn-open {
      width: 238 * @rem;
      height: 40 * @rem;
      line-height: 40 * @rem;
      margin-bottom: 18 * @rem;
      background: linear-gradient(90deg, #f64b4b 0%, #ffb07c 100%), #d9d9d9;
      border-radius: 20 * @rem;
      font-family: PingFang SC, PingFang SC;
      font-weight: 500;
      font-size: 15 * @rem;
      color: #ffffff;
      text-align: center;
      font-style: normal;
      text-transform: none;
    }
  }
}
</style>
