<template>
  <div>
    <van-dialog
      class="invitation-box"
      v-model="popup"
      :lock-scroll="false"
      :show-confirm-button="false"
      :close-on-click-overlay="false"
      confirmButtonColor="#161823"
    >
      <div class="invitation-dialog">
        <div class="invitation-title">
          <span class="title">邀请有礼</span>
        </div>
        <div class="invitation-input-box">
          <div class="input-desc">
            新用户填写邀请码 双方都获得<span>30福气值</span>~
            <br />*新用户：福气值为0
          </div>
          <div class="invitation-input">
            <input
              ref="inputText"
              class="input-text"
              v-model.trim="invitationCode"
              type="text"
              placeholder="请输入邀请码"
            />
          </div>
        </div>
        <div class="invitation-btn">
          <div class="cancel btn" @click="cancelHandle">取消</div>
          <div class="su"></div>
          <div class="confirm btn" @click="confirmHandle">确定</div>
        </div>
      </div>
    </van-dialog>
  </div>
</template>

<script>
import { ApiDoingsChunjieInvitationCode } from '@/api/views/25_new_year_activity.js';
import { mapGetters } from 'vuex';
import { BOX_login } from '@/utils/box.uni.js';
export default {
  name: 'invitationDialog',
  props: {
    show: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      invitationCode: '', //邀请码
    };
  },
  computed: {
    ...mapGetters({
      initData: 'system/initData',
      userInfo: 'user/userInfo',
    }),
    popup: {
      get() {
        return this.show;
      },
      set(value) {
        this.$nextTick(() => {
          setTimeout(() => {
            this.invitationCode = '';
          }, 200);
        });
        this.$emit('update:show', value);
      },
    },
  },
  watch: {
    invitationCode(newVal) {
      // const match = newVal.match(/yqm=(\w{1,12})/);
      const match = newVal.match(/yqm=([\S]{12})/);
      if (match) {
        this.invitationCode = match[1];
      }
    },
  },
  methods: {
    closePopup() {
      this.$emit('update:show', false);
    },
    cancelHandle() {
      this.closePopup();
    },
    async confirmHandle() {
      if (!this.userInfo.token) {
        this.$toast('请登录');
        this.login();
        return false;
      }
      if (!this.invitationCode.trim()) {
        this.$toast('请输入邀请码');
        const inputElement = this.$refs.inputText;

        if (inputElement) {
          const length = inputElement.value.length;
          inputElement.setSelectionRange(length, length);
          inputElement.focus();
        }
        return;
      }
      if (this.invitationCode.length !== 12) {
        this.$toast('邀请失败\n邀请码输入错误~');
        return;
      }

      await ApiDoingsChunjieInvitationCode({
        invitationCode: this.invitationCode,
      });
      this.$emit('confirmFunc');
      this.closePopup();
    },
    login() {
      BOX_login();
    },
  },
};
</script>

<style lang="less" scoped>
.invitation-box {
  width: 300 * @rem;
  box-shadow: inset 0px 1px 0px 0px #f3f3f3;
  .invitation-dialog {
    // padding: 20 * @rem 20 * @rem 14 * @rem;
    .invitation-title {
      margin: 28 * @rem auto 0;
      .title {
        height: 18 * @rem;
        font-weight: bold;
        font-size: 18 * @rem;
        color: #222222;
        line-height: 18 * @rem;
        display: flex;
        justify-content: center;
      }
    }
    .invitation-input-box {
      margin: 20 * @rem auto 0;
      padding: 0 20 * @rem 14 * @rem;
      border-bottom: 1px solid #f3f3f3;
      .input-desc {
        padding: 0 0 10 * @rem;
        font-weight: 400;
        font-size: 14 * @rem;
        color: #333333;
        line-height: 21 * @rem;
        text-align: left;
        span {
          color: #e75555;
        }
      }
      .invitation-input {
        height: 40 * @rem;
        background: #ffffff;
        border-radius: 8 * @rem;
        border: 1px solid #e6e6e6;
        box-sizing: border-box;
        padding: 10 * @rem 12 * @rem;
        .input-text {
          font-weight: 400;
          font-size: 15 * @rem;
          color: #999999;
        }
      }
    }

    .invitation-btn {
      height: 56 * @rem;
      background: #ffffff;
      display: flex;
      align-items: center;
      justify-content: center;
      .cancel,
      .confirm {
        flex: 1;
        height: 100%;
        flex-shrink: 0;
        min-width: 0;
        font-weight: 400;
        font-size: 15 * @rem;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .cancel {
        color: rgba(22, 24, 35, 0.75);
      }
      .confirm {
        font-weight: bold;
        color: #161823;
      }
      .su {
        width: 1 * @rem;
        height: 48 * @rem;
        background: #f2f2f2;
      }
    }
  }
}
</style>
