import { request } from '../index';

/**
 * 活动页/八月活动
 */

/**
 * 八月活动首页
 */
export function ApiActivityAugustIndex(params = {}) {
  return request('/activity/august/index', params);
}

/**
 * 八月活动 - 刮奖
 */
export function ApiActivityAugustScratchLottery(params = {}) {
  return request('/activity/august/scratchLottery', params);
}

/**
 * 八月活动 - 扭蛋
 */
export function ApiActivityAugustCapsuleToys(params = {}) {
  return request('/activity/august/capsuleToys', params);
}

/**
 * 八月活动 - 领取扭蛋币
 */
export function ApiActivityAugustTask(params = {}) {
  return request('/activity/august/task', params);
}

/**
 * 八月活动 - 奖励记录
 */
export function ApiActivityAugustRewardLog(params = {}) {
  return request('/activity/august/rewardLog', params);
}

/**
 * 八月活动 - 领取扭蛋币
 */
export function ApiActivityAugustTakeLottery(params = {}) {
  return request('/activity/august/takeLottery', params);
}
