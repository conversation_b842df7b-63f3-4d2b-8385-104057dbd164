<template>
  <div class="activity-rule">
    <nav-bar-2
      ref="topNavBar"
      bgStyle="transparent-white"
      :azShow="true"
      :placeholder="false"
      v-if="navBgTransparent"
    >
      <template #left>
        <div class="back" @click="back"></div>
      </template>
      <template #center>
        <div class="page-title">活动规则</div>
      </template>
    </nav-bar-2>
    <nav-bar-2 :placeholder="false" :border="true" v-else>
      <template #center>
        <div class="page-title">活动规则</div>
      </template>
    </nav-bar-2>
    <div class="leaf-bg"></div>
    <div class="main">
      <div class="section">
        <div class="title"><span>活动介绍：</span></div>
        <div class="desc">
          趣味宾果活动限时开启，炎炎夏日，一起参与夏日消暑主题活动，清凉一夏吧~
        </div>
        <div class="desc color">活动时间：7月10日0点0分-7月17日23点59分</div>
      </div>
      <div class="section">
        <div class="container">
          <div class="big-text">一、登录领奖励</div>
          <div class="text"> 活动期间，累计登录游戏满3天可领取 1 木锤。 </div>
        </div>
        <div class="container">
          <div class="big-text">二、超值累充福利</div>
          <div class="text">
            活动期间，每日任意游戏实付大于10元，且累计达到3天，可免费领取388金币+7天SVIP。（活动期间每个用户仅可参与一次）<br>
            温馨提示：仅限<span class="color">游戏内使用微信/支付宝充值</span
            >，如有使用金币/平台币/代金券抵扣的，则只计算抵扣后的现金充值金额。
          </div>
        </div>
        <div class="container">
          <div class="big-text">三．累计任务</div>
          <div class="text">
            活动期间<br />
            累计实付充值满100元，可领取2木棰 + 188 金币<br />
            累计实付充值满300元，可领取4木棰 + 188 金币<br />
            累计实付充值满500元，可领取4木棰 + 388 金币 + 3天SVIP<br />
            累计实付充值满1000元，可领取6木棰 + 488金币 + 7天SVIP<br />
            累计实付充值满2000元，可领取8木棰 + 588金币 + 14天SVIP<br />
            累计实付充值满3000元，可领取8木锤 + 1288金币 + 30天SVIP<br />
          </div>
          <div class="text">
            温馨提示：仅限游戏内使用微信/支付宝充值，如有使用金币/平台币/代金券抵扣的，则只计算抵扣后的现金充值金额
          </div>
        </div>
        <div class="container">
          <div class="big-text">四、宾果大作战(玩法说明)</div>
          <div class="text">
            玩家可使用木锤敲击冰块，每次敲击消耗 1 把木锤并任意击碎 1 块冰块，同时每使用 1 把木锤可获得 66金币。完成第一轮BINGO后方可开启奖励更丰厚的第二轮BINGO。 <br />
            4 个被击碎的冰块连成 1
            条直线之后即可达成宾果领取对应奖励。每轮BINGO冰块都被击碎后还可以额外获得"夏日消暑大礼包"。
          </div>
        </div>
        <div class="container">
          <div class="big-text">五、活动说明：</div>
          <div class="text">
            1.活动期间充值完成后请返回本活动页面点击【刷新】领取奖励，并及时领取累计奖励，活动结束后将清空所有木锤和奖励领取机会。
          </div>
          <div class="text">
            2.温馨提示：由于游戏厂商的要求，部分游戏内充值暂不支持使用金币支付，请先确定需要用金币充值的游戏在不在可使用名单内。
          </div>
          <div class="text">
            <span @click="game_dialog_show = true" class="color underline btn"
              >查看名单&gt;</span
            >
          </div>
        </div>
        <div class="container">
          <div class="big-text">六、概率公示</div>
          <div class="text">
            夏日消暑大礼包1：<br />2888金币（70%） SVIP月卡（20%） 588绑定平台币（10%）<br />
            夏日消暑大礼包2：<br />3888金币（60%） SVIP月卡（20%） 888绑定平台币（20%）<br />
          </div>
        </div>
      </div>
    </div>
    <!-- 查看名单 -->
    <noGameList
      :game_dialog_show="game_dialog_show"
      @changeGameDialogShow="changeGameDialogShow"
    />
  </div>
</template>
<script>
import { ApiGameCardSearchGame } from '@/api/views/recharge.js';
import { BOX_goToGame, BOX_openInNewWindow } from '@/utils/box.uni.js';
import { envFun } from '@/utils/function';
import noGameList from '@/components/no-game-list';

export default {
  name: 'Rule',
  components: {
    noGameList,
  },
  data() {
    return {
      game_dialog_show: false, //查看名单弹窗
      navBgTransparent: true,
    };
  },
  mounted() {
    // 获取顶部导航栏的高度
    this.stickyOffsetTop =
      document.querySelector('.van-nav-bar').offsetHeight + 'px';
    window.addEventListener('scroll', this.handleScroll);
  },
  methods: {
    // 滚动处理
    handleScroll(e) {
      let scrollTop = e.target.scrollingElement.scrollTop;
      if (scrollTop > 100) {
        this.navBgTransparent = false;
      } else {
        this.navBgTransparent = true;
      }
    },
    changeGameDialogShow(show) {
      this.game_dialog_show = show;
    },
  },
};
</script>
<style lang="less" scoped>
.activity-rule {
  background-color: #fbdb8d;
  overflow: hidden;
  position: relative;

  .back {
    width: 30 * @rem;
    height: 30 * @rem;
    border-radius: 15 * @rem;
    background: rgba(0, 0, 0, 0.3) url(~@/assets/images/nav-bar-back-white.png)
      center center no-repeat;
    background-size: 8 * @rem 14 * @rem;
  }
  .leaf-bg {
    position: absolute;
    right: 0;
    top: 21 * @rem;
    width: 67 * @rem;
    height: 141 * @rem;
    .image-bg('~@/assets/images/july-activity/rule_bg.png');
    background-size: 67 * @rem 141 * @rem;
  }
  .page-title {
    color: #934028;
    font-size: 18 * @rem;
    font-weight: 600;
  }
  .main {
    background-color: #fceed8;
    border-radius: 30 * @rem 30 * @rem 0 0;
    margin-top: 91 * @rem;
    padding: 22 * @rem 20 * @rem 35 * @rem;
  }
  .title {
    margin-bottom: 15 * @rem;
    span {
      background-image: url('~@/assets/images/july-activity/rule_title_bg.png');
      background-size: 100%;
      background-repeat: no-repeat;
      background-position: 0 16 * @rem;
      font-size: 16 * @rem;
      font-family: PingFang HK-Semibold, PingFang HK;
      font-weight: 600;
      color: #934028;
    }
  }
  .big-text {
    margin-bottom: 7 * @rem;
    font-size: 14 * @rem;
    font-family: PingFang SC-Semibold, PingFang SC;
    font-weight: 600;
    color: #934028;
    line-height: 18 * @rem;
  }
  .desc {
    margin-bottom: 6 * @rem;
    font-size: 12 * @rem;
    color: #934028;
    line-height: 18 * @rem;
  }
  .text {
    margin-bottom: 6 * @rem;
    font-size: 11 * @rem;
    color: #934028;
    line-height: 18 * @rem;
  }
  .color {
    color: #ff4009;
  }
  .underline {
    text-decoration: underline;
  }
  .container {
    margin-top: 21 * @rem;
  }
}
.no-gold-game-popup {
  overflow: unset;
  width: 320 * @rem;
  .search-container {
    box-sizing: border-box;
    width: 320 * @rem;
    height: 450 * @rem;
    padding: 24 * @rem 19 * @rem 10 * @rem;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: unset;
    .close-search {
      width: 24 * @rem;
      height: 24 * @rem;
      background: url(~@/assets/images/close-search.png) center center no-repeat;
      background-size: 24 * @rem 24 * @rem;
      position: absolute;
      right: -10 * @rem;
      top: -10 * @rem;
    }
    .search-bar {
      display: flex;
      align-items: center;
      .input-text {
        width: 240 * @rem;
        height: 35 * @rem;
        border: 1 * @rem solid #e5e5e5;
        border-radius: 18 * @rem;
        flex: 1;
        overflow: hidden;
        form {
          display: block;
          width: 100%;
          height: 100%;
        }
        input {
          box-sizing: border-box;
          display: block;
          width: 100%;
          height: 100%;
          padding: 0 18 * @rem;
          font-size: 15 * @rem;
          color: #333333;
          background-color: #f6f6f6;
        }
      }
      .search-btn {
        font-size: 15 * @rem;
        color: #666666;
        padding-left: 13 * @rem;
        height: 35 * @rem;
        line-height: 35 * @rem;
      }
    }
    .game-list {
      flex: 1;
      overflow: auto;
      margin-top: 10 * @rem;
      .game-item {
        display: flex;
        align-items: center;
        padding: 21 * @rem 0;
        border-bottom: 1 * @rem solid #eeeeee;
        .game-icon {
          width: 50 * @rem;
          height: 50 * @rem;
          border-radius: 10 * @rem;
          background-color: #b5b5b5;
        }
        .right {
          flex: 1;
          min-width: 0;
          margin-left: 10 * @rem;
          .game-name {
            font-size: 16 * @rem;
            font-weight: bold;
            color: #000000;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
          }
          .use-card {
            font-size: 12 * @rem;
            color: #f72e2e;
            margin-top: 10 * @rem;
            &.can {
              color: #36b150;
            }
          }
        }
      }
    }
  }
}
</style>
