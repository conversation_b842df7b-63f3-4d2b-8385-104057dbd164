<template>
  <div class="exchange-page">
    <nav-bar-2
      ref="topNavBar"
      bgStyle="transparent-white"
      :azShow="true"
      :title="'天选之人名单公示'"
      v-if="navBgTransparent"
    ></nav-bar-2>
    <nav-bar-2 :title="'天选之人名单公示'" :border="true" v-else> </nav-bar-2>
    <table>
      <tr>
        <th>序号</th>
        <th>游戏账号</th>
        <th>游戏昵称</th>
      </tr>
      <tr v-for="(item, index) in list">
        <td>{{ index + 1 }}</td>
        <td>{{ item.username }}</td>
        <td>{{ item.nickname }}</td>
      </tr>
    </table>
    <div v-if="list.length > 0" class="explain">
      注：奖励将于活动结束后1-2个工作日内自动发放至获奖用户的账号之中，可在
      我的-金币 中查看
    </div>
    <div v-if="!finish && list.length === 0" class="empty">暂无名单公示</div>
  </div>
</template>
<script>
import { Api0101NameList } from '@/api/views/0101.js';

export default {
  data() {
    return {
      finish: false,
      list: [], //兑换列表
      navBgTransparent: true,
    };
  },
  async created() {
    await this.init();
  },
  mounted() {
    // 获取顶部导航栏的高度
    this.stickyOffsetTop =
      document.querySelector('.van-nav-bar').offsetHeight + 'px';
    window.addEventListener('scroll', this.handleScroll);
  },
  methods: {
    async init() {
      try {
        if (this.finish) {
          return false;
        }
        this.finish = true;
        const res = await Api0101NameList();
        this.list = res.data.list;
        this.finish = false;
      } catch {}
    },
    // 滚动处理
    handleScroll(e) {
      let scrollTop = e.target.scrollingElement.scrollTop;
      if (scrollTop > 100) {
        this.navBgTransparent = false;
      } else {
        this.navBgTransparent = true;
      }
    },
  },
};
</script>
<style lang="less" scoped>
.exchange-page {
  min-height: 100vh;
  .image-bg('~@/assets/images/0101/0101_bg3.png');
  background-color: #2f295c;
  overflow: hidden;
  table {
    width: 100%;
    box-sizing: border-box;
    margin: 15 * @rem;
    color: #fff;
    text-align: center;
    th,
    td {
      font-family: PingFang HK-Semibold, PingFang HK;
      font-weight: 600;
      height: 40 * @rem;
      line-height: 40 * @rem;
    }
    th {
      font-size: 16 * @rem;
    }
    td {
      font-size: 12 * @rem;
    }
  }
  .explain {
    margin: 20 * @rem 15 * @rem 0;
    line-height: 18 * @rem;
    font-size: 11 * @rem;
    text-align: center;
    color: #ffffff;
  }
  .empty {
    margin: 80 * @rem 0;
    text-align: center;
    font-size: 11 * @rem;
    color: #ffffff;
  }
}
</style>
