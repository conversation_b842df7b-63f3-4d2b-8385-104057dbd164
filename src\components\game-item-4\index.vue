<template>
  <div class="game-item-components" @click="toDetail(gameInfo)">
    <div
      class="game-icon"
      :style="{
        width: `${iconSize * remNumberLess}rem !important`,
        height: `${iconSize * remNumberLess}rem !important`,
      }"
    >
      <img :src="gameInfo.titlepic" alt="" />
    </div>
    <div class="game-info">
      <div class="game-name" :class="{ big: iconSize >= 80 }">
        {{ gameInfo.main_title }}
        <span class="game-subtitle" v-if="gameInfo.subtitle">{{
          gameInfo.subtitle
        }}</span>
      </div>
      <div class="info-center">
        <slot>
          <div class="game-hot" v-if="showHot && gameInfo.totaldown">
            <img class="hot-icon" src="@/assets/images/games/hot-icon.png" />
            <div class="hot-num">{{ gameInfo.totaldown }}</div>
          </div>
          <div class="types">
            <template v-for="(type, typeIndex) in gameInfo.type">
              <span class="type" :key="typeIndex" v-if="typeIndex < 2">{{
                type
              }}</span>
            </template>
          </div>
          <div
            class="game-subscribe"
            v-if="gameInfo.subscribe_num && gameInfo.state == 2"
            >{{ gameInfo.subscribe_num }}人已预约</div
          >
          <div
            class="server-date"
            v-if="gameInfo.server_date_str"
            v-html="gameInfo.server_date_str"
          >
          </div>
        </slot>
      </div>
      <div class="info-bottom" v-if="!gameInfo.gold_discount">
        <div class="discount-tag" v-if="discountTag">
          <img
            class="discount-icon discount-01"
            src="@/assets/images/games/discount-01.png"
            v-if="discountTag == 0.1"
          />
          <img
            class="discount-icon"
            src="@/assets/images/games/discount-normal.png"
            v-else-if="discountTag"
          />
          <div class="discount-text"
            ><span>{{ discountTag }}</span
            >折直充</div
          >
        </div>
        <!-- !discountTag && gameInfo.is_accelerate -->
        <div
          class="is-accelerate"
          v-if="!discountTag && gameInfo.is_accelerate"
        ></div>
        <div class="tags">
          <div
            class="tag"
            v-for="(tag, tagIndex) in gameInfo.extra_tag"
            :key="tagIndex"
          >
            <div class="tag-name">{{ tag.name }}</div>
          </div>
        </div>
      </div>
      <div class="info-bottom" v-else>
        <div class="gold-discount-text">{{ gameInfo.gold_discount_text }}</div>
      </div>
    </div>

    <!-- 根据dl_config隐藏下载和预约按钮 -->
    <!-- <div class="game-right">
      <template v-if="type == 1">
        <yy-download-btn
          class="yy-download-btn"
          :gameInfo="gameInfo"
        ></yy-download-btn>
      </template>
      <template v-else-if="type == 6">
        <yy-download-btn
          class="yy-download-btn"
          :gameInfo="gameInfo"
        ></yy-download-btn>
      </template>
    </div> -->
  </div>
</template>

<script>
import { isIos, isAndroid } from '@/utils/userAgent';
import { startCloudGame, startH5Game } from '@/utils/function';
import { BOX_goToGame } from '@/utils/box.uni.js';
import { mapGetters } from 'vuex';
import { remNumberLess } from '@/common/styles/_variable.less';

/**
 * @param type 1普通，2预约，3个人签, 4个人中心 5up详情页 60.1折列表页
 *  */

export default {
  name: 'GameItem',
  props: {
    gameInfo: {
      type: Object,
      default: () => {
        return {};
      },
    },
    showRight: {
      type: Boolean,
      default: false,
    },
    type: {
      type: Number,
      default: 1,
    },
    // 是否显示热度
    showHot: {
      type: Boolean,
      default: false,
    },
    // 游戏图标大小
    iconSize: {
      type: Number,
      default: 72,
    },
  },
  data() {
    return {
      remNumberLess,
      isIos,
      isAndroid,
    };
  },
  computed: {
    // 是否显示云玩
    showCloud() {
      return this.gameInfo.yyx_type
        ? parseInt(this.gameInfo.yyx_type.code) !== 0 && isIos
        : false;
    },
    // 按钮文案
    buttonText() {
      if (this.gameInfo.h5_url) {
        return this.$t('开始玩');
      } else if (this.showCloud) {
        return this.$t('云玩');
      } else {
        return this.$t('下载');
      }
    },
    // 按钮url
    downloadUrl() {
      if (this.gameInfo.h5_url) {
        return this.gameInfo.h5_url;
      } else if (isIos && this.gameInfo.down_ip) {
        return this.gameInfo.down_ip;
      } else if (!isIos && this.gameInfo.down_a) {
        return this.gameInfo.down_a;
      } else {
        return false;
      }
    },
    // 是否显示折扣tag
    discountTag() {
      if (this.gameInfo.classid != 107) {
        return '';
      }
      if (this.gameInfo.f_pay_rebate && this.gameInfo.f_pay_rebate != 100) {
        return `${parseFloat(this.gameInfo.f_pay_rebate) / 10}`;
      } else if (this.gameInfo.pay_rebate && this.gameInfo.pay_rebate != 100) {
        return `${parseFloat(this.gameInfo.pay_rebate) / 10}`;
      } else {
        return '';
      }
    },
  },
  methods: {
    // 下载逻辑
    handleDownload() {
      if (this.gameInfo.h5_url) {
        startH5Game(this.gameInfo.h5_url, this.gameInfo.app_id);
      } else if (this.showCloud) {
        startCloudGame(this.gameInfo.id);
      } else {
        isIos
          ? this.toDetail(this.gameInfo)
          : (window.location.href = this.downloadUrl);
      }
    },
    // 打开详情页
    toDetail(gameInfo) {
      if (gameInfo.classid == 40) {
        this.toPage('UpDetail', {
          id: gameInfo.id,
          gameInfo: gameInfo,
        });
      } else {
        BOX_goToGame(
          {
            params: {
              id: gameInfo.id,
              gameInfo: gameInfo,
            },
          },
          { id: gameInfo.id },
        );
      }
    },
  },
};
</script>

<style lang="less" scoped>
.game-item-components {
  position: relative;
  box-sizing: border-box;
  width: 100%;
  display: flex;
  padding: 10 * @rem 0;
  flex: 1;
  min-width: 0;
  .game-icon {
    width: 84 * @rem;
    height: 84 * @rem;
    border-radius: 16 * @rem;
    background-color: #eeeeee;
    overflow: hidden;
  }
  .game-info {
    margin-left: 10 * @rem;
    flex: 1;
    min-width: 0;
    // height: 65 * @rem;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    .game-name {
      display: flex;
      align-items: center;
      font-size: 14 * @rem;
      font-weight: 600;
      color: #111111;
      overflow: hidden;
      white-space: nowrap;
      &.big {
        font-size: 16 * @rem;
      }
      .game-subtitle {
        box-sizing: border-box;
        border: 1 * @rem solid #e0e0e0;
        border-radius: 3 * @rem;
        font-size: 11 * @rem;
        padding: 2 * @rem 3 * @rem;
        color: #808080;
        margin-left: 8 * @rem;
        vertical-align: middle;
        line-height: 1;
      }
      .modify_subtitle {
        border: 1 * @rem solid #e5e1ea;
        color: #888888;
      }
      .game-title {
        font-size: 14 * @rem;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 5em;
        font-weight: 600;
      }
    }
  }

  .info-center {
    display: flex;
    align-items: center;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    flex-wrap: wrap;
    height: 15 * @rem;
    margin-top: 2 * @rem;
    // margin-top: 8 * @rem;
    .hot-num,
    .game-subscribe {
      font-size: 12 * @rem;
      color: #999999;
      line-height: 15 * @rem;
      height: 15 * @rem;
      margin-right: 10 * @rem;
    }
    .game-hot {
      color: #666666;
      display: flex;
      height: 15 * @rem;
      .hot-icon {
        background-size: 10 * @rem 10 * @rem;
        width: 10 * @rem;
        height: 10 * @rem;
        margin-right: 2 * @rem;
        margin-top: 2 * @rem;
      }
    }
    .types {
      display: flex;
      align-items: center;
      height: 15 * @rem;
      line-height: 15 * @rem;
      .type {
        padding: 0 5 * @rem;
        position: relative;
        display: flex;
        align-items: center;
        color: #999999;
        line-height: 15 * @rem;
        font-size: 12 * @rem;
        &:first-of-type {
          padding-left: 0;
        }
        &:not(:first-child) {
          &:before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 1 * @rem;
            height: 10 * @rem;
            background-color: #999999;
          }
        }
      }
    }

    .server-date {
      font-size: 12 * @rem;
      color: #ffa16c;
      margin-left: 5 * @rem;
      height: 17 * @rem;
      display: flex;
      align-items: center;
      line-height: 17 * @rem;
      span {
        line-height: 17 * @rem;
      }
    }
  }
  .info-bottom {
    display: flex;
    align-items: center;
    margin-top: 2 * @rem;
    .discount-tag {
      display: flex;
      align-items: center;
      width: fit-content;
      margin-right: 8 * @rem;
      flex-shrink: 0;

      .discount-icon {
        width: 30 * @rem;
        height: 18 * @rem;
        position: relative;
        z-index: 1;
        &.discount-01 {
          width: 49 * @rem;
        }
      }
      .discount-text {
        display: flex;
        align-items: center;
        height: 18 * @rem;
        padding-right: 4 * @rem;
        flex: 1;
        min-width: 0;
        font-size: 11 * @rem;
        color: #ff6649;
        white-space: nowrap;
        background-color: #fff5ed;
        border-radius: 0 2 * @rem 2 * @rem 0;
        margin-left: -5 * @rem;
        padding-left: 5 * @rem;
      }
    }
    .is-accelerate {
      width: 73 * @rem;
      height: 18 * @rem;
      background: url('~@/assets/images/games/accelerate-tag.png');
      background-position: left center;
      background-size: 73 * @rem 18 * @rem;
      margin-right: 8 * @rem;
      flex-shrink: 0;
    }
    .tags {
      display: flex;
      height: 18 * @rem;
      overflow: hidden;
      flex-wrap: wrap;
      .tag {
        height: 18 * @rem;
        margin-right: 8 * @rem;
        display: flex;
        align-items: center;
        flex-wrap: nowrap;
        color: #9a9a9a;
        background-color: #ecfbf4;
        border-radius: 2 * @rem;
        padding: 0 4 * @rem;
        margin-bottom: 1 * @rem;
        .tag-icon {
          width: 13 * @rem;
          height: 13 * @rem;
          margin-right: 2 * @rem;
        }
        .tag-name {
          font-size: 11 * @rem;
          white-space: nowrap;
          color: #21b98a;
        }
      }
      .modify-tag {
        background-color: #fff;
      }
    }
    .gold-discount-text {
      color: #21b98a;
      white-space: nowrap;
      width: 139 * @rem;
      height: 15 * @rem;
      font-weight: 400;
      font-size: 11 * @rem;
      color: #21b98a;
      line-height: 15 * @rem;
    }
  }
  .game-right {
    .yy-download-btn {
      position: absolute;
      right: 0;
      top: 40 * @rem;
    }
    .yuyue {
      box-sizing: border-box;
      width: 58 * @rem;
      height: 28 * @rem;
      background-color: #fff;
      border-radius: 6 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12 * @rem;
      color: @themeColor;
      border: 1 * @rem solid @themeColor;
      position: absolute;
      right: 0;
      top: 40 * @rem;
      &.had {
        font-size: 12 * @rem;
        background-color: #c1c1c1;
        border: 0;
        color: #fff;
      }
    }
    .start {
      width: 70 * @rem;
      height: 30 * @rem;
      font-size: 14 * @rem;
      display: flex;
      justify-content: center;
      align-items: center;
      background: #fff;
      color: @themeColor;
      border-radius: 15 * @rem;
      border: 1px solid @themeColor;
      &.h5 {
        background: @themeBg;
        color: #fff;
        border: none;
      }
      &.cloud {
        background: #f3ac40;
        color: #fff;
        border: 1px solid #f3ac40;
      }
    }
    .kaifu-tip {
      margin-top: 10 * @rem;
      font-size: 12 * @rem;
      color: #999999;
      text-align: center;
    }
    .grq-right {
      margin-left: 15 * @rem;
    }
    .button {
      width: 65 * @rem;
      height: 27 * @rem;
      box-sizing: border-box;
      border: 1px solid @themeColor;
      font-size: 13 * @rem;
      color: @themeColor;
      text-align: center;
      line-height: 27 * @rem;
      border-radius: 14 * @rem;
      background: #fff;
      &.button2 {
        margin-bottom: 5 * @rem;
        background: @themeBg;
        color: #fff;
      }
      &.button3 {
        border: 1px solid #ff7d0a;
        color: #ff7d0a;
      }
    }
  }
}
@keyframes rotate {
  0% {
    transform: translate(-50%, -50%) rotate(0);
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}
</style>
