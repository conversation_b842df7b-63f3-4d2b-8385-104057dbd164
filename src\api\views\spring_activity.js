import { request } from '../index';

/**
 * 活动页/龙年 - 活动首页
 */
export function ApiDragonYearIndex(params = {}) {
  return request('/activity/dragon_year/index', params);
}

/**
 * 活动页/龙年 - 分享领取次数
 */
export function ApiDragonYearShare(params = {}) {
  return request('/activity/dragon_year/share', params);
}

/**
 * 活动页/龙年 - 刷新宝箱
 */
export function ApiDragonYearRefreshBagId(params = {}) {
  return request('/activity/dragon_year/refreshBagId', params);
}

/**
 * 活动页/龙年 - 领取宝箱
 */
export function ApiDragonYearTaskBox(params = {}) {
  return request('/activity/dragon_year/taskBox', params);
}

/**
 * 活动页/龙年 - 开启福袋
 */
export function ApiDragonYearTaskLuckyBag(params = {}) {
  return request('/activity/dragon_year/taskLuckyBag', params);
}

/**
 * 活动页/龙年 - 宝箱记录
 */
export function ApiDragonYearBoxLog(params = {}) {
  return request('/activity/dragon_year/boxLog', params);
}
