<template>
  <div class="heishenhua-activity-page page">
    <div class="main">
      <div @click="back2()" class="back btn"></div>
      <div @click="handleShare" class="share btn"></div>
      <div class="top-bg"> </div>
      <template v-if="step == 1">
        <section id="section1" class="section1">
          <div class="section-title"></div>
          <!-- 转盘 -->
          <div class="table-container">
            <div class="table-content">
              <div class="table-list">
                <div
                  class="table-item"
                  v-for="item in dialList"
                  :key="item.lottery_id"
                  :class="{ on: current == item.lottery_id }"
                >
                  <div class="reward-icon">
                    <img class="reward-img" :src="item.icon" alt="" />
                  </div>
                  <div class="reward-text">{{ item.title }}</div>
                </div>
                <div
                  class="table-item turn-btn-1 btn"
                  @click="handleRaffle()"
                ></div>
              </div>
            </div>
          </div>
          <div class="explain-text">当前抽奖次数:{{ lottery_count }}</div>
        </section>
        <div @click="getRewardList" class="my-reward-button btn">我的奖品</div>
        <section class="section2">
          <div class="section-title"></div>
          <div v-for="item in task_list" :key="item.task_id" class="task-item">
            <div class="task-item-left">
              <div class="task-name">{{ item.title }}</div>
              <div class="task-explain">
                <div v-html="item.desc" class="big-text"></div>
                <div class="task-small-text">（每日上限{{ item.num }}次）</div>
              </div>
            </div>
            <div
              @click="getTakeTask(item)"
              :class="{ cant: item.status != 1 }"
              class="task-item-right btn"
              >{{ item.submit_title }}</div
            >
          </div>
        </section>
        <section class="section3">
          <div class="section-title"></div>
          <div class="subtitle">
            <div class="text"
              >活动期间首位通关游戏即可<span
                >报销游戏购买款+赠送云挂机年卡</span
              ></div
            >
          </div>
          <div class="contant-content">
            <div class="contant-left"><i class="icon"></i>联系客服报名</div>
            <div @click="toKefu()" class="contant-right btn">立即报名</div>
          </div>
          <div class="contant-small-text"
            ><span>注意</span
            >:报名需提交《黑神话:悟空》账号信息+游戏个人信息页截图，报名成功后，报名信息将不可调整</div
          >
        </section>
        <section class="section4">
          <div class="section-title"></div>
          <div class="point-content">
            <div class="point-content-left">
              <div class="my-point">我的积分:{{ point }}</div>
            </div>
            <div class="point-content-right">
              <div @click="getRecordList" class="point-detail-button btn"
                >积分明细</div
              >
            </div>
          </div>
          <div class="exchange-list">
            <div
              v-for="(item, index) in exchange_list"
              :key="index"
              class="exchange-item"
            >
              <div class="exchange-bg">
                <img :src="item.icon" />
                <div class="exchange-num"
                  >{{ item.is_svip ? '仅' : '所' }}需积分：{{
                    item.need_num
                  }}</div
                >
              </div>
              <div class="exchange-text"
                >当日剩余库存：{{ item.is_svip ? svip_sku : sku }}</div
              >
              <div
                @click="getTakeExchange(item)"
                :class="{ cant: item.status == 2 }"
                class="exchange-button btn"
              >
                {{ item.status == 2 ? '已兑完' : '立即兑换' }}
              </div>
            </div>
          </div>
        </section>
        <section ref="section5" id="section5" class="section5">
          <div class="section-title"></div>
          <vue-danmaku
            v-model="message_list"
            class="marquee-container"
            use-slot
            loop
            :speeds="30"
            :channels="4"
          >
            <template slot="dm" slot-scope="{ index, danmu }">
              <div class="marquee">
                <div class="item">{{ danmu.content }}</div>
              </div>
            </template>
          </vue-danmaku>
          <div class="send-container">
            <div class="send-label">
              <input v-model="send_input" type="text" class="send-input" />
              <div class="count">{{ 50 - send_input.toString().length }}</div>
            </div>
            <div @click="sendMessage" class="send-button">发送</div>
          </div>
        </section>
        <div class="activity-explain">
          <div class="explain-title">活动说明</div>
          <div v-html="activity_explain" class="explain-content"></div>
        </div>
      </template>
      <template v-if="step == 2">
        <section ref="game-list" id="game-list">
          <div class="section-title"></div>
          <div class="game-list">
            <div
              v-for="(item, index2) in game_list"
              :key="index2"
              class="game-item"
            >
              <img :src="item.titlepic" alt="" class="game-img" />
              <div class="game-info">
                <div class="game-name">
                  <div class="name">{{ item.main_title }}</div>
                  <div v-if="item.submit_title" class="subtitle">{{
                    item.subtitle
                  }}</div>
                </div>
                <div class="game-type-list">
                  <div
                    v-for="(item2, index) in item.type.slice(0, 3)"
                    :key="index"
                    class="game-type-item"
                    >{{ item2
                    }}<span v-if="index != item.type.slice(0, 3).length - 1"
                      >&nbsp;&nbsp;|&nbsp;&nbsp;</span
                    ></div
                  >
                </div>
              </div>
              <div
                @click="downloadGame(item, index2)"
                :class="{ cant: item.is_down }"
                class="game-button btn"
              >
                {{ item.is_down ? '已下载' : '下载' }}</div
              >
            </div>
          </div>
        </section>
      </template>
    </div>
    <!-- 兑奖记录 -->
    <van-popup
      v-model="record_list_popup"
      :lock-scroll="false"
      :close-on-click-overlay="false"
      class="popup record-list-popup"
    >
      <section>
        <div class="popup-close" @click="record_list_popup = false"></div>
        <div class="section-title"></div>
        <div v-if="record_list.length > 0" class="list">
          <div v-for="(item, index) in record_list" :key="index" class="item">
            <div class="left">{{ item.date }}</div>
            <div v-html="item.num" class="right"></div>
          </div>
        </div>
        <div v-else class="empty">暂无名单</div>
      </section>
    </van-popup>
    <!-- 我的奖品 -->
    <van-popup
      v-model="reward_list_popup"
      :lock-scroll="false"
      :close-on-click-overlay="false"
      class="popup record-list-popup reward-list-popup"
    >
      <section>
        <div class="popup-close" @click="reward_list_popup = false"></div>
        <div class="section-title"></div>
        <div v-if="reward_list.length > 0" class="list">
          <div v-for="(item, index) in reward_list" :key="index" class="item">
            <div class="left">{{ item.title }}</div>
            <div v-html="item.date" class="right"></div>
          </div>
        </div>
        <div v-else class="empty"
          >暂无获奖记录，<span class="btn" @click="toLottery"
            >立即前往抽奖</span
          ></div
        >
      </section>
    </van-popup>
    <van-popup
      v-model="lottery_result_popup"
      :lock-scroll="false"
      :close-on-click-overlay="false"
      class="popup lottery-result-popup"
    >
      <section>
        <div @click="lottery_result_popup = false" class="popup-close"></div>
        <div
          :style="{ backgroundImage: `url(${lottery_result.pop_title})` }"
          class="section-title"
        ></div>
        <img
          class="lottery-result-img"
          :src="lottery_result.lottery?.pop_icon"
          alt=""
        />
        <div class="lottery-result-text">{{ lottery_result.msg }}</div>
        <div
          @click="lottery_result_popup = false"
          class="lottery-result-button btn"
          >我知道了</div
        >
      </section>
    </van-popup>
  </div>
</template>

<script>
import {
  BOX_login,
  platform,
  boxInit,
  BOX_checkInstall,
  BOX_goToGame,
  BOX_openInNewWindow,
  BOX_downloadGame,
} from '@/utils/box.uni.js';
import {
  ApiHeishenhuaIndex,
  ApiHeishenhuaTakeTask,
  ApiHeishenhuaExchange,
  ApiHeishenhuaSendMessage,
  ApiHeishenhuaLotteryLog,
  ApiHeishenhuaLottery,
  ApiHeishenhuaPointLog,
  ApiHeishenhuaGameList,
  ApiDownloadAdded,
} from '@/api/views/heishenhua_activity.js';
import { ApiCommonShareInfo } from '@/api/views/system.js';
import h5Page from '@/utils/h5Page.js';
import { mapGetters, mapActions } from 'vuex';
import vueDanmaku from 'vue-danmaku';

export default {
  components: {
    vueDanmaku,
  },
  data() {
    return {
      current: 1, //当前选中奖项
      task_list: [], //任务列表
      activity_status: 0, //活动状态
      exchange_list: [], //兑换列表
      dialList: [], // 转盘内容
      timer: null, // 定时器
      activity_explain: '', //活动说明
      lottery_count: 0, //抽奖剩余次数
      lottery_result_popup: false, //抽奖结果
      lottery_result: {}, //抽奖结果
      point: 0, //当前积分
      sku: 0, //剩余库存
      svip_sku: 0, //剩余SVIP库存
      isStarting: false, //是否开始转盘
      shareInfo: {}, //分享信息
      record_list_popup: false, //记录弹窗
      record_list: [], //记录列表
      reward_list_popup: false, //奖品记录
      reward_list: [], //奖品列表
      step: 1,
      message_list: [], // 需要滚动的消息列表
      duration: 8, // 单个消息停留时间（秒）
      currentIndex: 0,
      intervalId: null,
      send_input: '', //发送内容
      finished: true, //防抖
      game_list: [], //游戏列表
    };
  },
  computed: {
    activity_status_text() {
      const arr = [
        '活动已开始！',
        '活动不存在！',
        '活动未开始！',
        '活动已结束！',
      ];
      return arr[this.activity_status - 1];
    },
    ...mapGetters({
      initData: 'system/initData',
    }),
  },
  async mounted() {
    // 官包触发页面刷新（将onResume挂载在window上）
    if (platform == 'android') {
      window.onResume = this.onResume;
    }

    // 页面数据初始化
    await this.getIndexData();
    const res = await ApiHeishenhuaGameList();
    this.game_list = res.data.list;
  },
  beforeDestroy() {
    clearInterval(this.timer);
    this.timer = null;
  },
  watch: {
    send_input() {
      let str = this.send_input.toString();
      if (str.length > 50) {
        this.$toast('已超过最大字数限制');
        this.send_input = str.slice(0, 50);
      }
    },
  },
  methods: {
    async onResume() {
      await boxInit();
      await this.SET_USER_INFO(true);
      await this.getIndexData();
    },
    async getIndexData() {
      const res = await ApiHeishenhuaIndex();
      this.activity_status = res.data.activity_status;
      this.exchange_list = res.data.exchange_list.list;
      this.point = res.data.exchange_list.integral;
      this.sku = res.data.exchange_list.sku;
      this.svip_sku = res.data.exchange_list.svip_sku;
      this.dialList = res.data.lottery_list.list;
      this.lottery_count = res.data.lottery_list.remain_lottery;
      this.task_list = res.data.task_list;
      this.activity_explain = res.data.illustrate;
      this.message_list = res.data.message_list;
    },
    login() {
      BOX_login();
    },
    toLottery() {
      this.reward_list_popup = false;
      window.scrollTo(0, 0);
    },
    back2() {
      if (this.step == 2) {
        this.step = 1;
      } else {
        this.back();
      }
    },
    toKefu() {
      BOX_openInNewWindow(
        { name: 'Kefu' },
        { url: `https://${h5Page.env}game.3733.com/#/kefu` },
      );
    },
    async sendMessage() {
      if (!this.finished) {
        return false;
      }
      if (!this.send_input) {
        this.$toast('请输入内容');
        return false;
      }
      const res = await ApiHeishenhuaSendMessage({ content: this.send_input });
      await this.getIndexData();
      this.send_input = '';
    },
    // 获取记录列表
    async getRecordList() {
      if (!this.userInfo.token) {
        this.login();
        return false;
      }
      if (this.activity_status == 2 || this.activity_status == 3) {
        this.$toast(this.activity_status_text);
        return false;
      }
      this.$toast({
        type: 'loading',
        duration: 0,
        message: '拼命加载中...',
      });
      try {
        const res = await ApiHeishenhuaPointLog();
        this.record_list = res.data.list;
        this.record_list_popup = true;
      } finally {
        this.$toast.clear();
      }
    },
    // 获取奖品列表
    async getRewardList() {
      if (!this.userInfo.token) {
        this.login();
        return false;
      }
      if (this.activity_status == 2 || this.activity_status == 3) {
        this.$toast(this.activity_status_text);
        return false;
      }
      this.$toast({
        type: 'loading',
        duration: 0,
        message: '拼命加载中...',
      });
      try {
        const res = await ApiHeishenhuaLotteryLog();
        this.reward_list = res.data;
        this.reward_list_popup = true;
      } finally {
        this.$toast.clear();
      }
    },
    async getTakeTask(item) {
      if (this.activity_status != 1) {
        this.$toast(this.activity_status_text);
        return false;
      }
      // if (!this.userInfo.token) {
      //   this.$toast("请登录");
      //   this.login();
      //   return false;
      // }
      // if (item.status == 0) {
      //   this.$toast("尚未达到领取条件");
      //   return false;
      // }
      // if (item.status == 2) {
      //   this.$toast("您已领取过了~");
      //   return false;
      // }
      switch (item.task_id) {
        case 1:
          // sign
          const res = await ApiHeishenhuaTakeTask({ task_id: item.task_id });
          break;
        case 2:
          // todownload
          this.step = 2;
          break;
        case 3:
          // toshare
          await this.handleShare(item.task_id);
          await ApiHeishenhuaTakeTask({ task_id: item.task_id });
          break;
        case 4:
          this.$refs['section5'].scrollIntoView();
          break;
      }
      await this.getIndexData();
    },
    async getShareInfo() {
      let data = {
        type: 7,
        id: this.userInfo.user_id ? this.userInfo.user_id : 1,
      };
      const res = await ApiCommonShareInfo(data);
      this.shareInfo = res.data;
    },
    async handleShare() {
      await this.getShareInfo();
      if (this.initData?.share_info?.length) {
        if (!this.finished) {
          return false;
        }
        this.finished = false;
        setTimeout(() => {
          this.finished = true;
        }, 1000);
        window.BOX.mobShare(7, this.userInfo.user_id);
      } else {
        this.$copyText(this.shareInfo.share_text + this.shareInfo.url).then(
          async res => {
            this.$toast('链接已复制到剪贴板，快去邀请好友吧~');
          },
          err => {
            this.$dialog.alert({
              message: '复制失败',
              lockScroll: false,
            });
          },
        );
      }
    },
    async getTakeExchange(item) {
      if (this.activity_status != 1 && this.activity_status != 4) {
        this.$toast(this.activity_status_text);
        return false;
      }
      if (this.activity_status == 4 && this.exchange_status == 0) {
        this.$toast(this.activity_status_text);
        return false;
      }
      if (!this.userInfo.token) {
        this.$toast('请登录');
        this.login();
        return false;
      }
      const res = await ApiHeishenhuaExchange({ is_svip: item.is_svip });
      await this.getIndexData();
    },
    async handleRaffle() {
      if (this.isStarting) {
        return false;
      }
      this.isStarting = true;
      this.current = 0;
      try {
        const res = await ApiHeishenhuaLottery();
        await this.turning(res.data.lottery.lottery_id);
        this.lottery_result = res.data;
        this.lottery_result_popup = true;
        await this.getIndexData();
      } finally {
        this.isStarting = false;
      }
    },
    turning(id) {
      return new Promise((resolve, reject) => {
        this.turnCount = this.dialList.findIndex(item => {
          return item.lottery_id == id;
        });
        this.turnCount = this.turnCount + 1 + 40;
        this.timer = setInterval(() => {
          if (this.turnCount > 10) {
            this.currentChange();
            this.turnCount--;
          } else {
            clearInterval(this.timer);
            this.timer = setInterval(() => {
              if (this.turnCount > 0) {
                this.currentChange();
                this.turnCount--;
              } else {
                clearInterval(this.timer);
                this.timer = null;
                resolve();
              }
            }, 300);
          }
        }, 100);
      });
    },
    currentChange() {
      if (this.current > 7) {
        this.current = 1;
      } else {
        this.current++;
      }
    },
    async downloadGame(game, index) {
      if (!this.userInfo.token) {
        this.login();
        return false;
      }
      if (platform == 'android') {
        BOX_downloadGame(game);
      }
      if (!game.is_down) {
        this.game_list[index].is_down = true;
        if (platform !== 'android') {
          await ApiDownloadAdded({
            gameId: game.id,
            classId: game.classid,
          });
        }
        await ApiHeishenhuaTakeTask({ task_id: 2 });
        if (platform !== 'android') {
          // 打开游戏详情页
          BOX_goToGame(
            {
              params: {
                id: game.id,
              },
            },
            { id: game.id },
          );
        }
        const res = await ApiHeishenhuaGameList();
        this.game_list = res.data.list;
      } else {
        this.$toast('已完成该任务');
        if (platform !== 'android') {
          // 打开游戏详情页
          BOX_goToGame(
            {
              params: {
                id: game.id,
              },
            },
            { id: game.id },
          );
        }
      }
    },
  },
  ...mapActions({
    SET_USER_INFO: 'user/SET_USER_INFO',
  }),
};
</script>

<style lang="less" scoped>
.heishenhua-activity-page {
  width: 100%;
}

.back {
  width: 32 * @rem;
  height: 32 * @rem;
  position: fixed;
  top: 50 * @rem;
  left: 25 * @rem;
  z-index: 999;
  border-radius: 15 * @rem;
  background: rgba(255, 255, 255, 0.1)
    url(~@/assets/images/nav-bar-back-white.png) center center no-repeat;
  background-size: 8 * @rem 14 * @rem;
  border-radius: 50%;
}

.share {
  width: 32 * @rem;
  height: 32 * @rem;
  position: absolute;
  top: 50 * @rem;
  right: 12 * @rem;
  z-index: 999;
  .image-bg('~@/assets/images/heishenhua-activity/share.png');
}

.main {
  padding-bottom: 20 * @rem;
  background-image: url('~@/assets/images/heishenhua-activity/top-bg.png'),
    url('~@/assets/images/heishenhua-activity/bottom-bg.png'),
    url('~@/assets/images/heishenhua-activity/center-bg.png');
  background-position: center top, center bottom, center center;
  background-repeat: no-repeat, no-repeat, no-repeat;
  background-size: 100% auto, 100% auto, 100% auto;
  background-color: rgb(14, 14, 14);
}

.top-bg {
  width: 100%;
  height: 448 * @rem;
}

.table-container {
  width: 100%;
  margin: 22 * @rem 35 * @rem 16 * @rem;

  .table-content {
    box-sizing: border-box;

    .table-list {
      background-color: #fff;
      position: relative;

      .table-item {
        position: absolute;
        width: 95 * @rem;
        height: 95 * @rem;
        .image-bg('~@/assets/images/heishenhua-activity/lottery-bg.png');

        .reward-icon {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;

          .reward-img {
            width: 100%;
            height: 100%;
          }
        }

        .reward-text {
          position: absolute;
          left: 0;
          bottom: 6 * @rem;
          width: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 9 * @rem;
          color: #b8b2ae;
        }

        &:nth-of-type(1) {
          left: 0;
          top: 0;
        }

        &:nth-of-type(2) {
          left: 104 * @rem;
          top: 0;
        }

        &:nth-of-type(3) {
          left: 208 * @rem;
          top: 0;
        }

        &:nth-of-type(4) {
          left: 208 * @rem;
          top: 104 * @rem;
        }

        &:nth-of-type(5) {
          left: 208 * @rem;
          top: 208 * @rem;
        }

        &:nth-of-type(6) {
          left: 104 * @rem;
          top: 208 * @rem;
        }

        &:nth-of-type(7) {
          left: 0;
          top: 208 * @rem;
        }

        &:nth-of-type(8) {
          left: 0;
          top: 104 * @rem;
        }

        &.turn-btn-1 {
          left: 104 * @rem;
          top: 104 * @rem;
          width: 94 * @rem;
          height: 94 * @rem;
          .image-bg('~@/assets/images/heishenhua-activity/lottery-button.png');
        }

        &.on {
          .image-bg('~@/assets/images/heishenhua-activity/lottery-current2.png');
        }
      }
    }
  }
}

.my-reward-button {
  width: 150 * @rem;
  height: 32 * @rem;
  color: #eee9dd;
  font-family: Songti TC, Songti TC;
  font-size: 16 * @rem;
  color: #eee9dd;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 15 * @rem auto 35 * @rem;
  .image-bg('~@/assets/images/heishenhua-activity/my-reward-button.png');
}

section {
  width: 100%;
  height: 200 * @rem;
  background-image: url('~@/assets/images/heishenhua-activity/bg_top.png'),
    url('~@/assets/images/heishenhua-activity/bg_bottom.png'),
    url('~@/assets/images/heishenhua-activity/bg_center.png');
  background-position: center top, center bottom, center center;
  background-repeat: no-repeat, no-repeat, repeat;
  background-size: 100% auto, 100% auto, 100% auto;
  overflow: hidden;

  .section-title {
    background-size: auto 100%;
    background-repeat: no-repeat;
    background-position: center;
  }

  &.section1 {
    height: 425 * @rem;
    margin-top: 0;

    .section-title {
      background-image: url('~@/assets/images/heishenhua-activity/section1-title.png');
    }

    .explain-text {
      margin-top: 340 * @rem;
      text-align: center;
      font-size: 16 * @rem;
      color: #909090;
    }
  }

  &.section2 {
    height: 358 * @rem;
    margin-top: 30 * @rem;

    .section-title {
      background-image: url('~@/assets/images/heishenhua-activity/section2-title.png');
    }

    .task-item {
      display: flex;
      align-items: center;
      margin: 26 * @rem 28 * @rem 0;

      .task-item-left {
        flex: 1;
        min-width: 0;

        .task-name {
          width: 70 * @rem;
          height: 28 * @rem;
          box-sizing: border-box;
          font-size: 12 * @rem;
          color: #eee9dd;
          line-height: 15 * @rem;
          display: flex;
          padding-left: 10 * @rem;
          align-items: center;
          margin-bottom: 4 * @rem;
          .image-bg('~@/assets/images/heishenhua-activity/task-name-bg.png');
        }

        .task-explain {
          display: flex;
          align-items: end;

          .big-text {
            font-family: Songti TC, Songti TC;
            font-size: 12 * @rem;
            color: #eee9dd;
            line-height: 14 * @rem;
            white-space: nowrap;
          }

          /deep/ span {
            color: #f5cd6f;
          }

          .task-small-text {
            font-size: 10 * @rem;
            color: #eee9dd;
            line-height: 13 * @rem;
            white-space: nowrap;
          }
        }
      }

      .task-item-right {
        flex: 0 0 65 * @rem;
        width: 65 * @rem;
        height: 25 * @rem;
        font-size: 12 * @rem;
        color: #eee9dd;
        line-height: 19 * @rem;
        display: flex;
        justify-content: center;
        align-items: center;
        .image-bg('~@/assets/images/heishenhua-activity/task-button-bg.png');

        &.cant {
          color: #6d6b67;
          .image-bg('~@/assets/images/heishenhua-activity/task-button-cant.png');
        }
      }
    }
  }

  &.section3 {
    height: 197 * @rem;
    margin-top: 30 * @rem;

    .section-title {
      background-image: url('~@/assets/images/heishenhua-activity/section3-title.png');
    }

    .subtitle {
      position: relative;
      font-size: 12 * @rem;
      color: #eee9dd;
      line-height: 14 * @rem;
      text-align: center;
      margin: 19 * @rem 25 * @rem 25 * @rem;

      .text {
        position: relative;
        z-index: 1;
      }

      span {
        color: #f5cd6f;
      }

      &::after {
        position: absolute;
        left: 0;
        bottom: -3 * @rem;
        z-index: 0;
        content: '';
        width: 100%;
        height: 8 * @rem;
        background: #561b00;
      }
    }

    .contant-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 0 28 * @rem 25 * @rem;

      .contant-left {
        display: flex;
        font-size: 12 * @rem;
        color: #eee9dd;
        line-height: 15 * @rem;

        .icon {
          display: block;
          width: 18 * @rem;
          height: 15 * @rem;
          margin-right: 5 * @rem;
          .image-bg('~@/assets/images/heishenhua-activity/contant-icon.png');
        }
      }

      .contant-right {
        flex: 0 0 65 * @rem;
        width: 65 * @rem;
        height: 25 * @rem;
        font-size: 12 * @rem;
        color: #eee9dd;
        line-height: 19 * @rem;
        display: flex;
        justify-content: center;
        align-items: center;
        .image-bg('~@/assets/images/heishenhua-activity/task-button-bg.png');
      }
    }

    .contant-small-text {
      font-size: 10px;
      color: #99958d;
      line-height: 13px;
      padding: 0 37 * @rem;

      span {
        color: #d04606;
      }
    }
  }

  &.section4 {
    height: 350 * @rem;
    margin-top: 30 * @rem;

    .section-title {
      background-image: url('~@/assets/images/heishenhua-activity/section4-title.png');
      margin-bottom: 28 * @rem;
    }

    .point-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 0 27 * @rem 8 * @rem 50 * @rem;
      font-size: 12 * @rem;
      color: #eee9dd;

      .point-content-left {
        span {
          color: #f5cd6f;
        }
      }

      .point-content-right {
        display: flex;
        justify-content: center;
        align-items: center;

        .point-detail-button {
          width: 63 * @rem;
          height: 23 * @rem;
          margin-left: 6 * @rem;
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 10 * @rem;
          color: #fff;
          .image-bg('~@/assets/images/heishenhua-activity/point-detail.png');
        }
      }
    }

    .exchange-list {
      display: flex;
      justify-content: space-between;
      margin: 18 * @rem 32 * @rem 0;

      .exchange-item {
        .exchange-bg {
          position: relative;
          width: 140 * @rem;
          height: 140 * @rem;
          background-size: 100%;
          background-repeat: no-repeat;

          .exchange-num {
            position: absolute;
            left: 22 * @rem;
            bottom: 8 * @rem;
            color: #f5cd6f;
            font-size: 10 * @rem;
          }
        }

        .exchange-text {
          margin-top: 8 * @rem;
          text-align: center;
          color: #eee9dd;
        }

        .exchange-button {
          width: 105 * @rem;
          height: 32 * @rem;
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 16 * @rem;
          color: #eee9dd;
          margin: 15 * @rem auto 0;
          .image-bg('~@/assets/images/heishenhua-activity/exchange-button1.png');

          &.cant {
            opacity: 0.4;
            .image-bg('~@/assets/images/heishenhua-activity/exchange-button2.png');
          }
        }
      }
    }
  }

  &.section5 {
    height: 291 * @rem;
    margin-top: 30 * @rem;

    .section-title {
      background-image: url('~@/assets/images/heishenhua-activity/section5-title.png');
    }
  }

  .section-title {
    height: 23 * @rem;
    margin: 18 * @rem auto 0;
    font-family: Songti TC, Songti TC;
    font-weight: bold;
    font-size: 18px;
    color: #eee9dd;
    line-height: 23px;
  }
}

.activity-explain {
  margin: 26 * @rem 15 * @rem;

  .explain-title {
    font-family: Songti TC, Songti TC;
    font-weight: bold;
    font-size: 18 * @rem;
    color: #eee9dd;
    line-height: 23 * @rem;
  }

  .explain-content {
    margin: 13 * @rem auto 0;

    /deep/ p {
      font-family: Dream Han Sans CN, Dream Han Sans CN;
      font-weight: normal;
      font-size: 11 * @rem;
      color: #eee9dd;
      line-height: 22 * @rem;
    }
  }

  .activity-explain-small {
    margin-top: 7 * @rem;
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 11 * @rem;
    color: #403f3c;
    line-height: 14 * @rem;
    text-align: center;
  }
}

.popup {
  overflow: hidden;
  width: 100%;
  height: 200 * @rem;
  background: transparent;
  overflow: hidden;
  max-width: 374 * @rem;

  .popup-close {
    width: 22 * @rem;
    height: 22 * @rem;
    .image-bg('~@/assets/images/heishenhua-activity/popup-close.png');
    position: absolute;
    right: 16 * @rem;
    top: 0 * @rem;
  }

  .title {
    font-size: 16 * @rem;
    font-family: PingFang SC-Semibold, PingFang SC;
    font-weight: 600;
    color: rgba(124, 52, 63, 1);
    line-height: 20 * @rem;
    text-align: center;
    margin-bottom: 30 * @rem;
  }

  &.message-popup {
    height: 320 * @rem;

    .text {
      width: 254 * @rem;
      margin: 0 auto;
      padding: 20 * @rem 0;
      font-size: 14 * @rem;
      font-family: PingFang SC-Semibold, PingFang SC;
      font-weight: 600;
      color: #7b74aa;
      line-height: 18 * @rem;
      text-align: center;
    }
  }
}

.record-list-popup {
  height: 320 * @rem;

  section {
    height: 100%;

    .section-title {
      background-image: url('~@/assets/images/heishenhua-activity/point-title.png');
    }
  }

  .list {
    height: 250 * @rem;
    overflow-y: scroll;
    margin: 14 * @rem 20 * @rem;
    padding: 0 15 * @rem;

    .item {
      display: flex;
      justify-content: space-between;
      height: 30 * @rem;
      align-items: center;
      font-size: 14 * @rem;

      .left {
        flex: 0 0 90 * @rem;
        white-space: nowrap;
        color: #838079;
      }

      .right {
        flex: 1;
        text-align: right;
        color: #eee9dd;
      }

      /deep/ span {
        color: #f8582e;
      }
    }
  }

  .empty {
    width: 100%;
    height: 210 * @rem;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #838079;
  }
}

.reward-list-popup {
  height: 190 * @rem;

  section {
    .section-title {
      background-image: url('~@/assets/images/heishenhua-activity/reward-title.png');
    }
  }

  .list {
    .item {
      .left {
        flex: 0 0 90 * @rem;
        white-space: nowrap;
        color: #eee9dd;
      }

      .right {
        flex: 1;
        text-align: right;
        color: #838079;
      }
    }
  }

  .empty {
    height: 128 * @rem;
    margin: 10 * @rem 0;

    span {
      color: #d04606;
    }
  }
}

.lottery-result-popup {
  height: 230 * @rem;

  section {
    height: 100%;
  }

  .lottery-result-img {
    display: block;
    width: 100 * @rem;
    height: 100 * @rem;
    margin: 3 * @rem auto 5 * @rem;
  }

  .lottery-result-text {
    font-size: 12 * @rem;
    color: #838079;
    text-align: center;
    line-height: 16 * @rem;
  }

  .lottery-result-button {
    width: 150 * @rem;
    height: 32 * @rem;
    color: #eee9dd;
    font-family: Songti TC, Songti TC;
    font-size: 16 * @rem;
    color: #eee9dd;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 15 * @rem auto;
    .image-bg('~@/assets/images/heishenhua-activity/my-reward-button.png');
  }
}

.marquee-container {
  margin: 15 * @rem 17 * @rem;
  height: 170 * @rem;
}

.marquee {
  .item {
    color: #eee9dd;
    height: 30 * @rem;
    line-height: 30 * @rem;
    white-space: nowrap;
    margin-bottom: 10 * @rem;
    margin-right: 10 * @rem;
    padding: 0 13 * @rem;
    background: #2e2c2c;
    border-radius: 15 * @rem;
    font-size: 12 * @rem;
  }
}

.send-container {
  display: flex;
  margin: 0 30 * @rem;

  .send-label {
    flex: 1;
    display: flex;
    background: #fff;
    background: #ffffff;
    box-shadow: inset 2 * @rem 2 0 0 #9e9e9d;
    border-radius: 15 * @rem;
    height: 32 * @rem;
    line-height: 32 * @rem;
    color: #333;
    overflow: hidden;

    .send-input {
      flex: 1;
      padding: 0 8 * @rem 0 12 * @rem;
    }

    .count {
      margin-right: 8 * @rem;
      color: #cccccc;
    }
  }

  .send-button {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 84 * @rem;
    height: 32 * @rem;
    margin-left: 8 * @rem;
    font-size: 16px;
    color: #eee9dd;
    .image-bg('~@/assets/images/heishenhua-activity/send.png');
  }
}

#game-list {
  height: auto;
}

.game-list {
  margin: 0 30 * @rem;

  .game-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20 * @rem;

    .game-img {
      flex: 0 0 48 * @rem;
      width: 48 * @rem;
      height: 48 * @rem;
      margin-right: 10 * @rem;
    }

    .game-info {
      flex: 1;
      min-width: 0;
      overflow: hidden;
      margin-right: 10 * @rem;

      .game-name {
        .name {
          font-size: 14px;
          color: #eee9dd;
          line-height: 18px;
        }

        .subtitle {
          font-size: 10px;
          color: #dedede;
          padding: 2 * @rem 4 * @rem;
          border: 1px solid #dedede;
        }
      }

      .game-type-list {
        display: flex;
        flex-wrap: nowrap;
        margin-top: 9 * @rem;

        .game-type-item {
          color: #999999;
        }
      }
    }

    .game-button {
      width: 65 * @rem;
      height: 25 * @rem;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #fff;
      .image-bg('~@/assets/images/heishenhua-activity/task-button-bg.png');

      &.cant {
        color: #6d6b67;
        .image-bg('~@/assets/images/heishenhua-activity/task-button-cant.png');
      }
    }
  }
}
</style>
