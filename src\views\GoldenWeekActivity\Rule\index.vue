<template>
  <div class="activity-rule">
    <nav-bar-2
      ref="topNavBar"
      :bgStyle="navBgTransparent ? 'transparent-white' : 'white'"
      :azShow="true"
      :placeholder="false"
      title="活动规则"
    >
      <template #left>
        <div class="back" @click="back"></div>
      </template>
    </nav-bar-2>
    <div class="top-bg"></div>
    <div class="main">
      <div class="section">
        <div class="title"><span>活动介绍：</span></div>
        <div class="desc">
          月满人团圆，欢度中秋节！参与双节福利活动，豪礼领不停~
        </div>
        <div class="desc color">
          活动时间：2023年9月28日00:00 - 2023年10月6日23:59
        </div>
      </div>
      <div class="section">
        <div class="container">
          <div class="big-text">一、双节活跃奖励</div>
          <div class="text">
            每日完成指定任务获得月饼兑换券，使用月饼兑换卷可兑换超值奖励，快来参与吧！
          </div>
          <div class="list-container">
            <div class="list-title">任务列表:</div>
            <div class="list-content">
              1.每日登录游戏<br />
              2.每日玩游戏30分钟<br />
              3.每日不低于10元的实付充值1次<br />
              每日完成所有活跃任务得月饼兑换券*2（0/3）
            </div>
          </div>

          <div class="list-container">
            <div class="list-title">月饼兑换商店：</div>
            <div class="list-content">
              1.28金币（消耗兑换券*1）活动期间可兑换4次<br />
              2.66金币（消耗兑换券*2）活动期间可兑换1次<br />
              3.88金币+1天SVIP（消耗兑换券*4）活动期间可兑换1次<br />
              4.平台币*50（消耗兑换券*8）活动期间可兑换1次
            </div>
          </div>
        </div>
        <div class="container">
          <div class="big-text">二、充值得积分</div>
          <div class="text">
            活动期间，玩家实付充值可获得积分，RMB对积分的比例为1:10，即充值1元获得10积分。
          </div>
          <div class="desc color">
            温馨提示：仅限游戏内使用微信/支付宝充值，如有使用金币/平台币/代金券抵扣的，则只计算抵扣后的现金充值金额。
          </div>
        </div>
        <div class="container">
          <div class="big-text">三、黄金周福利转盘</div>
          <div class="text">
            玩家消耗积分进行抽奖，每次转动消耗100积分，累计转盘次数还将获得额外奖励，多转多得，不容错过！支持十连抽！
          </div>

          <div class="list-container">
            <div class="list-title">累计转盘次数奖励：</div>
            <div class="list-content">
              累计转动10次可领取“中秋快乐”礼盒：288金币。<br />
              累计转动30次可领取“桂子飘香”礼盒：588金币。<br />
              累计转动50次可领取“皓月当空”礼盒：688金币 + 3天SVIP。<br />
              累计转动100次可领取“花好月圆”礼盒：888金币 + 7天SVIP。<br />
              累计转动200次可领取“月满乾坤”礼盒：388平台币。<br />
              累计转动300次可领取“合家团圆”礼盒：688平台币。
            </div>
          </div>
        </div>
        <div class="container">
          <div class="big-text">四、转盘概率公示</div>
          <div class="table">
            <div class="tr">
              <div class="th">奖品</div>
              <div class="th">概率</div>
            </div>
            <div class="tr">
              <div class="td">88平台币</div>
              <div class="td">1%</div>
            </div>
            <div class="tr">
              <div class="td">3天SVIP</div>
              <div class="td">3%</div>
            </div>
            <div class="tr">
              <div class="td">288金币</div>
              <div class="td">6%</div>
            </div>
            <div class="tr">
              <div class="td">1天SVIP</div>
              <div class="td">10%</div>
            </div>
            <div class="tr">
              <div class="td">66金币</div>
              <div class="td">20%</div>
            </div>
            <div class="tr">
              <div class="td">28金币</div>
              <div class="td">60%</div>
            </div>
          </div>
        </div>
        <div class="container">
          <div class="big-text">五、活动说明：</div>
          <div class="text">
            1.活动期间充值完成后请返回本活动页面点击<span class="color"
              >【刷新】</span
            >领取奖励，请及时领取累计奖励，活动结束后将清空所有奖励领取机会。
          </div>
          <div class="text">
            <span class="color">2.温馨提示：</span
            >由于游戏厂商的要求，部分游戏内充值暂不支持使用金币支付，请先确定需要用金币充值的游戏在不在可使用名单内。
          </div>
          <div class="text">
            <span @click="game_dialog_show = true" class="color underline btn"
              >查看名单&gt;</span
            >
          </div>
        </div>
      </div>
    </div>
    <!-- 查看名单 -->
    <noGameList
      :game_dialog_show="game_dialog_show"
      @changeGameDialogShow="changeGameDialogShow"
    />
  </div>
</template>
<script>
import { ApiGameCardSearchGame } from '@/api/views/recharge.js';
import { BOX_goToGame, BOX_openInNewWindow } from '@/utils/box.uni.js';
import { envFun } from '@/utils/function';
import noGameList from '@/components/no-game-list';

export default {
  name: 'Rule',
  components: {
    noGameList,
  },
  data() {
    return {
      game_dialog_show: false, //查看名单弹窗
      navBgTransparent: true,
    };
  },
  mounted() {
    // 获取顶部导航栏的高度
    this.stickyOffsetTop =
      document.querySelector('.van-nav-bar').offsetHeight + 'px';
    window.addEventListener('scroll', this.handleScroll);
  },
  methods: {
    // 滚动处理
    handleScroll(e) {
      let scrollTop = e.target.scrollingElement.scrollTop;
      if (scrollTop > 100) {
        this.navBgTransparent = false;
      } else {
        this.navBgTransparent = true;
      }
    },
    changeGameDialogShow(show) {
      this.game_dialog_show = show;
    },
  },
};
</script>
<style lang="less" scoped>
.activity-rule {
  overflow: hidden;
  position: relative;

  .back {
    width: 30 * @rem;
    height: 30 * @rem;
    border-radius: 15 * @rem;
    background: rgba(0, 0, 0, 0.3) url(~@/assets/images/nav-bar-back-white.png)
      center center no-repeat;
    background-size: 8 * @rem 14 * @rem;
  }
  .top-bg {
    width: 100%;
    height: 190 * @rem;
    .image-bg('~@/assets/images/golden-week-activity/rule-bg.png');
    background-size: 100% 190 * @rem;
  }
  .main {
    background-color: #fff1d8;
    border-radius: 30 * @rem 30 * @rem 0 0;
    margin-top: -100 * @rem;
    padding: 22 * @rem 20 * @rem 35 * @rem;
  }
  .title {
    margin-bottom: 15 * @rem;
    span {
      background-image: url('~@/assets/images/july-activity/rule_title_bg.png');
      background-size: 100%;
      background-repeat: no-repeat;
      background-position: 0 16 * @rem;
      font-size: 16 * @rem;
      font-family: PingFang HK-Semibold, PingFang HK;
      font-weight: 600;
      color: #934028;
    }
  }
  .big-text {
    margin-bottom: 7 * @rem;
    font-size: 14 * @rem;
    font-family: PingFang SC-Semibold, PingFang SC;
    font-weight: 600;
    color: #934028;
    line-height: 18 * @rem;
  }
  .desc {
    margin-bottom: 6 * @rem;
    font-size: 12 * @rem;
    color: #934028;
    line-height: 18 * @rem;
  }
  .text {
    margin-bottom: 6 * @rem;
    font-size: 11 * @rem;
    color: #934028;
    line-height: 18 * @rem;
  }
  .color {
    color: #ff4009;
  }
  .underline {
    text-decoration: underline;
  }
  .container {
    margin-top: 21 * @rem;
  }
}
.no-gold-game-popup {
  overflow: unset;
  width: 320 * @rem;
  .search-container {
    box-sizing: border-box;
    width: 320 * @rem;
    height: 450 * @rem;
    padding: 24 * @rem 19 * @rem 10 * @rem;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: unset;
    .close-search {
      width: 24 * @rem;
      height: 24 * @rem;
      background: url(~@/assets/images/close-search.png) center center no-repeat;
      background-size: 24 * @rem 24 * @rem;
      position: absolute;
      right: -10 * @rem;
      top: -10 * @rem;
    }
    .search-bar {
      display: flex;
      align-items: center;
      .input-text {
        width: 240 * @rem;
        height: 35 * @rem;
        border: 1 * @rem solid #e5e5e5;
        border-radius: 18 * @rem;
        flex: 1;
        overflow: hidden;
        form {
          display: block;
          width: 100%;
          height: 100%;
        }
        input {
          box-sizing: border-box;
          display: block;
          width: 100%;
          height: 100%;
          padding: 0 18 * @rem;
          font-size: 15 * @rem;
          color: #333333;
          background-color: #f6f6f6;
        }
      }
      .search-btn {
        font-size: 15 * @rem;
        color: #666666;
        padding-left: 13 * @rem;
        height: 35 * @rem;
        line-height: 35 * @rem;
      }
    }
    .game-list {
      flex: 1;
      overflow: auto;
      margin-top: 10 * @rem;
      .game-item {
        display: flex;
        align-items: center;
        padding: 21 * @rem 0;
        border-bottom: 1 * @rem solid #eeeeee;
        .game-icon {
          width: 50 * @rem;
          height: 50 * @rem;
          border-radius: 10 * @rem;
          background-color: #b5b5b5;
        }
        .right {
          flex: 1;
          min-width: 0;
          margin-left: 10 * @rem;
          .game-name {
            font-size: 16 * @rem;
            font-weight: bold;
            color: #000000;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
          }
          .use-card {
            font-size: 12 * @rem;
            color: #f72e2e;
            margin-top: 10 * @rem;
            &.can {
              color: #36b150;
            }
          }
        }
      }
    }
  }
}
.list-container {
  padding-top: 1 * @rem;
  .list-title {
    font-size: 12 * @rem;
    color: #934028;
    font-weight: 600;
    margin: 10 * @rem 0 10 * @rem;
  }
  .list-content {
    font-size: 12 * @rem;
    color: #934028;
    line-height: 21 * @rem;
  }
}

.table {
  border-left: 1px solid #7c343f;
  border-top: 1px solid #7c343f;
  .tr {
    display: flex;
  }
  .td,
  .th {
    width: 50%;
    font-size: 12 * @rem;
    color: #97603d;
    height: 24 * @rem;
    display: flex;
    justify-content: center;
    align-items: center;
    border-right: 1px solid #7c343f;
    border-bottom: 1px solid #7c343f;
  }
  .th {
    background: rgba(124, 52, 63, 0.1);
    width: 50%;
    font-size: 14 * @rem;
    color: #97603d;
    font-weight: bold;
  }
}
</style>
