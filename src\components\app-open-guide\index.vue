<template>
  <div v-if="showGuide" class="app-guide-overlay">
    <div class="down-container">
      <div class="left-box">
        <div class="logo">
          <img src="~@/assets/images/app-icon2.png" alt="" />
        </div>
        <div class="content">
          <div class="title">3733游戏盒</div>
          <div class="tags">
            <span class="tag">充值低至0.1折</span>
            <span class="tag">免费领首充 </span>
          </div>
        </div>
      </div>
      <div class="right-box">
        <div class="btn" @click="handleDownload()">下载APP</div>
      </div>
    </div>
    <div class="btn-container">
      <div class="btn-box">
        <div class="btn-bg">
          <img src="~@/assets/images/btn-box-bg.png" alt="" />
        </div>
        <div class="btn-close btn" @click="handleDownload()"></div
      ></div>
    </div>
  </div>
</template>

<script>
import { openSchemeWithCheck } from '@/utils/function';
import { isAndroid, isInIframe } from '@/utils/userAgent';
export default {
  name: 'AppOpenGuide',
  data() {
    return {
      showGuide: false,
    };
  },
  mounted() {
    this.showGuide = this.showAppOpenGuide();
  },
  methods: {
    handleDownload() {
      openSchemeWithCheck(
        'a3733://action?',
        this.handleOpenSchemeFail,
        3000,
        true,
      );
    },
    handleOpenSchemeFail() {
      window.location.href = 'https://app.3733.com';
    },
    // 是否显示APP打开引导
    showAppOpenGuide() {
      return !isInIframe && isAndroid && !isWechat;
    },
  },
};
</script>

<style scoped lang="less">
.app-guide-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
  .down-container {
    position: fixed;
    box-sizing: border-box;
    z-index: 9;
    left: 0;
    top: 0;
    width: 100%;
    height: 66 * @rem;
    background: #ffffff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 18 * @rem;
    .left-box {
      display: flex;
      align-items: center;
      .logo {
        width: 50 * @rem;
        height: 50 * @rem;
      }
      .content {
        margin-left: 10 * @rem;
        display: flex;
        flex-direction: column;
        .title {
          height: 20 * @rem;
          font-family: PingFang SC, PingFang SC;
          font-weight: bold;
          font-size: 16 * @rem;
          color: #333333;
          line-height: 20 * @rem;
        }
        .tags {
          display: flex;
          align-items: center;
          margin-top: 6 * @rem;
          .tag {
            height: 18 * @rem;
            line-height: 18 * @rem;
            padding: 0 4 * @rem;
            background: rgba(0, 0, 0, 0.05);
            border-radius: 4 * @rem;
            font-size: 11 * @rem;
            color: #868686;
            margin-right: 8 * @rem;
          }
        }
      }
    }
    .right-box {
      .btn {
        width: 73 * @rem;
        height: 28 * @rem;
        background: linear-gradient(270deg, #6ddc8c 0%, #21b98a 99%);
        border-radius: 23 * @rem;
        font-weight: 600;
        font-size: 12 * @rem;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        white-space: nowrap;
      }
    }
  }
  .btn-container {
    position: fixed;
    bottom: 60 * @rem;
    .btn-box {
      position: relative;

      .btn-bg {
        width: 266 * @rem;
        height: 110 * @rem;
      }
      .btn-close {
        position: absolute;
        bottom: 0;
        width: 264 * @rem;
        height: 48 * @rem;
      }
    }
  }
}
</style>
