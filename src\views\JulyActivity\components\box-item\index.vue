<template>
  <div class="box-item-component">
    <!-- 普通冰块 -->
    <template v-if="!itemInfo.is_reward">
      <div class="ice frozen" v-if="frozen"></div>
      <template v-else>
        <div
          class="ice ice-base"
          key="ice-base"
          v-if="itemInfo.status == 0"
        ></div>
        <template v-if="itemInfo.status == 2">
          <div
            class="ice result-gold"
            key="result-gold"
            v-if="itemInfo.type == 1"
          >
            {{ itemInfo.num }}金币
          </div>
          <div
            class="ice result-vip"
            key="result-vip"
            v-else-if="itemInfo.type == 2"
          >
            {{ itemInfo.num }}天SVIP
          </div>
          <div
            class="ice result-ptb"
            key="result-ptb"
            v-else-if="itemInfo.type == 3"
          >
            {{ itemInfo.num }}平台币(绑)
          </div>
        </template>
      </template>
    </template>
    <!-- 箱子 -->
    <template v-else>
      <div class="ice frozen-box" v-if="frozen" 
          @click="$toast(`宝箱内含：${itemInfo.title}`)"></div>
      <template v-else>
        <div
          class="ice box-ice"
          key="box-ice"
          v-if="itemInfo.status == 0"
          @click="$toast(`宝箱内含：${itemInfo.title}`)"
        ></div>
        <div
          ref="giftLottie"
          class="ice box-lottie"
          v-else-if="itemInfo.status == 1"
          key="box-lottie"
          @click.capture.stop="handleKnock"
        ></div>
        <template v-else-if="itemInfo.status == 2">
          <div
            class="ice result-gold"
            key="result-gold"
            v-if="itemInfo.type == 1"
          >
            {{ itemInfo.title }}
          </div>
          <div
            class="ice result-vip"
            key="result-vip"
            v-else-if="itemInfo.type == 2"
          >
            {{ itemInfo.title }}
          </div>
          <div
            class="ice result-ptb"
            key="result-ptb"
            v-else-if="itemInfo.type == 3"
          >
            {{ itemInfo.title }}
          </div>
        </template>
      </template>
    </template>
    <div
      ref="knockLottie"
      class="knock-lottie"
      :class="{ 'z-index-down': !isAnimating }"
    ></div>
  </div>
</template>

<script>
import lottie from 'lottie-web';
import ae_gift_data from '@/assets/ae/july-activity/ae_gift_data/data.json';
import ae_knock_data from '@/assets/ae/july-activity/ae_knock_data/data.json';
import { ApiActivityJulyTakeReward } from '@/api/views/july_activity.js';
export default {
  props: {
    info: {
      type: Object,
      required: true,
    },
    frozen: {
      type: Boolean,
      default: false,
    },
    knockingInfo: {
      type: Object,
      default: () => {},
    },
    tabIndex: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      itemInfo: {},
      knockLottie: null,
      giftLottie: null,
      isAnimating: false,
    };
  },
  watch: {
    info: {
      handler(val, oldVal) {
        this.itemInfo = val;
      },
      deep: true,
      immediate: true,
    },
    knockingInfo: {
      handler(val, oldVal) {
        if (this.itemInfo.id == val.ice_id) {
          this.handleKnock();
        }
      },
      deep: true,
    },
  },
  mounted() {
    this.loadGiftLottie();
    this.loadKnockLottie();
  },
  updated() {
    this.loadGiftLottie();
  },
  methods: {
    handleKnock() {
      this.isAnimating = true;
      this.knockLottie.play();
    },
    async getBoxReward() {
      // 可领取
      const res = await ApiActivityJulyTakeReward({
        reward_id: this.itemInfo.reward_id,
        num: this.tabIndex + 1,
      });
      this.itemInfo.status = 2;
      this.isAnimating = false;
      this.$emit('refresh');
    },
    loadGiftLottie() {
      if (this.giftLottie) {
        this.giftLottie.destroy();
      }
      let params = {
        container: this.$refs.giftLottie,
        renderer: 'svg',
        loop: true,
        autoplay: true,
        animationData: ae_gift_data,
      };
      this.giftLottie = lottie.loadAnimation(params);
    },
    loadKnockLottie() {
      if (this.knockLottie) {
        this.knockLottie.destroy();
      }
      let params = {
        container: this.$refs.knockLottie,
        renderer: 'svg',
        loop: true,
        autoplay: false,
        animationData: ae_knock_data,
      };
      this.knockLottie = lottie.loadAnimation(params);
      this.knockLottie.addEventListener('loopComplete', () => {
        this.knockLottie.pause();
        if (this.itemInfo.is_reward) {
          this.getBoxReward();
          return false;
        }
        this.$toast(this.knockingInfo.msg);
        this.itemInfo.status = 2;
        this.isAnimating = false;
        this.$emit('refresh');
      });
    },
  },
};
</script>

<style lang="less" scoped>
.box-item-component {
  position: relative;
  .knock-lottie {
    left: -15 * @rem;
    top: -28 * @rem;
    margin: auto;
    position: absolute;
    width: 100 * @rem;
    height: 100 * @rem;
    z-index: 10;
    &.z-index-down {
      z-index: -1;
    }
  }
}

.ice {
  position: relative;
  width: 58 * @rem;
  height: 58 * @rem;
  box-shadow: 0 * @rem 4 * @rem 4 * @rem 0 * @rem rgba(0, 0, 0, 0.25);
  border-radius: 10 * @rem;
  line-height: 18*@rem;
  &.ice-base {
    .image-bg('~@/assets/images/july-activity/ice-base.png');
    background-size: 58 * @rem 58 * @rem;
  }
  &.result-gold {
    box-sizing: border-box;
    font-size: 11 * @rem;
    font-weight: 600;
    color: #4d96e3;
    text-align: center;
    padding-top: 40 * @rem;
    white-space: nowrap;
    .image-bg('~@/assets/images/july-activity/gold-result.png');
    background-size: 58 * @rem 58 * @rem;
  }
  &.result-vip {
    box-sizing: border-box;
    font-size: 11 * @rem;
    font-weight: 600;
    color: #4d96e3;
    text-align: center;
    padding-top: 40 * @rem;
    white-space: nowrap;
    .image-bg('~@/assets/images/july-activity/vip-result.png');
    background-size: 58 * @rem 58 * @rem;
  }
  &.result-ptb {
    box-sizing: border-box;
    font-size: 8 * @rem;
    font-weight: 600;
    color: #4d96e3;
    text-align: center;
    padding-top: 40 * @rem;
    white-space: nowrap;
    .image-bg('~@/assets/images/july-activity/ptb-result.png');
    background-size: 58 * @rem 58 * @rem;
  }
  &.ice-lock {
    .image-bg('~@/assets/images/july-activity/ice-lock.png');
    background-size: 58 * @rem 58 * @rem;
  }
  &.box-ice {
    .image-bg('~@/assets/images/july-activity/ice-box.png');
    background-size: 58 * @rem 58 * @rem;
  }
  &.box-result {
    .image-bg('~@/assets/images/july-activity/ice-box.png');
    background-size: 58 * @rem 58 * @rem;
  }
  &.frozen {
    .image-bg('~@/assets/images/july-activity/ice-lock.png');
    background-size: 58 * @rem 58 * @rem;
  }
  &.frozen-box {
    .image-bg('~@/assets/images/july-activity/ice-box-lock.png');
    background-size: 58 * @rem 58 * @rem;
  }
}
</style>
