import store from '../store';
import router from '../router';
import { service } from './axios';
import { encrypt, decrypt, initKeyIv } from '@/utils/Encrypt';
import { Toast, Dialog } from 'vant';
import BASEPARAMS from '@/utils/baseParams';
import h5Page from '@/utils/h5Page';
import { themeColorLess } from '@/common/styles/_variable.less';

import {
  platform,
  from,
  BOX_login,
  authInfo,
  packageName,
  BOX_memAuth,
  BOX_openInNewNavWindow,
} from '@/utils/box.uni.js';
import { isIos } from '@/utils/userAgent';
import qs from 'qs';
import { devLog } from '@/utils/function.js';

/**
 * 封装请求方法
 * @param url
 * @param parmas
 * @param isEncrypt 是否加密
 * @param domain 传输的域名
 * @param res.data.code 返回参数
 *  1.通用成功（明文）
 *  2.通用成功（RSA密文）
 *  3.通用成功（AES密文）
 *  0.通用错误消息，客户端尽可能将此消息通过界面提示给用户
 *  -1.静默错误消息，客户端不要将此消息提示给用户，仅用于调试打印LOG
 *  -2.登录失效，需要重新登录
 *  -3.登录过期，需要重新登录
 *  -4.服务器维护中，弹个什么东西告知下用户吧
 *  -5.RSA秘钥相关错误，需要尝试重新获取RSA秘钥
 *  -6.提示客户端需要升级
 *  -7.设备号错误
 *  -8.直接弹出绑定手机号的界面
 *  -9.弹出对话框展示错误消息，标题：需要实名认证，按钮：去认证、继续游戏
 *  -10.不是SVIP用户
 *  -11.客户端清除登录信息，并弹出使用手机验证码登录的界面
 *  -12.微信第一次登录，弹出绑定手机号界面
 *  -13.拉黑状态下的微信登录弹窗
 *  -14.跳转到SVIP充值界面
 *  -15.AES秘钥相关错误
 *  -30.错误信息以弹窗形式展现，标题：错误，按钮：我知道了
 */

export function request(
  url = '',
  params = {},
  isEncrypt = true,
  domain = h5Page.api_url,
) {
  // 拦截重复请求
  let currentLoadingUrl = `${url}&${qs.stringify(params)}`; // 要拼接上参数才能保证请求的唯一性
  if (store.getters['system/loadingUrl'] == currentLoadingUrl) {
    devLog('activity', '拦截到重复请求：', currentLoadingUrl);
    return Promise.reject({ code: -1000 });
  }
  store.commit('system/setLoadingUrl', currentLoadingUrl);
  // 整理参数
  if (packageName) {
    // 带上包名（不同包名有不同的默认头像）
    BASEPARAMS.packageName = packageName;
  }
  //  带上token 和 from
  if (store.getters['user/userInfo']?.token) {
    BASEPARAMS.token = store.getters['user/userInfo']?.token;
  }
  if (store.getters['user/from']) {
    BASEPARAMS.from = store.getters['user/from'];
  }
  if (store.getters['user/uuid']) {
    BASEPARAMS.uuid = store.getters['user/uuid'];
  }

  switch (platform) {
    case 'ios':
    case 'android':
      // 官包其他公共参数
      if (authInfo) {
        BASEPARAMS.uuid = authInfo.uuid;
        BASEPARAMS.versionCode = authInfo.versionCode;
        BASEPARAMS.channel = authInfo.channel;
      }
      break;
    case 'androidBox':
      // 安卓马甲包都是用web的公共参数，但是需要一个壳的版本号
      if (authInfo) {
        BASEPARAMS.packageVersionCode = authInfo.versionCode;
      }
    default:
      break;
  }

  const reqData = { ...BASEPARAMS, ...params };
  url = `${domain}${url}`;
  let headers = { 'Content-Type': 'multipart/form-data' };
  devLog('req:activity', url, reqData);
  // 判断是否加密
  let data = {};
  let [key, iv] = [];
  if (isEncrypt) {
    // 生成随机数key iv
    [key, iv] = initKeyIv();
    // 加密
    data = encrypt(reqData, key, iv);
    // 旧
    // data.code = 4;
    // 新
    data.code = 113;
  } else {
    data = reqData;
    data.code = 1;
  }
  // 转成formData格式，支持跨域
  let formData = new FormData(); // 创建form对象
  for (let i in data) {
    formData.append(i, data[i]);
  }

  let serviceData = {
    method: 'post',
    url: url,
    data: formData,
    headers: headers,
  };

  // 测试h5游戏互通，登录接口跨域传cookie，通讯方法不一定靠谱，可能以后废弃，放一起好删;还有一块在打开h5游戏方法那
  if (
    url.indexOf('/api/user/login') > -1 &&
    process.env.NODE_ENV !== 'development'
  ) {
    url = url.replace('api.', 'api2.');
    url = url.replace('a3733', '3733');
    serviceData.url = url;
    serviceData.withCredentials = true;
  }

  return new Promise((resolve, reject) => {
    service(serviceData)
      .then(res => {
        devLog('res:activity', url, res);
        let code = parseInt(res.data.code);
        // points全局判断
        // res.data.points = [{ rule_name: '12313', text: '34234', num: '123' }, { rule_name: '12313', text: '34234', num: '123' }]
        if (
          res.data.points &&
          res.data.points.length > 0 &&
          res.config.url.indexOf('/api/user/activeSign') == -1
        ) {
          // 是否有金币信息 并且不是签到页面的请求(签到请求单独处理弹窗)
          let pointStr = '';
          res.data.points.forEach(point => {
            pointStr += `${point.rule_name}，${point.text}${point.num}\n`;
          });
          Dialog.alert({
            message: pointStr,
            lockScroll: false,
          });
        }
        switch (code) {
          case 3:
            const resData = decrypt(res.data.data, key, iv);
            try {
              res.data.data = JSON.parse(resData);
            } catch {
              res.data.data = resData;
            }
            if (res.data.msg) {
              // 中秋博饼延后弹窗配合骰子动画
              if (url.indexOf('/activity/autumn/diceBySelf')) {
                setTimeout(() => {
                  Toast(res.data.msg);
                }, 1000);
              } else {
                Toast(res.data.msg);
              }
            }
            resolve(res.data);
            break;
          case 1:
            if (res.data.msg) {
              Toast(res.data.msg);
            }
            resolve(res.data);
            break;
          case 0:
            Toast(res.data.msg);
            reject(res.data);
            break;
          case -2:
          case -3:
            store.commit('user/setUserInfo', {});
            store.commit('user/setUserInfoEx', {});
            Toast.clear();
            Dialog.confirm({
              message: res.data.msg,
              confirmButtonText: '确定',
              confirmButtonColor: themeColorLess,
              lockScroll: false,
            }).then(() => {
              setTimeout(() => {
                BOX_login();
              }, 0);
            });
            reject(res.data);
            break;
          case -4:
            Toast('我知道了');
            break;
          case -8:
            Toast.clear();
            Dialog.confirm({
              message: res.data.msg,
              confirmButtonText: '去绑定',
              cancelButtonText: '取消',
              confirmButtonColor: themeColorLess,
              lockScroll: false,
            }).then(() => {
              router.push({ name: 'ChangePhone' });
            });
            break;
          case -9:
            Toast.clear();
            Toast('请先实名认证');
            setTimeout(() => {
              BOX_memAuth();
            }, 500);
            break;
          case -10:
            Toast.clear();
            Dialog.confirm({
              message: res.data.msg,
              confirmButtonText: '去开通',
              cancelButtonText: '取消',
              confirmButtonColor: themeColorLess,
              lockScroll: false,
            }).then(() => {
              BOX_openInNewNavWindow(
                { name: 'Svip' },
                { url: h5Page.svip_url },
              );
            });
            break;
          case -11:
            store.commit('user/setUserInfo', {});
            store.commit('user/setUserInfoEx', {});
            Toast(res.data.msg);
            BOX_login();
            break;
          case -14:
            resolve(res.data); // svip
            break;
          case -30:
            Toast.clear();
            Dialog.alert({
              message: res.data.msg,
              confirmButtonText: '我知道了',
              confirmButtonColor: themeColorLess,
              lockScroll: false,
            }).then(() => {});
            break;
          case 500:
            Toast(res.data.msg);
            reject(res.data);
            break;
          default:
            // Toast(res.data.msg);
            break;
        }
      })
      .catch(err => {
        devLog('activity', url, err);
        Toast.clear(); //清除所有正在loading的弹窗
        Toast('网络不好请稍后重试');
      })
      .finally(() => {
        store.commit('system/setLoadingUrl');
      });
  });
}
