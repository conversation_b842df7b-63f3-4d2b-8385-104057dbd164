<template>
  <div class="double-eleven-activity">
    <nav-bar-2
      ref="topNavBar"
      bgStyle="transparent-white"
      :azShow="true"
      :placeholder="false"
    >
      <template #left>
        <div class="back" @click="back"></div>
      </template>
    </nav-bar-2>

    <div class="activity-top">
      <img
        class="top-banner"
        src="@/assets/images/double-eleven-activity/double-eleven-top.png"
        alt=""
      />
    </div>
    <div class="main">
      <div class="main-top">
        <div class="main-top-text">
          SVIP会员服务全年zui低价，错过再等一年！
        </div>
      </div>
      <div class="svip-list">
        <div
          class="svip-item"
          v-for="(item, index) in cardList"
          :key="index"
          @click="clickBuyCard(item)"
        >
          <img :src="item.banner" alt="" />
        </div>
      </div>
      <div class="task-container">
        <div class="task-top"></div>
        <div class="task-top-text">解锁所有任务,可享上千金币</div>
        <div class="task-list">
          <div class="task-item" v-for="(item, index) in taskList" :key="index">
            <div class="task-info">
              <div class="task-title">
                {{ item.title }}
                <template v-if="item.doorsill"
                  >({{ item.total }}/{{ item.doorsill }})</template
                >
              </div>
              <div class="task-reward">金币+{{ item.gold }}</div>
            </div>
            <div
              class="task-btn btn-can"
              v-if="item.status == 1"
              @click="takeReward(item)"
            >
              领取
            </div>
            <div class="task-btn btn-had" v-else-if="item.status == 2">
              已完成
            </div>
            <template v-else>
              <div
                class="task-btn"
                v-if="item.type == 'share'"
                @click="handleShare"
              >
                去分享
              </div>
              <div class="task-btn" v-else @click="handleTask(item)">
                去完成
              </div>
            </template>
          </div>
        </div>
      </div>
      <div class="main-bottom">已经到底了~</div>
    </div>

    <van-popup
      v-model="gamePopup"
      position="bottom"
      :safe-area-inset-bottom="true"
      :lock-scroll="false"
      class="popup-container"
      @close="onGamePopupClose"
    >
      <div class="title-container">
        <div class="title">游戏列表</div>
        <div class="popup-close" @click="gamePopup = false"></div>
      </div>
      <div class="game-container">
        <yy-list
          v-model="loadingObj"
          :finished="finished"
          @refresh="onRefresh()"
          @loadMore="loadMore()"
          :empty="empty"
          :check="false"
        >
          <div class="game-list">
            <div
              class="game-item"
              v-for="(game, index) in gameList"
              :key="game.id + '' + index"
            >
              <game-item4
                :gameInfo="game"
                :iconSize="72"
                :showHot="true"
              ></game-item4>
              <div class="game-btn" @click="goToGame(game)">
                <div class="download-btn" v-if="!openGameShow(game)">下载</div>
                <div class="download-btn" v-else>打开</div>
              </div>
            </div>
          </div>
        </yy-list>
      </div>
    </van-popup>

    <pay-type-popup
      :show.sync="payPopupShow"
      :list="payList"
      @choosePayType="choosePayType"
      :money="selectedMeal.amount"
      unit="¥"
    ></pay-type-popup>
  </div>
</template>

<script>
import { mapGetters, mapMutations, mapActions } from 'vuex';
import { platform, boxInit } from '@/utils/box.uni.js';
import {
  ApiV2024ActivityDouble11,
  ApiV2024ActivityGetDouble11Reward,
  ApiV2024ActivityGetSuggestGames,
} from '@/api/views/double_eleven_activity.js';
import { ApiGameDownloadDone } from '@/api/views/system.js';
import {
  BOX_login,
  BOX_showActivity,
  BOX_openInNewWindow,
  BOX_goToGame,
} from '@/utils/box.uni.js';
import { envFun } from '@/utils/function';
import ptbRechargePopup from '@/components/ptb-recharge-popup';
import gameItem4 from '@/components/game-item-4';

import cardJi from '@/assets/images/double-eleven-activity/card-jika.png';
import cardNian from '@/assets/images/double-eleven-activity/card-nianka.png';
import { ApiCommonShareInfo } from '@/api/views/system.js';

import {
  ApiCreateOrderSvip,
  ApiGetPayUrl,
  ApiGetOrderStatus,
  ApiGetPaymentMethod,
} from '@/api/views/recharge.js';

export default {
  components: {
    ptbRechargePopup,
    gameItem4,
  },
  data() {
    return {
      activity_status: 3, // 活动状态 1活动已开始 2 活动不存在 3活动未开始 4活动已结束

      cardList: [
        {
          amount: 129,
          banner: cardNian,
          day: 730,
          rebate_gold: 12900,
        },
        {
          amount: 34,
          banner: cardJi,
          day: 90,
          rebate_gold: 3400,
        },
      ],
      payPopupShow: false,
      payList: [], // 支付方式列表
      selectedPayType: 'wx', // 支付方式
      selectedMeal: {},
      taskList: [], // 任务列表

      gamePopup: false, // 游戏列表弹窗
      shareInfo: {}, //分享信息

      operationLoading: false, // 操作防抖

      gameList: [],
      loadingObj: {
        loading: false,
        reloading: false,
      },
      finished: false,
      page: 1,
      listRows: 10,
      empty: false,
    };
  },
  computed: {
    ...mapGetters({
      initData: 'system/initData',
    }),
    activity_status_text() {
      const arr = [
        '活动已开始！',
        '活动不存在！',
        '活动未开始！',
        '活动已结束！',
      ];
      return arr[this.activity_status - 1];
    },
  },
  async created() {
    await this.getPayMethod(); // 获取支付方式
  },
  async mounted() {
    // 官包触发页面刷新（将onResume挂载在window上）
    if (platform == 'android') {
      window.onResume = this.onResume;
    }
  },
  async activated() {
    await this.getIndexData();
  },
  methods: {
    ...mapMutations({
      setShowPtbRechargePopup: 'recharge/setShowPtbRechargePopup',
    }),
    ...mapActions({
      SET_USER_INFO: 'user/SET_USER_INFO',
    }),
    async onResume() {
      await boxInit();
      await this.SET_USER_INFO(true);
      await this.getIndexData();
    },
    login() {
      BOX_login();
    },
    async getIndexData() {
      const res = await ApiV2024ActivityDouble11();
      let { task, activity_status } = res.data;
      this.activity_status = activity_status;
      this.taskList = task;
    },

    async clickBuyCard(item) {
      if ([2, 3, 4].includes(this.activity_status)) {
        this.$toast(this.activity_status_text);
        return false;
      }
      if (!this.userInfo.token) {
        this.$toast('请登录');
        this.login();
        return false;
      }

      this.selectedMeal = item;

      this.payPopupShow = true;
    },

    async getShareInfo() {
      let data = {
        type: 9,
        id: this.userInfo.user_id ? this.userInfo.user_id : 1,
      };
      const res = await ApiCommonShareInfo(data);
      this.shareInfo = res.data;
    },
    async handleShare() {
      if ([2, 3, 4].includes(this.activity_status)) {
        this.$toast(this.activity_status_text);
        return false;
      }
      await this.getShareInfo();
      if (this.initData?.share_info?.length) {
        if (this.operationLoading) {
          return false;
        }
        this.operationLoading = true;
        setTimeout(() => {
          this.operationLoading = false;
        }, 1000);
        window.BOX.mobShare(9, this.userInfo.user_id);
      } else {
        this.$copyText(this.shareInfo.share_text + this.shareInfo.url).then(
          async res => {
            this.$toast('链接已复制到剪贴板，快去邀请好友吧~');
          },
          err => {
            this.$dialog.alert({
              message: '复制失败',
              lockScroll: false,
            });
          },
        );
      }
      setTimeout(async () => {
        await this.getIndexData();
      }, 1000);
    },
    // 点击领取
    async takeReward(item) {
      if ([2, 3, 4].includes(this.activity_status)) {
        this.$toast(this.activity_status_text);
        return false;
      }
      this.$toast.loading('加载中...');
      try {
        const res = await ApiV2024ActivityGetDouble11Reward({
          rule_id: item.rule_id,
        });
      } finally {
        this.getIndexData();
        setTimeout(() => {
          this.$toast.clear();
        }, 2000);
      }
    },
    // 点击去完成
    async handleTask(item) {
      if ([2, 3, 4].includes(this.activity_status)) {
        this.$toast(this.activity_status_text);
        return false;
      }
      switch (item.type) {
        case 'play_time': // 游戏时长
          // 跳转我的游戏
          BOX_showActivity(
            { name: 'MyGame' },
            { page: 'com.a3733.gamebox.ui.user.MyGameTabActivity' },
          );
          break;
        case 'pay': // 充值达标
          // 跳转平台币页面
          BOX_openInNewWindow(
            { name: 'PlatformCoin' },
            { url: `https://${envFun()}game.3733.com/#/platform_coin` },
          );
          break;
        case 'download': // 下载游戏
          this.gamePopup = true;
          this.gameList = [];
          await this.onRefresh();
          break;
        default:
          this.$toast('敬请期待');
          break;
      }
    },

    async getPayMethod() {
      let res = await ApiGetPaymentMethod({
        orderType: 103,
      });
      this.payList = res.data;
    },

    choosePayType(selectedPayType) {
      this.selectedPayType = selectedPayType.symbol;
      this.handlePay();
    },
    handlePay() {
      this.payPopupShow = false;
      const orderParams = {
        day: this.selectedMeal.day,
        amount: this.selectedMeal.amount,
        rebate_gold: this.selectedMeal.rebate_gold,
        payWay: this.selectedPayType,
        is_cycle: 0,
      };
      ApiCreateOrderSvip(orderParams).then(async orderRes => {
        await ApiGetPayUrl({
          orderId: orderRes.data.orderId,
          orderType: 103,
          payWay: this.selectedPayType,
          packageName: '',
        }).finally(() => {
          ApiGetOrderStatus({
            order_id: orderRes.data.orderId,
            order_type: 103,
          });
        });
        await this.getIndexData();
      });
    },

    async getGameList(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      const res = await ApiV2024ActivityGetSuggestGames({
        page: this.page,
        listRow: this.listRows,
      });
      if (action === 1 || this.page === 1) {
        this.gameList = [];
      }
      this.gameList.push(...res.data.list);
      if (this.gameList.length) {
        this.empty = false;
      } else {
        this.empty = true;
      }
      if (res.data.list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
    },
    async onRefresh() {
      await this.getGameList();
      this.loadingObj.reloading = false;
    },
    async loadMore() {
      if (!this.gameList.length) {
        await this.getGameList();
      } else {
        await this.getGameList(2);
      }

      this.loadingObj.loading = false;
    },
    openGameShow(detail) {
      if (platform == 'android' || platform == 'androidBox') {
        try {
          return BOX.checkInstall(detail.package_name);
        } catch (error) {
          return false;
        }
      } else {
        return false;
      }
    },
    onGamePopupClose() {
      this.getIndexData();
    },
    async goToGame(game) {
      if (platform !== 'android') {
        await ApiGameDownloadDone({ gameId: game.id, classId: game.classid });
      }
      BOX_goToGame(
        {
          params: {
            id: game.id,
          },
        },
        { id: game.id },
      );
    },
  },
};
</script>

<style lang="less" scoped>
.double-eleven-activity {
  font-family: PingFang SC, PingFang SC;
  display: flex;
  flex-direction: column;
  width: 100%;
  overflow: hidden;
  min-height: 100vh;
  background: #ffc6c8;
  .back {
    width: 28 * @rem;
    height: 28 * @rem;
    border-radius: 15 * @rem;
    background: rgba(0, 0, 0, 0.3) url(~@/assets/images/nav-bar-back-white.png)
      center center no-repeat;
    background-size: 8 * @rem 14 * @rem;
  }
  .activity-top {
    width: 100%;
    height: 331 * @rem;
    .top-banner {
      width: 100%;
      height: 331 * @rem;
    }
  }
  .main {
    width: 100%;
    margin-top: -40 * @rem;
    position: relative;
    flex: 1;
    min-height: 0;
    .main-top {
      width: 100%;
      height: 116 * @rem;
      background: url(~@/assets/images/double-eleven-activity/main-top.png)
        center center no-repeat;
      background-size: 100% 116 * @rem;
      overflow: hidden;
      .main-top-text {
        font-size: 12 * @rem;
        line-height: 17 * @rem;
        color: #c45621;
        font-weight: bold;
        margin-top: 58 * @rem;
        padding-left: 38 * @rem;
      }
    }
    .svip-list {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0 3 * @rem;
      gap: 1 * @rem;
      overflow-x: auto;
      margin-top: -29 * @rem;
      &::-webkit-scrollbar {
        display: none;
      }
      .svip-item {
        width: 182 * @rem;
        height: 109 * @rem;
        flex-shrink: 0;
      }
    }
    .task-container {
      box-sizing: border-box;
      position: relative;
      margin: 12 * @rem 13 * @rem 0;
      background: linear-gradient(158deg, #ffffff 0%, #fff0e3 100%);
      border-radius: 12 * @rem;
      padding: 12 * @rem 0;
      &::before {
        content: '';
        width: 36 * @rem;
        height: 44 * @rem;
        position: absolute;
        top: 11 * @rem;
        right: -13 * @rem;
        background: url(~@/assets/images/double-eleven-activity/belt.png) center
          center no-repeat;
        background-size: 36 * @rem 44 * @rem;
      }
      &::after {
        content: '';
        width: 35 * @rem;
        height: 50 * @rem;
        position: absolute;
        bottom: -25 * @rem;
        left: -13 * @rem;
        background: url(~@/assets/images/double-eleven-activity/star.png) center
          center no-repeat;
        background-size: 35 * @rem 50 * @rem;
      }
      .task-top {
        width: 100%;
        height: 39 * @rem;
        margin: 0 auto;
        background: url(~@/assets/images/double-eleven-activity/task-top.png)
          center center no-repeat;
        background-size: 100% 39 * @rem;
      }
      .task-top-text {
        font-size: 14 * @rem;
        color: #c45621;
        margin-top: 5 * @rem;
        line-height: 20 * @rem;
        font-weight: bold;
        text-align: center;
      }
      .task-list {
        padding: 0 15 * @rem;
        .task-item {
          display: flex;
          align-items: center;
          padding: 13 * @rem 0 12 * @rem;
          &:not(:first-of-type) {
            border-top: 1px dashed #f3b595;
          }
          .task-info {
            flex: 1;
            min-width: 0;
            .task-title {
              text-align: left;
              font-size: 14 * @rem;
              font-weight: 600;
              line-height: 21 * @rem;
              color: #4d4744;
            }
            .task-reward {
              font-size: 12 * @rem;
              line-height: 17 * @rem;
              color: #e04b00;
              margin-top: 4 * @rem;
            }
          }
          .task-btn {
            width: 80 * @rem;
            height: 32 * @rem;
            background: url(~@/assets/images/double-eleven-activity/btn-go.png)
              center center no-repeat;
            background-size: 80 * @rem 32 * @rem;
            font-size: 12 * @rem;
            color: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
            &.btn-can {
              background-image: url(~@/assets/images/double-eleven-activity/btn-can.png);
            }
            &.btn-had {
              background-image: url(~@/assets/images/double-eleven-activity/btn-had.png);
            }
          }
        }
      }
    }
    .main-bottom {
      text-align: center;
      font-size: 12 * @rem;
      line-height: 13 * @rem;
      color: #5c2409;
      padding: 15 * @rem 0;
      opacity: 0.5;
    }
  }
}

.popup-container {
  box-sizing: border-box;
  background: #fff;
  height: 520 * @rem;
  border-radius: 14 * @rem 14 * @rem 0 0;
  display: flex;
  flex-direction: column;
  .title-container {
    box-sizing: border-box;
    position: relative;
    height: 48 * @rem;
    .title {
      height: 48 * @rem;
      line-height: 48 * @rem;
      font-size: 16 * @rem;
      font-weight: bold;
      padding: 0 15 * @rem;
    }
    .popup-close {
      width: 15 * @rem;
      height: 15 * @rem;
      background: url(~@/assets/images/close-dialog.png) center center no-repeat;
      background-size: 18 * @rem 18 * @rem;
      position: absolute;
      right: 15 * @rem;
      top: 50%;
      transform: translateY(-50%);
      opacity: 0.5;
    }
  }
  .search-container {
    box-sizing: border-box;
    width: 342 * @rem;
    height: 33 * @rem;
    border-radius: 17 * @rem;
    background: #f8f8f9;
    margin: 0 auto;
    overflow: hidden;
    .search-input {
      width: 100%;
      padding: 0 16 * @rem;
      line-height: 33 * @rem;
      background: #f8f8f9;
      font-size: 12 * @rem;
      color: #333;
    }
  }
  .game-container {
    box-sizing: border-box;
    flex: 1;
    min-height: 0;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
  }
}

.game-list {
  padding: 5 * @rem 15 * @rem;
  overflow: hidden;
  .game-item {
    display: flex;
    align-items: center;
    .btn {
      flex-shrink: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 64 * @rem;
      height: 30 * @rem;
      background: @themeBg;
      border-radius: 19 * @rem;
      color: #fff;
      line-height: 30 * @rem;
      text-align: center;
      font-weight: 500;
      font-size: 12 * @rem;
    }
    .game-btn {
      position: relative;
      .download-btn {
        box-sizing: border-box;
        width: 64 * @rem;
        height: 30 * @rem;
        font-size: 12 * @rem;
        color: #fff;
        border-radius: 30 * @rem;
        background: @themeBg;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(85deg, #0fb089 0%, #5be06f 100%);
      }
    }
  }
}
</style>
