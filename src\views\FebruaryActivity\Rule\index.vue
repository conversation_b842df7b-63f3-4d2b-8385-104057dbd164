<template>
  <div class="shuangshier-rule">
    <nav-bar-2
      ref="topNavBar"
      bgStyle="transparent-white"
      :azShow="true"
      :title="'活动规则'"
      :placeholder="false"
      v-if="navBgTransparent"
    ></nav-bar-2>
    <nav-bar-2 :title="'活动规则'" :placeholder="false" :border="true" v-else>
    </nav-bar-2>
    <div class="section">
      <div class="title"><span>活动介绍：</span></div>
      <div class="text">
        二月初二，龙抬头，万物复苏，纳祥转运。活动期间不仅可以参与瓜分千万金币，更可通过投入金币获取最高8倍奖励。
      </div>
      <div class="text color">活动时间：2月18日0点0分-2月21日23点59分</div>
    </div>
    <div class="section">
      <div class="container">
        <div class="big-text">一．小投入大回报</div>
        <div class="text">
          活动期间，游戏实付满足条件后，可投入金币参与转盘活动，获取额外奖励。<br />
          游戏实付达到100元，可投入1000金币（活动期间仅一次）<br />
          游戏实付达到300元，可投入3000金币（活动期间仅一次）<br />
          游戏实付达到500元，可投入5000金币（活动期间仅一次）<br />
          游戏实付达到1000元，可投入10000金币（活动期间仅一次）<br />
          游戏实付达到2000元，可投入20000金币（活动期间仅一次）<br />
          温馨提示：仅限游戏内使用微信/支付宝充值，如有使用金币/平台币/代金券抵扣的，则只计算抵扣后的现金充值金额。
        </div>
        <div class="text color2">转盘奖励</div>
        <div class="text">
          随机获得1.2倍金币、1.5倍金币、2倍金币、5倍金币、8倍金币、10倍金币其中一项。<br />
          例：投入1000金币，转到5倍奖励，则获得5000金币。
        </div>
      </div>
      <div class="container">
        <div class="big-text">二．SVIP回馈</div>
        <div class="text">
          活动期间SVIP天数大于30天的用户可以免费领取<span class="color">88</span
          >礼金（每日限一次）
        </div>
      </div>
      <div class="container">
        <div class="big-text">三．瓜分千万金币</div>
        <div class="text">
          动期间，每日前<span class="color">2</span
          >次充值平台币，即可参与瓜分千万金币。金额随机，祝君好运满满！每个用户每日仅能参与<span
            class="color"
            >2</span
          >次。
        </div>
      </div>
      <div class="container">
        <div class="big-text">四．活动规则</div>
        <div class="text">
          1.概率公示<br />
          1.2倍金币获取几率为30%<br />
          1.5倍金币获取几率为40%<br />
          2倍金币获取几率为20%<br />
          5倍金币获取几率为5%<br />
          8倍金币获取几率为4%<br />
          10倍金币获取几率为1%<br />
          2. 礼金为平台新增货币，可用于每日兑换金币，<br />
          具体明细请：<span @click="toCashGift" class="color2 underline"
            >点击查看</span
          ><br />
          3. 活动期间充值完成后请返回本活动页面点击<span class="color3"
            >【刷新】</span
          >参与金币瓜分，并在活动期间内进行转盘投入。活动结束后将清空所有金币瓜分和投入次数。<br />
          4.
          由于游戏厂商的要求，部分游戏内充值暂不支持使用金币支付，请先确定需要用金币充值的游戏在不在可使用名单内。
        </div>
        <div class="text"
          ><span @click="game_dialog_show = true" class="color2 btn"
            >查看名单></span
          ></div
        >
      </div>
    </div>
    <!-- 查看名单 -->
    <van-dialog
      v-model="game_dialog_show"
      :lock-scroll="false"
      :show-confirm-button="false"
      :close-on-click-overlay="true"
      class="no-gold-game-popup"
    >
      <div class="search-container">
        <div class="close-search" @click="game_dialog_show = false"></div>
        <div class="search-bar">
          <div class="input-text">
            <form @submit.prevent="searchGame">
              <input
                type="text"
                v-model.trim="input_game"
                placeholder="输入游戏名"
              />
            </form>
          </div>
          <div class="search-btn" @click="searchGame">搜索</div>
        </div>
        <yy-list
          class="yy-list game-list"
          v-model="loading_obj"
          :finished="finished"
          @refresh="onRefresh"
          @loadMore="loadMore"
          :empty="!game_list.length"
          tips="请输入您想搜索的游戏"
          :check="false"
        >
          <div
            class="game-item btn"
            v-for="item in game_list"
            :key="item.id"
            @click="toGame(item)"
          >
            <div class="game-icon">
              <img :src="item.titlepic" alt="" />
            </div>
            <div class="right">
              <div class="game-name">{{ item.title }}</div>
              <div class="use-card" :class="{ can: item.use_gold_pay == 1 }">
                {{ item.use_gold_pay == 1 ? '' : '不' }}支持使用金币支付
              </div>
            </div>
          </div>
        </yy-list>
      </div>
    </van-dialog>
  </div>
</template>
<script>
import { ApiGameCardSearchGame } from '@/api/views/recharge.js';
import { BOX_goToGame, BOX_openInNewWindow } from '@/utils/box.uni.js';
import { envFun } from '@/utils/function';

export default {
  name: 'Rule',
  data() {
    return {
      game_list: [], //查看的游戏名单
      loading_obj: {
        loading: false,
        reloading: false,
      }, //loading对象
      input_game: '', //search的input框
      page: 1, //查看名单的页码
      listRows: 20, //查看名单的大小
      empty: false, //查看名单的空
      game_dialog_show: false, //查看名单弹窗
      finished: false, //防抖
      navBgTransparent: true,
    };
  },
  mounted() {
    // 获取顶部导航栏的高度
    this.stickyOffsetTop =
      document.querySelector('.van-nav-bar').offsetHeight + 'px';
    window.addEventListener('scroll', this.handleScroll);
  },
  methods: {
    // 滚动处理
    handleScroll(e) {
      let scrollTop = e.target.scrollingElement.scrollTop;
      if (scrollTop > 100) {
        this.navBgTransparent = false;
      } else {
        this.navBgTransparent = true;
      }
    },
    // 搜索不可使用金币的游戏
    async searchGame() {
      if (!this.input_game) {
        this.$toast('请输入游戏名');
        return false;
      }
      this.$toast({
        type: 'loading',
        duration: 0,
        message: '拼命加载中...',
      });
      this.game_list = [];
      await this.getGameList();
    },
    async getGameList(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      let res = await ApiGameCardSearchGame({
        keyword: this.input_game,
        listRows: 20,
        page: this.page,
      });
      this.$toast.clear();
      let { list } = res.data;
      if (action === 1 || this.page === 1) {
        this.game_list = [];
      }
      if (!list.length) {
        this.empty = true;
      }
      this.game_list.push(...list);
      if (list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
    },
    async onRefresh() {
      try {
        await this.getGameList();
      } finally {
        this.loading_obj.reloading = false;
      }
    },
    async loadMore() {
      try {
        await this.getGameList(2);
      } finally {
        this.loading_obj.loading = false;
      }
    },
    toGame(item) {
      BOX_goToGame(
        {
          params: {
            id: item.id,
          },
        },
        { id: item.id },
      );
    },
    toCashGift() {
      BOX_openInNewWindow(
        { name: 'MyCashGift' },
        { url: `https://${envFun()}game.3733.com/#/my_cashgift` },
      );
    },
  },
};
</script>
<style lang="less" scoped>
.shuangshier-rule {
  background-color: rgba(38, 101, 69, 1);
  padding: 50 * @rem 22 * @rem 22 * @rem;
  overflow: hidden;
  .title {
    margin: 20 * @rem 0 15 * @rem;
    span {
      background-image: url('~@/assets/images/february-activity/fa_bg7.png');
      background-size: 100%;
      background-repeat: no-repeat;
      background-position: 0 16 * @rem;
      font-size: 16 * @rem;
      font-family: PingFang HK-Semibold, PingFang HK;
      font-weight: 600;
      color: #ffffff;
    }
  }
  .big-text {
    margin-bottom: 6 * @rem;
    font-size: 14 * @rem;
    font-family: PingFang SC-Semibold, PingFang SC;
    font-weight: 600;
    color: #ffffff;
    line-height: 21 * @rem;
  }
  .text {
    margin-bottom: 6 * @rem;
    font-size: 11 * @rem;
    color: #ffffff;
    line-height: 18 * @rem;
  }
  .color {
    color: rgba(248, 191, 46, 1);
  }
  .color2 {
    font-weight: 600;
    color: rgba(255, 233, 34, 1);
  }
  .color3 {
    color: rgba(15, 244, 176, 1);
  }
  .underline {
    text-decoration: underline;
  }
  .container {
    margin-top: 15 * @rem;
  }
}
.no-gold-game-popup {
  overflow: unset;
  width: 320 * @rem;
  .search-container {
    box-sizing: border-box;
    width: 320 * @rem;
    height: 450 * @rem;
    padding: 24 * @rem 19 * @rem 10 * @rem;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: unset;
    .close-search {
      width: 24 * @rem;
      height: 24 * @rem;
      background: url(~@/assets/images/close-search.png) center center no-repeat;
      background-size: 24 * @rem 24 * @rem;
      position: absolute;
      right: -10 * @rem;
      top: -10 * @rem;
    }
    .search-bar {
      display: flex;
      align-items: center;
      .input-text {
        width: 240 * @rem;
        height: 35 * @rem;
        border: 1 * @rem solid #e5e5e5;
        border-radius: 18 * @rem;
        flex: 1;
        overflow: hidden;
        form {
          display: block;
          width: 100%;
          height: 100%;
        }
        input {
          box-sizing: border-box;
          display: block;
          width: 100%;
          height: 100%;
          padding: 0 18 * @rem;
          font-size: 15 * @rem;
          color: #333333;
          background-color: #f6f6f6;
        }
      }
      .search-btn {
        font-size: 15 * @rem;
        color: #666666;
        padding-left: 13 * @rem;
        height: 35 * @rem;
        line-height: 35 * @rem;
      }
    }
    .game-list {
      flex: 1;
      overflow: auto;
      margin-top: 10 * @rem;
      .game-item {
        display: flex;
        align-items: center;
        padding: 21 * @rem 0;
        border-bottom: 1 * @rem solid #eeeeee;
        .game-icon {
          width: 50 * @rem;
          height: 50 * @rem;
          border-radius: 10 * @rem;
          background-color: #b5b5b5;
        }
        .right {
          flex: 1;
          min-width: 0;
          margin-left: 10 * @rem;
          .game-name {
            font-size: 16 * @rem;
            font-weight: bold;
            color: #000000;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
          }
          .use-card {
            font-size: 12 * @rem;
            color: #f72e2e;
            margin-top: 10 * @rem;
            &.can {
              color: #36b150;
            }
          }
        }
      }
    }
  }
}
</style>
