<template>
  <div class="shuangshiyi-page">
    <nav-bar-2
      ref="topNavBar"
      bgStyle="transparent-white"
      :azShow="true"
      :placeholder="false"
    ></nav-bar-2>
    <div class="bg1">
      <div
        v-if="activity_status !== 0"
        class="button1"
        :class="{ out: activity_status === 4 }"
      >
        {{ button1_text }}
      </div>
    </div>
    <div class="user-container">
      <div class="left" v-if="userInfo.token">
        <div class="avatar">
          <UserAvatar />
        </div>
        <div class="nick-name">{{ userInfo.nickname }}</div>
      </div>
      <div class="left" @click="login" v-else>
        <div class="avatar">
          <div class="default-avatar"></div>
        </div>
        <div class="nick-name underline">请登录</div>
      </div>
      <div @click="getLotteryList" class="right"></div>
    </div>
    <div class="bg2">
      <div class="remain_gold">{{ remain_gold }}</div>
      <div class="scroll">
        <van-swipe
          vertical
          :autoplay="3000"
          :touchable="false"
          :show-indicators="false"
        >
          <van-swipe-item v-for="(item, index) in record_list" :key="index">
            {{ handleRemain_gold(item, 1) }}
            <span>{{ handleRemain_gold(item, 2) }}</span>
            金币
          </van-swipe-item>
        </van-swipe>
      </div>
      <div class="lottery-button">
        <div class="left">
          刮奖次数：<span class="number">{{ remain_lottery_ptb_count }}</span
          >次
        </div>
        <div @click="getActivityInfo" class="right"></div>
      </div>
      <div class="canvas-container">
        <div class="prize">{{ first_gold }}金币</div>
        <canvas
          id="canvas"
          @touchmove="touchMove"
          @touchstart="touchStart"
          @touchend="touchEnd"
        ></canvas>
      </div>
      <div
        @click="toRecharge"
        v-if="
          activity_status != 1 ||
          remain_lottery_pay_status != 0 ||
          remain_lottery_ptb_status != 0 ||
          !userInfo.token
        "
        class="bottom-button"
      ></div>
      <div v-else class="bottom-button empty"></div>
    </div>
    <div class="bg3">
      <div
        v-if="activity_status === 4"
        @click="toExchangePage"
        class="small-button"
      ></div>
      <div class="lottery-button">
        <div class="left">
          剩下次数：<span class="number">{{ remain_lottery_pay_count }}</span
          >次
        </div>
        <div @click="getActivityInfo" class="right"></div>
      </div>
      <div
        @click="getPoint"
        :class="{ empty: remain_lottery_pay_status == 0 && userInfo.token }"
        class="bottom-button"
      ></div>
    </div>
    <div @click="toExchangePage" class="big-button"></div>
    <div class="explain">
      <div class="big-title"></div>
      <section>
        <div class="title">活动时间：</div>
        <div class="text">11月11日0点0分 - 11月13日23点59分</div>
      </section>
      <section>
        <div class="title">活动内容：</div>
        <div class="small-title">1.瓜分千万金币红包</div>
        <div class="text">
          活动期间，每日前3次充值平台币，即可参与瓜分千万金币红包。金额随机，祝君好运满满！每个用户每日仅能参与3次。
        </div>
        <div class="small-title">2.游戏现金充值抽积分</div>
        <div class="text">
          活动期间，每天前3笔游戏现金充值订单均可抽取一次积分，
          最高返还500%积分。（仅限<span class="color"
            >游戏内使用微信/支付宝充值</span
          >） RMB对积分的比例为1：10，即充值1元最高可获得50积分,如
          有使用金币/平台币/代金券抵扣的，则只计算抵扣后的现金 充值金额。
        </div>
        <div class="small-title">3.积分兑好礼</div>
        <div class="text">消耗150积分可兑换88金币（每个用户可兑换3次）</div>
        <div class="text">
          消耗300积分可兑换188金币+1天SVIP（每个用户可兑换1次）
        </div>
        <div class="text">消耗400积分可兑换288金币（每个用户可兑换3次）</div>
        <div class="text">
          消耗600积分可兑换488金币+3天SVIP（每个用户可兑换1次）
        </div>
        <div class="text">
          消耗1200积分可兑换988金币+7天SVIP（每个用户可兑换1次）
        </div>
        <div class="text">消耗3500积分可兑换3733金币（每个用户可兑换2次）</div>
        <div class="text">消耗4500积分可兑换30天SVIP（每个用户可兑换1次）</div>
        <div class="text">消耗8000积分可兑换400平台币（每个用户可兑换1次）</div>

        <div class="text color2">
          温馨提醒：请务必在活动期间内兑换心仪奖励，活动结束后将无法再进行兑换。
        </div>
      </section>
      <section>
        <div class="title">活动规则：</div>
        <div class="text">
          1.活动期间充值完成后请返回本活动页面点击<span class="color2"
            >【刷新】</span
          >参与金币瓜分以及积分抽奖，并在活动期间内进行积分兑换。活动结束后将清空所有金币瓜分和积分抽奖次数，且积分余额不再保留。
        </div>
        <div class="text">
          2.由于游戏厂商的要求，部分游戏内充值暂不支持使用金币支付，请先确定需要用金币充值的游戏在不在可使用名单内。<span
            @click="game_dialog_show = true"
            class="color underline"
            >查看名单></span
          >
        </div>
      </section>
    </div>
    <!-- 刮奖记录 -->
    <van-dialog
      v-model="lottery_record_popup"
      :lock-scroll="false"
      :show-confirm-button="false"
      :close-on-click-overlay="false"
      class="popup lottery-record-popup"
    >
      <div class="popup-close" @click="lottery_record_popup = false"></div>
      <div class="title">刮奖记录</div>
      <div v-if="lottery_record_list.length > 0" class="list">
        <div
          v-for="(item, index) in lottery_record_list"
          :key="index"
          class="item"
        >
          <div class="left">{{ item.title }}</div>
          <div class="right">
            <span>{{ item.num }}</span
            >{{ item.desc }}
          </div>
        </div>
      </div>
      <div v-else class="empty">暂无刮奖记录</div>
    </van-dialog>
    <!-- 暂无刮奖机会弹窗 -->
    <van-dialog
      v-model="lottery_empty_popup"
      :lock-scroll="false"
      :show-confirm-button="false"
      :close-on-click-overlay="false"
      class="popup lottery-empty-popup"
    >
      <div class="popup-close" @click="lottery_empty_popup = false"></div>
      <div class="title">当前未有剩余刮奖机会</div>
      <div class="text">
        活动期间，每日前3次充值平台币，即可参与瓜分千万金币红包。金额随机，祝君好运满满！
      </div>
      <div @click="toRecharge()" class="bottom-button"></div>
    </van-dialog>
    <!-- 暂无抽取积分机会弹窗 -->
    <van-dialog
      v-model="point_empty_popup"
      :lock-scroll="false"
      :show-confirm-button="false"
      :close-on-click-overlay="false"
      class="popup point-empty-popup"
    >
      <div class="popup-close" @click="point_empty_popup = false"></div>
      <div class="title">当前未有剩余抽取积分机会</div>
      <div class="text">
        活动期间，每天前3笔游戏现金充值订单均可抽取一次积分，最高返还500%积分
      </div>
      <div @click="point_empty_popup = false" class="bottom-button"></div>
    </van-dialog>
    <!-- 查看名单 -->
    <van-dialog
      v-model="game_dialog_show"
      :lock-scroll="false"
      :show-confirm-button="false"
      :close-on-click-overlay="true"
      class="no-gold-game-popup"
    >
      <div class="search-container">
        <div class="close-search" @click="game_dialog_show = false"></div>
        <div class="search-bar">
          <div class="input-text">
            <form @submit.prevent="searchGame">
              <input
                type="text"
                v-model.trim="input_game"
                placeholder="输入游戏名"
              />
            </form>
          </div>
          <div class="search-btn" @click="searchGame">搜索</div>
        </div>
        <yy-list
          class="yy-list game-list"
          v-model="loading_obj"
          :finished="finished"
          @refresh="onRefresh"
          @loadMore="loadMore"
          :empty="!game_list.length"
          tips="请输入您想搜索的游戏"
          :check="false"
        >
          <div
            class="game-item btn"
            v-for="item in game_list"
            :key="item.id"
            @click="toGame(item)"
          >
            <div class="game-icon">
              <img :src="item.titlepic" alt="" />
            </div>
            <div class="right">
              <div class="game-name">{{ item.title }}</div>
              <div class="use-card" :class="{ can: item.use_gold_pay == 1 }">
                {{ item.use_gold_pay == 1 ? '' : '不' }}支持使用金币支付
              </div>
            </div>
          </div>
        </yy-list>
      </div>
    </van-dialog>
    <!-- 抽奖结果 -->
    <van-dialog
      v-model="lottery_result_popup"
      :lock-scroll="false"
      :show-confirm-button="false"
      :close-on-click-overlay="false"
      class="popup lottery-result-popup"
    >
      <div class="popup-close" @click="lottery_result_popup = false"></div>
      <div class="title">恭喜，获得{{ lottery_result }}积分</div>
      <div @click="lottery_result_popup = false" class="bottom-button"></div>
    </van-dialog>
    <ptb-recharge-popup @success="getActivityInfo"></ptb-recharge-popup>
  </div>
</template>

<script>
import { BOX_login } from '@/utils/box.uni.js';
import {
  Api1111Index,
  Api1111GetSelfInfo,
  Api1111ScratchLottery,
  Api1111LotteryList,
  Api1111IntegralLottery,
} from '@/api/views/1111.js';
import { ApiGameCardSearchGame } from '@/api/views/recharge.js';
import ptbRechargePopup from '@/components/ptb-recharge-popup';
import UserAvatar from '@/components/user-avatar';
import { mapGetters, mapMutations } from 'vuex';
import canvasImg from '@/assets/images/1111/gua-area.png';

export default {
  data() {
    return {
      activity_status: 0, //1活动已开始 2 活动不存在 3活动未开始 4活动已结束
      remain_gold: 0, //剩余金币数
      record_list: [], //领取日志
      remain_lottery_ptb_count: 0, //今日刮奖剩余次数
      remain_lottery_pay_count: 0, //游戏充值抽奖剩余次数
      canvas: '', // 画布
      title_message_popup: false, //带title的通用消息弹窗
      game_list: [], //查看的游戏名单
      loading_obj: {
        loading: false,
        reloading: false,
      }, //loading对象
      input_game: '', //search的input框
      page: 1, //查看名单的页码
      listRows: 20, //查看名单的大小
      empty: false, //查看名单的空
      game_dialog_show: false, //查看名单弹窗
      finished: false, //防抖
      lottery_record_popup: false, //刮奖记录弹窗
      lottery_record_list: [], //刮奖记录列表
      lottery_empty_popup: false, //暂无刮奖机会弹窗
      point_empty_popup: false, //暂无获取积分机会弹窗
      scratch_status: 0, //刮奖状态 0未开始1刮奖中2刮奖完
      remain_lottery_ptb_status: 0, //平台币今日刮奖状态 1:可以刮奖 0不可刮奖
      remain_lottery_pay_status: 0, //游戏充值今日刮奖状态 1:可以充值 0不可充值
      clearCount: 0, // 刮奖区被刮开的程度 最大150就全解开
      ctx: '', // 画笔
      ratio: 0,
      first_gold: 0, //刮奖中的金币
      lottery_result: 0, //抽奖中的积分
      lottery_result_popup: false, //抽奖结果弹窗
    };
  },
  computed: {
    button1_text() {
      const arr = ['活动已开始', '活动不存在', '活动未开始', '活动已结束'];
      return arr[this.activity_status - 1];
    },
    ...mapGetters({
      userInfo: 'user/userInfo',
    }),
  },
  async created() {
    await this.getActivityInfo();
  },
  mounted() {
    this.ratio = document.body.clientWidth / 375;
    this.canvas = document.getElementById('canvas');
    this.canvas.width = this.canvas.clientWidth;
    this.canvas.height = this.canvas.clientHeight;
    this.ctx = this.canvas.getContext('2d');
    this.initCanvas();
  },
  methods: {
    async getActivityInfo() {
      const res = await Api1111Index();
      this.activity_status = parseInt(res.data.activity_status);
      this.remain_gold = res.data.remain_gold;
      this.record_list = res.data.record_list;
      const res2 = await Api1111GetSelfInfo();
      this.first_gold = res2.data.first_gold;
      this.remain_lottery_ptb_count = res2.data.remain_lottery_ptb_count;
      this.remain_lottery_pay_count = res2.data.remain_lottery_pay_count;
      this.remain_lottery_ptb_status = res2.data.remain_lottery_ptb_status;
      this.remain_lottery_pay_status = res2.data.remain_lottery_pay_status;
    },
    login() {
      BOX_login();
    },
    handleRemain_gold(str, number) {
      let temp = '';
      switch (number) {
        case 1:
          temp = `${str.match(/(\S*)已获取/)[1]}已获取`;
          break;
        case 2:
          temp = `${str.match(/已获取(\S*)金币/)[1]}`;
          break;
        case 3:
          temp = '金币';
      }
      return temp;
    },
    async initCanvas() {
      this.ctx.globalCompositeOperation = 'source-over';
      let image = new Image();
      image.src = canvasImg;
      image.onload = async () => {
        await this.ctx.drawImage(
          image,
          0,
          0,
          this.canvas.width,
          this.canvas.height,
        );
      };
    },
    touchStart(e) {
      if (this.activity_status != 1) {
        this.$toast(this.button1_text);
        return false;
      }
      if (!this.userInfo.token) {
        this.login();
        return false;
      }
      switch (this.scratch_status) {
        case 0: // 未开始
          if (this.remain_lottery_ptb_status == 0) {
            this.$toast('今日次数已用完');
            return;
          } else if (this.remain_lottery_ptb_count <= 0) {
            // 当没有刮奖次数时;
            this.lottery_empty_popup = true;
            return;
          } else {
            // 开始刮奖
            this.scratch_status = 1;
          }
          break;
          yingg;
        case 1: // 刮奖中
          break;
        case 2: // 已结束
          break;
      }
    },
    touchMove(e) {
      if (this.scratch_status == 1) {
        let x = e.touches[0].clientX - this.canvas.getBoundingClientRect().left,
          y = e.touches[0].clientY - this.canvas.getBoundingClientRect().top;
        e.preventDefault();
        this.ctx.clearRect(x, y, 15 * this.ratio, 15 * this.ratio);
        this.clearCount++;
      }
    },
    async touchEnd(e) {
      if (this.scratch_status == 1) {
        if (this.clearCount > 80) {
          // 当手指累计滑动超过80时触发清空矩形画布, 数值越大要刮得越久
          this.scratch_status = 2;
          this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
          await this.handleScratch();
        }
      }
    },
    async handleScratch() {
      try {
        const res = await Api1111ScratchLottery();
        let {
          code,
          data: { remain_lottery_ptb_count, gold },
        } = res;
        if (code > 0) {
          this.remain_lottery_ptb_count = remain_lottery_ptb_count;
          this.first_gold = gold;
          this.$toast(`恭喜获得${gold}金币`);
        }
      } finally {
        setTimeout(async () => {
          this.scratch_status = 0;
          this.clearCount = 0;
          this.initCanvas();
          await this.getActivityInfo();
        }, 500);
      }
    },
    // 刷新刮奖次数，状态
    async handleRefresh() {
      const res = await ApiFestivalRemainLotteryCount({ refresh: 1 });
      this.remain_lottery_ptb_count = res.data.remain_lottery_ptb_count;
      this.clearCount = 0;
      this.scratch_status = 0;
      this.initCanvas();
    },
    // 搜索不可使用金币的游戏
    async searchGame() {
      if (!this.input_game) {
        this.$toast('请输入游戏名');
        return false;
      }
      this.$toast({
        type: 'loading',
        duration: 0,
        message: '拼命加载中...',
      });
      this.game_list = [];
      await this.getGameList();
    },
    async getGameList(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      let res = await ApiGameCardSearchGame({
        keyword: this.input_game,
        listRows: 20,
        page: this.page,
      });
      this.$toast.clear();
      let { list } = res.data;
      if (action === 1 || this.page === 1) {
        this.game_list = [];
      }
      if (!list.length) {
        this.empty = true;
      }
      this.game_list.push(...list);
      if (list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
    },
    async onRefresh() {
      try {
        await this.getGameList();
      } finally {
        this.loading_obj.reloading = false;
      }
    },
    async loadMore() {
      try {
        await this.getGameList(2);
      } finally {
        this.loading_obj.loading = false;
      }
    },
    toExchangePage() {
      if (!this.userInfo.token) {
        this.login();
        return false;
      }
      this.toPage('1111ExchangePage');
    },
    async getLotteryList() {
      this.$toast({
        type: 'loading',
        duration: 0,
        message: '拼命加载中...',
        forbidClick: true,
      });
      try {
        const res = await Api1111LotteryList();
        this.lottery_record_list = res.data.list;
        this.lottery_record_popup = true;
        this.$toast.clear();
      } finally {
      }
    },
    toRecharge() {
      if (!this.userInfo.token) {
        this.login();
        return false;
      }
      if (this.activity_status != 1) {
        this.$toast(this.button1_text);
        return false;
      }
      this.lottery_empty_popup = false;
      this.setShowPtbRechargePopup(true);
    },
    async getPoint() {
      if (this.remain_lottery_pay_status == 0) {
        this.point_empty_popup = true;
        return false;
      }
      if (this.finished) return false;
      this.finished = true;
      this.$toast({
        type: 'loading',
        duration: 0,
      });
      try {
        const res = await Api1111IntegralLottery();
        this.lottery_result = res.data.integral;
        this.remain_lottery_pay_count = res.data.remain_lottery_count;
        this.lottery_result_popup = true;
        this.$toast.clear();
      } finally {
        this.finished = false;
      }
    },
    toGame(item) {
      BOX_goToGame(
        {
          params: {
            id: item.id,
          },
        },
        { id: item.id },
      );
    },
    ...mapMutations({
      setShowPtbRechargePopup: 'recharge/setShowPtbRechargePopup',
    }),
  },
  components: {
    ptbRechargePopup,
    UserAvatar,
  },
};
</script>

<style lang="less" scoped>
.shuangshiyi-page {
  width: 100%;
  height: auto;
  padding-bottom: 20 * @rem;
  background: #400b7c;
  font-family: PingFang SC-Medium, PingFang SC;
  .bg1 {
    position: relative;
    width: 100%;
    height: 328 * @rem;
    .image-bg('~@/assets/images/1111/1111_bg.png');
    .button1 {
      position: absolute;
      bottom: 12 * @rem;
      left: 50%;
      transform: translate(-50%, 0);
      width: 185 * @rem;
      height: 34 * @rem;
      display: flex;
      justify-content: center;
      align-items: center;
      background: linear-gradient(180deg, #5f29b5 0%, #711d99 100%);
      border-top-left-radius: 10 * @rem;
      border-top-right-radius: 10 * @rem;
      font-size: 18 * @rem;
      font-family: Alimama ShuHeiTi-Bold, Alimama ShuHeiTi;
      font-weight: bold;
      color: #ffffff;
      &.out {
        background: linear-gradient(180deg, #d7cee5 0%, #b8aacf 100%);
      }
    }
  }
  .user-container {
    margin: 20 * @rem auto 20 * @rem;
    display: flex;
    justify-content: space-between;
    .left {
      display: flex;
      align-items: center;
      margin-left: 17 * @rem;
      .avatar {
        width: 30 * @rem;
        height: 30 * @rem;
        .default-avatar {
          width: 100%;
          height: 100%;
          .image-bg('~@/assets/images/1111/1111_user.png');
        }
      }
      .nick-name {
        margin-left: 10 * @rem;
        color: #fff;
        font-size: 15 * @rem;
        &.underline {
          text-decoration: underline;
        }
      }
    }
    .right {
      width: 76 * @rem;
      height: 30 * @rem;
      .image-bg('~@/assets/images/1111/1111_button2.png');
    }
  }
  .bg2 {
    position: relative;
    width: 100%;
    height: 482 * @rem;
    overflow: hidden;
    .image-bg('~@/assets/images/1111/1111_bg2.png');
    .remain_gold {
      margin: 92 * @rem 0 0;
      text-align: center;
      font-size: 40 * @rem;
      color: #fff;
      font-family: Arial-Bold, Arial;
    }
    .scroll {
      height: 20 * @rem;
      margin-top: 20 * @rem;
      text-align: center;
      color: #fff;
      overflow: hidden;
      span {
        color: #faff00;
        margin: 0 5 * @rem;
      }
    }
    .canvas-container {
      width: 100%;
      margin-top: 34 * @rem;
      position: relative;
      .prize {
        width: 263 * @rem;
        height: 96 * @rem;
        margin: 0 auto;
        line-height: 96 * @rem;
        background: #f5f5f5;
        font-size: 35 * @rem;
        letter-spacing: 3 * @rem;
        text-align: center;
        color: #f7572d;
        font-weight: bold;
        border-radius: 12 * @rem;
      }
      #canvas {
        width: 263 * @rem;
        height: 96 * @rem;
        position: absolute;
        top: 0;
        left: 50%;
        transform: translate(-50%, 0);
      }
    }
    .lottery-button {
      margin: 20 * @rem 55 * @rem 0;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .left {
        color: #835cad;
        font-size: 15 * @rem;
        font-weight: 500;
        .number {
          margin: 0 5 * @rem;
          color: #f7572d;
        }
      }
      .right {
        width: 62 * @rem;
        height: 35.5 * @rem;
        .image-bg('~@/assets/images/1111/1111_button3.png');
      }
    }
    .bottom-button {
      position: absolute;
      bottom: 20 * @rem;
      right: 40 * @rem;
      width: 190 * @rem;
      height: 42 * @rem;
      .image-bg('~@/assets/images/1111/1111_button4.png');
      &.empty {
        .image-bg('~@/assets/images/1111/1111_button6.png');
      }
    }
  }
  .bg3 {
    position: relative;
    top: -1 * @rem;
    width: 100%;
    height: 700 * @rem;
    .image-bg('~@/assets/images/1111/1111_bg3.png');
    .small-button {
      position: absolute;
      top: 170 * @rem;
      left: 20 * @rem;
      width: 97 * @rem;
      height: 34 * @rem;
      .image-bg('~@/assets/images/1111/1111_button9.png');
    }
    .lottery-button {
      position: absolute;
      bottom: 205 * @rem;
      left: 0;
      width: 100%;
      box-sizing: border-box;
      padding: 0 35 * @rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .left {
        color: #fff;
        font-size: 15 * @rem;
        font-weight: 500;
        .number {
          margin: 0 5 * @rem;
          color: #f7572d;
        }
      }
      .right {
        width: 62 * @rem;
        height: 35.5 * @rem;
        .image-bg('~@/assets/images/1111/1111_button3.png');
      }
    }
    .bottom-button {
      position: absolute;
      bottom: 150 * @rem;
      left: 50%;
      transform: translate(-50%, 0);
      width: 144 * @rem;
      height: 45 * @rem;
      .image-bg('~@/assets/images/1111/1111_button5.png');
      &.empty {
        .image-bg('~@/assets/images/1111/1111_button8.png');
      }
    }
  }
  .big-button {
    margin: 16 * @rem auto;
    width: 180 * @rem;
    height: 47 * @rem;
    .image-bg('~@/assets/images/1111/1111_button7.png');
  }
  .explain {
    position: relative;
    margin: 0 15 * @rem;
    padding: 40 * @rem 12 * @rem 12 * @rem;
    background: #56419f;
    border-radius: 5 * @rem;
    font-size: 12 * @rem;
    color: #ffffff;
    line-height: 21 * @rem;
    section {
      margin-top: 20 * @rem;
    }
    .big-title {
      position: absolute;
      top: -2 * @rem;
      left: -5 * @rem;
      width: 124 * @rem;
      height: 40 * @rem;
      .image-bg('~@/assets/images/1111/1111_title.png');
    }
    .title {
      position: relative;
      font-size: 16 * @rem;
      font-family: PingFang HK-Semibold, PingFang HK;
      font-weight: 600;
      line-height: 20 * @rem;
      &::after {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 92 * @rem;
        height: 8 * @rem;
        background: #9f4fff;
        border-radius: 7 * @rem;
      }
    }
    .small-title {
      margin: 15 * @rem 0 7 * @rem;
      font-size: 14 * @rem;
      font-family: PingFang HK-Semibold, PingFang HK;
      font-weight: 600;
      color: #ffffff;
      line-height: 18 * @rem;
    }
    .text {
      margin-bottom: 2 * @rem;
    }
    .color {
      color: #deff12;
    }
    .color2 {
      color: #46e9ff;
    }
    .underline {
      text-decoration: underline;
    }
  }
  .popup {
    width: 290 * @rem;
    padding: 18 * @rem;
    box-shadow: 0 * @rem 2 * @rem 15 * @rem 0 * @rem rgba(248, 0, 0, 0.1);
    border: 2 * @rem solid #9680d9;
    background: #f8f9ff;
    .text {
      font-size: 14 * @rem;
      color: #835cad;
    }
    .popup-close {
      width: 33 * @rem;
      height: 27 * @rem;
      background: #835cad url(~@/assets/images/1111/popup-close.png) center
        center no-repeat;
      background-size: 22 * @rem 22 * @rem;
      position: absolute;
      right: -1 * @rem;
      top: -1 * @rem;
      border-radius: 0 12 * @rem 0 12 * @rem;
    }
    .title {
      margin-bottom: 15 * @rem;
      text-align: center;
      font-size: 16 * @rem;
      font-family: PingFang SC-Semibold, PingFang SC;
      font-weight: 600;
      color: #835cad;
    }
  }
  .lottery-record-popup {
    padding: 18 * @rem 0;
    height: 245 * @rem;
    .list {
      height: 210 * @rem;
      overflow-y: scroll;
      padding: 0 18 * @rem;
      .item {
        display: flex;
        justify-content: space-between;
        height: 30 * @rem;
        align-items: center;
        font-size: 14 * @rem;
        color: #835cad;
        span {
          color: #f8582e;
        }
      }
    }
    .empty {
      width: 100%;
      height: 210 * @rem;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #835cad;
    }
  }
  .lottery-empty-popup {
    .bottom-button {
      width: 254 * @rem;
      height: 48 * @rem;
      margin: 15 * @rem auto 0;
      .image-bg('~@/assets/images/1111/1111_button13.png');
    }
  }
  .point-empty-popup,
  .lottery-result-popup {
    .bottom-button {
      width: 254 * @rem;
      height: 48 * @rem;
      margin: 15 * @rem auto 0;
      .image-bg('~@/assets/images/1111/1111_button12.png');
    }
  }
  .no-gold-game-popup {
    overflow: unset;
    width: 320 * @rem;
    .search-container {
      box-sizing: border-box;
      width: 320 * @rem;
      height: 450 * @rem;
      padding: 24 * @rem 19 * @rem 10 * @rem;
      display: flex;
      flex-direction: column;
      position: relative;
      overflow: unset;
      .close-search {
        width: 24 * @rem;
        height: 24 * @rem;
        background: url(~@/assets/images/close-search.png) center center
          no-repeat;
        background-size: 24 * @rem 24 * @rem;
        position: absolute;
        right: -10 * @rem;
        top: -10 * @rem;
      }
      .search-bar {
        display: flex;
        align-items: center;
        .input-text {
          width: 240 * @rem;
          height: 35 * @rem;
          border: 1 * @rem solid #e5e5e5;
          border-radius: 18 * @rem;
          flex: 1;
          overflow: hidden;
          form {
            display: block;
            width: 100%;
            height: 100%;
          }
          input {
            box-sizing: border-box;
            display: block;
            width: 100%;
            height: 100%;
            padding: 0 18 * @rem;
            font-size: 15 * @rem;
            color: #333333;
            background-color: #f6f6f6;
          }
        }
        .search-btn {
          font-size: 15 * @rem;
          color: #666666;
          padding-left: 13 * @rem;
          height: 35 * @rem;
          line-height: 35 * @rem;
        }
      }
      .game-list {
        flex: 1;
        overflow: auto;
        margin-top: 10 * @rem;
        .game-item {
          display: flex;
          align-items: center;
          padding: 21 * @rem 0;
          border-bottom: 1 * @rem solid #eeeeee;
          .game-icon {
            width: 50 * @rem;
            height: 50 * @rem;
            border-radius: 10 * @rem;
            background-color: #b5b5b5;
          }
          .right {
            flex: 1;
            min-width: 0;
            margin-left: 10 * @rem;
            .game-name {
              font-size: 16 * @rem;
              font-weight: bold;
              color: #000000;
              white-space: nowrap;
              text-overflow: ellipsis;
              overflow: hidden;
            }
            .use-card {
              font-size: 12 * @rem;
              color: #f72e2e;
              margin-top: 10 * @rem;
              &.can {
                color: #36b150;
              }
            }
          }
        }
      }
    }
  }
}
</style>
