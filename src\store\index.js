import Vue from 'vue';
import Vuex from 'vuex';

const modulesFiles = require.context('./modules', false, /\.js$/);

const modules = modulesFiles.keys().reduce((modules, modulePath) => {
  const moduleName = modulePath.replace(/^\.\/(.*)\.\w+$/, '$1');
  const value = modulesFiles(modulePath);
  modules[moduleName] = value.default;
  modules[moduleName]['namespaced'] = true;
  return modules;
}, {});

Vue.use(Vuex);

const store = new Vuex.Store({
  modules,
  strict: false, // 非正式环境使用严格模式
});

// store持久化(放在App.vue的话这个文件读取时还没获取到state,导致所有to.meta.requiresAuth的页面会跳登录页)
if (localStorage.getItem('STORE')) {
  // store.replaceState(Object.assign({}, store.state, JSON.parse(localStorage.getItem('STORE'))));
  let currentState = Object.assign({}, store.state);
  store.replaceState(
    Object.assign(
      {},
      deepMerge(currentState, JSON.parse(localStorage.getItem('STORE'))),
    ),
  );
}
// pagehide unload beforeunload
window.addEventListener('unload', () => {
  // 程序关闭之前要清除支付二维码弹窗(防止用户在二维码弹窗下刷新/关闭程序)
  store.commit('recharge/setShowEwmPopup');
  // 保存store数据
  localStorage.setItem('STORE', JSON.stringify(store.state));
});

// 深度合并两个对象
function deepMerge(obj1, obj2) {
  var key;
  for (key in obj2) {
    // 如果target(也就是obj1[key])存在，且是对象的话再去调用deepMerge，否则就是obj1[key]里面没这个对象，需要与obj2[key]合并
    obj1[key] =
      obj1[key] && obj1[key].toString() === '[object Object]'
        ? deepMerge(obj1[key], obj2[key])
        : (obj1[key] = obj2[key]);
  }
  return obj1;
}

export default store;
