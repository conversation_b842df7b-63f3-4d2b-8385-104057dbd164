<template>
  <div id="app" :style="{ '--statusHeight': statusHeight }">
    <!-- 主程 -->
    <!-- 微信引导屏 -->
    <weixin-tips></weixin-tips>
    <!-- h5打开引导组件 -->
    <app-open-guide></app-open-guide>
    <!-- 在iframe中要拿到token后才开始渲染 -->
    <template v-if="!(platform == 'iframe' && !from)">
      <keep-alive>
        <router-view
          v-if="$route.meta.keepAlive"
          :key="$route.path"
        ></router-view>
      </keep-alive>
      <router-view
        v-if="!$route.meta.keepAlive"
        :key="$route.path"
      ></router-view>
    </template>
  </div>
</template>
<script>
import { mapGetters, mapActions, mapMutations } from 'vuex';
import { platform, boxInit, windowMessage, authInfo } from '@/utils/box.uni.js';
import WeixinTips from '@/components/weixin-tips';
import AppOpenGuide from '@/components/app-open-guide';

// 安卓壳顶部状态栏设置
let statusHeight = '0px';
if (['android', 'androidBox'].includes(platform)) {
  // 安卓versionCode:3600以上才隐藏状态栏
  if (platform == 'andriod' && authInfo.versionCode < 3600) {
    statusHeight = '0px';
  } else {
    try {
      window.BOX.setStatusBarVisibility(false);
      statusHeight =
        window.BOX.getStatusBarHeight() / window.devicePixelRatio + 'px';
    } catch (e) {
      statusHeight = '0px';
    }
  }
}

export default {
  components: {
    WeixinTips,
    AppOpenGuide,
  },
  data() {
    return {
      statusHeight,
      platform,
    };
  },
  computed: {
    ...mapGetters({
      from: 'user/from',
    }),
  },
  async created() {
    try {
      // 接收消息
      if (platform == 'iframe') {
        await windowMessage();
      } else {
        // 初始化：token、from、uuid等
        await boxInit();
      }
    } finally {
      await this.$isResolve();
    }
  },
  mounted() {
    // 官包触发页面刷新（将onResume挂载在window上）
    if (platform == 'android') {
      window.onResume = this.onResume;
    }
  },
  methods: {
    async onResume() {
      await boxInit();
      this.SET_USER_INFO(true);
    },
    ...mapActions({
      SET_USER_INFO: 'user/SET_USER_INFO',
    }),
    ...mapMutations({
      setInitDownloadStatus: 'game/setInitDownloadStatus',
    }),
  },
};
</script>
