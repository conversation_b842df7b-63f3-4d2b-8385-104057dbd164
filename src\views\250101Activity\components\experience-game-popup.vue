<template>
  <div>
    <van-popup
      v-model="popup"
      position="bottom"
      class="experience-game-popup"
      :lock-scroll="false"
    >
      <div class="title-container">
        <img
          class="title-pic"
          src="@/assets/images/250101/experience-popup-title.png"
          alt=""
        />
        <div class="close-btn" @click="closePopup"></div>
        <div class="btn change-game" @click="handleChangeGame">换一批</div>
      </div>

      <div class="game-list">
        <div
          class="game-item"
          v-for="(item, index) in showGameList"
          :key="index"
          @click="goToGame(item)"
        >
          <div class="game-pic">
            <img :src="item.titlepic" alt="" />
          </div>
          <div class="game-info">
            <div class="game-name">{{ item.main_title }}</div>
            <div class="game-line">下载并体验游戏30分钟即可完成任务</div>
          </div>
          <div class="btn game-btn">
            <div class="download-btn" v-if="!openGameShow(item)">下载</div>
            <div class="download-btn" v-else>打开</div>
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { ApiDoingsYuandanGetSuggestGames } from '@/api/views/250101.js';
import { ApiGameDownloadDone } from '@/api/views/system.js';
import { platform, BOX_goToGame } from '@/utils/box.uni.js';
export default {
  name: 'experienceGamePopup',
  props: {
    show: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      gameList: [],
      changeIndex: 0,
    };
  },
  computed: {
    popup: {
      get() {
        return this.show;
      },
      set(value) {
        this.$emit('update:show', value);
      },
    },
    pageNum() {
      return Math.ceil(this.gameList.length / 5);
    },
    showGameList() {
      return this.gameList.slice(
        this.changeIndex * 5,
        this.changeIndex * 5 + 5,
      );
    },
  },
  watch: {
    async popup(val) {
      if (val) {
        await this.getGameList();
      }
    },
  },
  methods: {
    closePopup() {
      this.popup = false;
    },
    handleChangeGame() {
      if (this.changeIndex >= this.pageNum - 1) {
        this.changeIndex = 0;
      } else {
        this.changeIndex = this.changeIndex + 1;
      }
    },
    async getGameList() {
      const res = await ApiDoingsYuandanGetSuggestGames({
        type: 2,
      });
      this.gameList = res.data.list;
    },

    openGameShow(detail) {
      if (platform == 'android' || platform == 'androidBox') {
        try {
          return BOX.checkInstall(detail.package_name);
        } catch (error) {
          return false;
        }
      } else {
        return false;
      }
    },
    async goToGame(game) {
      if (platform !== 'android') {
        await ApiGameDownloadDone({ gameId: game.id, classId: game.classid });
      }
      BOX_goToGame(
        {
          params: {
            id: game.id,
          },
        },
        { id: game.id },
      );
      setTimeout(() => {
        this.getGameList();
      }, 1000);
    },
  },
};
</script>

<style lang="less" scoped>
.experience-game-popup {
  box-sizing: border-box;
  padding: 20 * @rem 0 0;
  border-radius: 20 * @rem 20 * @rem 0 0;
  height: 428 * @rem;
  display: flex;
  flex-direction: column;
  background: rgba(250, 249, 245, 1);
  .title-container {
    position: relative;
    height: 24 * @rem;
    .title-pic {
      width: 292 * @rem;
      height: 24 * @rem;
      margin: 0 auto;
    }
    .close-btn {
      width: 16 * @rem;
      height: 16 * @rem;
      background: url('~@/assets/images/250101/250101_popup_close.png') center
        center no-repeat;
      background-size: 16 * @rem 16 * @rem;
      position: absolute;
      top: 0;
      right: 18 * @rem;
    }

    .change-game {
      width: 56 * @rem;
      height: 20 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
      position: absolute;
      top: 0;
      left: 18 * @rem;
      font-size: 11 * @rem;
      color: rgba(197, 102, 57, 0.8);
      background: #fff;
      border: 0.5 * @rem solid rgba(197, 102, 57, 0.8);
      border-radius: 10 * @rem;
    }
  }
  .game-list {
    margin-top: 20 * @rem;
    .game-item {
      display: flex;
      align-items: center;
      padding: 15 * @rem 18 * @rem;
      .game-pic {
        width: 36 * @rem;
        height: 36 * @rem;
        border-radius: 4 * @rem;
        overflow: hidden;
      }
      .game-info {
        flex: 1;
        min-width: 0;
        margin-left: 8 * @rem;
        .game-name {
          font-size: 14 * @rem;
          font-weight: bold;
          color: rgba(34, 34, 34, 1);
          font-weight: bold;
          line-height: 20 * @rem;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .game-line {
          font-size: 10 * @rem;
          color: rgba(122, 122, 122, 1);
          margin-top: 2 * @rem;
          line-height: 14 * @rem;
        }
      }
      .game-btn {
        width: 72 * @rem;
        height: 28 * @rem;
        background: @themeBg;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12 * @rem;
        color: #fff;
        border-radius: 14 * @rem;
      }
    }
  }
}
</style>
