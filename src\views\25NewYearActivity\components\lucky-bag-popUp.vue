<template>
  <div>
    <van-dialog
      v-model="popup"
      :show-confirm-button="false"
      :lock-scroll="false"
      class="lucky-bag-dialog"
    >
      <div class="logo-icon"></div>
      <div class="dialog-content">
        <div class="title">现在就要开福袋吗？</div>
        <div class="msg">
          继续做任务升级福袋，福袋等级越高，更有机会开出高价值的奖品噢！
        </div>
        <div class="tips">
          <span>* 手机需要4级福袋及以上才有机会开出哦 ~</span>
        </div>
        <div class="btn-close btn" @click="closeLuckyBag()">继续升级福袋</div>
        <div class="btn-open">
          <span @click="openLuckyBag()">马上开福袋</span>
        </div>
      </div>
    </van-dialog>
  </div>
</template>

<script>
import { envFun } from '@/utils/function';
import {
  platform,
  boxInit,
  BOX_showActivity,
  BOX_openInNewWindow,
  BOX_login,
  iframeCopy,
} from '@/utils/box.uni.js';
import { LOCAL_HOST } from '@/utils/constants.js';
export default {
  name: 'luckyBagDialog',
  props: {
    show: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {};
  },
  computed: {
    popup: {
      get() {
        return this.show;
      },
      set(value) {
        this.$emit('update:show', value);
      },
    },
  },

  methods: {
    closePopup() {
      this.$emit('update:show', false);
    },
    closeLuckyBag() {
      this.closePopup();
      this.$nextTick(() => {
        if (platform == 'android') {
          this.toPage('25NewYearActivityLuckyValueTask');
          return;
        }
        if (process.env.NODE_ENV == 'development') {
          BOX_openInNewWindow(
            {
              name: 'Activity',
              params: {
                url: `${LOCAL_HOST}/#/25_new_year_activity/lucky_value_task`,
              },
            },
            {
              url: `${LOCAL_HOST}/#/25_new_year_activity/lucky_value_task`,
            },
          );
        } else {
          BOX_openInNewWindow(
            {
              name: 'Activity',
              params: {
                url: `https://${envFun()}activity.3733.com/#/25_new_year_activity/lucky_value_task`,
              },
            },
            {
              url: `https://${envFun()}activity.3733.com/#/25_new_year_activity/lucky_value_task`,
            },
          );
        }
      });
    },
    openLuckyBag() {
      this.$emit('openUpLuckyBag', true);
      this.closePopup();
    },
  },
};
</script>

<style lang="less" scoped>
.lucky-bag-dialog {
  width: 300 * @rem;
  background: transparent;
  overflow: visible;
  .logo-icon {
    background: url(~@/assets/images/25newyear/25newyear-logo1.png) top center
      no-repeat;
    background-size: 176 * @rem 196 * @rem;
    width: 176 * @rem;
    height: 196 * @rem;
    margin: 0 auto;
    position: relative;
    z-index: 3;
  }
  .dialog-content {
    box-sizing: border-box;
    position: relative;
    border-radius: 16 * @rem;
    margin-top: -101 * @rem;
    z-index: 2;
    padding: 84 * @rem 27 * @rem 0;
    width: 300 * @rem;
    // height: 354 * @rem;
    background: url(~@/assets/images/25newyear/25newyear-bg0.png) center top
      no-repeat;
    background-size: 300 * @rem 200 * @rem;
    background-color: #fff;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    .title {
      margin-top: 18 * @rem;
      white-space: nowrap;
      height: 28 * @rem;
      font-family: 'Dream Han Sans CN', 'Dream Han Sans CN';
      font-weight: bold;
      font-size: 20 * @rem;
      line-height: 28 * @rem;
      text-align: center;
      font-style: normal;
      text-transform: none;
      color: #e75555;
    }

    .msg {
      font-weight: 400;
      font-size: 16 * @rem;
      color: #5a2a2a;
      line-height: 24 * @rem;
      text-align: left;
      margin: 17 * @rem 0 0 0;
    }
    .tips {
      margin: 12 * @rem 0 25 * @rem 0;
      font-weight: 400;
      font-size: 13 * @rem;
      color: #7a5252;
      line-height: 16 * @rem;
      white-space: nowrap;
    }
    .btn-close {
      // position: absolute;
      // bottom: 59 * @rem;
      width: 238 * @rem;
      height: 40 * @rem;
      line-height: 40 * @rem;
      background: linear-gradient(90deg, #f64b4b 0%, #ffb07c 100%), #d9d9d9;
      border-radius: 40 * @rem;
      font-family: PingFang SC, PingFang SC;
      font-weight: 500;
      font-size: 15 * @rem;
      color: #ffffff;
      text-align: center;
      font-style: normal;
      text-transform: none;
    }
    .btn-open {
      margin-top: 15 * @rem;
      margin-bottom: 23 * @rem;
      text-align: center;
      height: 21 * @rem;
      font-weight: 500;
      font-size: 15 * @rem;
      color: #9c7777;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
</style>
