import { ApiIndexExtra } from '@/api/views/system.js';
import appIcon from '@/assets/images/avatar/img_user_default.png';
export default {
  state: {
    initData: {},
    // 正在请求的接口，解决重复请求同一接口的问题
    loadingUrl: '',
  },
  getters: {
    initData(state) {
      return state.initData;
    },
    // 是否是海外
    isHw(state, getters, rootState) {
      return rootState?.user?.userInfo?.is_hw ?? !!state?.initData?.is_hw;
    },
    appName(state) {
      return state.initData?.app_name ?? '3733游戏盒';
    },
    defaultAvatar(state) {
      return state.initData?.default_avatar ?? appIcon;
    },
    loadingUrl(state) {
      return state.loadingUrl;
    },
  },
  mutations: {
    setInitData(state, payload) {
      if (payload) {
        state.initData = Object.assign({}, payload);
      } else {
        state.initData = {
          feedback_read: false,
          reply_read: false,
          inform_read: false,
        };
      }
    },
    setLoadingUrl(state, payload) {
      state.loadingUrl = payload || '';
    },
  },
  actions: {
    async SET_INIT_DATA({ commit }) {
      const res = await ApiIndexExtra();
      commit('setInitData', res.data);
    },
  },
};
