import Vue from 'vue';
import VueRouter from 'vue-router';
import store from '@/store';
import { platform } from '@/utils/box.uni.js';

// 重写push和replace,拦截跳转同一路由时报错
const originalPush = VueRouter.prototype.push;
VueRouter.prototype.push = function push(location) {
  return originalPush.call(this, location).catch(err => err);
};
const originalReplace = VueRouter.prototype.replace;
VueRouter.prototype.replace = function replace(location) {
  return originalReplace.call(this, location).catch(err => err);
};

Vue.use(VueRouter);

const routes = [
  {
    path: '/',
    redirect: '/combination_card',
  },
  {
    path: '/login',
    name: 'Login',
    component: () =>
      import(/* webpackChunkName: "users" */ '@/views/Users/<USER>'),
    meta: {
      keepAlive: true,
    },
  },

  {
    path: '/phone_login',
    name: 'PhoneLogin',
    component: () =>
      import(/* webpackChunkName: "users" */ '@/views/Users/<USER>'),
    meta: {
      keepAlive: true,
    },
  },
  {
    path: '/area_code',
    name: 'AreaCode',
    component: () =>
      import(/* webpackChunkName: "system" */ '@/views/System/AreaCode'),
    meta: {
      keepAlive: false,
    },
  },

  {
    path: '/iframe/:title/:url',
    name: 'Iframe',
    component: () =>
      import(/* webpackChunkName: "iframe" */ '@/views/Iframe/NoAuth'),
    meta: {
      keepAlive: false,
    },
  },
  // 组合卡
  {
    path: '/combination_card',
    name: 'CombinationCard',
    component: () =>
      import(/* webpackChunkName: "recharge" */ '@/views/CombinationCard'),
    meta: {
      requiresAuth: false,
      keepAlive: false,
    },
  },
  // 组合卡金币规则
  {
    path: '/combination_card_rule',
    name: 'CombinationCardRule',
    component: () =>
      import(
        /* webpackChunkName: "recharge" */ '@/views/CombinationCard/CombinationCardRule'
      ),
    meta: {
      requiresAuth: false,
      keepAlive: false,
    },
  },
  // {
  //   path: '/heishenhua_activity',
  //   name: 'HeishenhuaActivity',
  //   component: () => import(/* webpackChunkName: "heishenhua_activity" */ '@/views/HeishenhuaActivity'),
  //   meta: {
  //     keepAlive: true,
  //     requiresAuth: true,
  //   }
  // },
  // {
  //   path: '/1111_activity',
  //   name: '1111Activity',
  //   component: () => import(/* webpackChunkName: "1111_activity" */ '@/views/1111Activity'),
  //   meta: {
  //     keepAlive: true,
  //     requiresAuth: true,
  //   }
  // },
  // {
  //   path: '/1111_activity/exchange',
  //   name: '1111ExchangePage',
  //   component: () => import(/* webpackChunkName: "1111_activity" */ '@/views/1111Activity/ExchangePage'),
  //   meta: {
  //     requiresAuth: true,
  //   }
  // },
  // {
  //   path: '/1212_activity/exchange/:type',
  //   name: '1212ExchangePage',
  //   component: () => import(/* webpackChunkName: "1212_activity" */ '@/views/1212Activity/ExchangePage'),
  //   meta: {
  //     requiresAuth: true,
  //   }
  // },
  // {
  //   path: '/1212_activity',
  //   name: '1212Activity',
  //   component: () => import(/* webpackChunkName: "1212_activity" */ '@/views/1212Activity'),
  //   meta: {
  //     keepAlive: true,
  //     requiresAuth: true,
  //   }
  // }, {
  //   path: '/1212_activity/rule',
  //   name: '1212Rule',
  //   component: () => import(/* webpackChunkName: "1212_activity" */ '@/views/1212Activity/Rule'),
  //   meta: {
  //     requiresAuth: true,
  //   }
  // },
  // {
  //   path: '/0101_activity/name_list',
  //   name: '0101NameList',
  //   component: () => import(/* webpackChunkName: "0101_activity" */ '@/views/0101Activity/NameList'),
  //   meta: {
  //     requiresAuth: true,
  //   }
  // },
  // {
  //   path: '/0101_activity',
  //   name: '0101Activity',
  //   component: () => import(/* webpackChunkName: "0101_activity" */ '@/views/0101Activity'),
  //   meta: {
  //     keepAlive: true,
  //     requiresAuth: true,
  //   }
  // }, {
  //   path: '/0101_activity/rule',
  //   name: '0101Rule',
  //   component: () => import(/* webpackChunkName: "0101_activity" */ '@/views/0101Activity/Rule'),
  //   meta: {
  //     requiresAuth: true,
  //   }
  // },
  // {
  //   path: '/spring_festival',
  //   name: 'SpringFestival',
  //   component: () => import(/* webpackChunkName: "spring_festival" */ '@/views/SpringFestival'),
  //   meta: {
  //     requiresAuth: true,
  //   }
  // }, {
  //   path: '/spring_festival/exchange',
  //   name: 'SpringFestivalExchange',
  //   component: () => import(/* webpackChunkName: "spring_festival" */ '@/views/SpringFestival/Exchange'),
  //   meta: {
  //     requiresAuth: true,
  //   }
  // }, {
  //   path: '/spring_festival/rule',
  //   name: 'SpringFestivalRule',
  //   component: () => import(/* webpackChunkName: "spring_festival" */ '@/views/SpringFestival/Rule'),
  //   meta: {
  //     requiresAuth: true,
  //   }
  // },
  // {
  //   path: '/month_activity',
  //   name: 'MonthActivity',
  //   component: () => import(/* webpackChunkName: "month_activity" */ '@/views/MonthActivity'),
  //   meta: {
  //     keepAlive: true,
  //     requiresAuth: true,
  //   }
  // },
  // {
  //   path: '/zhongqiu_activity',
  //   name: 'ZhongqiuActivity',
  //   component: () => import(/* webpackChunkName: "zhongqiu_activity" */ '@/views/ZhongqiuActivity'),
  //   meta: {
  //     keepAlive: true,
  //     requiresAuth: true,
  //   }
  // }, {
  //   path: '/notice',
  //   name: 'Notice',
  //   component: () => import(/* webpackChunkName: "notice" */ '@/views/Notice'),
  // },
  // {
  //   path: '/guoqing_activity',
  //   name: 'GuoqingActivity',
  //   component: () => import(/* webpackChunkName: "guoqing_activity" */ '@/views/GuoqingActivity'),
  //   meta: {
  //     keepAlive: true,
  //     requiresAuth: false,
  //   }
  // },
  // {
  //   path: '/february_activity',
  //   name: 'FebruaryActivity',
  //   component: () => import(/* webpackChunkName: "february_activity" */ '@/views/FebruaryActivity'),
  //   meta: {
  //     requiresAuth: true,
  //   }
  // }, {
  //   path: '/february_activity/rule',
  //   name: 'FebruaryActivityRule',
  //   component: () => import(/* webpackChunkName: "february_activity" */ '@/views/FebruaryActivity/Rule'),
  //   meta: {
  //     requiresAuth: true,
  //   }
  // },
  // {
  //   path: '/march_activity',
  //   name: 'MarchActivity',
  //   component: () => import(/* webpackChunkName: "march_activity" */ '@/views/MarchActivity'),
  //   meta: {
  //     requiresAuth: true,
  //   }
  // }, {
  //   path: '/march_activity/rule',
  //   name: 'MarchActivityRule',
  //   component: () => import(/* webpackChunkName: "march_activity" */ '@/views/MarchActivity/Rule'),
  //   meta: {
  //     requiresAuth: true,
  //   }
  // },
  // {
  //   path: '/april_activity',
  //   name: 'AprilActivity',
  //   component: () => import(/* webpackChunkName: "april_activity" */ '@/views/AprilActivity'),
  //   meta: {
  //     requiresAuth: true,
  //   }
  // }, {
  //   path: '/april_activity/rule',
  //   name: 'AprilActivityRule',
  //   component: () => import(/* webpackChunkName: "april_activity" */ '@/views/AprilActivity/Rule'),
  //   meta: {
  //     requiresAuth: true,
  //   }
  // }, {
  //   path: '/april_activity/rank',
  //   name: 'AprilActivityRank',
  //   component: () => import(/* webpackChunkName: "april_activity" */ '@/views/AprilActivity/Rank'),
  //   meta: {
  //     requiresAuth: true,
  //   }
  // }, {
  //   path: '/0501_activity',
  //   name: '0501Activity',
  //   component: () => import(/* webpackChunkName: "0501_activity" */ '@/views/0501Activity'),
  //   meta: {
  //     requiresAuth: true,
  //   }
  // }, {
  //   path: '/0501_activity/rule',
  //   name: '0501ActivityRule',
  //   component: () => import(/* webpackChunkName: "0501_activity" */ '@/views/0501Activity/Rule'),
  //   meta: {
  //     requiresAuth: true,
  //   }
  // }, {
  //   path: '/duanwu_activity',
  //   name: 'DuanwuActivity',
  //   component: () => import(/* webpackChunkName: "duanwu_activity" */ '@/views/DuanwuActivity'),
  //   meta: {
  //     requiresAuth: true,
  //   }
  // },
  {
    path: '/july_activity',
    name: 'JulyActivity',
    component: () => import(/* webpackChunkName: "july_activity" */ '@/views/JulyActivity'),
    meta: {
      requiresAuth: true,
    }
  },
  {
    path: '/july_activity/rule',
    name: 'JulyActivityRule',
    component: () => import(/* webpackChunkName: "july_activity" */ '@/views/JulyActivity/Rule'),
    meta: {
      requiresAuth: true,
    }
  },
  // {
  //   path: '/august_activity',
  //   name: 'AugustActivity',
  //   component: () => import(/* webpackChunkName: "august_activity" */ '@/views/AugustActivity'),
  //   meta: {
  //     requiresAuth: true,
  //   }
  // }, {
  //   path: '/august_activity/rule',
  //   name: 'AugustActivityRule',
  //   component: () => import(/* webpackChunkName: "august_activity" */ '@/views/AugustActivity/Rule'),
  //   meta: {
  //     requiresAuth: true,
  //   }
  // },
  // {
  //   path: '/golden_week_activity',
  //   name: 'GoldenWeekActivity',
  //   component: () => import(/* webpackChunkName: "golden_week_activity" */ '@/views/GoldenWeekActivity'),
  //   meta: {
  //     requiresAuth: true,
  //   }
  // }, {
  //   path: '/golden_week_activity/rule',
  //   name: 'GoldenWeekActivityRule',
  //   component: () => import(/* webpackChunkName: "golden_week_activity" */ '@/views/GoldenWeekActivity/Rule'),
  //   meta: {
  //     requiresAuth: true,
  //   }
  // }, {
  //   path: '/eleven_carnival_activity',
  //   name: 'ElevenCarnivalActivity',
  //   component: () => import(/* webpackChunkName: "eleven_carnival_activity" */ '@/views/ElevenCarnivalActivity'),
  //   meta: {
  //     requiresAuth: true,
  //   }
  // }, {
  //   path: '/eleven_carnival_activity/rule',
  //   name: 'ElevenCarnivalActivityRule',
  //   component: () => import(/* webpackChunkName: "eleven_carnival_activity" */ '@/views/ElevenCarnivalActivity/Rule'),
  //   meta: {
  //     requiresAuth: true,
  //   }
  // }, {
  //   path: '/christmas_activity',
  //   name: 'ChristmasActivity',
  //   component: () => import(/* webpackChunkName: "christmas_activity" */ '@/views/ChristmasActivity'),
  //   meta: {
  //     requiresAuth: true,
  //   }
  // }, {
  //   path: '/laba_activity',
  //   name: 'LabaActivity',
  //   component: () => import(/* webpackChunkName: "laba_activity" */ '@/views/LabaActivity'),
  //   meta: {
  //     requiresAuth: true,
  //   }
  // }, {
  //   path: '/spring_activity',
  //   name: 'SpringActivity',
  //   component: () => import(/* webpackChunkName: "spring_activity" */ '@/views/SpringActivity'),
  //   meta: {
  //     requiresAuth: true,
  //   }
  // }, {
  //   path: '/double_eleven_activity',
  //   name: 'DoubleElevenActivity',
  //   component: () => import(/* webpackChunkName: "double_eleven_activity" */ '@/views/DoubleElevenActivity'),
  //   meta: {
  //     keepAlive: true,
  //     requiresAuth: true,
  //   }
  // },
  // {
  //   path: '/250101_activity',
  //   name: '250101Activity',
  //   component: () => import(/* webpackChunkName: "250101_activity" */ '@/views/250101Activity'),
  //   meta: {
  //     keepAlive: true,
  //     requiresAuth: true,
  //   }
  // },
  // {
  //   path: '/250101_activity/danmaku',
  //   name: '250101ActivityDanmaku',
  //   component: () => import(/* webpackChunkName: "250101_activity" */ '@/views/250101Activity/Danmaku'),
  //   meta: {
  //     keepAlive: true,
  //     requiresAuth: true,
  //   }
  // },
  // {
  //   path: '/250101_activity/new_year_lottery',
  //   name: '250101ActivityNewYearLottery',
  //   component: () => import(/* webpackChunkName: "250101_activity" */ '@/views/250101Activity/NewYearLottery'),
  //   meta: {
  //     keepAlive: true,
  //     requiresAuth: true,
  //   }
  // },
  // {
  //   path: '/250101_activity/seckill',
  //   name: '250101ActivitySeckill',
  //   component: () => import(/* webpackChunkName: "250101_activity" */ '@/views/250101Activity/Seckill'),
  //   meta: {
  //     keepAlive: true,
  //     requiresAuth: true,
  //   }
  // },
  {
    path: '/25_new_year_activity',
    name: '25NewYearActivity',
    component: () =>
      import(
        /* webpackChunkName: "25_new_year_activity" */ '@/views/25NewYearActivity'
      ),
    meta: {
      keepAlive: true,
      requiresAuth: true,
    },
  },
  {
    path: '/25_new_year_activity/fillIn_address',
    name: '25NewYearActivityFillInAddress',
    component: () =>
      import(
        /* webpackChunkName: "25_new_year_activity" */ '@/views/25NewYearActivity/FillInAddress'
      ),
    meta: {
      keepAlive: true,
      requiresAuth: true,
    },
  },
  {
    path: '/25_new_year_activity/lucky_value_task',
    name: '25NewYearActivityLuckyValueTask',
    component: () =>
      import(
        /* webpackChunkName: "25_new_year_activity" */ '@/views/25NewYearActivity/LuckyValueTask'
      ),
    meta: {
      keepAlive: true,
      requiresAuth: true,
    },
  },
  {
    path: '/25_new_year_activity/rule_page',
    name: '25NewYearActivityRulePage',
    component: () =>
      import(
        /* webpackChunkName: "25_new_year_activity" */ '@/views/25NewYearActivity/RulePage'
      ),
    meta: {
      keepAlive: true,
      requiresAuth: true,
    },
  },
  {
    path: '/25_new_year_activity/couplets',
    name: '25NewYearActivityCouplets',
    component: () =>
      import(
        /* webpackChunkName: "25_new_year_activity" */ '@/views/25NewYearActivity/Couplets'
      ),
    meta: {
      keepAlive: true,
      requiresAuth: true,
    },
  },
  {
    path: '/25_new_year_activity/more_couplets',
    name: '25NewYearActivityMoreCouplets',
    component: () =>
      import(
        /* webpackChunkName: "25_new_year_activity" */ '@/views/25NewYearActivity/MoreCouplets'
      ),
    meta: {
      keepAlive: true,
      requiresAuth: true,
    },
  },
  {
    path: '/25_new_year_activity/hbCoverStep',
    name: '25NewYearActivityHbCoverStep',
    component: () =>
      import(
        /* webpackChunkName: "25_new_year_activity" */ '@/views/25NewYearActivity/HbCoverStep'
      ),
    meta: {
      keepAlive: true,
      requiresAuth: true,
    },
  },
];

const router = new VueRouter({
  // 记录各个滚动条位置
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition;
    } else {
      return { x: 0, y: 0 };
    }
  },
  routes,
});

router.afterEach((to, from) => {
  // 记录第一次进入的页面url
  // 当页面为打开的第一个页面时，url添加进sessionStorage，用来给官包判断是返回还是关闭窗口
  if (platform == 'android' && !window.sessionStorage.firstUrl) {
    window.sessionStorage.firstUrl = window.location.href;
  }
});

export default router;
