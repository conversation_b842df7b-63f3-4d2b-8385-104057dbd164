export default {
  state: {
    showEwmPopup: false, //二维码弹窗显示
    ewmSrc: '', // 需要生成二维码的地址
    showPtbRechargePopup: false, // 平台币充值弹窗
    showSvipRechargePopup: false, // svip充值弹窗
    showCwkRechargePopup: false, // 畅玩卡充值弹窗
  },
  mutations: {
    setShowEwmPopup(state, showEwmPopup) {
      state.showEwmPopup = showEwmPopup ?? false;
    },
    setEwmSrc(state, ewmSrc) {
      state.ewmSrc = ewmSrc ? ewmSrc : '';
    },
    setShowPtbRechargePopup(state, showPtbRechargePopup) {
      state.showPtbRechargePopup = showPtbRechargePopup ?? false;
    },
    setShowSvipRechargePopup(state, showSvipRechargePopup) {
      state.showSvipRechargePopup = showSvipRechargePopup ?? false;
    },
    setShowCwkRechargePopup(state, showCwkRechargePopup) {
      state.showCwkRechargePopup = showCwkRechargePopup ?? false;
    },
  },
  getters: {
    showEwmPopup(state) {
      return state.showEwmPopup;
    },
    ewmSrc(state) {
      return state.ewmSrc;
    },
    showPtbRechargePopup(state) {
      return state.showPtbRechargePopup;
    },
    showSvipRechargePopup(state) {
      return state.showSvipRechargePopup;
    },
    showCwkRechargePopup(state) {
      return state.showCwkRechargePopup;
    },
  },
};
