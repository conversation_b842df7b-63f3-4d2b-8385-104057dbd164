<template>
  <div>
    <van-popup
      v-model="popup"
      :close-on-click-overlay="true"
      position="bottom"
      round
      class="svip-recharge-popup"
      :lock-scroll="false"
    >
      <div class="close" @click="setShowSvipRechargePopup(false)"></div>
      <div class="popup-title"></div>
      <div class="swiper" :class="{ short: selectList.length <= 2 }">
        <swiper
          id="svipSwiper"
          class="svip-swiper"
          ref="svipSwiper"
          :options="swiperOptions"
          :auto-update="true"
          style="width: 100%; margin: 0 auto"
          v-if="selectList.length > 0"
        >
          <swiper-slide
            class="select-item"
            v-for="(item, index) in selectList"
            :class="{
              on: selectedMeal.amount == item.amount,
            }"
            :key="index"
          >
            <div v-if="item.tips" class="tips">
              {{ item.tips }}
            </div>
            <div class="select-title">{{ item.title }}会员</div>
            <div class="money">
              ¥<span>{{ item.amount }}</span>
            </div>
            <div class="small-text-container">
              <span
                v-if="item.show_tips.amount"
                class="small-text small-text1"
                >{{ item.show_tips.amount }}</span
              >
              <span v-if="item.show_tips.desc" class="small-text small-text2">{{
                item.show_tips.desc
              }}</span>
            </div>
            <div class="tip">
              开通立返<span>{{ item.rebate_gold }}</span
              >金币<br />每日签到奖励翻倍
            </div>
          </swiper-slide>
        </swiper>
        <!-- <div class="svip-scrollbar"></div> -->
      </div>
      <div class="pay-way-line" @click="payWayPopup = true">
        <div class="pay-way-title">支付方式</div>
        <div class="pay-way-item">
          <i
            class="icon"
            :style="{ backgroundImage: `url(${payWayItem.icon})` }"
          ></i>
          <span class="text">{{ payWayItem.name }}</span>
          <div class="right-icon"></div>
        </div>
      </div>
      <div class="recharge btn" @click="handlePay"></div>
      <bottom-safe-area></bottom-safe-area>
    </van-popup>
    <van-popup
      v-model="payWayPopup"
      :close-on-click-overlay="true"
      position="bottom"
      round
      class="svip-recharge-popup"
      :lock-scroll="false"
    >
      <div class="close" @click="payWayPopup = false"></div>
      <div class="popup-title pay-way-popup-title">选择支付方式</div>
      <ul class="pay-list">
        <li
          class="pay-item"
          :class="{ on: payWayItem.key == item.key }"
          v-for="(item, index) in payWayList"
          :key="index"
          @click="payWayItem = item"
        >
          <i class="icon" :style="{ backgroundImage: `url(${item.icon})` }"></i>
          <span class="text">{{ item.name }}</span>
          <div class="select-icon"></div>
        </li>
      </ul>
      <div class="recharge btn" @click="handlePay"></div>
      <bottom-safe-area></bottom-safe-area>
    </van-popup>
  </div>
</template>
<script>
import { mapMutations, mapGetters } from 'vuex';
import {
  ApiSvipIndex,
  ApiGetPayUrl,
  ApiCreateOrderSvip,
} from '@/api/views/recharge.js';
import { ApiActivityJanuarySvipInfo } from '@/api/views/laba_activity.js';
export default {
  data() {
    let that = this;
    return {
      swiperOptions: {
        observer: true, //开启动态检查器，监测swiper和slide
        observeSlideChildren: true, //监测Swiper的子元素wrapper、pagination、navigation、scrollbar或其他一级子元素
        slidesPerView: 'auto',
        freeMode: true,
        freeModeMomentumRatio: 0.3, // 运动惯量
        scrollbar: {
          el: '.svip-scrollbar',
        },
        on: {
          click: function () {
            setTimeout(() => {
              that.selectedMeal = that.selectList[this.clickedIndex];
            }, 0);
          },
        },
      },
      payWayItem: '', // 支付方式
      selectedMeal: {}, // 选中的套餐
      selectList: [], // 套餐列表
      payWayList: [], // 支付方式
      payWayPopup: false,
    };
  },
  computed: {
    popup: {
      get() {
        return this.showSvipRechargePopup;
      },
      set(value) {
        this.setShowSvipRechargePopup(value);
      },
    },
    ...mapGetters({
      showSvipRechargePopup: 'recharge/showSvipRechargePopup',
    }),
  },
  async created() {
    await this.getSvipList();
  },
  methods: {
    ...mapMutations({
      setShowSvipRechargePopup: 'recharge/setShowSvipRechargePopup',
    }),
    // 支付逻辑
    handlePay() {
      this.payWayPopup = false;
      this.setShowSvipRechargePopup(false);
      const orderParams = {
        day: this.selectedMeal.day,
        amount: this.selectedMeal.amount,
        rebate_gold: this.selectedMeal.rebate_gold,
        payWay: this.payWayItem.key,
        is_cycle: 0,
      };
      ApiCreateOrderSvip(orderParams).then(async orderRes => {
        ApiGetPayUrl({
          orderId: orderRes.data.orderId,
          orderType: 103,
          payWay: this.payWayItem.key,
          packageName: '',
        }).finally(() => {
          this.$emit('success');
          this.getSvipList();
        });
      });
    },
    // 获取套餐列表和支付方式
    async getSvipList() {
      const res = await ApiActivityJanuarySvipInfo();
      let { payway_list, svipList } = res.data;
      this.selectList = svipList;
      this.selectedMeal = this.selectList[0];
      this.payWayList = payway_list;
      this.payWayItem = this.payWayList[0];
    },
  },
};
</script>
<style lang="less" scoped>
.svip-recharge-popup {
  width: 100%;
  .close {
    position: absolute;
    right: 19 * @rem;
    top: 19 * @rem;
    width: 23 * @rem;
    height: 23 * @rem;
    background: url(~@/assets/images/laba-activity/svip-close.png) center center
      no-repeat;
    background-size: 13 * @rem 14 * @rem;
  }
  .popup-title {
    padding-left: 20 * @rem;
    font-size: 18 * @rem;
    line-height: 23 * @rem;
    height: 23 * @rem;
    font-weight: bold;
    color: #fd8312;
    margin-top: 19 * @rem;
    &.pay-way-popup-title {
      color: #111111;
      text-align: center;
      font-size: 16 * @rem;
      padding: 0;
    }
  }
  .swiper {
    box-sizing: border-box;
    width: 100%;
    height: 142 * @rem;
    margin-top: 14 * @rem;
    &.short {
      padding: 0 35 * @rem;
    }
    .svip-scrollbar {
      margin: 8 * @rem auto 0;
      width: 60 * @rem;
      height: 4 * @rem;
      background-color: #e0e0e0;
      border-radius: 2 * @rem;
      overflow: hidden;
    }
    /deep/ .swiper-scrollbar-drag {
      background: @themeBg;
    }
    .svip-swiper {
      .select-item {
        box-sizing: border-box;
        width: 129 * @rem;
        height: 142 * @rem;
        border-radius: 8 * @rem;
        border: 2 * @rem solid #ffdecc;
        margin-right: 8 * @rem;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        position: relative;
        background: #ffffff;
        overflow: hidden;
        .tips {
          position: absolute;
          top: 8 * @rem;
          left: -22 * @rem;
          transform: rotate(-45deg);
          width: 80 * @rem;
          height: 20 * @rem;
          line-height: 20 * @rem;
          background-color: #fe6b2c;
          color: #fff;
          text-align: center;
          font-size: 11 * @rem;
        }
        &.on {
          background: linear-gradient(158deg, #ffffff 0%, #fff4df 100%);
          &:before {
            content: '已选';
            width: 48 * @rem;
            height: 21 * @rem;
            border-radius: 0 10 * @rem 0 10 * @rem;
            position: absolute;
            right: -2 * @rem;
            top: -2 * @rem;
            text-align: center;
            background: #ff873f;
            font-size: 12 * @rem;
            color: #ffffff;
            line-height: 21 * @rem;
          }
          .money {
            color: #ff471f;
            span {
              color: #ff471f;
            }
          }
        }
        &:first-of-type {
          margin-left: 20 * @rem;
        }
        .select-title {
          font-size: 14 * @rem;
          color: rgba(186, 57, 24, 0.7);
          line-height: 18 * @rem;
          font-weight: 600;
          margin-top: 15 * @rem;
        }
        .money {
          font-size: 12 * @rem;
          line-height: 15 * @rem;
          color: #fea665;
          margin-top: 1 * @rem;
          span {
            font-size: 28 * @rem;
            color: #fd8312;
            line-height: 35 * @rem;
            font-weight: bold;
            margin-left: 6 * @rem;
          }
        }
        .small-text-container {
          height: 14 * @rem;
          .small-text {
            line-height: 14 * @rem;
            position: relative;
            color: #ba3918;
          }
          .small-text1 {
            text-decoration: line-through;
          }
        }
        .tip {
          text-align: center;
          font-size: 11 * @rem;
          color: rgba(186, 57, 24, 0.7);
          line-height: 14 * @rem;
          margin-top: 4 * @rem;
          span {
            color: #ff471f;
          }
        }
      }
    }
  }
  .pay-way-line {
    box-sizing: border-box;
    padding: 0 12 * @rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 331 * @rem;
    height: 37 * @rem;
    border-radius: 6 * @rem;
    background: #fff8ee;
    margin: 19 * @rem auto 0;
    .pay-way-title {
      font-size: 13 * @rem;
      color: #ba3918;
      font-weight: 600;
    }
    .pay-way-item {
      display: flex;
      align-items: center;
      .icon {
        display: block;
        width: 24 * @rem;
        height: 24 * @rem;
        background-position: center center;
        background-repeat: no-repeat;
        background-size: 20 * @rem 20 * @rem;
      }
      .text {
        margin-left: 4 * @rem;
        font-size: 13 * @rem;
        color: #ba3918;
      }
      .right-icon {
        margin-left: 2 * @rem;
        width: 8 * @rem;
        height: 8 * @rem;
        background: url(~@/assets/images/laba-activity/svip-right-icon.png)
          center center no-repeat;
        background-size: 8 * @rem 8 * @rem;
      }
    }
  }
  .payway-title {
    font-size: 16 * @rem;
    color: #333333;
    font-weight: 600;
    line-height: 20 * @rem;
    padding: 0 23 * @rem;
    margin-top: 20 * @rem;
  }
  .pay-list {
    overflow: hidden;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    margin-top: 30 * @rem;
    padding: 0 23 * @rem;
  }
  .pay-item {
    box-sizing: border-box;
    width: 335 * @rem;
    height: 52 * @rem;
    padding-top: 10 * @rem;
    text-align: center;
    border: 0.5 * @rem solid #e1e1e1;
    border-radius: 6 * @rem;
    font-size: 0;
    display: flex;
    align-items: center;
    padding: 0 12 * @rem;
    &:not(:first-of-type) {
      margin-top: 10 * @rem;
    }
    &.on {
      border-color: #ffd5ae;
      border-width: 1 * @rem;
    }
    .icon {
      display: block;
      width: 24 * @rem;
      height: 24 * @rem;
      background-repeat: no-repeat;
      background-size: 24 * @rem 24 * @rem;
    }
    .text {
      display: block;
      font-size: 14 * @rem;
      font-weight: 400;
      color: #333;
      flex: 1;
      min-width: 0;
      margin-left: 6 * @rem;
      text-align: left;
    }
    .select-icon {
      width: 16 * @rem;
      height: 16 * @rem;
      background: url(~@/assets/images/recharge/pay-no.png) no-repeat;
      background-size: 16 * @rem 16 * @rem;
    }
    &.on .select-icon {
      background-image: url(~@/assets/images/recharge/pay-yes.png);
    }
  }
  .recharge {
    width: 341 * @rem;
    height: 63 * @rem;
    .image-bg('~@/assets/images/laba-activity/svip-pay-btn.png');
    margin: 30 * @rem auto 20 * @rem;
  }
}
</style>
