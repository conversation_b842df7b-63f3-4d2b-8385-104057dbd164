<template>
  <div class="user-avatar">
    <img :src="avatar" />
  </div>
</template>

<script>
export default {
  props: {
    src: {
      // 外部传入的头像链接（可选）
      type: String,
      default: '',
    },
    self: {
      type: Boolean,
      default: true,
    },
  },
  computed: {
    avatar() {
      if (this.self) {
        return this.src || this.userInfo.avatar || this.defaultAvatar;
      } else {
        return this.src || this.defaultAvatar;
      }
    },
  },
};
</script>

<style lang="less" scoped>
.user-avatar {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  overflow: hidden;
  img {
    border-radius: 50%;
    display: block;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}
</style>
