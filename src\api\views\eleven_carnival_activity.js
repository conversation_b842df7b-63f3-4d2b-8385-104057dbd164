import { request } from '../index';

/**
 * 双十一狂欢来袭
 */

/**
 * 活动页/双十一狂欢来袭 - 双十一首页接口
 */
export function ApiActivityElevenCarnivalIndex(params = {}) {
  return request('/activity/eleven_carnival/index', params);
}

/**
 * 活动页/双十一狂欢来袭 - 双十一活动内容
 * @param { number } type 活动类型 1 瓜分千万金币红包 2 寻找双十一锦鲤 3充值积分兑换
 */
export function ApiActivityElevenCarnivalGetContent(params = {}) {
  return request('/activity/eleven_carnival/getContent', params);
}

/**
 * 活动页/双十一狂欢来袭 - 刮奖
 */
export function ApiActivityElevenCarnivalLottery(params = {}) {
  return request('/activity/eleven_carnival/lottery', params);
}

/**
 * 活动页/双十一狂欢来袭 - 奖励记录
 */
export function ApiActivityElevenCarnivalElevenLog(params = {}) {
  return request('/activity/eleven_carnival/elevenLog', params);
}

/**
 * 活动页/双十一狂欢来袭 - 积分兑换
 */
export function ApiActivityElevenCarnivalExchange(params = {}) {
  return request('/activity/eleven_carnival/exchange', params);
}

/**
 * 活动页/双十一狂欢来袭 - 领取刮奖次数
 */
export function ApiActivityElevenCarnivalReceiveLottery(params = {}) {
  return request('/activity/eleven_carnival/receiveLottery', params);
}
