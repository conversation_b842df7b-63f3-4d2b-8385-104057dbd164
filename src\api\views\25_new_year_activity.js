import { request } from '../index';

/**
 * 春节活动 - 活动一 首页
 * */
export function ApiDoingsChunjieIndex(params = {}) {
  return request('/doings/Chunjie/index', params);
}

/**
 * 春节活动 - 收货地址
 * */
export function ApiDoingsChunjieDeliveryAddress(params = {}) {
  return request('/doings/Chunjie/deliveryAddress', params);
}

/**
 * 春节活动 - 查询收货地址
 * */
export function ApiDoingsChunjieViewDeliveryAddress(params = {}) {
  return request('/doings/Chunjie/viewdeliveryAddress', params);
}

/**
 * 春节活动 - 开福袋
 * */
export function ApiDoingsChunjieTaskLuckyBag(params = {}) {
  return request('/doings/Chunjie/taskLuckyBag', params);
}

/**
 * 春节活动 - 查概率
 * */
export function ApiDoingsChunjieProbability(params = {}) {
  return request('/doings/Chunjie/probability', params);
}

/**
 * 春节活动 - 任务列表
 * */
export function ApiDoingsChunjieTaskList(params = {}) {
  return request('/doings/Chunjie/taskList', params);
}

/**
 * 春节活动 - 任务奖励领取
 * */
export function ApiDoingsChunjieTaskPrize(params = {}) {
  return request('/doings/Chunjie/taskPrize', params);
}

/**
 * 春节活动 -  领取任务
 * */
export function ApiDoingsChunjieTask(params = {}) {
  return request('/doings/Chunjie/task', params);
}

/**
 * 春节活动 -  游戏列表
 * */
export function ApiDoingsChunjieGetSuggestGames(params = {}) {
  return request('/doings/Chunjie/getSuggestGames', params);
}

/**
 * 春节活动 -  奖励记录
 * */
export function ApiDoingsChunjieTaskLog(params = {}) {
  return request('/doings/Chunjie/taskLog', params);
}

/**
 * 春节活动 -  活动二对联首页
 * */
export function ApiDoingsChunjieCouplet(params = {}) {
  return request('/doings/Chunjie/couplet', params);
}

/**
 * @function 春节活动 - 保存对联
 * @param {number} dl_id 上联的id
 * @param {string} text 对联内容
 */
export function ApiDoingsChunjieDowncouplet(params = {}) {
  return request('/doings/Chunjie/downcouplet', params);
}

/**
 * 春节活动 - 更多下联
 * */
export function ApiDoingsChunjieCouplets(params = {}) {
  return request('/doings/Chunjie/couplets', params);
}

/**
 * @function 春节活动 - 对联点赞
 * @param {int} id  id
 * @param {string} action yes no
 */
export function ApiDoingsChunjieThumb(params = {}) {
  return request('/doings/Chunjie/thumb', params);
}

/**
 * @function 春节活动 - 邀请有礼
 * @param {string} invitationCode  邀请码
 */
export function ApiDoingsChunjieInvitationCode(params = {}) {
  return request('/doings/Chunjie/invitationCode', params);
}

/**
 * @function 春节活动 - 获取红包封面序列号
 */
export function ApiDoingsChunjieCover(params = {}) {
  return request('/doings/Chunjie/cover', params);
}
