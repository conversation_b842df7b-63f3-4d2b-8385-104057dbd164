<template>
  <div class="shuangshier-page">
    <nav-bar-2
      ref="topNavBar"
      bgStyle="transparent-white"
      :azShow="true"
      :placeholder="false"
    ></nav-bar-2>
    <div class="bg1">
      <div class="user-container">
        <div class="active-status" v-if="activity_status !== 1">
          {{ active_status_text }}
        </div>
        <div class="user-info" v-else-if="userInfo.token">
          <div class="avatar">
            <UserAvatar />
          </div>
          <div class="nick-name">{{ userInfo.nickname }}</div>
        </div>
        <div class="user-info btn" @click="login" v-else>
          <div class="avatar">
            <div class="default-avatar"></div>
          </div>
          <div class="nick-name">请登录</div>
        </div>
      </div>
      <div class="point-container">
        <div class="left">
          <div class="text">
            当前积分：<span>{{ point }}</span>
          </div>
          <div
            @click="explain_toast_show = !explain_toast_show"
            class="icon2 btn"
          ></div>
        </div>
        <div @click="toPage('1212ExchangePage', { type: 2 })" class="right btn"
          >获取次数</div
        >
        <div v-if="explain_toast_show" class="explain">
          活动期间，每天前5笔游戏现金充值订单可获得等额积分奖励（仅限游戏内使用微信/支付宝充值）
        </div>
      </div>
      <div class="right-button-container">
        <div class="button button1 btn" @click="toPage('1212Rule')"></div>
        <div class="button button2 btn" @click="getRecordList">
          <div v-if="record_list_update" class="icon1"></div>
        </div>
      </div>
      <div class="bottom-button">
        <div class="count">剩余次数：{{ lottery_count }}</div>
        <div
          @click="getPoint"
          class="button"
          :class="{
            empty:
              today_lottery_status === 0 &&
              lottery_count === 0 &&
              userInfo.token,
          }"
        ></div>
        <div
          @click="toPage('1212ExchangePage', { type: 1 })"
          class="point-exchange"
        ></div>
      </div>
    </div>
    <div class="bg2">
      <div class="lottery-button">
        <div class="left">
          刮奖次数：<span class="number">{{ scratch_count }}</span
          >次
        </div>
        <div @click="getActivityInfo" class="right btn">刷新</div>
      </div>
      <div class="canvas-container">
        <div class="prize">{{ first_gold }}金币</div>
        <canvas
          id="canvas"
          @touchmove="touchMove"
          @touchstart="touchStart"
          @touchend="touchEnd"
        ></canvas>
      </div>
      <div
        class="bottom-button empty"
        v-if="today_scratch_status === 0 && userInfo.token"
      ></div>
      <div class="bottom-button btn" v-else @click="toRecharge"></div>
    </div>
    <div class="bg3">
      <div class="list">
        <div v-for="(item, index) in prize_list" :key="index" class="item">
          <div class="left">
            <div class="big-text">{{ item.title }}</div>
            <div class="small-text">{{ item.desc }}</div>
          </div>
          <div
            @click="handleExchange(item)"
            :class="{ empty: item.is_get || !item.is_ok }"
            class="right btn"
          >
            {{ item.is_get == 1 ? '已领取' : '领取' }}
          </div>
        </div>
      </div>
    </div>
    <!-- 刮奖记录 -->
    <van-dialog
      v-model="record_list_popup"
      :lock-scroll="false"
      :show-confirm-button="false"
      :close-on-click-overlay="false"
      class="popup record-list-popup"
    >
      <div class="popup-close" @click="closeRecordPopup"></div>
      <div class="title">刮奖记录</div>
      <div v-if="record_list.length > 0" class="list">
        <div v-for="(item, index) in record_list" :key="index" class="item">
          <div class="left">{{ item.title }}</div>
          <div v-if="item.gold" class="right">
            <span>+{{ item.gold }}</span
            >金币
          </div>
          <div v-if="item.svip" class="right">
            <span>+{{ item.svip }}</span
            >SVIP
          </div>
          <div v-if="item.ptb" class="right">
            <span>+{{ item.ptb }}</span
            >平台币
          </div>
          <div v-if="item.get_int" class="right">
            <span>+{{ item.get_int }}</span
            >积分
          </div>
          <div v-if="item.scratch" class="right">
            <span>+{{ item.scratch }}</span
            >次
          </div>
          <div v-if="item.draw" class="right">
            <span>+{{ item.draw }}</span
            >次
          </div>
        </div>
      </div>
      <div v-else class="empty">暂无刮奖记录</div>
    </van-dialog>
    <!-- 暂无刮奖机会弹窗 -->
    <van-dialog
      v-model="lottery_empty_popup"
      :lock-scroll="false"
      :show-confirm-button="false"
      :close-on-click-overlay="false"
      class="popup lottery-empty-popup"
    >
      <div class="popup-close" @click="lottery_empty_popup = false"></div>
      <div class="title">当前未有剩余刮奖机会</div>
      <div class="text"
        >活动期间，每日前3次充值平台币，即可参与刮奖最高可赢充值金额等额金币奖励（免单）</div
      >
      <div @click="toRecharge()" class="bottom-button"></div>
    </van-dialog>
    <!-- 暂无抽奖机会弹窗 -->
    <van-dialog
      v-model="point_empty_popup"
      :lock-scroll="false"
      :show-confirm-button="false"
      :close-on-click-overlay="false"
      class="popup point-empty-popup"
    >
      <div class="popup-close" @click="point_empty_popup = false"></div>
      <div class="title">当前未有剩余抽奖机会</div>
      <div class="text"
        >活动期间可使用积分进行抽奖，每次消耗100积分每个用户每天可抽奖三次</div
      >
      <div @click="point_empty_popup = false" class="bottom-button"></div>
    </van-dialog>
    <!-- 抽奖结果 -->
    <van-dialog
      v-model="lottery_result_popup"
      :lock-scroll="false"
      :show-confirm-button="false"
      :close-on-click-overlay="false"
      class="popup lottery-result-popup"
    >
      <div class="popup-close" @click="lottery_result_popup = false"></div>
      <div class="title">{{ lottery_result }}</div>
      <div @click="lottery_result_popup = false" class="bottom-button"></div>
    </van-dialog>
    <ptb-recharge-popup @success="getActivityInfo"></ptb-recharge-popup>
  </div>
</template>

<script>
import { BOX_login } from '@/utils/box.uni.js';
import {
  Api1212Index,
  Api1212ExtraReward,
  Api1212RecordExchange,
  Api1212Scratch,
  Api1212Lottery,
} from '@/api/views/1212.js';
import ptbRechargePopup from '@/components/ptb-recharge-popup';
import UserAvatar from '@/components/user-avatar';
import { mapGetters, mapMutations } from 'vuex';
import canvasImg from '@/assets/images/1212/gua-area.png';

export default {
  data() {
    return {
      explain_toast_show: false, //解释显示
      activity_status: 2, //1活动已开始 2 活动不存在 3活动未开始 4活动已结束
      point: 0, //当前积分
      record_list: [], //领取日志
      scratch_count: 0, //今日刮奖剩余次数
      lottery_count: 0, //游戏充值抽奖剩余次数
      today_scratch_status: 0, //今日刮奖状态 1:可以刮奖 0不可刮奖
      today_lottery_status: 0, //今日抽奖状态 1:可以抽奖 0不可抽奖
      canvas: '', // 画布
      title_message_popup: false, //带title的通用消息弹窗
      prize_list: [], //奖品列表
      record_list_popup: false, //记录弹窗
      record_list: [], //记录列表
      lottery_empty_popup: false, //暂无刮奖机会弹窗
      point_empty_popup: false, //暂无获取积分机会弹窗
      scratch_status: 0, //刮奖状态 0未开始1刮奖中2刮奖完
      clearCount: 0, // 刮奖区被刮开的程度 最大150就全解开
      ctx: '', // 画笔
      ratio: 0,
      first_gold: 0, //刮奖中的金币
      lottery_result: '', //抽奖中的积分
      lottery_result_popup: false, //抽奖结果弹窗
      finished: false, //防抖
      record_list_update: true, //是否显示兑奖记录小红点
    };
  },
  computed: {
    active_status_text() {
      const arr = ['活动已开始', '活动不存在', '活动未开始', '活动已结束'];
      return arr[this.activity_status - 1];
    },
    ...mapGetters({
      userInfo: 'user/userInfo',
    }),
  },
  async created() {
    await this.getActivityInfo();
  },
  mounted() {
    this.ratio = document.body.clientWidth / 375;
    this.canvas = document.getElementById('canvas');
    this.canvas.width = this.canvas.clientWidth;
    this.canvas.height = this.canvas.clientHeight;
    this.ctx = this.canvas.getContext('2d');
    this.initCanvas();
  },
  activated() {
    this.getActivityInfo();
  },
  methods: {
    // 关闭记录弹窗
    closeRecordPopup() {
      this.record_list_popup = false;
      this.record_list_update = false;
    },
    // 初始化活动信息
    async getActivityInfo() {
      const res = await Api1212Index();
      this.activity_status = parseInt(res.data.activity_status);
      this.point = parseInt(res.data.int);
      this.first_gold = res.data.gold;
      this.scratch_count = parseInt(res.data.gj_num);
      this.lottery_count = parseInt(res.data.cj_num);
      this.prize_list = res.data.prize_info;
      this.today_scratch_status = parseInt(res.data.get_gj);
      this.today_lottery_status = parseInt(res.data.get_int);
    },
    login() {
      BOX_login();
    },
    //     handleRemain_gold(str, number) {
    //       let temp = "";
    //       switch (number) {
    //         case 1:
    //           temp = `${str.match(/(\S*)已获取/)[1]}已获取`;
    //           break;
    //         case 2:
    //           temp = `${str.match(/已获取(\S*)金币/)[1]}`;
    //           break;
    //         case 3:
    //           temp = "金币";
    //       }
    //       return temp;
    //     },
    async initCanvas() {
      this.ctx.globalCompositeOperation = 'source-over';
      let image = new Image();
      image.src = canvasImg;
      image.onload = async () => {
        await this.ctx.drawImage(
          image,
          0,
          0,
          this.canvas.width,
          this.canvas.height,
        );
      };
    },
    touchStart(e) {
      if (this.activity_status != 1) {
        this.$toast(this.active_status_text);
        return false;
      }
      if (!this.userInfo.token) {
        this.login();
        return false;
      }
      switch (this.scratch_status) {
        case 0: // 未开始
          if (this.today_scratch_status == 0 && this.scratch_count <= 0) {
            this.$toast('今日次数已用完');
            return;
          } else if (this.scratch_count <= 0) {
            // 当没有刮奖次数时;
            this.lottery_empty_popup = true;
            return;
          } else {
            // 开始刮奖
            this.scratch_status = 1;
          }
          break;
        case 1: // 刮奖中
          break;
        case 2: // 已结束
          break;
      }
    },
    touchMove(e) {
      if (this.scratch_status == 1) {
        let x = e.touches[0].clientX - this.canvas.getBoundingClientRect().left,
          y = e.touches[0].clientY - this.canvas.getBoundingClientRect().top;
        e.preventDefault();
        this.ctx.clearRect(x, y, 15 * this.ratio, 15 * this.ratio);
        this.clearCount++;
      }
    },
    async touchEnd(e) {
      if (this.scratch_status == 1) {
        if (this.clearCount > 80) {
          // 当手指累计滑动超过80时触发清空矩形画布, 数值越大要刮得越久
          this.scratch_status = 2;
          this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
          await this.handleScratch();
        }
      }
    },
    async handleScratch() {
      try {
        const res = await Api1212Scratch();
        let {
          code,
          data: { gold },
        } = res;
        if (code > 0) {
          this.$toast(`恭喜获得${gold}金币`);
          this.record_list_update = true;
        }
      } finally {
        setTimeout(async () => {
          this.scratch_status = 0;
          this.clearCount = 0;
          this.initCanvas();
          await this.getActivityInfo();
        }, 500);
      }
    },
    // 刷新刮奖次数，状态
    async handleRefresh() {
      const res = await ApiFestivalRemainLotteryCount({ refresh: 1 });
      this.scratch_count = res.data.scratch_count;
      this.clearCount = 0;
      this.scratch_status = 0;
      this.initCanvas();
    },
    // 领取奖励
    async handleExchange(item) {
      if (!item.is_ok) {
        this.$toast('尚未满足领取条件');
        return false;
      }
      if (item.is_get) {
        this.$toast('您已经领取过了');
        return false;
      }
      try {
        const res = await Api1212ExtraReward({ id: item.id });
        if (res.code == 3) {
          this.$toast(`领取成功，获得${res.data.get_int}积分`);
        }
        this.getActivityInfo();
      } catch {}
    },
    toExchangePage() {
      if (!this.userInfo.token) {
        this.login();
        return false;
      }
      if (this.activity_status != 1) {
        this.$toast(this.active_status_text);
        return false;
      }
      this.toPage('1212ExchangePage', { type: 1 });
    },
    async getRecordList() {
      if (!this.userInfo.token) {
        this.login();
        return false;
      }
      if (this.activity_status != 1) {
        this.$toast(this.active_status_text);
        return false;
      }
      this.$toast({
        type: 'loading',
        duration: 0,
        message: '拼命加载中...',
        forbidClick: true,
      });
      try {
        const res = await Api1212RecordExchange();
        this.record_list = res.data.list;
        this.record_list_popup = true;
        this.$toast.clear();
      } finally {
      }
    },
    toRecharge() {
      if (!this.userInfo.token) {
        this.login();
        return false;
      }
      if (this.activity_status != 1) {
        this.$toast(this.active_status_text);
        return false;
      }
      this.lottery_empty_popup = false;
      this.setShowPtbRechargePopup(true);
    },
    async getPoint() {
      if (!this.userInfo.token) {
        this.login();
        return false;
      }
      if (this.activity_status != 1) {
        this.$toast(this.active_status_text);
        return false;
      }
      if (this.today_lottery_status == 1 && this.lottery_count <= 0) {
        this.point_empty_popup = true;
        return false;
      }
      if (this.finished) return false;
      this.finished = true;
      this.$toast({
        type: 'loading',
        duration: 0,
      });
      try {
        const res = await Api1212Lottery();
        if (res.data.get_int) {
          this.lottery_result = `恭喜，获得${res.data.get_int}积分`;
        }
        if (res.data.gold) {
          this.lottery_result = `恭喜，获得${res.data.gold}金币`;
        }
        this.lottery_result_popup = true;
        this.record_list_update = true;
        await this.getActivityInfo();
        this.$toast.clear();
      } finally {
        this.finished = false;
      }
    },
    ...mapMutations({
      setShowPtbRechargePopup: 'recharge/setShowPtbRechargePopup',
    }),
  },
  components: {
    ptbRechargePopup,
    UserAvatar,
  },
};
</script>

<style lang="less" scoped>
.shuangshier-page {
  width: 100%;
  height: auto;
  background: #4d3188;
  font-family: PingFang SC-Medium, PingFang SC;
  .bg1 {
    position: relative;
    width: 100%;
    height: 550.5 * @rem;
    overflow: hidden;
    .image-bg('~@/assets/images/1212/1212_bg1.png');
    .active-status {
      position: absolute;
      top: 10 * @rem;
      left: 50%;
      transform: translate(-50%, 0);
      font-family: '黑体';
      color: #fff;
      font-size: 22 * @rem;
    }
    .user-container {
      position: absolute;
      top: 231 * @rem;
      left: 50%;
      transform: translate(-50%, 0);
      width: 230 * @rem;
      height: 47 * @rem;
      .user-info {
        height: 100%;
        display: flex;
        align-items: center;
      }
      .avatar {
        width: 30 * @rem;
        height: 30 * @rem;
        .default-avatar {
          width: 100%;
          height: 100%;
          .image-bg('~@/assets/images/1212/1212_button5.png');
        }
      }
      .nick-name {
        margin-left: 10 * @rem;
        color: #fff;
        font-size: 15 * @rem;
      }
    }
    .point-container {
      position: absolute;
      top: 300 * @rem;
      left: 50%;
      transform: translate(-50%, 0);
      display: flex;
      width: 250 * @rem;
      height: 40 * @rem;
      justify-content: space-between;
      .left {
        display: flex;
        flex: 1;
        position: relative;
        .text {
          width: 115 * @rem;
          font-size: 14 * @rem;
          font-family: PingFang SC-Semibold, PingFang SC;
          line-height: 41 * @rem;
          color: #fff;
          span {
            color: #ffe500;
          }
        }
        .icon2 {
          display: block;
          width: 14 * @rem;
          height: 14 * @rem;
          margin-top: 12.5 * @rem;
          margin-left: 9 * @rem;
          .image-bg('~@/assets/images/1212/1212_icon2.png');
        }
      }
      .right {
        width: 67 * @rem;
        height: 26.5 * @rem;
        margin-top: 6 * @rem;
        .image-bg('~@/assets/images/1212/1212_button3.png');
        color: #fff;
        text-align: center;
        line-height: 24 * @rem;
      }
      .explain {
        position: absolute;
        top: 38 * @rem;
        left: -2 * @rem;
        width: 264 * @rem;
        padding: 7 * @rem 10 * @rem;
        box-sizing: border-box;
        color: #fff;
        font-size: 11 * @rem;
        line-height: 18 * @rem;
        background-color: rgba(38, 7, 118, 1);
        border-radius: 5 * @rem;
        &::before {
          content: '';
          position: absolute;
          top: -5 * @rem;
          left: 50%;
          z-index: -1;
          transform: translate(-50%) rotate(45deg);
          width: 10 * @rem;
          height: 10 * @rem;
          background-color: rgba(38, 7, 118, 1);
        }
      }
    }
    .bottom-button {
      position: absolute;
      top: 460 * @rem;
      left: 50%;
      transform: translate(-50%, 0);
      .count {
        width: 100 * @rem;
        height: 33 * @rem;
        margin: 0 auto 5 * @rem;
        .image-bg('~@/assets/images/1212/1212_bg4.png');
        font-size: 12 * @rem;
        font-weight: 600;
        color: #7322da;
        text-align: center;
        line-height: 25 * @rem;
      }
      .button {
        width: 137 * @rem;
        height: 49 * @rem;
        .image-bg('~@/assets/images/1212/1212_button4.png');
        &.empty {
          .image-bg('~@/assets/images/1212/1212_button6.png');
        }
      }
      .point-exchange {
        position: absolute;
        top: 50 * @rem;
        left: -88 * @rem;
        width: 65 * @rem;
        height: 39 * @rem;
      }
    }
    .right-button-container {
      position: absolute;
      top: 140 * @rem;
      right: 0;
      .button {
        width: 26 * @rem;
        height: 57 * @rem;
        &.button1 {
          .image-bg('~@/assets/images/1212/1212_button1.png');
          margin-bottom: 9 * @rem;
        }
        &.button2 {
          position: relative;
          .image-bg('~@/assets/images/1212/1212_button2.png');
          .icon1 {
            position: absolute;
            top: -5 * @rem;
            left: -5 * @rem;
            width: 11 * @rem;
            height: 11 * @rem;
            .image-bg('~@/assets/images/1212/1212_icon1.png');
          }
        }
      }
    }
  }
  .bg2 {
    position: relative;
    width: 100%;
    height: 666 * @rem;
    .image-bg('~@/assets/images/1212/1212_bg2.png');
    .lottery-button {
      position: absolute;
      top: 380 * @rem;
      left: 0;
      width: 100%;
      box-sizing: border-box;
      padding: 0 58 * @rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .left {
        font-size: 14 * @rem;
        font-family: PingFang SC-Semibold, PingFang SC;
        font-weight: 600;
        color: #6a3dd3;
        .number {
          margin: 0 5 * @rem;
        }
      }
      .right {
        width: 57 * @rem;
        height: 29 * @rem;
        text-align: center;
        line-height: 26 * @rem;
        font-size: 14 * @rem;
        color: #fff;
        .image-bg('~@/assets/images/1212/1212_button10.png');
      }
    }
    .canvas-container {
      width: 100%;
      position: absolute;
      top: 442 * @rem;
      left: 0;
      .prize {
        width: 265 * @rem;
        height: 96 * @rem;
        margin: 0 auto;
        line-height: 96 * @rem;
        background: #f5f5f5;
        font-size: 35 * @rem;
        letter-spacing: 3 * @rem;
        text-align: center;
        color: #f7572d;
        font-weight: bold;
        border-radius: 12 * @rem;
      }
      #canvas {
        width: 265 * @rem;
        height: 96 * @rem;
        position: absolute;
        top: 0;
        left: 50%;
        transform: translate(-50%, 0);
      }
    }
    .bottom-button {
      position: absolute;
      top: 600 * @rem;
      left: 50%;
      transform: translate(-50%, 0);
      width: 214 * @rem;
      height: 47 * @rem;
      .image-bg('~@/assets/images/1212/1212_button9.png');
      &.empty {
        .image-bg('~@/assets/images/1212/1212_button7.png');
      }
    }
  }
  .bg3 {
    position: relative;
    width: 100%;
    height: 415 * @rem;
    .image-bg('~@/assets/images/1212/1212_bg3.png');
    .list {
      position: absolute;
      top: 58 * @rem;
      left: 0;
      width: 100%;
      .item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 335 * @rem;
        height: 70 * @rem;
        box-sizing: border-box;
        padding: 15 * @rem 10 * @rem;
        margin: 0 auto 10 * @rem;
        background: linear-gradient(113deg, #99a3ff 0%, #8f48ff 93%);
        border-radius: 12 * @rem 12 * @rem 12 * @rem 12 * @rem;
        .left {
          flex: 1;
          display: flex;
          height: 100%;
          flex-direction: column;
          justify-content: space-between;
          font-size: 12 * @rem;
          font-family: PingFang SC-Regular, PingFang SC;
          color: #ffffff;
          .big-text {
            font-size: 14 * @rem;
            font-family: PingFang SC-Semibold, PingFang SC;
            font-weight: 600;
          }
          .small-text {
          }
        }
        .right {
          width: 57 * @rem;
          height: 29 * @rem;
          text-align: center;
          line-height: 26 * @rem;
          font-size: 14 * @rem;
          color: #fff;
          .image-bg('~@/assets/images/1212/1212_button10.png');
          &.empty {
            .image-bg('~@/assets/images/1212/1212_button8.png');
          }
        }
      }
    }
  }
}
.popup {
  width: 290 * @rem;
  padding: 18 * @rem;
  box-shadow: 0 * @rem 2 * @rem 15 * @rem 0 * @rem rgba(248, 0, 0, 0.1);
  border: 2 * @rem solid #9680d9;
  background: #f8f9ff;
  .text {
    font-size: 14 * @rem;
    color: #835cad;
  }
  .popup-close {
    width: 33 * @rem;
    height: 27 * @rem;
    background: #835cad url(~@/assets/images/1212/popup-close.png) center center
      no-repeat;
    background-size: 22 * @rem 22 * @rem;
    position: absolute;
    right: -1 * @rem;
    top: -1 * @rem;
    border-radius: 0 12 * @rem 0 12 * @rem;
  }
  .title {
    margin-bottom: 15 * @rem;
    text-align: center;
    font-size: 16 * @rem;
    font-family: PingFang SC-Semibold, PingFang SC;
    font-weight: 600;
    color: #835cad;
  }
}
.record-list-popup {
  padding: 18 * @rem 0;
  height: 245 * @rem;
  .list {
    height: 210 * @rem;
    overflow-y: scroll;
    padding: 0 18 * @rem;
    .item {
      display: flex;
      justify-content: space-between;
      height: 30 * @rem;
      align-items: center;
      font-size: 14 * @rem;
      color: #835cad;
      span {
        color: #f8582e;
      }
    }
  }
  .empty {
    width: 100%;
    height: 210 * @rem;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #835cad;
  }
}
.lottery-empty-popup {
  .bottom-button {
    width: 254 * @rem;
    height: 48 * @rem;
    margin: 15 * @rem auto 0;
    .image-bg('~@/assets/images/1212/1212_button12.png');
  }
}
.point-empty-popup,
.lottery-result-popup {
  .bottom-button {
    width: 254 * @rem;
    height: 48 * @rem;
    margin: 15 * @rem auto 0;
    .image-bg('~@/assets/images/1212/1212_button11.png');
  }
}
</style>
