<template>
  <div class="shuangshier-rule">
    <nav-bar-2
      ref="topNavBar"
      bgStyle="transparent-white"
      :azShow="true"
      :title="'活动规则'"
      :placeholder="false"
      v-if="navBgTransparent"
    ></nav-bar-2>
    <nav-bar-2 :title="'活动规则'" :placeholder="false" :border="true" v-else>
    </nav-bar-2>
    <div class="section">
      <div class="title"><span>活动时间：</span></div>
      <div class="text">2022-12-10 00:00:00 ~ 2022-12-12 23:59:59</div>
    </div>
    <div class="section">
      <div class="title"><span>活动内容：</span></div>
      <div class="container">
        <div class="big-text">1.游戏现金充值得积分</div>
        <div class="text">
          活动期间，每天前<span class="orange">5</span
          >笔游戏现金充值订单可获得等额积分奖励（仅限<span class="orange"
            >游戏内使用微信/支付宝充值</span
          >）。<br />RMB对积分的比例为1：10，即充值1元获得10积分。
        </div>
        <div class="text">
          如有使用金币/平台币/代金券抵扣的，则只计算抵扣后的现金充值金额。
        </div>
      </div>
      <div class="container">
        <div class="big-text">2.积分抽好礼</div>
        <div class="text">
          活动期间可使用积分进行抽奖。<br />每次消耗100积分。每个用户每天可抽奖三次。<br />
          概率公示：
        </div>
        <table>
          <tr>
            <th>抽奖</th>
            <th>概率</th>
          </tr>
          <tr>
            <td>IPhone14ProMax 256G</td>
            <td>0.5%</td>
          </tr>
          <tr>
            <td>switch国行</td>
            <td>1.5%</td>
          </tr>
          <tr>
            <td>888平台币</td>
            <td>4%</td>
          </tr>
          <tr>
            <td>3733金币</td>
            <td>6%</td>
          </tr>
          <tr>
            <td>888金币</td>
            <td>8%</td>
          </tr>
          <tr>
            <td>288<span class="yellow">积分</span></td>
            <td>10%</td>
          </tr>
          <tr>
            <td>88金币</td>
            <td>20%</td>
          </tr>
          <tr>
            <td>68<span class="yellow">积分</span></td>
            <td>50%</td>
          </tr>
        </table>
      </div>
      <div class="container">
        <div class="big-text">3.积分兑好礼</div>
        <div class="text">
          活动期间参与刮奖，可额外获得积分奖励。<br />
          活动期间每日登录盒子可免费领取20积分（每个用户每日只能 领取一次）<br />
          累计参与刮奖3次，可免费领取50积分（每个用户只能领取一次）<br />
          累计参与刮奖5次，可免费领取100积分（每个用户只能领取一次）<br />
          累计参与刮奖8次，可免费领取200积分（每个用户只能领取一次
        </div>
      </div>
      <div class="container">
        <div class="big-text">4.积分兑好礼</div>
        <div class="text">
          活动期间可消耗积分兑换礼品。<br />
          消耗150积分可兑换88金币<span class="blue"
            >（每个用户可兑换<i class="orange">3</i>次）</span
          ><br />
          消耗300积分可兑换288金币<span class="blue"
            >（每个用户可兑换<i class="orange">3</i>次）</span
          ><br />
          消耗500积分可兑换488金币+3天SVIP<span class="blue"
            >（每个用户可兑换<i class="orange">1</i>次）</span
          ><br />
          消耗1000积分可兑换988金币+7天SVIP<span class="blue"
            >（每个用户可兑换<i class="orange">1</i>次）</span
          ><br />
          消耗3000积分可兑换3733金币<span class="blue"
            >（每个用户可兑换<i class="orange">2</i>次）</span
          ><br />
          消耗7000积分可兑换400平台币<span class="blue"
            >（每个用户可兑换<i class="orange">1</i>次）</span
          ><br />
          消耗20000积分可兑换1500平台币<span class="blue"
            >（每个用户可兑换<i class="orange">1</i>次）</span
          ><br />
          温馨提醒：请务必在活动期间内兑换心仪奖励，活动结束后将无法再
          进行兑换。
        </div>
      </div>
      <div class="container">
        <div class="big-text">5.充值平台币赢免单</div>
        <div class="text">
          活动期间，每日前3次充值平台币，即可参与刮奖。最高可赢充值金
          额等额金币奖励（<span class="orange">免单</span
          >）。金币奖励会自动发放至账号的金币钱包中， 请在【<span class="orange"
            >我的-金币</span
          >】中查看。每个用户每日仅能参与3次。
        </div>
      </div>
      <div class="container">
        <div class="big-text">6.活动规则</div>
        <div class="text">
          活动期间充值完成后请返回本活动页面点击【刷新】查看刮奖次数及
          时刮奖领取奖励，活动结束后将清空所有刮奖次数。<br />
          温馨提示：由于游戏厂商的要求，部分游戏内充值暂不支持使用金币
          抵扣，请先确定需要用金币充值的游戏在不在可使用名单内。<br />
          <span @click="game_dialog_show = true" class="orange btn"
            >查看名单></span
          >
        </div>
      </div>
    </div>
    <!-- 查看名单 -->
    <van-dialog
      v-model="game_dialog_show"
      :lock-scroll="false"
      :show-confirm-button="false"
      :close-on-click-overlay="true"
      class="no-gold-game-popup"
    >
      <div class="search-container">
        <div class="close-search" @click="game_dialog_show = false"></div>
        <div class="search-bar">
          <div class="input-text">
            <form @submit.prevent="searchGame">
              <input
                type="text"
                v-model.trim="input_game"
                placeholder="输入游戏名"
              />
            </form>
          </div>
          <div class="search-btn" @click="searchGame">搜索</div>
        </div>
        <yy-list
          class="yy-list game-list"
          v-model="loading_obj"
          :finished="finished"
          @refresh="onRefresh"
          @loadMore="loadMore"
          :empty="!game_list.length"
          tips="请输入您想搜索的游戏"
          :check="false"
        >
          <div
            class="game-item btn"
            v-for="item in game_list"
            :key="item.id"
            @click="toGame(item)"
          >
            <div class="game-icon">
              <img :src="item.titlepic" alt="" />
            </div>
            <div class="right">
              <div class="game-name">{{ item.title }}</div>
              <div class="use-card" :class="{ can: item.use_gold_pay == 1 }">
                {{ item.use_gold_pay == 1 ? '' : '不' }}支持使用金币支付
              </div>
            </div>
          </div>
        </yy-list>
      </div>
    </van-dialog>
  </div>
</template>
<script>
import { ApiGameCardSearchGame } from '@/api/views/recharge.js';
import { BOX_goToGame } from '@/utils/box.uni.js';

export default {
  name: 'Rule',
  data() {
    return {
      game_list: [], //查看的游戏名单
      loading_obj: {
        loading: false,
        reloading: false,
      }, //loading对象
      input_game: '', //search的input框
      page: 1, //查看名单的页码
      listRows: 20, //查看名单的大小
      empty: false, //查看名单的空
      game_dialog_show: false, //查看名单弹窗
      finished: false, //防抖
      navBgTransparent: true,
    };
  },
  mounted() {
    // 获取顶部导航栏的高度
    this.stickyOffsetTop =
      document.querySelector('.van-nav-bar').offsetHeight + 'px';
    window.addEventListener('scroll', this.handleScroll);
  },
  methods: {
    // 滚动处理
    handleScroll(e) {
      let scrollTop = e.target.scrollingElement.scrollTop;
      if (scrollTop > 100) {
        this.navBgTransparent = false;
      } else {
        this.navBgTransparent = true;
      }
    },
    // 搜索不可使用金币的游戏
    async searchGame() {
      if (!this.input_game) {
        this.$toast('请输入游戏名');
        return false;
      }
      this.$toast({
        type: 'loading',
        duration: 0,
        message: '拼命加载中...',
      });
      this.game_list = [];
      await this.getGameList();
    },
    async getGameList(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      let res = await ApiGameCardSearchGame({
        keyword: this.input_game,
        listRows: 20,
        page: this.page,
      });
      this.$toast.clear();
      let { list } = res.data;
      if (action === 1 || this.page === 1) {
        this.game_list = [];
      }
      if (!list.length) {
        this.empty = true;
      }
      this.game_list.push(...list);
      if (list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
    },
    async onRefresh() {
      try {
        await this.getGameList();
      } finally {
        this.loading_obj.reloading = false;
      }
    },
    async loadMore() {
      try {
        await this.getGameList(2);
      } finally {
        this.loading_obj.loading = false;
      }
    },
    toGame(item) {
      BOX_goToGame(
        {
          params: {
            id: item.id,
          },
        },
        { id: item.id },
      );
    },
  },
};
</script>
<style lang="less" scoped>
.shuangshier-rule {
  font-family: PingFang SC-Regular, PingFang SC;
  .image-bg('~@/assets/images/1212/1212_bg7.png');
  background-color: #510a6a;
  padding: 50 * @rem 22 * @rem 22 * @rem;
  overflow: hidden;
  .title {
    margin: 20 * @rem 0 15 * @rem;
    font-size: 16 * @rem;
    font-family: PingFang HK-Semibold, PingFang HK;
    font-weight: 600;
    color: #ffffff;
    span {
      background-image: url('~@/assets/images/1212/1212_bg6.png');
      background-size: 100%;
      background-repeat: no-repeat;
      background-position: 0 16 * @rem;
    }
  }
  .big-text {
    margin-bottom: 6 * @rem;
    font-size: 14 * @rem;
    font-family: PingFang SC-Semibold, PingFang SC;
    font-weight: 600;
    color: #ffffff;
    line-height: 21 * @rem;
  }
  .text {
    margin-bottom: 6 * @rem;
    font-size: 11 * @rem;
    color: #ffffff;
    line-height: 18 * @rem;
  }
  .orange {
    color: #ff5c00;
  }
  .yellow {
    color: #ffe500;
  }
  .blue {
    color: #05ffff;
  }
  .container {
    margin-top: 15 * @rem;
  }
  table {
    width: 100%;
    margin-top: 8 * @rem;
    color: #fff;
    tr {
      width: 100%;
      text-align: center;
      background: rgba(98, 28, 136, 0.6);
    }
    td,
    th {
      height: 30 * @rem;
      line-height: 30 * @rem;
      font-size: 14 * @rem;
      font-family: PingFang HK-Regular, PingFang HK;
      border: 1px solid #9160ac;
      &:nth-of-type(2n) {
        width: 40%;
      }
    }
    th {
      font-weight: 600;
    }
  }
}
.no-gold-game-popup {
  overflow: unset;
  width: 320 * @rem;
  .search-container {
    box-sizing: border-box;
    width: 320 * @rem;
    height: 450 * @rem;
    padding: 24 * @rem 19 * @rem 10 * @rem;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: unset;
    .close-search {
      width: 24 * @rem;
      height: 24 * @rem;
      background: url(~@/assets/images/close-search.png) center center no-repeat;
      background-size: 24 * @rem 24 * @rem;
      position: absolute;
      right: -10 * @rem;
      top: -10 * @rem;
    }
    .search-bar {
      display: flex;
      align-items: center;
      .input-text {
        width: 240 * @rem;
        height: 35 * @rem;
        border: 1 * @rem solid #e5e5e5;
        border-radius: 18 * @rem;
        flex: 1;
        overflow: hidden;
        form {
          display: block;
          width: 100%;
          height: 100%;
        }
        input {
          box-sizing: border-box;
          display: block;
          width: 100%;
          height: 100%;
          padding: 0 18 * @rem;
          font-size: 15 * @rem;
          color: #333333;
          background-color: #f6f6f6;
        }
      }
      .search-btn {
        font-size: 15 * @rem;
        color: #666666;
        padding-left: 13 * @rem;
        height: 35 * @rem;
        line-height: 35 * @rem;
      }
    }
    .game-list {
      flex: 1;
      overflow: auto;
      margin-top: 10 * @rem;
      .game-item {
        display: flex;
        align-items: center;
        padding: 21 * @rem 0;
        border-bottom: 1 * @rem solid #eeeeee;
        .game-icon {
          width: 50 * @rem;
          height: 50 * @rem;
          border-radius: 10 * @rem;
          background-color: #b5b5b5;
        }
        .right {
          flex: 1;
          min-width: 0;
          margin-left: 10 * @rem;
          .game-name {
            font-size: 16 * @rem;
            font-weight: bold;
            color: #000000;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
          }
          .use-card {
            font-size: 12 * @rem;
            color: #f72e2e;
            margin-top: 10 * @rem;
            &.can {
              color: #36b150;
            }
          }
        }
      }
    }
  }
}
</style>
