<template>
  <div class="activity-rule">
    <nav-bar-2
      ref="topNavBar"
      :bgStyle="navBgTransparent ? 'transparent-white' : 'white'"
      :azShow="true"
      :placeholder="false"
      title="活动规则"
    >
      <template #left>
        <div class="back" @click="back"></div>
      </template>
    </nav-bar-2>
    <div class="top-bg"></div>
    <div class="main">
      <div class="section">
        <div class="title"><span>活动介绍：</span></div>
        <div class="desc">
          告别了炎热喧闹的夏季，迎来了凉爽静谧的秋天。又到了欢送“神兽”回笼的日子，一起参与福利活动，领取开学大礼吧~
        </div>
        <div class="desc color">
          活动时间：2023/08/25 00:00 ~ 2023/08/27 23:59
        </div>
      </div>
      <div class="section">
        <div class="container">
          <div class="big-text">一、登录领奖励</div>
          <div class="text"> 活动期间，累计登录游戏满3天可领取 88金币。 </div>
        </div>
        <div class="container">
          <div class="big-text">二、瓜分千万金币红包</div>
          <div class="text">
            活动期间，每日前3次充值平台币，即可参与瓜分千万金币红包。每个
            用户每日仅能参与3次。 <br />
            金额随机，祝君好运满满！<br />
          </div>
        </div>
        <div class="container">
          <div class="big-text">三．充值获取开奖次数</div>
          <div class="text">
            活动期间游戏内每达到累计实付充值满100元、300元、500元、1000元，都可获得1次扭蛋的机会，开启必得开学小礼盒。<br />
            活动期间游戏内每达到累计实付充值满2000元、3000元、4000元、5000元，都可获得1次扭蛋的机会，开启必得开学大礼盒。<br />
            <span class="color"
              >温馨提示：仅限游戏内使用微信/支付宝充值，如有使用金币/平台币/代金券抵扣的，则只计算抵扣后的现金充值金额。</span
            >
          </div>
        </div>
        <div class="container">
          <div class="big-text">四、开学送大礼</div>
          <div class="text">
            通过游戏内实付充值可获取开启礼盒的机会，每种礼盒有四种奖励，每次开启随机获得其中一种，随后获得的奖励会从奖池中剔除掉，下次开启将从剩余的奖励中随机获得，以此类推，直至剩余最后一个奖励，即每种礼盒第四次开启必得最后一个奖励！<br />
            礼盒内容：<br />
            第一次开启：1888金币(70%)、2888金币(15%)、14天svip(10%)、288平台币(5%)<br />
            第二次开启：A(60%)、B(30%)、C(10%),(奖品价值从低到高分别为：A、B、C)<br />
            第三次开启：A(80%)、B(20%),(奖品价值从低到高分别为：A、B)<br />
            第四次开启：必得余下的奖品<br />
            开学大礼盒：<br />
            第一次开启：5888金币(70%)、30天SVIP(15%)、6888金币(10%)、588平台币(5%)<br />
            第二次开启：A(60%)、B(30%)、C(10%)，(奖品价值从低到高分别为：A、B、C)<br />
            第三次开启：A(80%)、B(20%),(奖品价值从低到高分别为：A、B)<br />
            第四次开启：必得余下的奖品
          </div>
        </div>
        <div class="container">
          <div class="big-text">五、活动说明：</div>
          <div class="text">
            1.活动期间充值完成后请返回本活动页面点击【刷新】领取奖励，请及时领取累计奖励，活动结束后将清空所有奖励领取机会。
          </div>
          <div class="text">
            <span class="color">2.温馨提示：</span
            >由于游戏厂商的要求，部分游戏内充值暂不支持使用金币支付，请先确定需要用金币充值的游戏在不在可使用名单内。
          </div>
          <div class="text">
            <span @click="game_dialog_show = true" class="color underline btn"
              >查看名单&gt;</span
            >
          </div>
        </div>
      </div>
    </div>
    <!-- 查看名单 -->
    <noGameList
      :game_dialog_show="game_dialog_show"
      @changeGameDialogShow="changeGameDialogShow"
    />
  </div>
</template>
<script>
import { ApiGameCardSearchGame } from '@/api/views/recharge.js';
import { BOX_goToGame, BOX_openInNewWindow } from '@/utils/box.uni.js';
import { envFun } from '@/utils/function';
import noGameList from '@/components/no-game-list';

export default {
  name: 'Rule',
  components: {
    noGameList,
  },
  data() {
    return {
      game_dialog_show: false, //查看名单弹窗
      navBgTransparent: true,
    };
  },
  mounted() {
    // 获取顶部导航栏的高度
    this.stickyOffsetTop =
      document.querySelector('.van-nav-bar').offsetHeight + 'px';
    window.addEventListener('scroll', this.handleScroll);
  },
  methods: {
    // 滚动处理
    handleScroll(e) {
      let scrollTop = e.target.scrollingElement.scrollTop;
      if (scrollTop > 100) {
        this.navBgTransparent = false;
      } else {
        this.navBgTransparent = true;
      }
    },
    changeGameDialogShow(show) {
      this.game_dialog_show = show;
    },
  },
};
</script>
<style lang="less" scoped>
.activity-rule {
  background-color: #f9a071;
  overflow: hidden;
  position: relative;

  .back {
    width: 30 * @rem;
    height: 30 * @rem;
    border-radius: 15 * @rem;
    background: rgba(0, 0, 0, 0.3) url(~@/assets/images/nav-bar-back-white.png)
      center center no-repeat;
    background-size: 8 * @rem 14 * @rem;
  }
  .top-bg {
    width: 100%;
    height: 142 * @rem;
    .image-bg('~@/assets/images/august-activity/rule-bg.png');
    background-size: 100% 142 * @rem;
  }
  .main {
    background-color: #fceed8;
    border-radius: 30 * @rem 30 * @rem 0 0;
    margin-top: -51 * @rem;
    padding: 22 * @rem 20 * @rem 35 * @rem;
  }
  .title {
    margin-bottom: 15 * @rem;
    span {
      background-image: url('~@/assets/images/july-activity/rule_title_bg.png');
      background-size: 100%;
      background-repeat: no-repeat;
      background-position: 0 16 * @rem;
      font-size: 16 * @rem;
      font-family: PingFang HK-Semibold, PingFang HK;
      font-weight: 600;
      color: #934028;
    }
  }
  .big-text {
    margin-bottom: 7 * @rem;
    font-size: 14 * @rem;
    font-family: PingFang SC-Semibold, PingFang SC;
    font-weight: 600;
    color: #934028;
    line-height: 18 * @rem;
  }
  .desc {
    margin-bottom: 6 * @rem;
    font-size: 12 * @rem;
    color: #934028;
    line-height: 18 * @rem;
  }
  .text {
    margin-bottom: 6 * @rem;
    font-size: 11 * @rem;
    color: #934028;
    line-height: 18 * @rem;
  }
  .color {
    color: #ff4009;
  }
  .underline {
    text-decoration: underline;
  }
  .container {
    margin-top: 21 * @rem;
  }
}
.no-gold-game-popup {
  overflow: unset;
  width: 320 * @rem;
  .search-container {
    box-sizing: border-box;
    width: 320 * @rem;
    height: 450 * @rem;
    padding: 24 * @rem 19 * @rem 10 * @rem;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: unset;
    .close-search {
      width: 24 * @rem;
      height: 24 * @rem;
      background: url(~@/assets/images/close-search.png) center center no-repeat;
      background-size: 24 * @rem 24 * @rem;
      position: absolute;
      right: -10 * @rem;
      top: -10 * @rem;
    }
    .search-bar {
      display: flex;
      align-items: center;
      .input-text {
        width: 240 * @rem;
        height: 35 * @rem;
        border: 1 * @rem solid #e5e5e5;
        border-radius: 18 * @rem;
        flex: 1;
        overflow: hidden;
        form {
          display: block;
          width: 100%;
          height: 100%;
        }
        input {
          box-sizing: border-box;
          display: block;
          width: 100%;
          height: 100%;
          padding: 0 18 * @rem;
          font-size: 15 * @rem;
          color: #333333;
          background-color: #f6f6f6;
        }
      }
      .search-btn {
        font-size: 15 * @rem;
        color: #666666;
        padding-left: 13 * @rem;
        height: 35 * @rem;
        line-height: 35 * @rem;
      }
    }
    .game-list {
      flex: 1;
      overflow: auto;
      margin-top: 10 * @rem;
      .game-item {
        display: flex;
        align-items: center;
        padding: 21 * @rem 0;
        border-bottom: 1 * @rem solid #eeeeee;
        .game-icon {
          width: 50 * @rem;
          height: 50 * @rem;
          border-radius: 10 * @rem;
          background-color: #b5b5b5;
        }
        .right {
          flex: 1;
          min-width: 0;
          margin-left: 10 * @rem;
          .game-name {
            font-size: 16 * @rem;
            font-weight: bold;
            color: #000000;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
          }
          .use-card {
            font-size: 12 * @rem;
            color: #f72e2e;
            margin-top: 10 * @rem;
            &.can {
              color: #36b150;
            }
          }
        }
      }
    }
  }
}
</style>
