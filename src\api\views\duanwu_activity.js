import { request } from '../index';

/**
 * 活动页端午活动
 */

export function ApiDWIndex(params = {}) {
  return request('/activity/dragon_boat/index', params);
}

export function ApiDWTaskTake(params = {}) {
  return request('/activity/dragon_boat/take', params);
}

export function ApiDWExchangeRank(params = {}) {
  return request('/activity/dragon_boat/campRank', params);
}

export function ApiDWJoinCamp(params = {}) {
  return request('/activity/dragon_boat/joinCamp', params);
}

export function ApiDWTaskReward(params = {}) {
  return request('/activity/dragon_boat/taskReward', params);
}

export function ApiDWRecordList(params = {}) {
  return request('/activity/dragon_boat/recordList', params);
}
