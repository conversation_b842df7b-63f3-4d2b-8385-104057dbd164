<template>
  <div class="spring-festival">
    <nav-bar-2
      ref="topNavBar"
      bgStyle="transparent-white"
      :azShow="true"
      :placeholder="false"
    ></nav-bar-2>
    <div class="bg1">
      <div class="user-container">
        <div v-if="userInfo.token" class="user">
          <div class="avatar">
            <UserAvatar />
          </div>
          <div class="nickname">{{ userInfo.nickname }}</div>
        </div>
        <div v-else class="no-login btn" @click="toLogin">未登录，立即登录</div>
      </div>
      <div :class="{ empty: activity_status !== 1 }" class="active-status">
        <div class="left">{{ active_status_text }}</div>
        <div class="right">2023/01/20 00:00 至 01/27 23:59</div>
      </div>
    </div>
    <div class="big-title">
      <div @click="toPage('SpringFestivalRule')" class="left btn">
        活动规则<i class="icon"></i>
      </div>
      <div @click="openRecordPopup" class="right btn">【兑奖记录】</div>
    </div>
    <section class="section1">
      <div class="item" v-for="(item, index) in index_list[1]" :key="index">
        <div class="left" :class="`icon${index + 1}`"></div>
        <div class="center">
          <div class="big-text">{{ item.title }}</div>
          <div class="small-text">限量500份</div>
          <div class="small-text">{{ item.desc }}</div>
        </div>
        <div
          class="right btn"
          :class="{ empty: !item.can_receive }"
          @click="takeReward(item.id)"
        >
          {{ item.is_receive ? '已领取' : '领取' }}
        </div>
      </div>
    </section>
    <div class="bg2"></div>
    <section class="section2">
      <div class="item" v-for="(item, index) in index_list[2]" :key="index">
        <div class="center">
          <div class="big-text">{{ item.title }}</div>
          <div class="small-text">{{ item.desc }}</div>
        </div>
        <div
          class="right btn"
          :class="{ empty: !item.can_receive }"
          @click="takeReward(item.id)"
        >
          {{ item.is_receive ? '已领取' : '领取' }}
        </div>
      </div>
    </section>
    <section class="section3">
      <div class="top-card">
        <div class="big-text">
          当前爆竹数：<span>{{ baozhu_count }}个</span
          ><span class="color2 btn" @click="refresh">【刷新】</span>
        </div>
        <div class="big-text">
          当前福卡数：<span>{{ fuka_count }}个</span>
        </div>
        <div class="small-text">
          福卡奖励：凭卡可在活动期间联系客服申请一张5折代金券（仅限BT游戏）
        </div>
        <div @click="toExchangePage" class="button btn">
          领取爆竹<i class="icon"></i>
        </div>
      </div>
      <div class="list">
        <div class="item" v-for="(item, index) in index_list[3]" :key="index">
          <div class="left" :class="`icon${index + 1}`"></div>
          <div class="center">
            <div class="big-text">{{ item.title }}</div>
            <div class="small-text">{{ item.desc }}</div>
          </div>
          <div
            class="right btn"
            :class="{ empty: !item.can_receive }"
            @click="takeReward(item.id)"
          >
            {{ item.is_receive ? '已领取' : '领取' }}
          </div>
        </div>
      </div>
    </section>
    <div class="section4" v-if="user_list.length">
      <div class="top" @click="tjfxTips"></div>
      <div class="bottom">
        <div class="white-wrapper"></div>
        <swiper :options="swiperOption" class="user-list">
          <!-- slides -->
          <swiper-slide
            v-for="(item, index) in user_list"
            :key="index"
            class="swiper-no-swiping"
          >
            <div class="swiper-item">
              恭喜<span>{{ item }}</span
              >成功触发天降福星，额外获得超级豪礼一份
            </div>
          </swiper-slide>
        </swiper>
      </div>
    </div>
    <!-- 兑奖记录 -->
    <van-popup
      v-model="record.popup"
      :lock-scroll="false"
      :show-confirm-button="false"
      :close-on-click-overlay="false"
      class="popup record-list-popup"
    >
      <div class="close" @click="record.popup = false"></div>
      <div class="title">兑奖记录</div>
      <div v-if="record.list.length > 0" class="list">
        <div v-for="(item, index) in record.list" :key="index" class="item">
          <div class="left">{{ item.title }}</div>
          <div v-html="item.info" class="right"></div>
        </div>
      </div>
      <div v-else class="empty">暂无兑奖记录</div>
    </van-popup>
  </div>
</template>
<script>
import UserAvatar from '@/components/user-avatar';
import {
  ApiChunJieIndex,
  ApiChunJieGetTjfxList,
  ApiChunJieBaozhuCount,
  ApiChunJieRecordExchange,
  ApiChunJieBaozhu,
  ApiChunJieExtraReward,
} from '@/api/views/spring_festival.js';
import { BOX_login } from '@/utils/box.uni.js';

export default {
  data() {
    return {
      activity_status: 2, //1活动已开始 2 活动不存在 3活动未开始 4活动已结束
      baozhu_count: 0, // 爆竹数
      fuka_count: 0, // 福卡数
      swiperOption: {
        observer: true,
        observeParents: true,
        noSwiping: true,
        direction: 'vertical',
        slidesPerView: 3,
        speed: 800,
        autoplay: {
          delay: 0,
        },
        loop: true,
        freeMode: true,
      },
      user_list: [], //天降福星名单
      index_list: [], // 活动列表
      record: {
        popup: false,
        list: [],
      }, //兑奖记录
    };
  },
  computed: {
    active_status_text() {
      const arr = ['活动已开始', '活动不存在', '活动未开始', '活动已结束'];
      return arr[this.activity_status - 1];
    },
  },
  async created() {
    await this.getIndexData();
    await this.getTjfxList();
  },
  methods: {
    async getIndexData() {
      const res = await ApiChunJieIndex();
      let { activity_status, baozhu_count, fuka_count, info } = res.data;

      this.index_list = info;
      this.activity_status = activity_status;
      this.baozhu_count = baozhu_count;
      this.fuka_count = fuka_count;
    },
    async getTjfxList() {
      try {
        const res = await ApiChunJieGetTjfxList();
        this.user_list = res.data.list;
      } catch (e) {}
    },
    async refresh() {
      try {
        const res = await ApiChunJieBaozhuCount();
        this.baozhu_count = res.data.baozhu_count;
      } catch (e) {}
    },
    async getRecords() {
      const res = await ApiChunJieRecordExchange();
      this.record.list = res.data.list;
    },
    openRecordPopup() {
      if (!this.checkActivityToast()) {
        return;
      }
      this.record.popup = true;
      this.getRecords();
    },
    async takeReward(id) {
      if (!this.checkActivityToast()) {
        return;
      }
      try {
        const res = await ApiChunJieExtraReward({ id });
        await this.getIndexData();
      } catch (e) {}
    },
    checkActivityToast() {
      if (!this.userInfo.token) {
        BOX_login();
        return false;
      }
      if (this.activity_status != 1) {
        this.$toast(this.active_status_text);
        return false;
      }
      return true;
    },
    toExchangePage() {
      if (!this.checkActivityToast()) {
        return;
      }
      this.toPage('SpringFestivalExchange');
    },
    toLogin() {
      BOX_login();
    },
    tjfxTips() {
      this.$toast(
        '开启以上红包均有机会触发祝福：天降福星。额外获得超级豪礼一份（包含18888金币+888平台币+福卡一张）。（每个用户仅能触发一次）',
      );
    },
  },
  components: {
    UserAvatar,
  },
};
</script>
<style lang="less" scoped>
.spring-festival {
  position: relative;
  min-height: 100vh;
  background: #ff523e;
  .bg1 {
    width: 100%;
    height: 258 * @rem;
    .image-bg('~@/assets/images/spring-festival/sf_bg1.png');
    .user-container {
      position: absolute;
      top: 183 * @rem;
      left: 20 * @rem;
      .user {
        display: flex;
        align-items: center;
        .avatar {
          width: 22 * @rem;
          height: 22 * @rem;
        }
        .nickname {
          color: #fff490;
          margin-left: 6 * @rem;
        }
      }
      .no-login {
        text-decoration: underline;
        color: #fff490;
      }
    }
    .active-status {
      position: absolute;
      top: 224 * @rem;
      left: 20 * @rem;
      width: 335 * @rem;
      height: 34 * @rem;
      line-height: 34 * @rem;
      display: flex;
      justify-content: space-between;
      box-sizing: border-box;
      padding: 0 18 * @rem;
      background: #da3e2b;
      border-radius: 8 * @rem;
      color: #fff8a9;
      &.empty {
        background: #da3e2b;
        color: #ff8d7f;
      }
      .left {
        font-size: 14 * @rem;
        font-weight: 600;
      }
      .right {
        font-size: 13 * @rem;
      }
    }
  }
  .big-title {
    margin: 18 * @rem 20 * @rem 50 * @rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 13 * @rem;
    color: #f2ffc4;
    .left {
      display: flex;
      align-items: center;
      .icon {
        display: block;
        width: 12 * @rem;
        height: 12 * @rem;
        margin-left: 3 * @rem;
        .image-bg('~@/assets/images/spring-festival/sf_icon13.png');
      }
    }
  }
  section {
    position: relative;
    width: 345 * @rem;
    margin: 0 auto;
    background: #feeacc;
    box-shadow: 0 * @rem 2 * @rem 6 * @rem 0 * @rem rgba(238, 64, 43, 0.3),
      inset 0 * @rem 0 * @rem 20 * @rem 0 * @rem rgba(255, 146, 0, 0.63);
    border-radius: 20 * @rem;
    border: 2 * @rem solid #fbeaa5;
    padding: 0 10 * @rem;
    box-sizing: border-box;
    &::before {
      content: '';
      display: block;
      position: absolute;
      top: -22 * @rem;
      left: 50%;
      transform: translate(-50%, 0);
      width: 140 * @rem;
      height: 36 * @rem;
      .image-bg('~@/assets/images/spring-festival/sf_title1.png');
    }
    &::after {
      content: '';
      display: block;
      position: absolute;
      top: -30 * @rem;
      right: 18 * @rem;
      width: 55 * @rem;
      height: 53.62 * @rem;
      .image-bg('~@/assets/images/spring-festival/sf_icon10.png');
    }
    .item {
      height: 82 * @rem;
      padding: 12 * @rem 0;
      border-bottom: 1 * @rem solid #eed8b6;
      display: flex;
      justify-content: space-between;
      align-items: center;
      &:last-of-type {
        border-bottom: none;
      }
      .left {
        flex: 0 0 60 * @rem;
        width: 60 * @rem;
        height: 60 * @rem;
        &.icon1 {
          .image-bg('~@/assets/images/spring-festival/sf_icon1.png');
        }
        &.icon2 {
          .image-bg('~@/assets/images/spring-festival/sf_icon9.png');
        }
        &.icon3 {
          .image-bg('~@/assets/images/spring-festival/sf_icon2.png');
        }
      }
      .center {
        flex: 1;
        min-width: 0;
        // white-space: nowrap;
        margin-left: 10 * @rem;
        height: 60 * @rem;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        .big-text {
          font-size: 13 * @rem;
          font-family: PingFangSC-Semibold, PingFang SC;
          font-weight: 600;
          color: #a6470e;
        }
        .small-text {
          font-size: 11 * @rem;
          color: #b17836;
        }
      }
      .right {
        // flex: 0 0 58 * @rem;
        width: 58 * @rem;
        height: 28 * @rem;
        background: linear-gradient(360deg, #ff6363 0%, #ff6e6e 100%);
        box-shadow: inset 0 * @rem 6 * @rem 10 * @rem 0 * @rem
            rgba(255, 233, 233, 0.71),
          inset 0 * @rem -4 * @rem 9 * @rem 0 * @rem rgba(255, 37, 0, 0.69);
        border-radius: 26 * @rem;
        // margin-top: 15 * @rem;
        font-size: 13 * @rem;
        color: #ffffff;
        text-align: center;
        line-height: 28 * @rem;
        &.empty {
          box-shadow: none;
          background: #dabd91;
        }
      }
    }
    &.section1 {
      padding-top: 10 * @rem;
      padding-bottom: 5 * @rem;
    }
    &.section2 {
      margin-top: 40 * @rem;
      &::before {
        .image-bg('~@/assets/images/spring-festival/sf_title3.png');
      }
      &::after {
        top: -25 * @rem;
        left: 15 * @rem;
        right: 0;
        width: 58 * @rem;
        height: 58 * @rem;
        .image-bg('~@/assets/images/spring-festival/sf_icon12.png');
      }
      .item {
        margin-top: 10 * @rem;
        height: 80 * @rem;
        .center {
          margin-right: 18 * @rem;
          justify-content: center;
          white-space: pre-wrap;
          .small-text {
            margin-top: 5 * @rem;
            line-height: 18 * @rem;
          }
        }
        .right {
          margin-top: 0;
        }
      }
    }
    &.section3 {
      margin-top: 60 * @rem;
      &::before {
        .image-bg('~@/assets/images/spring-festival/sf_title2.png');
      }
      &::after {
        width: 82 * @rem;
        height: 82 * @rem;
        top: -45 * @rem;
        right: 5 * @rem;
        .image-bg('~@/assets/images/spring-festival/sf_icon11.png');
      }
      .top-card {
        position: relative;
        width: 312 * @rem;
        height: 106 * @rem;
        box-sizing: border-box;
        padding: 12 * @rem 15 * @rem;
        margin-top: 38 * @rem;
        background: linear-gradient(180deg, #ffd28a 0%, #ffbf94 100%);
        border-radius: 14 * @rem;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        .big-text {
          font-size: 13 * @rem;
          font-weight: 600;
          color: #8f3912;
          span {
            font-size: 13 * @rem;
            font-weight: 600;
            color: #ff4233;
            &.color2 {
              margin-left: 5 * @rem;
              font-size: 12 * @rem;
              color: #954218;
            }
          }
        }
        .small-text {
          line-height: 18 * @rem;
          color: #ac3a23;
        }
        .button {
          position: absolute;
          top: 12 * @rem;
          right: 15 * @rem;
          width: 82 * @rem;
          height: 30 * @rem;
          display: flex;
          justify-content: center;
          align-items: center;
          color: #fff;
          .image-bg('~@/assets/images/spring-festival/sf_button1.png');
          .icon {
            display: block;
            width: 5 * @rem;
            height: 7 * @rem;
            margin-left: 3 * @rem;
            .image-bg('~@/assets/images/spring-festival/sf_icon14.png');
          }
        }
      }
      .item {
        .left {
          &.icon1 {
            .image-bg('~@/assets/images/spring-festival/sf_icon8.png');
          }
          &.icon2 {
            .image-bg('~@/assets/images/spring-festival/sf_icon3.png');
          }
          &.icon3 {
            .image-bg('~@/assets/images/spring-festival/sf_icon4.png');
          }
          &.icon4 {
            .image-bg('~@/assets/images/spring-festival/sf_icon5.png');
          }
          &.icon5 {
            .image-bg('~@/assets/images/spring-festival/sf_icon6.png');
          }
          &.icon6 {
            .image-bg('~@/assets/images/spring-festival/sf_icon7.png');
          }
        }
        .center {
          justify-content: center;
          .big-text {
            white-space: pre-wrap;
            line-height: 18 * @rem;
          }
          .small-text {
            margin-top: 5 * @rem;
            white-space: wrap;
          }
        }
      }
    }
  }
  .section4 {
    .top {
      width: 100%;
      height: 42 * @rem;
      margin-top: 18 * @rem;
      .image-bg('~@/assets/images/spring-festival/sf_bg3.png');
    }
    .bottom {
      background: #c33726;
      padding-bottom: 20 * @rem;
      position: relative;
    }
    .white-wrapper {
      position: absolute;
      top: 0;
      left: 0;
      z-index: 9999;
      width: 100%;
      height: 27 * @rem;
      background: linear-gradient(
        to bottom,
        rgba(195, 55, 38, 1),
        rgba(195, 55, 38, 0)
      );
    }
    .user-list {
      height: 114 * @rem;
      position: relative;
      /deep/ .swiper-wrapper {
        transition-timing-function: linear !important;
      }
      .swiper-item {
        text-align: center;
        color: #ffda6e;
        span {
          color: #fff;
        }
      }
    }
  }
  .bg2 {
    position: absolute;
    z-index: -1;
    top: 620 * @rem;
    left: 0;
    width: 100%;
    height: 302 * @rem;
    .image-bg('~@/assets/images/spring-festival/sf_bg2.png');
  }
}
.record-list-popup {
  width: 284 * @rem;
  height: 350 * @rem;
  background: #feeacc;
  border-radius: 20 * @rem;
  box-sizing: border-box;
  .title {
    font-size: 18 * @rem;
    color: #763006;
    text-align: center;

    line-height: 1;
    padding-top: 19 * @rem;
    padding-bottom: 9 * @rem;
  }
  .close {
    position: absolute;
    top: 12 * @rem;
    right: 12 * @rem;
    width: 20 * @rem;
    height: 20 * @rem;
    .image-bg('~@/assets/images/spring-festival/sf_icon17.png');
  }
  .list {
    height: 292 * @rem;
    overflow-y: scroll;
    padding: 0 18 * @rem;
    .item {
      display: flex;
      justify-content: space-between;
      height: 36 * @rem;
      border-bottom: 1px solid rgba(238, 216, 182, 1);
      align-items: center;
      font-size: 14 * @rem;
      color: rgba(130, 57, 14, 1);
      /deep/ span {
        color: rgba(255, 82, 62, 1);
      }
      &:last-of-type {
        border: none;
      }
    }
  }
  .empty {
    width: 100%;
    height: 292 * @rem;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #82390e;
  }
}
</style>
