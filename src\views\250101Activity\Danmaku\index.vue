<template>
  <div class="danmaku-page">
    <nav-bar-2
      ref="topNavBar"
      bgStyle="transparent-white"
      :azShow="true"
      :placeholder="false"
    >
      <template #left>
        <div class="back" @click="back"></div>
      </template>
    </nav-bar-2>

    <div class="main">
      <div class="activity-title"></div>
      <div class="danmaku-content">
        <DanmakuItem
          v-if="isShow"
          ref="danmaku"
          class="demo"
          v-model="danmus"
          isSuspend
          v-bind="config"
        >
          <!-- 容器slot -->
          <div></div>
          <!-- 弹幕slot -->
          <template v-slot:dm="{ index, danmu }">
            <div
              class="danmu-item"
              :class="{ 'btn-item-me': danmu.id == danmuId }"
            >
              <img
                v-if="danmu.user?.avatar"
                class="img"
                :src="danmu.user?.avatar"
              />
              <img v-else class="img" :src="defaultAvatar" />
              <span>{{ danmu.content }}</span>
              <div class="btn-box">
                <div
                  @click="handleClickDm(index, danmu)"
                  class="thumbs-up"
                  :class="{ 'thumbs-up-active': danmu.click_status }"
                ></div>
                <div
                  class="num"
                  v-show="danmu.click_count >= 0"
                  :class="{ 'num-active': danmu.click_status }"
                >
                  {{ danmu.click_count }}
                </div>
              </div>
            </div>
          </template>
        </DanmakuItem>
      </div>
      <div class="danmaku-bg"></div>

      <div class="danmaku-input">
        <div class="user-info">
          <div class="avatar">
            <user-avatar></user-avatar>
          </div>
          <div class="nickname">{{ userInfo.nickname }}</div>
        </div>
        <div class="danmaku-text">
          <van-field
            class="danmaku-field"
            ref="scrollwarp"
            v-model.trim="danmuMsg"
            autosize
            rows="5"
            type="textarea"
            maxlength="50"
            placeholder="请留下你的新年愿望"
            @focus="handleFocus"
          />
        </div>
        <!-- <div class="danmaku-text1">
          <div class="textarea-box">
            <textarea
              v-model.trim="danmuMsg"
              class="textarea-reset"
              placeholder="请留下你的新年愿望"
              maxlength="50"
              rows="5"
            />
          </div>
        </div> -->
      </div>
      <div class="bottom-container">
        <div class="bottom-fixed">
          <div class="bottom-btn" @click="handleAddDanmu">
            <div class="bottom-bg-icon"></div>
            <div class="operation-btn btn">放飞心愿</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
// import { danmus, getDanmuData } from "./danmu.js";
import DanmakuItem from '../../../components/danmaku-item';
import { platform, boxInit, BOX_login } from '@/utils/box.uni.js';
import {
  ApiDoingsYuandanMessage,
  ApiDoingsYuandanThumb,
  ApiDoingsYuandanMessageList,
} from '@/api/views/250101';
export default {
  components: { DanmakuItem },
  data() {
    return {
      // danmus: getDanmuData(),
      danmus: [],
      config: {
        channels: 5, // 轨道数量，为0则弹幕轨道数会撑满容器
        useSlot: true, // 是否开启slot
        loop: true, // 是否开启弹幕循环
        speeds: 30, // 弹幕速度，实际为每秒弹幕走过的像素距离
        fontSize: 20, // 文本模式下的字号
        top: 30, // 弹幕轨道间的垂直间距
        right: 0, // 同一轨道弹幕的水平间距
        debounce: 100, // 弹幕刷新频率（多少毫秒插入一条弹幕，建议不小于50）
        randomChannel: true,
      },
      danmuMsg: '',
      danmuId: '',
      isShow: false,
      oldHeight: '',
    };
  },
  created() {
    this.oldHeight = document.documentElement.clientHeight;
  },
  mounted() {
    // 官包触发页面刷新（将onResume挂载在window上）
    if (platform == 'android') {
      window.onResume = this.onResume;
    }
    window.onresize = () => this.resizeHandler();
  },
  beforeDestroy() {
    window.onresize = null;
  },
  async activated() {
    await this.getDoingsYuandanMessageList();
    this.isShow = true;
  },
  deactivated() {
    this.isShow = false;
    window.onresize = null;
  },
  methods: {
    handleFocus() {
      this.$nextTick(() => {
        setTimeout(() => {
          const danmakuInput = document.querySelector('.danmaku-input');
          if (danmakuInput) {
            danmakuInput.scrollIntoView({
              // top: danmakuInput.offsetTop,
              // behavior: "smooth",
              block: 'center',
            });
          }
        }, 200);
      });
    },
    login() {
      BOX_login();
    },
    async getDoingsYuandanMessageList() {
      const res = await ApiDoingsYuandanMessageList();
      this.danmus = res.data;
    },
    async onResume() {
      await boxInit();
      await this.SET_USER_INFO(true);
      await this.getDoingsYuandanMessageList();
    },
    async postYuandanMessage() {
      const res = await ApiDoingsYuandanMessage({
        content: this.danmuMsg,
      });
      const danmuMsg = this.config.useSlot ? res.data : this.danmuMsg;
      this.danmuId = res.data.id;
      this.$refs.danmaku.add(danmuMsg);
      this.danmuMsg = '';
    },
    async handleClickDm(index, dm) {
      if (!this.userInfo.token) {
        this.$toast('请登录');
        this.login();
        return false;
      }

      await ApiDoingsYuandanThumb({
        id: dm.id,
        action: !dm.click_status ? 'yes' : 'no',
      });
      dm.click_status = !dm.click_status;
      dm.click_count = dm.click_status
        ? dm.click_count + 1
        : dm.click_count - 1;
      // console.log("当前点击的弹幕:>> ", index, dm);
    },
    resizeHandler() {
      if (this.timer) clearTimeout(this.timer);
      this.timer = setTimeout(() => {
        this.$refs.danmaku.resize();
      });
      if (this.oldHeight) {
        this.HeightChange =
          document.documentElement.clientHeight === this.oldHeight;
        if (!this.HeightChange) {
          this.$refs.scrollwarp.scrollTop =
            this.$refs.scrollwarp.scrollHeight -
            this.$refs.scrollwarp.clientHeight;
        }
      }
    },
    async handleAddDanmu() {
      if (!this.userInfo.token) {
        this.$toast('请登录');
        this.login();
        return false;
      }
      if (!this.danmuMsg) return;
      await this.postYuandanMessage();
    },
  },
};
</script>

<style lang="less" scoped>
.danmaku-page {
  font-family: PingFang SC, PingFang SC;
  display: flex;
  flex-direction: column;
  width: 100%;
  overflow: hidden;
  min-height: 100vh;
  background: #ffcfba;
  .back {
    width: 30 * @rem;
    height: 30 * @rem;
    border-radius: 15 * @rem;
    background: rgba(0, 0, 0, 0.3) url(~@/assets/images/nav-bar-back-white.png)
      center center no-repeat;
    background-size: 8 * @rem 14 * @rem;
  }
  .main {
    width: 100%;
    position: relative;
    flex: 1;
    min-height: 0;
    background: url(~@/assets/images/250101/250101_bg1.png) center top no-repeat;
    background-size: 100% 227 * @rem;
    width: 100%;
    // height: 223 * @rem;
    .activity-title {
      margin: 49 * @rem auto 0;
      background: url(~@/assets/images/250101/lottery-title1.png) center top
        no-repeat;
      background-size: 288 * @rem 62 * @rem;
      width: 288 * @rem;
      height: 62 * @rem;
    }
    .danmaku-content {
      position: relative;
      min-height: 300 * @rem;
      height: 300 * @rem;
      padding: 35 * @rem 0;
      z-index: 9;
      .demo {
        width: 100%;
        height: 100%;
        z-index: 0;
        .danmu-item {
          width: auto;
          height: 32 * @rem;
          line-height: 32 * @rem;
          padding: 0 3 * @rem;
          box-sizing: border-box;
          display: flex;
          align-items: center;
          background: rgba(255, 255, 255, 0.3);
          border-radius: 22 * @rem;
          border: 1px solid rgba(255, 255, 255, 0.28);
          .img {
            height: 26 * @rem;
            width: 26 * @rem;
            border-radius: 50%;
            margin-right: 6 * @rem;
          }
          span {
            font-weight: 500;
            flex: 1;
            // height: 18 * @rem;
            // line-height: 18 * @rem;
            font-size: 13 * @rem;
            color: #c56639;
            display: flex;
            align-items: center;
          }
          .btn-box {
            flex: 1;
            height: 100%;
            display: flex;
            align-items: center;
            .thumbs-up {
              margin-left: 6 * @rem;
              background: url('~@/assets/images/250101/like_icon.png') center
                center no-repeat;
              background-size: 16 * @rem 16 * @rem;
              width: 16 * @rem;
              height: 16 * @rem;
              &.thumbs-up-active {
                background: url('~@/assets/images/250101/like_active_icon.png')
                  center center no-repeat;
                background-size: 16 * @rem 16 * @rem;
                width: 16 * @rem;
                height: 16 * @rem;
              }
            }
            .num {
              margin-left: 2 * @rem;
              // height: 16 * @rem;
              // line-height: 16 * @rem;
              vertical-align: middle;
              font-weight: 500;
              font-size: 13 * @rem;
              color: #c56639;
              &.num-active {
                color: #f43c27;
              }
            }
          }

          &.btn-item-me {
            border: 1px solid #ff9981;
            background: rgba(255, 255, 255, 0.3);
          }
        }
      }
    }
    .danmaku-bg {
      position: absolute;
      top: 73 * @rem;
      background: url(~@/assets/images/250101/lottery-bg1.png) no-repeat -10 * @rem -25 *
        @rem;
      background-size: 100% 545 * @rem;
      width: 100%;
      height: 545 * @rem;
      z-index: 1;
    }

    .main1 {
      position: absolute;
      z-index: 1;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      .intro {
        display: inline-block;
        color: #fff;
        text-align: center;
        text-shadow: 2 * @rem 4 * @rem 6 * @rem rgba(0, 0, 0, 0.4);
        h1 {
          font-size: 48 * @rem;
          line-height: 32 * @rem;
        }
      }
      .action {
        margin-top: 20 * @rem;
        color: #fff;
        min-width: 360 * @rem;
        .btn {
          color: #000;
          background: #fff;
          border: none;
          padding: 6 * @rem 16 * @rem;
          margin-right: 8 * @rem;
          border-radius: 5 * @rem;
          min-height: 31 * @rem;
          outline: none;
          cursor: pointer;
          transition: all 0.3s;
          &:hover {
            background-color: #f3f7fa;
          }
          &:active {
            background-color: #fff;
          }
        }
        .ipt {
          width: 130 * @rem;
          padding: 8 * @rem 16 * @rem;
          border-radius: 5 * @rem;
          outline: none;
          border: none;
          margin-right: 8 * @rem;
        }
      }
    }

    .danmaku-input {
      position: relative;
      padding: 0 27 * @rem;
      box-sizing: border-box;
      z-index: 999;
      .user-info {
        display: flex;
        align-items: center;
        margin-left: 3 * @rem;
        .avatar {
          width: 36 * @rem;
          height: 36 * @rem;
          border-radius: 50%;
          overflow: hidden;
          flex-shrink: 0;
        }
        .nickname {
          margin-left: 5 * @rem;
          font-weight: 500;
          font-size: 15 * @rem;
          font-weight: bold;
          color: #c56639;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
      .danmaku-text {
        width: 100%;
        margin-top: 10 * @rem;
        .danmaku-field {
          width: 100%;
          height: 149 * @rem;
          background: rgba(255, 252, 242, 0.6);
          border-radius: 12 * @rem;
        }
        /deep/.van-field__control::placeholder {
          font-weight: 400;
          font-size: 14 * @rem;
          color: rgba(197, 102, 57, 0.52);
        }
      }
      .danmaku-text1 {
        width: 100%;
        margin-top: 10 * @rem;
        .textarea-box {
          width: 100%;
          padding: 10 * @rem;
          box-sizing: border-box;
          height: 149 * @rem;
          background: rgba(255, 252, 242, 0.6);
          border-radius: 12 * @rem;
          .textarea-reset {
            width: 300 * @rem;
            background: transparent;
            display: flex;
            flex-wrap: wrap;
            &::placeholder {
              font-weight: 400;
              font-size: 14 * @rem;
              color: rgba(197, 102, 57, 0.52);
            }
          }
          textarea {
            font-family: PingFang SC, PingFang SC;
            all: unset;
            width: 100%;
            height: auto;
            background: transparent;
            border: none;
            padding: 0;
            font-size: 14 * @rem;
            line-height: 1.5;
            resize: none;
            box-sizing: border-box;
            overflow-y: auto;
          }
        }
      }
    }
    .bottom-container {
      flex-shrink: 0;
      width: 100%;
      // height: calc(70 * @rem + @safeAreaBottom);
      // height: calc(70 * @rem + @safeAreaBottomEnv);
      margin-top: 45 * @rem;
      margin-bottom: 60 * @rem;
      .bottom-fixed {
        box-sizing: border-box;
        // position: fixed;
        // bottom: 60 * @rem;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        .bottom-btn {
          position: relative;
          .bottom-bg-icon {
            position: absolute;
            z-index: 9;
            left: -19 * @rem;
            top: -35 * @rem;
            background: url('~@/assets/images/250101/danmaku_dl_icon.png')
              no-repeat -5 * @rem -5 * @rem;
            background-size: 100 * @rem 118 * @rem;
            width: 70 * @rem;
            height: 102 * @rem;
          }
          .operation-btn {
            width: 238 * @rem;
            height: 40 * @rem;
            border-radius: 20 * @rem;
            background: linear-gradient(221deg, #fd9083 0%, #ff495c 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 15 * @rem;
            font-weight: 500;
            color: #ffffff;
            background-color: #e1e1e1;
          }
        }
      }
    }
  }
  .github-corner:hover .octo-arm {
    animation: octocat-wave 560ms ease-in-out;
  }

  @keyframes octocat-wave {
    0%,
    100% {
      transform: rotate(0);
    }
    20%,
    60% {
      transform: rotate(-25deg);
    }
    40%,
    80% {
      transform: rotate(10deg);
    }
  }

  @media (max-width: 500px) {
    .github-corner:hover .octo-arm {
      animation: none;
    }
    .github-corner .octo-arm {
      animation: octocat-wave 560ms ease-in-out;
    }
  }
}</style
>>
