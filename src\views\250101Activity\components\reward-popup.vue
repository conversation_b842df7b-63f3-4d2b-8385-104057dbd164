<template>
  <div>
    <van-popup v-model="popup" class="reward-popup" :lock-scroll="false">
      <div class="title-container">
        <img
          class="title-pic"
          src="@/assets/images/250101/reward-popup-icon.png"
          alt=""
        />
        <div class="title-text">
          {{ info.task_id >= 9 ? '任务已全部完成！' : '任务已完成' }}
        </div>
      </div>
      <div class="reward-content">
        {{ info.msg }}
      </div>
      <!-- info.desc表示未在活动中开通会员 -->
      <div class="reward-btn" v-if="info.desc" @click="goToRecharge">
        {{ info.desc }}
        <span v-if="info.reward">{{ info.reward }}</span>
      </div>
      <div class="reward-btn" v-else @click="goToContinue">
        {{ info.btn_text || (info.task_id >= 9 ? '马上抽奖' : '继续闯关') }}
      </div>
      <div class="reward-btn-extra" v-if="info.desc" @click="goToContinue">
        {{ info.btn_text || (info.task_id >= 9 ? '马上抽奖' : '继续闯关') }}
      </div>
    </van-popup>
  </div>
</template>

<script>
export default {
  name: 'rulePopup',
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    info: {
      type: Object,
      default: () => {},
      required: true,
    },
  },
  data() {
    return {};
  },
  computed: {
    popup: {
      get() {
        return this.show;
      },
      set(value) {
        this.$emit('update:show', value);
      },
    },
  },
  methods: {
    closePopup() {
      this.popup = false;
    },
    goToRecharge() {
      this.closePopup();
      this.$nextTick(() => {
        const oB = document.querySelector('.svip-container');
        window.scrollTo({
          top: oB.scrollHeight,
          behavior: 'smooth',
        });
      });
    },
    goToContinue() {
      this.closePopup();
      this.$nextTick(() => {
        if (this.info.task_id >= 9) {
          this.toPage('250101ActivityNewYearLottery');
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
.reward-popup {
  box-sizing: border-box;
  padding: 20 * @rem 0 0;
  border-radius: 20 * @rem;
  width: 300 * @rem;
  overflow: visible;
  margin-top: -20 * @rem;
  padding-bottom: 30 * @rem;
  background: #fff url('~@/assets/images/250101/reward-popup-bg.png') center top
    no-repeat;
  background-size: 300 * @rem 130 * @rem;
  .title-container {
    position: relative;
    margin-top: -69 * @rem;
    .title-pic {
      width: 164 * @rem;
      height: 124 * @rem;
      margin: 0 auto;
    }
    .title-text {
      font-size: 24 * @rem;
      line-height: 29 * @rem;
      text-align: center;
      font-weight: bold;
      background: -webkit-linear-gradient(90deg, #ff691d 0%, #c03a00 100%);
      background: linear-gradient(90deg, #ff691d 0%, #c03a00 100%);
      -webkit-background-clip: text; /* Chrome, Safari */
      background-clip: text;
      -webkit-text-fill-color: transparent; /* Chrome, Safari */
      color: transparent; /* 兼容不支持background-clip的浏览器 */
      margin-top: 4 * @rem;
    }
  }
  .reward-content {
    padding: 0 22 * @rem;
    line-height: 20 * @rem;
    margin-top: 12 * @rem;
    font-size: 15 * @rem;
    color: rgba(197, 102, 57, 1);
    text-align: center;
  }
  .reward-btn {
    margin: 25 * @rem auto 0;
    width: 238 * @rem;
    height: 40 * @rem;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 20 * @rem;
    font-size: 15 * @rem;
    color: #ffffff;
    background: linear-gradient(221deg, #fd9083 0%, #ff495c 100%);
    position: relative;
    span {
      box-sizing: border-box;
      display: block;
      font-size: 10 * @rem;
      color: rgba(253, 140, 129, 1);
      height: 17 * @rem;
      padding: 0 5 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 10 * @rem 10 * @rem 10 * @rem 0;
      border: 1px solid rgba(253, 140, 129, 1);
      position: absolute;
      right: -14 * @rem;
      top: -10 * @rem;
      background: #fff;
    }
  }
  .reward-btn-extra {
    font-size: 15 * @rem;
    color: rgba(144, 45, 23, 1);
    line-height: 21 * @rem;
    margin: 20 * @rem auto 0;
    text-align: center;
    margin-bottom: -10 * @rem;
    font-weight: 500;
  }
}
</style>
