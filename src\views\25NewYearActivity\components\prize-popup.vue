<template>
  <div>
    <van-popup
      v-model="popup"
      position="bottom"
      class="prize-popup"
      :lock-scroll="false"
    >
      <div class="title-container">
        <div class="top-title">
          <div class="title">我的奖品</div>
        </div>
        <div class="close-btn" @click="closePopup"></div>
      </div>
      <div class="prize-container">
        <content-empty
          style="height: 80%"
          v-if="!prizeInfo.length"
          tips="暂无数据"
        ></content-empty>
        <template v-else>
          <div class="prize-item" v-for="item in prizeInfo" :key="item.id">
            <div class="top-content">
              <div class="prize-info">
                <div class="prize-img">
                  <img
                    :src="item.icon"
                    :class="{
                      jdk: item.type == 2,
                      jb_ptb: item.type == 0 || item.type == 1,
                      hbfm: item.type == 3,
                      djq: item.type == 4,
                      sj: item.type == 5,
                    }"
                    alt=""
                  />
                </div>
                <div class="prize-name">
                  <div class="title">{{ item.desc }}</div>
                  <div class="text" v-if="[0, 1].includes(item.type)">
                    奖励已为您自动存入账户
                  </div>
                  <div class="text" v-if="item.type == 5 && item.status == 0">
                    待填写
                  </div>
                  <div class="text" v-if="item.type == 4 && item.status == 0">
                    待使用
                  </div>
                  <div class="text" v-if="item.type == 5 && item.status == 1">
                    已填写
                  </div>
                  <div class="text" v-if="item.type == 4 && item.status == 1">
                    已使用
                  </div>
                  <div
                    class="text"
                    v-if="[4, 5].includes(item.type) && item.status == 2"
                  >
                    已过期
                  </div>
                </div>
              </div>
              <div
                class="prize-btn"
                v-if="item.type == 2"
                @click="goToService()"
              >
                联系客服
              </div>
              <div
                class="prize-btn"
                v-if="item.type == 3 && item.status !== 1 && item.status !== 2"
                @click="viewUseFunc()"
              >
                查看使用方法
              </div>
              <div
                class="prize-btn"
                v-if="item.type == 5 && item.status == 0"
                @click="goToAddAddress()"
              >
                去填收货地址
              </div>
              <div
                class="prize-btn"
                v-if="item.type == 5 && item.status == 1"
                @click="goToAddAddress()"
              >
                查看收货地址
              </div>
            </div>
            <div class="tips" v-if="item.tip == 0">迎新接福</div>
            <div class="tips" v-else-if="item.tip == 1">妙笔贺岁</div>
            <div class="tips" v-else-if="item.tip == 2">入会见喜</div>
            <div class="bottom-content" v-if="item.msg">
              <div class="prize-msg" v-html="item.msg"></div>
            </div>
          </div>
        </template>
      </div>
    </van-popup>
  </div>
</template>

<script>
import {
  platform,
  boxInit,
  BOX_showActivity,
  BOX_openInNewWindow,
  BOX_login,
  iframeCopy,
} from '@/utils/box.uni.js';
import { envFun } from '@/utils/function';
import { LOCAL_HOST } from '@/utils/constants.js';
import { ApiDoingsChunjieTaskLog } from '@/api/views/25_new_year_activity.js';
export default {
  name: 'prizePopup',
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    is_task: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      // prizeInfo: [
      //   {
      //     id: 1,
      //     icon: "",
      //     title: "100元京东卡",
      //     status: 0, //0待填写||待使用 1已填写||已使用 2已过期
      //     msg: " 请在 2025年2月14日 24点前联系客服登记，届时获取卡密 *过期未联系客服者视为自动放弃~",
      //     tips: 0, //0迎新接福 1妙笔贺岁 2入会见喜
      //     type: 2, //0金币 1平台币 2京东卡 3红包封面 4代金券 5红米手机
      //   },
      //   {
      //     id: 2,
      //     icon: "",
      //     title: "红米K80 12G+256G手机",
      //     status: 1,
      //     msg: "请在 2025年2月14日 24点前完成收货地址填写",
      //     tips: 0,
      //     type: 5,
      //   },
      //   {
      //     id: 3,
      //     icon: "",
      //     title: "588金币",
      //     status: 0,
      //     msg: "",
      //     tips: 0,
      //     type: 1,
      //   },
      //   {
      //     id: 4,
      //     icon: "",
      //     title: "3733定制微信红包封面",
      //     status: 0,
      //     msg: "请在 2025年1月1日 前使用",
      //     tips: 1,
      //     type: 3,
      //   },
      //   {
      //     id: 5,
      //     icon: "",
      //     title: "满10-6代金券",
      //     status: 2,
      //     msg: "请在 2025年2月28日 24点前使用",
      //     tips: 2,
      //     type: 4,
      //   },
      // ],
      prizeInfo: [],
    };
  },
  computed: {
    popup: {
      get() {
        return this.show;
      },
      set(value) {
        this.$emit('update:show', value);
      },
    },
  },
  watch: {
    async popup(val) {
      if (val) {
        await this.getPrizeInfo();
      }
    },
  },
  methods: {
    closePopup() {
      this.popup = false;
    },
    async getPrizeInfo() {
      const res = await ApiDoingsChunjieTaskLog();
      this.prizeInfo = res.data;
    },
    goToService() {
      this.closePopup();
      // if (this.is_task) {
      //   let url = "";
      //   if (process.env.NODE_ENV === "development") {
      //     url = `${LOCAL_HOST}/#/25_new_year_activity/couplets`;
      //   } else {
      //     url = `https://${envFun()}activity.3733.com/#/25_new_year_activity/couplets`;
      //   }
      //   parent.postMessage(
      //     {
      //       from: "activity",
      //       type: "selectUrl",
      //       data: { url },
      //     },
      //     "*"
      //   );
      // }
      this.$nextTick(() => {
        if (platform == 'android') {
          BOX_openInNewWindow(
            { name: 'Kefu' },
            { url: `https://${envFun()}game.3733.com/#/kefu` },
          );
        } else {
          BOX_openInNewWindow(
            { name: 'KefuChat' },
            { url: `https://${envFun()}game.3733.com/#/kefu_chat` },
          );
        }
      });
    },
    viewUseFunc() {
      this.closePopup();
      this.$nextTick(() => {
        this.toPage('25NewYearActivityHbCoverStep');
      });
    },
    goToAddAddress() {
      this.closePopup();
      this.$nextTick(() => {
        this.toPage('25NewYearActivityFillInAddress');
      });
    },
  },
};
</script>

<style lang="less" scoped>
.prize-popup {
  box-sizing: border-box;
  padding: 20 * @rem 0 0;
  border-radius: 20 * @rem 20 * @rem 0 0;
  height: 568 * @rem;
  display: flex;
  flex-direction: column;
  background: #ffffff;
  .title-container {
    position: relative;
    height: 24 * @rem;
    .title-pic {
      width: 268 * @rem;
      height: 24 * @rem;
      margin: 0 auto;
    }
    .top-title {
      display: flex;
      align-items: center;
      justify-content: center;
      .title {
        position: relative;
        height: 23 * @rem;
        font-weight: 600;
        font-size: 18 * @rem;
        color: #5a2a2a;
        line-height: 23 * @rem;
        text-align: center;
        &::before {
          content: '';
          position: absolute;
          left: -38 * @rem;
          top: 50%;
          transform: translateY(-50%);
          width: 32 * @rem;
          height: 32 * @rem;
          background: url(~@/assets/images/25newyear/bt_left.png) no-repeat;
          background-size: 32 * @rem 32 * @rem;
        }
        &::after {
          content: '';
          position: absolute;
          right: -38 * @rem;
          top: 50%;
          transform: translateY(-50%);
          width: 32 * @rem;
          height: 32 * @rem;
          background: url(~@/assets/images/25newyear/bt_right.png) no-repeat;
          background-size: 32 * @rem 32 * @rem;
        }
      }
    }
    .close-btn {
      width: 13 * @rem;
      height: 13 * @rem;
      background: url('~@/assets/images/25newyear/btn-close.png') center top
        no-repeat;
      background-size: 13 * @rem 13 * @rem;
      position: absolute;
      top: -4 * @rem;
      right: 16 * @rem;
    }
  }
  .prize-container {
    box-sizing: border-box;
    flex: 1;
    overflow-y: auto;
    color: rgba(197, 102, 57, 1);
    margin-top: 11 * @rem;
    padding: 10 * @rem 20 * @rem 20 * @rem;
    .prize-item {
      position: relative;
      border-radius: 10 * @rem;
      border: 0.5 * @rem solid #f1f0ef;
      &:not(:last-child) {
        margin-bottom: 12 * @rem;
      }
      .top-content {
        padding: 12 * @rem;
        height: 72 * @rem;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .prize-info {
          display: flex;
          align-items: center;
          .prize-img {
            width: 48 * @rem;
            height: 48 * @rem;
            background: #f8f7f7;
            border-radius: 6 * @rem;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            .jdk {
              width: 36 * @rem;
              height: 23 * @rem;
            }
            .sj {
              width: 29 * @rem;
              height: 36 * @rem;
            }
            .jb_ptb {
              width: 34 * @rem;
              height: 34 * @rem;
            }
            .hbfm {
              width: 22 * @rem;
              height: 36 * @rem;
            }
            .djq {
              width: 36 * @rem;
              height: 24 * @rem;
            }
          }
          .prize-name {
            margin-left: 8 * @rem;
            width: 177 * @rem;
            flex-shrink: 0;
            min-width: 0;
            .title {
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              height: 18 * @rem;
              font-weight: 600;
              font-size: 14 * @rem;
              color: #5a2a2a;
              line-height: 18 * @rem;
              text-align: left;
            }
            .text {
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              margin-top: 8 * @rem;
              font-weight: 400;
              font-size: 12 * @rem;
              color: #a17272;
            }
          }
        }
        .prize-btn {
          position: absolute;
          top: 36 * @rem;
          right: 12 * @rem;
          height: 24 * @rem;
          text-align: center;
          padding: 4 * @rem 11 * @rem;
          box-sizing: border-box;
          border-radius: 24 * @rem;
          border: 0.5 * @rem solid #f0bcbc;
          font-weight: 400;
          font-size: 12 * @rem;
          color: #e75555;
        }
      }
      .tips {
        position: absolute;
        top: 0;
        right: 0;
        width: 58 * @rem;
        height: 20 * @rem;
        line-height: 20 * @rem;
        background: #fff0f0;
        border-radius: 0 10 * @rem 0 10 * @rem;
        font-weight: 400;
        font-size: 11 * @rem;
        color: #a17272;
        text-align: center;
      }
      .bottom-content {
        background: #f8f7f7;
        padding: 9 * @rem 12 * @rem;
        .prize-msg {
          color: #7a5252;
          font-size: 12 * @rem;
          font-weight: 400;
          line-height: 18 * @rem;
        }
      }
    }
  }
}
</style>
