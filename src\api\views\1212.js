import { request } from '../index';

/**
 * 双十二 - 双十二首页
 */
export function Api1212Index(params = {}) {
  return request('/activity/double_twelfth/index', params);
}

/**
 * 双十二 - 兑换明细
 */
export function Api1212RecordExchange(params = {}) {
  return request('/activity/double_twelfth/recordExchange', params);
}

/**
 * 双十二 - 积分抽奖
 */
export function Api1212Lottery(params = {}) {
  return request('/activity/double_twelfth/lottery', params);
}

/**
 * 双十二 - 双12积分兑换信息
 */
export function Api1212PointsRedemptionInfo(params = {}) {
  return request('/activity/double_twelfth/pointsRedemptionInfo', params);
}

/**
 * 双十二 - 领取额外积分奖励
 */
export function Api1212ExtraReward(params = {}) {
  return request('/activity/double_twelfth/extraReward', params);
}

/**
 * 双十二 - 刮奖
 */
export function Api1212Scratch(params = {}) {
  return request('/activity/double_twelfth/scratch', params);
}

/**
 * 双十二 - 双12积分兑换
 */
export function Api1212PointsRedemption(params = {}) {
  return request('/activity/double_twelfth/pointsRedemption', params);
}
