<template>
  <div class="page-250101-activity">
    <nav-bar-2
      ref="topNavBar"
      title="更多下联"
      :azShow="true"
      :placeholder="false"
    >
    </nav-bar-2>

    <div class="main">
      <div class="activity-content">
        <div class="activity-content-dl">
          <div
            class="content-dl-item"
            :class="{
              'dl1-default': item.dl_id == 1,
              'dl2-default': item.dl_id == 2,
              'dl3-default': item.dl_id == 3,
              'dl1-active': item.dl_id == 1 && selectDlInfo.dl_id == 1,
              'dl2-active': item.dl_id == 2 && selectDlInfo.dl_id == 2,
              'dl3-active': item.dl_id == 3 && selectDlInfo.dl_id == 3,
            }"
            v-for="item in dlInfoList"
            :key="item.dl_id"
            @click="handleClickDl(item)"
          >
            <div class="yy-item"></div>
            <div
              v-if="item.dl_id == selectDlInfo.dl_id"
              class="active-item"
            ></div>
            <div
              v-if="item.dl_id == selectDlInfo.dl_id"
              class="active-snake"
            ></div>
          </div>
        </div>
      </div>
      <div class="activity-bottom" v-if="myCoupletsInfo.length">
        <div class="more-xl-logo my-xl"></div>
        <div class="more-xl-btn" v-if="myCoupletsInfo.length">
          <div class="more-btn-box">
            <div>更多</div>
            <span></span>
          </div>
        </div>
        <div class="more-xl-content">
          <content-empty
            v-if="!myCoupletsInfo.length"
            tips="暂无数据"
          ></content-empty>
          <template v-else>
            <div
              class="more-xl-content-item"
              v-for="item in myCoupletsInfo"
              :key="item.id"
            >
              <div class="left-content">
                <div class="user-info">
                  <div class="avatar">
                    <img
                      v-if="item.userinfo?.avatar"
                      class="img"
                      :src="item.userinfo?.avatar"
                    />
                    <img v-else class="img" :src="defaultAvatar" />
                  </div>
                  <span class="user-name">{{
                    formatNickname(item.userinfo.nickname)
                  }}</span>
                </div>
                <div class="dl-info">
                  <div class="xl-title">
                    <div class="xl-content">{{ item.text.text }}</div>
                    <div class="me">我</div>
                  </div>
                </div>
              </div>
              <div class="likes" @click="handleClickDm(item)">
                <div
                  class="like"
                  :class="{ active: item.text.click_status }"
                ></div>
                <span class="num">{{ item.text.click_count }}</span>
              </div>
            </div>
          </template>
        </div>
      </div>
      <div class="activity-bottom">
        <div class="more-xl-logo"></div>
        <div class="more-xl-btn" v-if="moreCoupletsInfo.length">
          <div class="more-btn-box">
            <div>更多</div>
            <span></span>
          </div>
        </div>
        <div class="more-xl-content">
          <content-empty
            style="height: 60%"
            v-if="!moreCoupletsInfo.length"
            tips="暂无数据"
          ></content-empty>
          <template v-else>
            <yy-list
              class="game-list-box"
              v-model="loadingObj"
              :finished="finished"
              @refresh="onRefresh()"
              @loadMore="loadMore()"
              :empty="empty"
              tips="暂无数据"
            >
              <div
                class="more-xl-content-item"
                v-for="item in moreCoupletsInfo"
                :key="item.id"
                :class="{ 'only-one': moreCoupletsInfo.length === 1 }"
              >
                <div class="left-content">
                  <div class="user-info">
                    <div class="avatar">
                      <img
                        v-if="item.userinfo.avatar"
                        class="img"
                        :src="item.userinfo.avatar"
                      />
                      <img v-else class="img" :src="defaultAvatar" />
                    </div>
                    <span class="user-name">{{
                      formatNickname(item.userinfo.nickname)
                    }}</span>
                  </div>
                  <div class="dl-info">
                    <div class="xl-title">
                      <div class="xl-content">{{ item.text.text }}</div>
                    </div>
                  </div>
                </div>
                <div class="likes" @click="handleClickDm(item)">
                  <div
                    class="like"
                    :class="{ active: item.text.click_status }"
                  ></div>
                  <span class="num">{{ item.text.click_count }}</span>
                </div>
              </div>
            </yy-list>
          </template>
        </div>
      </div>
    </div>

    <div
      class="return-top"
      v-show="showReturnTop"
      @click="handleReturnTop"
    ></div>
    <!-- 我的奖品弹窗 -->
    <prize-popup :show.sync="prizePopup"></prize-popup>
  </div>
</template>

<script>
import { mapActions } from 'vuex';
import { platform, boxInit, BOX_login } from '@/utils/box.uni.js';
import {
  ApiDoingsChunjieCouplets,
  ApiDoingsChunjieThumb,
} from '@/api/views/25_new_year_activity.js';
export default {
  name: 'MoreCouplets',
  components: {
    prizePopup: () => import('../components/prize-popup.vue'),
  },
  data() {
    return {
      luckyBagOpened: false, //福袋是否开启
      activity_status: 3, // 活动状态 1活动已开始 2 活动不存在 3活动未开始 4活动已结束
      info: {}, // 活动信息
      shareInfo: {}, //分享信息
      prizePopup: false,
      rulePopup: false,
      showReturnTop: false,
      selectDlInfo: {
        dl_id: 1,
        text: '丰年盛景龙蛇舞',
      },
      dlInfoList: [
        {
          dl_id: 1,
          text: '丰年盛景龙蛇舞',
        },
        {
          dl_id: 2,
          text: '春风入喜财入户',
        },
        {
          dl_id: 3,
          text: '国色方兴普天开景运',
        },
      ], //对联信息
      selectCoupletsTxtInfo: [
        { id: 1, text: '新', isSelect: false },
        { id: 2, text: '锦', isSelect: false },
        { id: 3, text: '岁', isSelect: false },
        { id: 4, text: '绣', isSelect: true },
        { id: 5, text: '春', isSelect: false },
        { id: 6, text: '盛', isSelect: false },
        { id: 7, text: '光', isSelect: false },
        { id: 8, text: '世', isSelect: false },
        { id: 9, text: '彩', isSelect: false },
        { id: 10, text: '舞', isSelect: false },
        { id: 11, text: '碟', isSelect: false },
        { id: 12, text: '凤', isSelect: false },
        { id: 13, text: '飞', isSelect: false },
        { id: 14, text: '龙', isSelect: false },
      ],
      // myCoupletsInfo: [
      //   {
      //     id: 1,
      //     userinfo: {
      //       user_id: 0,
      //       nickname: "用户昵称",
      //       avatar: "",
      //     },
      //     sl: "丰年盛景龙蛇舞",
      //     xl: "春风入喜财入户",
      //     is_like: false,
      //     likes_num: 10,
      //   },
      //   {
      //     id: 2,
      //     userinfo: {
      //       user_id: 0,
      //       nickname: "用户昵称",
      //       avatar: "",
      //     },
      //     sl: "国色方兴普天开景运",
      //     xl: "国色方兴普天开景运",
      //     is_like: true,
      //     likes_num: 10,
      //   },
      // ],
      // moreCoupletsInfo: [
      //   {
      //     id: 1,
      //     userinfo: {
      //       user_id: 0,
      //       nickname: "用户昵称",
      //       avatar: "",
      //     },
      //     sl: "丰年盛景龙蛇舞",
      //     xl: "春风入喜财入户",
      //     is_like: false,
      //     likes_num: 10,
      //   },
      //   {
      //     id: 2,
      //     userinfo: {
      //       user_id: 0,
      //       nickname: "用户昵称",
      //       avatar: "",
      //     },
      //     sl: "国色方兴普天开景运",
      //     xl: "国色方兴普天开景运",
      //     is_like: true,
      //     likes_num: 10,
      //   },
      //   {
      //     id: 3,
      //     userinfo: {
      //       user_id: 0,
      //       nickname: "用户昵称",
      //       avatar: "",
      //     },
      //     sl: "丰年盛景龙蛇舞",
      //     xl: "春风入喜财入户",
      //     is_like: false,
      //     likes_num: 10,
      //   },
      //   {
      //     id: 4,
      //     userinfo: {
      //       user_id: 0,
      //       nickname: "用户昵称",
      //       avatar: "",
      //     },
      //     sl: "国色方兴普天开景运",
      //     xl: "国色方兴普天开景运",
      //     is_like: true,
      //     likes_num: 10,
      //   },
      //   {
      //     id: 5,
      //     userinfo: {
      //       user_id: 0,
      //       nickname: "用户昵称",
      //       avatar: "",
      //     },
      //     sl: "丰年盛景龙蛇舞",
      //     xl: "春风入喜财入户",
      //     is_like: false,
      //     likes_num: 10,
      //   },
      //   {
      //     id: 6,
      //     userinfo: {
      //       user_id: 0,
      //       nickname: "用户昵称",
      //       avatar: "",
      //     },
      //     sl: "国色方兴普天开景运",
      //     xl: "国色方兴普天开景运",
      //     is_like: true,
      //     likes_num: 10,
      //   },
      //   {
      //     id: 7,
      //     userinfo: {
      //       user_id: 0,
      //       nickname: "用户昵称",
      //       avatar: "",
      //     },
      //     sl: "丰年盛景龙蛇舞",
      //     xl: "春风入喜财入户",
      //     is_like: false,
      //     likes_num: 10,
      //   },
      //   {
      //     id: 8,
      //     userinfo: {
      //       user_id: 0,
      //       nickname: "用户昵称",
      //       avatar: "",
      //     },
      //     sl: "国色方兴普天开景运",
      //     xl: "国色方兴普天开景运",
      //     is_like: true,
      //     likes_num: 10,
      //   },
      //   {
      //     id: 9,
      //     userinfo: {
      //       user_id: 0,
      //       nickname: "用户昵称",
      //       avatar: "",
      //     },
      //     sl: "丰年盛景龙蛇舞",
      //     xl: "春风入喜财入户",
      //     is_like: false,
      //     likes_num: 10,
      //   },
      //   {
      //     id: 10,
      //     userinfo: {
      //       user_id: 0,
      //       nickname: "用户昵称",
      //       avatar: "",
      //     },
      //     sl: "国色方兴普天开景运",
      //     xl: "国色方兴普天开景运",
      //     is_like: true,
      //     likes_num: 10,
      //   },
      // ],
      myCoupletsInfo: [],
      moreCoupletsInfo: [],
      loadingObj: {
        loading: false,
        reloading: false,
      },
      finished: false,
      page: 1,
      listRows: 20,
    };
  },
  computed: {
    groupedSelectCoupletsTxtInfo() {
      const grouped = [];
      for (let i = 0; i < this.selectCoupletsTxtInfo.length; i += 7) {
        grouped.push(this.selectCoupletsTxtInfo.slice(i, i + 7));
      }
      return grouped;
    },
  },
  async mounted() {
    // 官包触发页面刷新（将onResume挂载在window上）
    if (platform == 'android') {
      window.onResume = this.onResume;
    }
    document.body.addEventListener('touchstart', function () {});
  },
  beforeDestroy() {
    this.removeScrollEvent();
    document.body.removeEventListener('touchstart', function () {});
    if (this.timeClock) {
      clearInterval(this.timeClock);
      this.timeClock = null;
    }
  },
  created() {
    this.addScrollEvent();
  },
  async activated() {
    await this.getSimulatorGetNewGameList();
  },
  methods: {
    ...mapActions({
      SET_USER_INFO: 'user/SET_USER_INFO',
    }),
    async onResume() {
      await boxInit();
      await this.SET_USER_INFO(true);
      await this.getSimulatorGetNewGameList();
    },
    addScrollEvent() {
      window.addEventListener('scroll', this.handleScroll);
    },
    removeScrollEvent() {
      window.removeEventListener('scroll', this.handleScroll);
    },
    handleScroll() {
      let windowScrollTop =
        document.documentElement.scrollTop || document.body.scrollTop;

      if (!this.showReturnTop && windowScrollTop > 400) {
        this.showReturnTop = true;
      } else if (this.showReturnTop && windowScrollTop <= 400) {
        this.showReturnTop = false;
      }
    },
    handleReturnTop() {
      window.scrollTo({
        top: 0,
        behavior: 'smooth',
      });
    },
    formatNickname(nickname) {
      if (nickname.length > 5) {
        return nickname.slice(0, 2) + '**' + nickname.slice(-2);
      }
      return nickname;
    },
    // 选中对联
    async handleClickDl(item) {
      if (this.selectDlInfo.dl_id === item.dl_id) {
        return;
      }
      this.selectDlInfo = item;
      await this.getSimulatorGetNewGameList();
    },
    selectCoupletsTxt(item) {
      this.selectCoupletsTxtInfo.forEach(select => {
        if (select.dl_id === item.dl_id) {
          select.isSelect = !select.isSelect;
        }
      });
    },
    // 活动规则
    clickRule() {
      this.toPage('25NewYearActivityRulePage');
    },
    login() {
      BOX_login();
    },
    // 初始化信息
    async getInitData() {},
    async getRecodeList() {},
    async getSimulatorGetNewGameList(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      this.loadingObj.loading = true;
      const res = await ApiDoingsChunjieCouplets({
        page: this.page,
        listRows: this.listRows,
        dl_id: this.selectDlInfo.dl_id,
      });
      this.loadingObj.loading = false;
      if (action === 1 || this.page === 1) {
        this.myCoupletsInfo = [];
        this.moreCoupletsInfo = [];
        if (!res.data.others.data.length) {
          this.empty = true;
        } else {
          this.empty = false;
        }
      }
      if (action === 1 || this.page === 1) {
        this.myCoupletsInfo.push(...res.data.self);
      }

      this.moreCoupletsInfo.push(...res.data.others.data);
      if (res.data.others.data.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
    },
    async onRefresh() {
      this.finished = false;
      await this.getSimulatorGetNewGameList();
      this.loadingObj.reloading = false;
    },
    async loadMore() {
      await this.getSimulatorGetNewGameList(2);
      this.loadingObj.loading = false;
    },
    getDlSl(dl_id) {
      const item = this.dlInfoList.find(item => item.dl_id == dl_id);
      return item ? item.text : null;
    },
    async handleClickDm(item) {
      if (!this.userInfo.token) {
        this.$toast('请登录');
        this.login();
        return false;
      }
      await ApiDoingsChunjieThumb({
        id: item.id,
        action: !item.text.click_status ? 'yes' : 'no',
      });
      item.text.click_status = !item.text.click_status;
      item.text.click_count = item.text.click_status
        ? item.text.click_count + 1
        : item.text.click_count - 1;
    },
    debounce(fn, delay) {
      let timer = null;
      return function (value) {
        clearTimeout(timer);
        timer = setTimeout(() => {
          fn.call(this, value);
        }, delay);
      };
    },
  },
};
</script>

<style lang="less" scoped>
.page-250101-activity {
  font-family: PingFang SC, PingFang SC;
  display: flex;
  flex-direction: column;
  width: 100%;
  overflow: hidden;
  min-height: 100vh;
  background: #fcf4e3;
  .back,
  .rule {
    width: 40 * @rem;
    height: 40 * @rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(109, 24, 39, 0.3);
    border-radius: 14 * @rem;
  }
  .back {
    background: rgba(109, 24, 39, 0.3)
      url(~@/assets/images/25newyear/left-back.png) center center no-repeat;
    background-size: 9 * @rem 14 * @rem;
  }
  .rule {
    font-weight: 500;
    font-size: 13 * @rem;
    color: #ffffff;
  }
  .fixed-btns {
    width: 100%;
    position: fixed;
    top: 106 * @rem;
    z-index: 999;
    padding: 0 16 * @rem;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .fixed-share-btn,
    .fixed-prize-btn {
      box-sizing: border-box;
      padding: 0 7 * @rem;
      min-width: 40 * @rem;
      height: 40 * @rem;
      background: rgba(109, 24, 39, 0.3);
      border-radius: 14 * @rem;
      font-size: 12 * @rem;
      color: #ffffff;
      flex-wrap: wrap;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
  .main {
    width: 100%;
    position: relative;
    height: 100vh;
    flex: 1;
    min-height: 0;
    background: url(~@/assets/images/25newyear/25newyear-bg11.png) center top
      no-repeat;
    background-size: 100% 338 * @rem;
    padding-bottom: 47 * @rem;
    .activity-content {
      position: relative;
      margin: 85 * @rem auto 0;
      padding-top: @safeAreaTop;
      padding-top: @safeAreaTopEnv;
      display: flex;
      flex-direction: column;
      // align-items: center;
      .activity-content-dl {
        padding: 0 19 * @rem;
        box-sizing: border-box;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .content-dl-item {
          position: relative;
          width: 94 * @rem;
          height: 260 * @rem;
          .yy-item {
            position: absolute;
            bottom: -6 * @rem;
            width: 96 * @rem;
            height: 11 * @rem;
            background: url(~@/assets/images/25newyear/25newyear-icon11.png)
              center center no-repeat;
            background-size: 96 * @rem 11 * @rem;
          }
          .active-item {
            position: absolute;
            right: -7 * @rem;
            top: -5 * @rem;
            width: 18 * @rem;
            height: 18 * @rem;
            background: url(~@/assets/images/25newyear/btn-check1.png) center
              center no-repeat;
            background-size: 18 * @rem 18 * @rem;
          }
          .active-snake {
            position: absolute;
            bottom: -5 * @rem;
            right: -24 * @rem;
            width: 62 * @rem;
            height: 62 * @rem;
            background: url(~@/assets/images/25newyear/25newyear-logo3.png)
              center top no-repeat;
            background-size: 62 * @rem 62 * @rem;
          }
          &.dl1-default {
            background: url(~@/assets/images/25newyear/25newyear-dl1-default.png)
              center top no-repeat;
            background-size: 94 * @rem 260 * @rem;
          }
          &.dl1-active {
            background: url(~@/assets/images/25newyear/25newyear-dl1-active.png)
              center top no-repeat;
            background-size: 94 * @rem 260 * @rem;
          }
          &.dl2-default {
            background: url(~@/assets/images/25newyear/25newyear-dl2-default.png)
              center top no-repeat;
            background-size: 94 * @rem 260 * @rem;
          }
          &.dl2-active {
            background: url(~@/assets/images/25newyear/25newyear-dl2-active.png)
              center top no-repeat;
            background-size: 94 * @rem 260 * @rem;
          }
          &.dl3-default {
            background: url(~@/assets/images/25newyear/25newyear-dl3-default.png)
              center top no-repeat;
            background-size: 94 * @rem 260 * @rem;
          }
          &.dl3-active {
            background: url(~@/assets/images/25newyear/25newyear-dl3-active.png)
              center top no-repeat;
            background-size: 94 * @rem 260 * @rem;
          }
        }
      }
    }
    .activity-bottom {
      position: relative;
      margin: 24 * @rem auto 0;
      width: 351 * @rem;
      // height: 468 * @rem;
      background: #fafafa;
      border-radius: 12 * @rem;
      border: 2px solid #ffbf76;
      .more-xl-logo {
        position: absolute;
        top: -6 * @rem;
        left: 50%;
        transform: translateX(-50%);
        width: 164 * @rem;
        height: 46 * @rem;
        background: url(~@/assets/images/25newyear/25newyear-title5.png) center
          top no-repeat;
        background-size: 164 * @rem 46 * @rem;
        &.my-xl {
          background: url(~@/assets/images/25newyear/25newyear-title6.png)
            center top no-repeat;
          background-size: 164 * @rem 46 * @rem;
        }
      }
      .more-xl-btn {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        margin: 10 * @rem 13 * @rem 0 0;
        .more-btn-box {
          display: flex;
          align-items: center;
          opacity: 0;
          > div {
            display: inline-block;
            height: 15 * @rem;
            font-weight: 500;
            font-size: 12 * @rem;
            color: #ad8585;
            margin-right: 5 * @rem;
          }
          > span {
            width: 7 * @rem;
            height: 11 * @rem;
            background: url(~@/assets/images/25newyear/btn-right-arrow.png)
              center top no-repeat;
            background-size: 7 * @rem 11 * @rem;
          }
        }
      }
      .more-xl-content {
        margin: 25 * @rem 12 * @rem 24 * @rem;
        .more-xl-content-item {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-top: 14 * @rem;
          .left-content {
            display: flex;
            align-items: center;
            .user-info {
              display: flex;
              align-items: center;
              .avatar {
                width: 36 * @rem;
                height: 36 * @rem;
                border-radius: 50%;
                overflow: hidden;
                background: #ccc;
              }
              .user-name {
                width: 63 * @rem;
                margin-left: 8 * @rem;
                font-weight: 400;
                font-size: 13 * @rem;
                color: #5a2a2a;
                height: 15 * @rem;
                line-height: 15 * @rem;
                // text-overflow: ellipsis;
                overflow: hidden;
                white-space: nowrap;
              }
            }
            .dl-info {
              margin-left: 20 * @rem;
              display: flex;
              flex-direction: column;

              .xl-title {
                display: flex;
                align-items: center;
                .xl-content {
                  max-width: 140 * @rem;
                  height: 20 * @rem;
                  font-weight: 500;
                  font-size: 14 * @rem;
                  color: #5a2a2a;
                  line-height: 20 * @rem;
                  text-align: left;
                  font-style: normal;
                  text-transform: none;
                  text-overflow: ellipsis;
                  overflow: hidden;
                  white-space: nowrap;
                }
                .me {
                  margin-left: 4 * @rem;
                  width: 20 * @rem;
                  height: 14 * @rem;
                  background: #ffd1d1;
                  border-radius: 3 * @rem;
                  font-weight: 500;
                  font-size: 11 * @rem;
                  color: #a71111;
                  line-height: 14 * @rem;
                  text-align: center;
                }
              }
            }
          }

          .likes {
            display: flex;
            align-items: center;
            .like {
              width: 16 * @rem;
              height: 16 * @rem;
              background: url(~@/assets/images/25newyear/btn-likes.png) center
                top no-repeat;
              background-size: 16 * @rem 16 * @rem;
              &.active {
                background: url(~@/assets/images/25newyear/btn-likes-active.png)
                  center top no-repeat;
                background-size: 16 * @rem 16 * @rem;
              }
            }
            .num {
              margin-left: 4 * @rem;
              font-weight: 400;
              font-size: 12 * @rem;
              color: #a17272;
            }
          }
          &:not(:first-child) {
            padding-top: 16 * @rem;
            border-top: 1px solid #efebeb;
          }
          &.only-one {
            padding-top: 0;
            border-top: none !important;
          }
        }
      }
    }
  }
}
.return-top {
  position: fixed;
  right: 5 * @rem;
  bottom: 100 * @rem;
  width: 50 * @rem;
  height: 50 * @rem;
  background: url(~@/assets/images/return-top.png) center center no-repeat;
  background-size: 50 * @rem 50 * @rem;
  z-index: 100;
}
@keyframes tipShow {
  0% {
    opacity: 0;
    top: 0;
  }
  100% {
    opacity: 1;
    top: -12 * @rem;
  }
}
@keyframes tipShowLast {
  0% {
    opacity: 0;
    top: 0;
  }
  100% {
    opacity: 1;
    top: -30 * @rem;
  }
}
</style>
