<template>
  <div class="address-gage">
    <nav-bar-2
      ref="topNavBar"
      title="收货地址"
      :azShow="true"
      :placeholder="false"
    >
    </nav-bar-2>
    <div class="loading-content" v-if="!loadSuccess">
      <van-loading vertical>加载中...</van-loading>
    </div>
    <div class="main" v-else>
      <div class="address-info" v-if="isAddressComplete">
        <div class="top-info">
          <div class="name">{{ saveReceivingAddress.name }}</div>
          <div class="phoneNumber">{{ saveReceivingAddress.phoneNumber }}</div>
        </div>
        <div class="bottom-info">
          {{ saveReceivingAddress.address }}
          {{ saveReceivingAddress.detailedAddress }}
        </div>
        <div class="status">已提交</div>
      </div>
      <div class="address-box" v-else>
        <van-field
          maxlength="10"
          v-model="receivingAddress.name"
          label="收货人"
          placeholder="填写收货人姓名"
        />
        <van-field
          maxlength="11"
          v-model="receivingAddress.phoneNumber"
          type="tel"
          label="手机号"
          placeholder="填写正确的收货人号码"
        />
        <van-field
          v-model="receivingAddress.address"
          label="所在地区"
          readonly
          :right-icon="rightIcon"
          clickable
          placeholder="选择收货人所在地区"
          @click="showAddress = true"
        >
        </van-field>
        <van-field
          v-model="receivingAddress.detailedAddress"
          label="详细地址"
          placeholder="填写详细地址，例:1层101室"
          maxlength="30"
        />
      </div>
      <div
        v-if="!isAddressComplete"
        class="add-btn btn"
        :class="{ active: isShowBtnAdd }"
        @click="handleClick()"
      >
        提交
      </div>
      <div class="tips">
        <ul>
          <li>
            如需修改收货地址或查看物流信息，请前往【我的-右上角耳麦图标-在线客服】联系客服
          </li>
          <li>受春节假期影响，奖品预计在14个工作日内为您发货</li>
        </ul>
      </div>
    </div>
    <!-- 地址弹窗 -->

    <van-popup v-model="showAddress" closeable round position="bottom">
      <van-area
        title="标题"
        :area-list="areaList"
        @cancel="cancelAddress"
        @confirm="confirmAddress"
      />
    </van-popup>
  </div>
</template>

<script>
import { mapActions } from 'vuex';
import {
  ApiDoingsChunjieDeliveryAddress,
  ApiDoingsChunjieViewDeliveryAddress,
} from '@/api/views/25_new_year_activity.js';
import { platform, boxInit, BOX_login } from '@/utils/box.uni.js';
import rightIcon from '@/assets/images/25newyear/btn-right-arrow1.png';
import { areaList } from '@vant/area-data';
export default {
  name: 'FillInAddress',
  props: {},
  components: {},
  data() {
    return {
      areaList,
      searchResult: [],
      rightIcon,
      saveReceivingAddress: {},
      receivingAddress: {
        name: '',
        phoneNumber: '',
        address: '',
        detailedAddress: '',
      },
      showAddress: false,
      loadSuccess: false, //加载完毕
    };
  },
  async mounted() {
    // 官包触发页面刷新（将onResume挂载在window上）
    if (platform == 'android') {
      window.onResume = this.onResume;
    }
    document.body.addEventListener('touchstart', function () {});
  },
  beforeDestroy() {
    document.body.removeEventListener('touchstart', function () {});
    if (this.timeClock) {
      clearInterval(this.timeClock);
      this.timeClock = null;
    }
  },
  async activated() {
    await this.getInitData();
  },
  methods: {
    ...mapActions({
      SET_USER_INFO: 'user/SET_USER_INFO',
    }),
    async onResume() {
      await boxInit();
      await this.SET_USER_INFO(true);
      await this.getInitData();
    },
    login() {
      BOX_login();
    },
    // 初始化信息
    async getInitData() {
      try {
        const res = await ApiDoingsChunjieViewDeliveryAddress();
        this.saveReceivingAddress = res.data;
      } catch (error) {
      } finally {
        this.loadSuccess = true;
      }
    },
    async addAddress(data) {
      try {
        this.$toast.loading();
        await ApiDoingsChunjieDeliveryAddress(data);
        this.$toast.clear();
        await this.getInitData();
        this.$toast('地址提交成功');
      } catch (error) {
        this.$toast.clear();
      } finally {
      }
    },
    cancelAddress() {
      this.showAddress = false;
    },
    confirmAddress(e) {
      this.showAddress = false;

      this.receivingAddress.address = e.map(item => item.name).join(' ');
    },
    handleClick() {
      if (
        this.receivingAddress.name &&
        this.receivingAddress.phoneNumber &&
        this.receivingAddress.address &&
        this.receivingAddress.detailedAddress
      ) {
        const phoneRegex = /^1[3-9]\d{9}$/;
        if (!phoneRegex.test(this.receivingAddress.phoneNumber)) {
          this.$toast('请输入正确的手机号');
          return;
        }
        this.saveReceivingAddress = JSON.parse(
          JSON.stringify(this.receivingAddress),
        );
        this.addAddress(this.saveReceivingAddress);
      }
    },
  },
  computed: {
    isAddressComplete() {
      return (
        this.saveReceivingAddress?.name &&
        this.saveReceivingAddress?.phoneNumber &&
        this.saveReceivingAddress?.address &&
        this.saveReceivingAddress?.detailedAddress
      );
    },
    isShowBtnAdd() {
      return (
        this.receivingAddress.name &&
        this.receivingAddress.phoneNumber &&
        this.receivingAddress.address &&
        this.receivingAddress.detailedAddress
      );
    },
  },
};
</script>

<style lang="less" scoped>
.address-gage {
  font-family: PingFang SC, PingFang SC;
  display: flex;
  flex-direction: column;
  width: 100%;
  overflow: hidden;
  min-height: 100vh;
  .main {
    width: 100%;
    position: relative;
    height: 100vh;
    flex: 1;
    min-height: 0;
    margin-top: 54 * @rem;
    padding-top: @safeAreaTop;
    padding-top: @safeAreaTopEnv;
    background: #f5f5f6;
    display: flex;
    flex-direction: column;
    align-items: center;
    .address-info {
      position: relative;
      margin: 10 * @rem auto 0;
      width: 339 * @rem;
      height: 108 * @rem;
      background: #ffffff;
      border-radius: 10 * @rem;
      padding: 18 * @rem 12 * @rem 16 * @rem;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      .top-info {
        display: flex;
        align-items: center;
        .name,
        .phoneNumber {
          max-width: 150 * @rem;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          font-weight: 400;
          height: 20 * @rem;
          font-size: 14 * @rem;
          color: #333333;
          line-height: 20 * @rem;
        }
        .phoneNumber {
          margin-left: 24 * @rem;
        }
      }
      .bottom-info {
        margin-top: 12 * @rem;
        font-weight: 400;
        height: 42 * @rem;
        font-size: 14 * @rem;
        color: #333333;
        line-height: 21 * @rem;
        text-align: left;
        overflow: hidden;
      }
      .status {
        position: absolute;
        right: 0;
        top: 15 * @rem;
        height: 22 * @rem;
        padding: 3 * @rem 8 * @rem 3 * @rem 12 * @rem;
        box-sizing: border-box;
        background: #ededed;
        border-radius: 12 * @rem 0 0 12 * @rem;
        font-weight: 400;
        font-size: 12 * @rem;
        color: #666666;
      }
    }
    .address-box {
      margin: 10 * @rem auto 0;
      width: 339 * @rem;
      height: 220 * @rem;
      background: #ffffff;
      border-radius: 10 * @rem;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-around;
    }
    .add-btn {
      margin-top: 28 * @rem;
      width: 238 * @rem;
      height: 40 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
      background: linear-gradient(90deg, #fbb8b7 0%, #ffdfca 100%);
      border-radius: 20 * @rem;
      font-weight: 500;
      font-size: 15 * @rem;
      color: #fffdf1;
      &.active {
        background: linear-gradient(90deg, #f64b4b 0%, #ffb07c 100%), #d9d9d9;
      }
    }
    .tips {
      margin-top: 40 * @rem;
      padding: 0 25 * @rem 0 26 * @rem;
      ul {
        li {
          font-weight: 400;
          font-size: 13px;
          color: #999999;
          line-height: 22 * @rem;
          list-style-type: disc;
        }
      }
    }
  }
  .loading-content {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
  }
  /deep/.van-icon__image {
    width: 5 * @rem;
    height: 9 * @rem;
  }
}
</style>
