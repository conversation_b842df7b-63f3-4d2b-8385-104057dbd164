import { request } from '../index';

/**
 * 元旦 - 元旦首页
 */
export function Api0101Index(params = {}) {
  return request('/activity/new_year/index', params);
}

/**
 * 元旦 - 卡牌列表
 */
export function Api0101CardList(params = {}) {
  return request('/activity/new_year/prizeList', params);
}

/**
 * 活动页/元旦 - 任务列表
 */
export function Api0101TaskList(params = {}) {
  return request('/activity/new_year/missionList', params);
}

/**
 * 活动页/元旦 - 任务列表
 */
export function Api0101CardExchangeList(params = {}) {
  return request('/activity/new_year/exchangeList', params);
}

/**
 * 元旦 - 兑换明细
 */
export function Api0101RecordExchange(params = {}) {
  return request('/activity/new_year/lotteryList', params);
}

/**
 * 元旦 - 抽奖
 */
export function Api0101Lottery(params = {}) {
  return request('/activity/new_year/lottery', params);
}

/**
 * 元旦 - 刮奖
 */
export function Api0101Scratch(params = {}) {
  return request('/activity/new_year/takeHb', params);
}

/**
 * 元旦 - 兑换
 */
export function Api0101Exchange(params = {}) {
  return request('/activity/new_year/takeExchange', params);
}

/**
 * 元旦 - 获奖名单
 */
export function Api0101NameList(params = {}) {
  return request('/activity/new_year/chosenList', params);
}

/**
 * 元旦 - 抽奖次数兑换
 */
export function Api0101TaskExchange(params = {}) {
  return request('/activity/new_year/takeMission', params);
}
