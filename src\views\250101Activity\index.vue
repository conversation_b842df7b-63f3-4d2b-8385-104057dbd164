<template>
  <div class="page-250101-activity">
    <nav-bar-2
      ref="topNavBar"
      bgStyle="transparent-white"
      :azShow="true"
      :placeholder="false"
    >
      <template #left>
        <div class="back" @click="back"></div>
      </template>
    </nav-bar-2>

    <div class="fixed-btns">
      <div class="fixed-share-btn" @click="handleShare">分<br />享</div>
      <div class="fixed-rule-btn" @click="rulePopup = true">规<br />则</div>
    </div>

    <div class="main">
      <div class="activity-top">
        <!-- 顶部内容 -->
        <div class="activity-title"></div>
        <div class="activity-sub-title">
          有机会赢SVIP年卡+云挂机年卡+2025金币
        </div>
        <div class="activity-time" v-if="info.start_time && info.end_time">
          活动时间：2024.12.28 0点 ~ 2025.1.1 24点
        </div>
      </div>
      <div class="step-container">
        <div class="my-reward" @click="openRewardPopup">
          <img src="@/assets/images/250101/250101_reward_icon.png" alt="" />
        </div>

        <div class="task-container" v-if="taskList.length">
          <div class="task-list">
            <div class="col-line">
              <div
                class="fill-line"
                :style="{ height: `${fillLineHeight}rem` }"
              ></div>
            </div>
            <!-- 任务状态 //0=未完 1=可领取 2=已领取 -->
            <div
              class="task-item"
              :class="{ active: currentTaskIndex >= index + 1 }"
              v-for="(item, index) in taskList"
              :key="index"
            >
              <div class="task-tooltip" v-if="currentTaskIndex == index + 1">
                <div
                  class="task-reward-icon"
                  :class="{
                    reward1: item.reward_type == 1,
                    reward2: item.reward_type == 2,
                    reward3: item.reward_type == 3,
                    reward4: item.reward_type == 4,
                  }"
                ></div>
                <div class="task-text">{{ item.title }}</div>
              </div>
              <div class="task-title" v-else>
                {{ index + 1 }}.{{ item.title }}
              </div>
            </div>
          </div>
        </div>

        <div
          class="step"
          :class="`step${index + 1}`"
          :style="{
            zIndex: `${item.showTip ? 12 : item.reward_type ? 11 : 10}`,
          }"
          v-for="(item, index) in rewardListStep"
          :key="index"
        >
          <div
            v-if="item.reward_type"
            class="reward"
            @click="clickShowTip(item)"
            :class="{
              reward1: item.reward_type == 1 && myPosition <= item.index,
              reward2: item.reward_type == 2 && myPosition <= item.index,
              reward3: item.reward_type == 3 && myPosition <= item.index,
              reward4: item.reward_type == 4 && myPosition <= item.index,
            }"
          >
            <div
              class="reward-tip"
              :class="{ last: item.index == 20 }"
              v-if="
                (item.index == 20 || item.reward_type == 3 || item.showTip) &&
                myPosition < item.index
              "
            >
              {{ item.reward }}
            </div>
          </div>
        </div>
        <div
          class="step-mine-box"
          v-if="myPosition > 0"
          :class="[`step${myPosition}`, positonAnimation ? 'animate' : '']"
        >
          <div class="step-my">
            <div class="my-avatar">
              <user-avatar></user-avatar>
            </div>
          </div>
        </div>
      </div>
      <div class="step-bottom-container">
        <div class="wish-btn" @click="goToWish"></div>
        <div class="go-btn" @click="handleGoBtn"></div>
        <div class="seckill-btn" @click="goToSeckill"></div>
      </div>
      <div class="svip-container">
        <div class="svip-card">
          <div class="svip-border">
            <div class="svip-content">
              <div class="svip-title"></div>
              <div class="svip-desc">每项每人仅可购买一次</div>
              <div class="meal-list">
                <div
                  class="meal-item"
                  :class="{
                    active: meal.amount == selectedMealSvip.amount,
                  }"
                  v-for="(meal, index) in vipList"
                  :key="index"
                  @click="handleSelectMealSvip(meal)"
                >
                  <!-- 0不可购买  1可以购买 -->
                  <div class="tag" v-if="meal.buy_status != 1">已购买</div>
                  <div class="title">{{ meal.title }}</div>
                  <div class="price-line">
                    <div class="price"><span>¥</span>{{ meal.amount }}</div>
                    <div class="old-price no-through" v-if="meal.show_title">
                      {{ meal.show_title }}
                    </div>
                    <div class="old-price" v-else-if="meal.show_amount">
                      ¥{{ meal.show_amount }}
                    </div>
                  </div>
                  <div class="desc">{{ meal.desc }}</div>
                </div>
              </div>

              <div class="btn svip-btn" @click="handleSvipBtn">
                立即购买
                <template v-if="selectedMealSvip.amount"
                  >¥{{ selectedMealSvip.amount }}</template
                >
              </div>
              <div class="time-left">{{ activityTimeLeft }}</div>
            </div>
          </div>
        </div>
      </div>
      <div class="svip-container ptb-container" v-if="ptbList.length">
        <div class="svip-card">
          <div class="svip-border">
            <div class="svip-content">
              <div class="svip-title"></div>
              <div class="svip-desc">每项每天每人仅可购买一次</div>
              <div class="meal-list">
                <div
                  class="meal-item"
                  :class="{
                    active: meal.price == selectedMealPtb.price,
                  }"
                  v-for="(meal, index) in ptbList"
                  :key="index"
                  @click="handleSelectMealPtb(meal)"
                >
                  <!-- 0不可购买  1可以购买 -->
                  <div class="tag" v-if="meal.buy_status != 1">已购买</div>
                  <div class="title">
                    {{ meal.gear_position + '' + meal.date_unit }}
                  </div>
                  <div class="price-line">
                    <div class="price">
                      <span>{{ meal.money_unit }}</span
                      >{{ meal.price }}
                    </div>
                  </div>
                  <div class="desc">
                    {{ meal.desc }}
                  </div>
                </div>
              </div>

              <div class="anchor-btn" v-if="!is_svip" @click="handleAnchorBtn">
                开通SVIP<span>（享充值平台币立返绑定平台币或金币）</span>
              </div>

              <div class="btn svip-btn" @click="handlePtbBtn">
                {{ is_svip ? '立即购买' : '直接购买' }}
                <template v-if="selectedMealPtb.price"
                  >¥{{ selectedMealPtb.price }}</template
                >
              </div>
              <div class="time-left">{{ ptbTimeLeft }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <van-popup
      v-model="clockInPopup"
      position="bottom"
      :safe-area-inset-bottom="true"
      :lock-scroll="false"
      class="popup-container clock-in-popup"
      @close="onClockInPopupClose"
    >
      <div class="title-container">
        <img
          class="title-pic"
          src="@/assets/images/250101/clock-in-title.png"
          alt=""
        />
        <div class="close-btn" @click="clockInPopup = false"></div>
      </div>
      <div class="clock-in-container" v-if="clockInfo.list.length > 0">
        <div
          class="clock-in-item"
          v-for="(item, index) in clockInfo.list"
          :key="index"
          @click="getClockInReward(item)"
        >
          <div class="ptb-num">x{{ item.ptb_fake }}</div>
          <div class="ptb-icon"></div>
          <div class="clock-in-desc" v-if="item.status == 0">
            {{ item.title }}
          </div>
          <div class="clock-in-desc" v-else-if="item.status == 1">待领取</div>
          <div class="clock-in-desc done" v-else-if="item.status == 2">
            已领取
          </div>
        </div>
        <div
          class="clock-in-btn"
          v-if="!clockInfo.status"
          @click="handleClockIn"
        >
          签到
        </div>
        <div class="clock-in-btn done" v-else>已完成</div>
      </div>
    </van-popup>
    <!-- 支付方式弹窗 -->
    <pay-type-popup
      :show.sync="payPopupShow"
      :list="payList"
      @choosePayType="choosePayType"
      :money="Number(selectedMeal.amount)"
      unit="¥"
    ></pay-type-popup>

    <!-- 规则弹窗 -->
    <rule-popup :show.sync="rulePopup"></rule-popup>
    <!-- 我的奖品弹窗 -->
    <reward-record-popup
      :show.sync="rewardRecordPopup"
      :isPaySvip="is_pay_svip"
    ></reward-record-popup>
    <!-- 下载游戏弹窗 -->
    <download-game-popup :show.sync="downloadGamePopup"></download-game-popup>
    <!-- 体验游戏弹窗 -->
    <experience-game-popup
      :show.sync="experienceGamePopup"
    ></experience-game-popup>
    <reward-popup
      :show.sync="rewardPopup"
      :info="currentRewardInfo"
    ></reward-popup>
  </div>
</template>

<script>
import dayjs from 'dayjs';
import { mapGetters, mapActions } from 'vuex';
import {
  platform,
  boxInit,
  BOX_showActivity,
  BOX_openInNewWindow,
  BOX_login,
  iframeCopy,
} from '@/utils/box.uni.js';
import { envFun } from '@/utils/function.js';
import { ApiCommonShareInfo } from '@/api/views/system.js';
import {
  ApiDoingsYuandanIndex,
  ApiDoingsYuandanTaskPrize,
  ApiDoingsYuandanTask,
  ApiDoingsYuandanSignInfo,
  ApiDoingsYuandanCompleteRewardPage,
} from '@/api/views/250101.js';
import {
  ApiCreateOrderSvip,
  ApiGetPayUrl,
  ApiGetOrderStatus,
  ApiGetPaymentMethod,
  ApiCreateOrderPtbNew,
  ApiPlatformGetPreferential,
} from '@/api/views/recharge.js';

import { Dialog } from 'vant';

import { remNumberLess, themeColorLess } from '@/common/styles/_variable.less';

// 任务状态 //0=未完 1=可领取 2=已领取

export default {
  components: {
    rulePopup: () => import('./components/rule-popup.vue'),
    rewardRecordPopup: () => import('./components/reward-record-popup.vue'),
    downloadGamePopup: () => import('./components/download-game-popup.vue'),
    experienceGamePopup: () => import('./components/experience-game-popup.vue'),
    rewardPopup: () => import('./components/reward-popup.vue'),
  },
  data() {
    return {
      doLoading: false,

      activity_status: 3, // 活动状态 1活动已开始 2 活动不存在 3活动未开始 4活动已结束
      info: {}, // 活动信息
      is_svip: false, // 是否是svip
      is_pay_svip: 0, // 是否购买过svip 0未购买 1已购买
      nowTime: Date.now(),
      timeClock: null,

      rulePopup: false,
      rewardRecordPopup: false,
      downloadGamePopup: false,
      experienceGamePopup: false,

      rewardPopup: false, // 奖励领取弹窗
      currentRewardInfo: {}, // 当前领取的奖励信息

      clock: null,
      position: 1, // 1-9
      positonAnimation: false, // 跳格子是否需要动画
      myPosition: 0, // 1-20
      taskList: [],
      vipList: [],
      ptbList: [],
      preferential: {}, // ptb优惠信息

      selectedMeal: {},
      selectedMealSvip: {},
      selectedMealPtb: {},
      selectedType: 'svip', // svip ptb

      payPopupShow: false,
      payList: [], // 支付方式列表
      selectedPayType: 'wx', // 支付方式

      clockInPopup: false,
      clockInfo: {
        status: 0, // 0未签到 1已签到
        list: [], // 签到信息
      },

      shareInfo: {}, //分享信息

      tipTimeClock: null, // 格子提示倒计时

      isPrizeDraw: false, // 是否参加过最终抽奖

      appLoaded: false, // app是否加载完成
    };
  },
  computed: {
    ...mapGetters({
      initData: 'system/initData',
      userInfo: 'user/userInfo',
    }),
    activity_status_text() {
      const arr = [
        '活动已开始！',
        '活动不存在！',
        '活动未开始！',
        '活动已结束！',
      ];
      return arr[this.activity_status - 1];
    },
    // 格子列表
    rewardListStep() {
      return Array(20)
        .fill({})
        .map((item, index) => {
          if (this.taskList.find(item => item.index === index + 1)) {
            return this.taskList.find(item => item.index === index + 1);
          } else {
            return {
              index: index + 1,
            };
          }
        });
    },
    // 当前需要完成的任务索引 从1开始
    currentTaskIndex() {
      let index = this.taskList.findIndex(item => item.status != 2);
      if (this.taskList.every(item => item.status == 0)) {
        return 1;
      } else if (this.taskList.every(item => item.status == 2)) {
        return 9;
      } else if (index != -1) {
        return index + 1;
      }
    },
    // 当前可领取或status=2的最后一个奖励的索引 从1开始
    currentRewardIndex() {
      if (this.taskList.every(item => item.status == 0)) {
        return 0;
      } else if (this.taskList.every(item => item.status == 2)) {
        return 9;
      } else {
        // status=2的最后一个奖励的索引
        for (let i = 0; i < this.taskList.length; i++) {
          if (this.taskList[i].status != 2) {
            return i;
          }
        }
      }
    },
    // 纵向完成的红线高度
    fillLineHeight() {
      return ((this.currentTaskIndex - 1) * 33 + 16) * remNumberLess;
    },
    // 活动剩余时间
    activityTimeLeft() {
      return this.fromSecondToHour(
        this.info.end_time - Math.floor(this.nowTime / 1000),
      );
    },

    // 活动剩余时间
    ptbTimeLeft() {
      if (!this.preferential.expire_time) {
        return '';
      }
      return this.fromSecondToHour(
        this.preferential.expire_time - Math.floor(this.nowTime / 1000),
      );
    },
  },
  async created() {
    await this.getIndexData();
    this.positonAnimation = false;
    await this.goToPosition();
    await this.getPayMethod(); // 获取支付方式
  },
  async mounted() {
    // 官包触发页面刷新（将onResume挂载在window上）
    if (platform == 'android') {
      window.onResume = this.onResume;
    }
    document.body.addEventListener('touchstart', function () {});
  },
  beforeDestroy() {
    document.body.removeEventListener('touchstart', function () {});
    if (this.timeClock) {
      clearInterval(this.timeClock);
      this.timeClock = null;
    }
  },
  async activated() {
    if (this.taskList.length) {
      await this.getIndexData();
    }
  },
  methods: {
    ...mapActions({
      SET_USER_INFO: 'user/SET_USER_INFO',
    }),
    async onResume() {
      await boxInit();
      await this.SET_USER_INFO(true);
      await this.getIndexData();
    },
    login() {
      BOX_login();
    },

    // 获取活动首页数据
    async getIndexData() {
      const res = await ApiDoingsYuandanIndex();
      let {
        task_list,
        activity_status,
        info,
        vip_list,
        ptb_list,
        is_pay_svip,
        is_svip,
      } = res.data;
      this.activity_status = activity_status;
      this.info = info;
      this.is_pay_svip = is_pay_svip;
      this.is_svip = is_svip;
      this.taskList = task_list.map(item => {
        return {
          ...item,
          showTip: false,
        };
      });
      this.vipList = vip_list;
      this.ptbList = ptb_list;
      this.selectedMealSvip =
        this.vipList.find(item => item.buy_status === 1) ?? {};

      this.selectedMealPtb =
        this.ptbList.find(item => item.buy_status === 1) ?? {};
      await this.getRecodeList(this.selectedMealPtb);
      if (this.timeClock) {
        clearInterval(this.timeClock);
        this.timeClock = null;
      }
      this.timeClock = setInterval(() => {
        this.nowTime = new Date().getTime();
      }, 1000);
    },

    async getIsPrizeDraw() {
      const res = await ApiDoingsYuandanCompleteRewardPage();
      return res.data?.iscj === 1 ? true : false;
    },

    // 点击向前冲！按钮
    async handleGoBtn() {
      if (this.doLoading) {
        return;
      }

      if ([2, 3, 4].includes(this.activity_status)) {
        this.$toast(this.activity_status_text);
        return false;
      }
      if (!this.userInfo.token) {
        this.$toast('请登录');
        this.login();
        return false;
      }

      if (this.taskList.every(item => item.status == 2)) {
        const isPrizeDraw = await this.getIsPrizeDraw();
        if (isPrizeDraw) {
          this.currentRewardInfo = {
            task_id: 10,
            msg: '请耐心等待新年开奖~',
            btn_text: '查看抽奖进度',
          };
        } else {
          this.currentRewardInfo = {
            task_id: 10,
            msg: '去参加新年抽奖吧~',
            btn_text: '马上抽奖',
          };
        }
        this.rewardPopup = true;
        return;
      }
      this.doLoading = true;
      this.$toast.loading('加载中...');
      try {
        const prizeRes = await ApiDoingsYuandanTaskPrize();
        if (prizeRes.data.status == 0) {
          this.$toast.clear();
          // 任务没完成，去做任务
          this.goToTask(prizeRes.data);
        } else {
          // 任务已完成
          this.currentRewardInfo = prizeRes.data;
          await this.getIndexData();
          this.$toast.clear();
          this.positonAnimation = true;
          const res = await this.goToPosition();
          if (res) {
            this.rewardPopup = true;
          }
        }
      } finally {
        this.doLoading = false;
      }
    },
    goToTask(task) {
      switch (task.task_id) {
        case 1:
        case 4:
        case 7:
          this.clockInPopup = true;
          this.getClockInfo();
          break;
        case 2:
          this.goToWish();
          break;
        case 3:
          BOX_showActivity({ name: 'TurnTable' }, { page: 'jbzp' });
          break;
        case 5:
          this.handleShare();
          break;
        case 6:
          this.experienceGamePopup = true;
          break;
        case 8:
          this.downloadGamePopup = true;
          break;
        case 9:
          Dialog.confirm({
            title: '提示',
            message: '累计充值满68元，\n即可获得2025新年抽奖券',
            confirmButtonText: '确定',
            confirmButtonColor: themeColorLess,
            lockScroll: false,
            showCancelButton: false,
          });
          break;
      }
    },

    clickShowTip(item) {
      this.taskList.forEach(item => {
        item.showTip = false;
      });
      item.showTip = true;
      if (this.tipTimeClock) {
        clearTimeout(this.tipTimeClock);
        this.tipTimeClock = null;
      }
      this.tipTimeClock = setTimeout(() => {
        item.showTip = false;
      }, 2000);
    },

    goToSeckill() {
      if ([2, 3, 4].includes(this.activity_status)) {
        this.$toast(this.activity_status_text);
        return false;
      }
      if (!this.userInfo.token) {
        this.$toast('请登录');
        this.login();
        return false;
      }

      // this.toPage("250101ActivitySeckill");
      if (platform == 'android') {
        this.toPage('250101ActivitySeckill');
        return;
      }

      BOX_openInNewWindow(
        {
          name: 'Activity',
          params: {
            url: `https://${envFun()}activity.3733.com/#/250101_activity/seckill`,
          },
        },
        {
          url: `https://${envFun()}activity.3733.com/#/250101_activity/seckill`,
        },
      );
    },
    goToWish() {
      if ([2, 3, 4].includes(this.activity_status)) {
        this.$toast(this.activity_status_text);
        return false;
      }
      if (!this.userInfo.token) {
        this.$toast('请登录');
        this.login();
        return false;
      }

      this.toPage('250101ActivityDanmaku');
    },
    async getClockInfo() {
      const res = await ApiDoingsYuandanSignInfo();
      this.clockInfo = res.data;
    },
    async handleClockIn() {
      this.$toast.loading('加载中...');
      try {
        const res = await ApiDoingsYuandanTask();
        await this.getClockInfo();
      } finally {
        setTimeout(() => {
          this.$toast.clear();
        }, 1500);
      }
    },

    goToPosition() {
      let position;
      if (this.currentRewardIndex < 1) {
        position = 1;
      } else {
        position = this.taskList[this.currentRewardIndex - 1].index;
      }

      return new Promise(resolve => {
        if (!this.positonAnimation) {
          this.myPosition = position;
          resolve(false);
          return;
        }
        if (this.clock) {
          clearInterval(this.clock);
          this.clock = null;
        }
        if (this.myPosition < position) {
          const clock = setInterval(() => {
            if (this.myPosition < position) {
              this.myPosition = this.myPosition + 1;
            } else {
              resolve(true);
              clearInterval(clock);
              this.clock = null;
              return;
            }
          }, 500);
        } else {
          resolve(true);
        }
      });
    },

    // 选择套餐
    handleSelectMealSvip(item) {
      if (!item.buy_status) {
        // 不可购买 buy_status: 0 不可购买 1 可购买
        return;
      }
      if (this.selectedMealSvip.amount === item.amount) {
        this.selectedMealSvip = {};
        return;
      }
      this.selectedMealSvip = item;
    },
    async handleSelectMealPtb(item) {
      if (!item.buy_status) {
        // 不可购买 buy_status: 0 不可购买 1 可购买
        return;
      }
      if (this.selectedMealPtb.price === item.price) {
        this.selectedMealPtb = {};
        return;
      }

      this.selectedMealPtb = item;
      await this.getRecodeList(item);
    },

    async getRecodeList(item) {
      if (!item.id) {
        return;
      }
      let res = await ApiPlatformGetPreferential({
        id: item.id || '',
      });
      this.preferential = res.data?.list?.[0] ?? {};
    },

    // 点击购买svip
    handleSvipBtn() {
      if ([2, 3, 4].includes(this.activity_status)) {
        this.$toast(this.activity_status_text);
        return false;
      }
      if (!this.userInfo.token) {
        this.$toast('请登录');
        this.login();
        return false;
      }

      if (this.vipList.every(item => item.buy_status === 0)) {
        this.$toast('您已全部开通，每项每人仅可购买一次');
        return false;
      }

      if (!this.selectedMealSvip.amount) {
        this.$toast('请先选择套餐');
        return false;
      }

      this.selectedMeal = this.selectedMealSvip;
      this.selectedType = 'svip';
      this.payPopupShow = true;
    },
    handlePtbBtn() {
      if ([2, 3, 4].includes(this.activity_status)) {
        this.$toast(this.activity_status_text);
        return false;
      }
      if (!this.userInfo.token) {
        this.$toast('请登录');
        this.login();
        return false;
      }

      if (this.ptbList.every(item => item.buy_status === 0)) {
        this.$toast('您已全部开通，每项每天每人仅可购买一次');
        return false;
      }

      if (!this.selectedMealPtb.id) {
        this.$toast('请先选择套餐');
        return false;
      }

      this.selectedMeal = {
        ...this.selectedMealPtb,
        amount: this.selectedMealPtb.price,
      };
      this.selectedType = 'ptb';
      this.payPopupShow = true;
    },

    handleAnchorBtn() {
      this.$nextTick(() => {
        const oB = document.querySelector('.svip-container');
        window.scrollTo({
          top: oB.scrollHeight,
          behavior: 'smooth',
        });
      });
    },

    // 获取分享信息
    // 当is_share为1时，表示上报分享成功
    async getShareInfo(is_share = 0) {
      let params = {
        type: 10,
        id: this.userInfo.user_id ? this.userInfo.user_id : 1,
      };
      if (is_share) {
        params.is_share = 1;
      }
      const res = await ApiCommonShareInfo(params);
      if (!is_share) {
        this.shareInfo = res.data;
      }
    },
    async handleShare() {
      if ([2, 3, 4].includes(this.activity_status)) {
        this.$toast(this.activity_status_text);
        return false;
      }
      await this.getShareInfo();
      if (this.initData?.share_info?.length) {
        if (this.operationLoading) {
          return false;
        }
        this.operationLoading = true;
        setTimeout(() => {
          this.operationLoading = false;
        }, 1000);
        window.BOX.mobShare(10, this.userInfo.user_id);
      } else {
        // this.$copyText(this.shareInfo.share_text + this.shareInfo.url).then(
        //   async (res) => {
        //     this.$toast(JSON.stringify(res));
        //     // this.$toast("链接已复制到剪贴板，快去邀请好友吧~");
        //     await this.getShareInfo(1);
        //   },
        //   (err) => {
        //     this.$dialog.alert({
        //       message: "复制失败",
        //       lockScroll: false,
        //     });
        //   }
        // );

        // web的iframe安全策略导致无法复制，故需使用postMessage转移至父级窗口中复制
        iframeCopy(this.shareInfo.share_text + this.shareInfo.url);
      }
      setTimeout(async () => {
        await this.getIndexData();
      }, 1000);
    },
    openRewardPopup() {
      if ([2, 3, 4].includes(this.activity_status)) {
        this.$toast(this.activity_status_text);
        return false;
      }
      if (!this.userInfo.token) {
        this.$toast('请登录');
        this.login();
        return false;
      }

      this.rewardRecordPopup = true;
    },

    // 获取支付方式
    async getPayMethod() {
      let res = await ApiGetPaymentMethod({
        orderType: 103,
      });
      this.payList = res.data;
    },

    // 选择支付方式
    choosePayType(selectedPayType) {
      this.selectedPayType = selectedPayType.symbol;
      this.handlePay();
    },

    // 支付逻辑
    handlePay() {
      this.payPopupShow = false;
      if (this.selectedType == 'ptb') {
        // 平台币限时充值
        const orderParams = {
          isNew: 1,
          money: this.selectedMeal.price,
          payWay: this.selectedPayType,
          positionId: this.selectedMeal.id,
          recodeId: this.preferential.id,
        };
        ApiCreateOrderPtbNew(orderParams).then(async orderRes => {
          await ApiGetPayUrl({
            orderId: orderRes.data.orderId,
            orderType: 102,
            payWay: this.selectedPayType,
            packageName: '',
          }).finally(() => {
            ApiGetOrderStatus({
              order_id: orderRes.data.orderId,
              order_type: 102,
            });
          });

          await this.getIndexData();
        });
        return;
      }

      // 会员服务
      if (this.selectedMeal.position_id) {
        // 平台币充值
        const orderParams = {
          isNew: 1,
          money: this.selectedMeal.amount,
          payWay: this.selectedPayType,
          positionId: this.selectedMeal.position_id,
        };
        ApiCreateOrderPtbNew(orderParams).then(async orderRes => {
          await ApiGetPayUrl({
            orderId: orderRes.data.orderId,
            orderType: 102,
            payWay: this.selectedPayType,
            packageName: '',
          }).finally(() => {
            ApiGetOrderStatus({
              order_id: orderRes.data.orderId,
              order_type: 102,
            });
          });

          await this.getIndexData();
        });
      } else {
        // svip充值
        const orderParams = {
          day: this.selectedMeal.day,
          amount: this.selectedMeal.amount,
          rebate_gold: this.selectedMeal.rebate_gold,
          payWay: this.selectedPayType,
          is_cycle: 0,
        };
        ApiCreateOrderSvip(orderParams).then(async orderRes => {
          await ApiGetPayUrl({
            orderId: orderRes.data.orderId,
            orderType: 103,
            payWay: this.selectedPayType,
            packageName: '',
          }).finally(() => {
            ApiGetOrderStatus({
              order_id: orderRes.data.orderId,
              order_type: 103,
            });
          });
          await this.getIndexData();
        });
      }
    },

    // 签到弹窗关闭时
    onClockInPopupClose() {
      this.getIndexData();
    },
    getClockInReward(item) {
      if (item.status == 1) {
        this.clockInPopup = false;
        this.$nextTick(() => {
          this.handleGoBtn();
        });
      }
    },

    timeFormat(time) {
      return dayjs(time * 1000).format('YYYY.MM.DD H点');
    },
    fromSecondToHour(secondStemp) {
      if (secondStemp <= 0) {
        if (this.timeClock) {
          clearInterval(this.timeClock);
          this.timeClock = null;
        }
        return `${this.activity_status_text}`;
      }
      let day = Math.floor(secondStemp / (60 * 60 * 24));

      let hour = Math.floor((secondStemp - day * 60 * 60 * 24) / (60 * 60));
      let minute = Math.floor(
        (secondStemp - day * 60 * 60 * 24 - hour * 60 * 60) / 60,
      );
      let second = Math.floor(
        secondStemp - day * 60 * 60 * 24 - hour * 60 * 60 - minute * 60,
      );
      if (hour < 10) {
        hour = '0' + hour;
      }
      if (minute < 10) {
        minute = '0' + minute;
      }
      if (second < 10) {
        second = '0' + second;
      }
      if (Number(day) === 0) {
        return `仅剩余${hour}:${minute}:${second}`;
      }
      return `仅剩余${day}天 ${hour}:${minute}:${second}`;
    },
    debounce(fn, delay) {
      let timer = null;
      return function (value) {
        clearTimeout(timer);
        timer = setTimeout(() => {
          fn.call(this, value);
        }, delay);
      };
    },
  },
};
</script>

<style lang="less" scoped>
.page-250101-activity {
  font-family: PingFang SC, PingFang SC;
  display: flex;
  flex-direction: column;
  width: 100%;
  overflow: hidden;
  min-height: 100vh;
  background: rgba(255, 221, 183, 1);
  .back {
    width: 28 * @rem;
    height: 28 * @rem;
    border-radius: 15 * @rem;
    background: rgba(0, 0, 0, 0.3) url(~@/assets/images/nav-bar-back-white.png)
      center center no-repeat;
    background-size: 8 * @rem 14 * @rem;
  }
  .fixed-btns {
    width: 28 * @rem;
    position: fixed;
    top: 80 * @rem;
    right: 0;
    z-index: 999;
    .fixed-share-btn {
      box-sizing: border-box;
      width: 28 * @rem;
      height: 48 * @rem;
      background: rgba(133, 0, 0, 0.26);
      font-size: 13 * @rem;
      color: #fff;
      line-height: 18 * @rem;
      border-radius: 12 * @rem 0 0 12 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .fixed-rule-btn {
      margin-top: 8 * @rem;
      box-sizing: border-box;
      width: 28 * @rem;
      height: 48 * @rem;
      background: rgba(133, 0, 0, 0.26);
      font-size: 13 * @rem;
      color: #fff;
      line-height: 18 * @rem;
      border-radius: 12 * @rem 0 0 12 * @rem;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
  .main {
    width: 100%;
    position: relative;
    flex: 1;
    min-height: 0;
    background: url(~@/assets/images/250101/250101_bg.png) center top no-repeat;
    background-size: 100% 755 * @rem;
    padding-bottom: 30 * @rem;
    .activity-top {
      width: 100%;
      height: 146 * @rem;
      overflow: hidden;
      .activity-title {
        width: 284 * @rem;
        height: 62 * @rem;
        margin: 49 * @rem auto 0;
        background: url(~@/assets/images/250101/top_title.png) center top
          no-repeat;
        background-size: 284 * @rem 62 * @rem;
      }
      .activity-sub-title {
        font-size: 14 * @rem;
        color: rgba(255, 255, 255, 0.9);
        text-align: center;
        margin-top: -13 * @rem;
        line-height: 17 * @rem;
      }
      .activity-time {
        width: fit-content;
        height: 23 * @rem;
        border-radius: 12 * @rem;
        background: rgba(255, 255, 255, 0.2);
        font-size: 11 * @rem;
        color: #fff;
        display: flex;
        padding: 0 11 * @rem;
        margin: 8 * @rem auto 0;
        line-height: 23 * @rem;
      }
    }
  }

  @step-width: 71.5 * @rem;
  @step-height: 40 * @rem;
  @step-x: 41 * @rem;
  @step-y: 42.5 * @rem;
  .generate-steps(@n, @left, @bottom, @change) when (@n <= 20) {
    .step@{n} {
      left: @left;
      bottom: @bottom;
    }
    @next-change: if(mod(@n, 4) = 1, -@change, @change);
    @next-left: (@left + @next-change);
    @next-bottom: (@bottom + (@step-y / 2));
    @next-n: (@n + 1);
    .generate-steps(@next-n, @next-left, @next-bottom, @next-change);
  }
  .generate-steps(1, 54*@rem, 86*@rem, -@step-x);

  .step-container {
    width: 100%;
    height: 609 * @rem;
    margin: 0 auto;
    position: relative;
    .my-reward {
      position: absolute;
      right: 10 * @rem;
      bottom: 97 * @rem;
      width: 66 * @rem;
      height: 64 * @rem;
    }
    .step {
      width: @step-width;
      height: @step-height;
      position: absolute;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #000;
      z-index: 10;
      .reward {
        position: absolute;
        width: 50 * @rem;
        height: 74 * @rem;
        bottom: 14 * @rem;
        left: 10 * @rem;
        background-size: 50 * @rem 74 * @rem;
        background-position: center top;
        background-repeat: no-repeat;
        &.reward1 {
          background-image: url(~@/assets/images/250101/step_reward_1.png);
        }
        &.reward2 {
          background-image: url(~@/assets/images/250101/step_reward_2.png);
        }
        &.reward3 {
          background-image: url(~@/assets/images/250101/step_reward_3.png);
        }
        &.reward4 {
          width: 58 * @rem;
          height: 76 * @rem;
          background-image: url(~@/assets/images/250101/step_reward_4.png);
          background-size: 58 * @rem 76 * @rem;
          left: 9 * @rem;
          bottom: 11 * @rem;
        }
        .reward-tip {
          height: 21 * @rem;
          background: #fff;
          position: absolute;
          width: auto;
          white-space: nowrap;
          padding: 0 6 * @rem;
          line-height: 21 * @rem;
          border-radius: 6 * @rem;
          left: 50%;
          transform: translateX(-50%);
          top: -12 * @rem;
          font-size: 9 * @rem;
          color: rgba(244, 68, 5, 1);
          font-weight: 600;
          animation: tipShow 0.3s;
          &::before {
            content: '';
            width: 10 * @rem;
            height: 10 * @rem;
            transform-origin: center center;
            transform: rotate(45deg) translateX(-50%);
            background: #fff;
            position: absolute;
            left: 50%;
            bottom: -6 * @rem;
            z-index: -1;
          }
          &.last {
            top: -30 * @rem;
            animation: tipShowLast 0.3s;
          }
        }
      }
    }

    .step-mine-box {
      width: @step-width;
      height: @step-height;
      position: absolute;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #000;
      z-index: 11;
      &.animate {
        transition: 0.5s;
      }

      .step-my {
        position: absolute;
        width: 57 * @rem;
        height: 77 * @rem;
        bottom: 15 * @rem;
        left: 7 * @rem;
        background-size: 57 * @rem 77 * @rem;
        background-position: center top;
        background-repeat: no-repeat;
        background-image: url(~@/assets/images/250101/position_icon.png);
        .my-avatar {
          box-sizing: border-box;
          width: 43 * @rem;
          height: 43 * @rem;
          border: 1px solid #fff;
          border-radius: 50%;
          position: absolute;
          top: 7 * @rem;
          left: 50%;
          transform: translateX(-50%);
        }
      }
    }

    .task-container {
      position: relative;
      width: 300 * @rem;
      z-index: 9;
    }
    .task-list {
      display: flex;
      flex-direction: column-reverse;
      position: relative;
      .col-line {
        position: absolute;
        left: 20.5 * @rem;
        bottom: 0;
        width: 3 * @rem;
        height: 275 * @rem;
        background: rgba(255, 255, 255, 0.4);
        border-radius: 2 * @rem;
        overflow: hidden;
        .fill-line {
          width: 3 * @rem;
          height: 8 * @rem;
          background: rgba(255, 104, 90, 1);
          position: absolute;
          bottom: 0;
        }
      }
      .task-item {
        padding-left: 34 * @rem;
        position: relative;
        &::before {
          content: '';
          box-sizing: border-box;
          width: 10 * @rem;
          height: 10 * @rem;
          overflow: hidden;
          background: rgba(255, 226, 214, 1);
          position: absolute;
          left: 17 * @rem;
          top: 50%;
          transform: translateY(-50%) rotate(45deg);
          transform-origin: center center;
          border-radius: 2 * @rem;
        }
        &.active {
          &::before {
            box-sizing: border-box;
            border: 3 * @rem solid #ffffff;
            width: 10 * @rem;
            height: 10 * @rem;
            background: rgba(255, 104, 90, 1);
          }
        }
        .task-title {
          font-size: 11 * @rem;
          color: #fff;
          font-weight: bold;
          -webkit-text-stroke: 1px rgba(243, 0, 0, 0.1);
          line-height: 33 * @rem;
          height: 33 * @rem;
        }
        .task-tooltip {
          box-sizing: border-box;
          width: 66 * @rem;
          height: 33 * @rem;
          background: url(~@/assets/images/250101/tooltip-icon.png) center
            center no-repeat;
          background-size: 66 * @rem 33 * @rem;
          display: flex;
          align-items: center;
          padding-left: 8 * @rem;
          padding-right: 5 * @rem;
          .task-reward-icon {
            width: 17 * @rem;
            height: 17 * @rem;
            background-size: 17 * @rem 17 * @rem;
            background-repeat: no-repeat;
            background-position: center center;
            &.reward1 {
              background-image: url(~@/assets/images/250101/task_reward_1.png);
            }
            &.reward2 {
              background-image: url(~@/assets/images/250101/task_reward_2.png);
            }
            &.reward3 {
              background-image: url(~@/assets/images/250101/task_reward_3.png);
            }
            &.reward4 {
              background-image: url(~@/assets/images/250101/task_reward_4.png);
            }
          }
          .task-text {
            flex: 1;
            min-width: 0;
            text-align: center;
            font-size: 11 * @rem;
            color: rgba(255, 104, 90, 1);
          }
        }
      }
    }
  }
  .step-bottom-container {
    position: relative;
    width: 100%;
    height: 102 * @rem;
    background: url(~@/assets/images/250101/cloud_bg.png) no-repeat;
    background-size: 100% 102 * @rem;
    margin-top: -44 * @rem;
    display: flex;
    justify-content: space-between;
    .wish-btn {
      width: 84 * @rem;
      height: 79 * @rem;
      background: url(~@/assets/images/250101/250101_wish_icon.png) no-repeat;
      background-size: 84 * @rem 79 * @rem;
      margin-top: -32 * @rem;
    }
    .go-btn {
      width: 140 * @rem;
      height: 71 * @rem;
      background: url(~@/assets/images/250101/go_icon.png) center bottom
        no-repeat;
      background-size: 140 * @rem 71 * @rem;
      margin-top: -17 * @rem;
      &:active {
        background-image: url(~@/assets/images/250101/go_icon_active.png);
        background-size: 140 * @rem 66 * @rem;
      }
    }
    .seckill-btn {
      width: 84 * @rem;
      height: 79 * @rem;
      background: url(~@/assets/images/250101/seckill_icon.png) no-repeat;
      background-size: 84 * @rem 79 * @rem;
      margin-top: -32 * @rem;
    }
  }

  .svip-container {
    box-sizing: border-box;
    position: relative;
    width: 351 * @rem;
    margin: -26 * @rem auto 0;
    &::before {
      content: '';
      position: relative;
      box-sizing: border-box;
      display: block;
      width: 351 * @rem;
      height: 36 * @rem;
      overflow: hidden;
      background: url(~@/assets/images/250101/svip-bg-top.png) no-repeat;
      background-size: 351 * @rem 36 * @rem;
      z-index: 1;
    }
    &::after {
      content: '';
      position: relative;
      box-sizing: border-box;
      display: block;
      width: 351 * @rem;
      height: 36 * @rem;
      overflow: hidden;
      background: url(~@/assets/images/250101/svip-bg-bottom.png) center top
        no-repeat;
      background-size: 351 * @rem 36 * @rem;
      z-index: 1;
    }
    .svip-card {
      box-sizing: border-box;
      background: #fff;
      width: 351 * @rem;
      padding: 0 6 * @rem;
      position: relative;
      z-index: 99;
      .svip-border {
        border-left: 1 * @rem solid rgba(255, 221, 183, 1);
        border-right: 1 * @rem solid rgba(255, 221, 183, 1);
        position: relative;
        z-index: 2;
        .svip-content {
          position: relative;
          padding-top: 1 * @rem;
          padding-bottom: 1 * @rem;
          .svip-title {
            width: 167 * @rem;
            height: 24 * @rem;
            background: url(~@/assets/images/250101/svip-title.png) center top
              no-repeat;
            background-size: 167 * @rem 24 * @rem;
            margin: 0 auto;
            margin-top: -16 * @rem;
          }
          .svip-desc {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 15 * @rem;
            margin: 6 * @rem auto 0;
            line-height: 15 * @rem;
            font-size: 12 * @rem;
            color: rgba(197, 102, 57, 1);
            text-align: center;
            position: relative;
            &::before {
              content: '';
              width: 84 * @rem;
              height: 10 * @rem;
              display: block;
              background: url(~@/assets/images/250101/svip-decoration.png)
                center center no-repeat;
              background-size: 84 * @rem 10 * @rem;
            }
            &::after {
              content: '';
              width: 84 * @rem;
              height: 10 * @rem;
              display: block;
              background: url(~@/assets/images/250101/svip-decoration.png)
                center center no-repeat;
              background-size: 84 * @rem 10 * @rem;
              transform: rotateY(180deg);
            }
          }
          .meal-list {
            display: flex;
            align-items: flex-start;
            overflow-x: auto;
            padding-right: 10 * @rem;
            padding-left: 10 * @rem;
            margin-top: 16 * @rem;
            &::-webkit-scrollbar {
              width: 0;
              display: none;
            }
            .meal-item {
              position: relative;
              box-sizing: border-box;
              flex-shrink: 0;
              width: 119 * @rem;
              height: 107 * @rem;
              border: 1 * @rem solid rgba(249, 241, 222, 1);
              background: rgba(255, 252, 243, 1);
              border-radius: 8 * @rem;
              padding-top: 12 * @rem;
              padding-bottom: 4 * @rem;
              transition: all 0.3s;
              display: flex;
              flex-direction: column;
              justify-content: space-between;
              &:not(:last-of-type) {
                margin-right: 10 * @rem;
              }
              .tag {
                width: 36 * @rem;
                height: 17 * @rem;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 9 * @rem;
                color: rgba(197, 102, 57, 1);
                font-weight: bold;
                border-radius: 0 8 * @rem 0 8 * @rem;
                background-color: rgba(255, 207, 186, 1);
                position: absolute;
                right: 0;
                top: 0;
              }

              .title {
                font-size: 14 * @rem;
                font-weight: bold;
                color: rgba(183, 147, 63, 1);
                padding-left: 15 * @rem;
                line-height: 20 * @rem;
              }
              .price-line {
                display: flex;
                align-items: flex-end;
                padding-left: 15 * @rem;
                margin-top: 10 * @rem;
                .price {
                  font-size: 22 * @rem;
                  line-height: 22 * @rem;
                  font-weight: bold;
                  color: rgba(183, 147, 63, 1);
                  span {
                    font-size: 14 * @rem;
                    font-weight: bold;
                  }
                }
                .old-price {
                  font-size: 11 * @rem;
                  line-height: 17 * @rem;
                  color: rgba(183, 147, 63, 0.5);
                  margin-left: 2 * @rem;
                  text-decoration: line-through;
                  &.no-through {
                    text-decoration: none;
                  }
                }
              }
              .desc {
                height: 23 * @rem;
                width: 111 * @rem;
                border-radius: 8 * @rem;
                font-size: 11 * @rem;
                color: rgba(183, 147, 63, 1);
                text-align: center;
                background: rgba(250, 240, 217, 1);
                line-height: 23 * @rem;
                margin: 0 3 * @rem;
              }
              &.active {
                border: 1 * @rem solid rgba(255, 221, 134, 1);
                background: linear-gradient(180deg, #ffe9be 0%, #fff9de 100%);
                .title {
                  color: rgba(135, 66, 11, 1);
                }
                .price-line {
                  .price {
                    color: rgba(135, 66, 11, 1);
                  }
                  .old-price {
                    color: rgba(158, 79, 15, 0.5);
                  }
                }
                .desc {
                  color: rgba(158, 79, 15, 1);
                  background: rgba(255, 233, 190, 1);
                }
              }
            }
          }
          .svip-btn {
            width: 210 * @rem;
            height: 40 * @rem;
            background: linear-gradient(221deg, #fd9083 0%, #ff495c 100%);
            border-radius: 20 * @rem;
            margin: 20 * @rem auto 0;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 17 * @rem;
            color: #ffffff;
            font-weight: bold;
          }
          .time-left {
            font-size: 11 * @rem;
            color: rgba(144, 45, 23, 1);
            margin-top: 8 * @rem;
            line-height: 14 * @rem;
            text-align: center;
            margin-bottom: -10 * @rem;
          }
        }
      }
    }
  }

  .ptb-container {
    margin-top: 30 * @rem;
    .svip-card {
      .svip-border {
        .svip-content {
          .svip-title {
            background-image: url(~@/assets/images/250101/ptb-title.png);
          }
          .meal-list {
            .meal-item {
              flex-shrink: 0;
              min-width: 152 * @rem;
              width: auto;
              .desc {
                display: block;
                text-align: left;
                width: auto;
                padding: 0 10 * @rem;
                white-space: nowrap;
              }
            }
          }
          .anchor-btn {
            box-sizing: border-box;
            width: 295 * @rem;
            height: 40 * @rem;
            border-radius: 0 20 * @rem 20 * @rem 0;
            background: rgba(255, 231, 171, 1);
            margin: 20 * @rem auto 0;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            transform: translateX(10 * @rem);
            font-size: 15 * @rem;
            color: rgba(135, 66, 11, 1);
            font-weight: bold;
            padding-left: 20 * @rem;
            white-space: nowrap;
            span {
              font-size: 12 * @rem;
            }
            &::before {
              content: '';
              width: 38 * @rem;
              height: 40 * @rem;
              background: url(~@/assets/images/250101/diamand.png) center center
                no-repeat;
              background-size: 38 * @rem 40 * @rem;
              position: absolute;
              left: -19 * @rem;
              top: 0;
            }
          }
        }
      }
    }
  }

  .clock-in-popup {
    box-sizing: border-box;
    padding: 20 * @rem 0 0;
    border-radius: 20 * @rem 20 * @rem 0 0;
    height: 207 * @rem;
    background: rgba(250, 249, 245, 1);
    .title-container {
      position: relative;

      .title-pic {
        width: 308 * @rem;
        height: 24 * @rem;
        margin: 0 auto;
      }
      .close-btn {
        width: 16 * @rem;
        height: 16 * @rem;
        background: url('~@/assets/images/250101/250101_popup_close.png') center
          center no-repeat;
        background-size: 16 * @rem 16 * @rem;
        position: absolute;
        top: 0;
        right: 18 * @rem;
      }
    }
    .clock-in-container {
      margin-top: 40 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
      .clock-in-item {
        width: 58 * @rem;
        height: 73 * @rem;
        border-radius: 10 * @rem;
        border: 1 * @rem solid rgba(255, 233, 190, 1);
        background-color: rgba(249, 246, 239, 1);
        position: relative;
        display: flex;
        flex-direction: column;
        margin-right: 27 * @rem;

        .ptb-num {
          box-sizing: border-box;
          position: absolute;
          left: 39 * @rem;
          top: -8 * @rem;
          height: 14 * @rem;
          background: linear-gradient(221deg, #fd9083 0%, #ff495c 100%);
          border-radius: 7 * @rem 7 * @rem 7 * @rem 0;
          font-size: 10 * @rem;
          color: #fff;
          font-weight: bold;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 0 6 * @rem;
        }
        .ptb-icon {
          width: 32 * @rem;
          height: 34 * @rem;
          background: url('~@/assets/images/250101/clock-in-ptb.png') center
            center no-repeat;
          background-size: 32 * @rem 34 * @rem;
          margin: 11 * @rem auto 0;
        }
        .clock-in-desc {
          height: 14 * @rem;
          text-align: center;
          background: rgba(242, 225, 219, 1);
          margin-top: auto;
          font-size: 9 * @rem;
          color: rgba(160, 123, 119, 1);
          line-height: 14 * @rem;
          border-radius: 0 0 9 * @rem 9 * @rem;
          &.done {
            color: rgba(160, 123, 119, 0.5);
          }
        }
      }
      .clock-in-btn {
        width: 60 * @rem;
        height: 30 * @rem;
        background: linear-gradient(221deg, #fd9083 0%, #ff495c 100%);
        border-radius: 15 * @rem;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 12 * @rem;
        font-weight: bold;
        color: #fff;
        &.done {
          background: rgba(207, 207, 207, 1);
        }
      }
    }
  }
}
@keyframes tipShow {
  0% {
    opacity: 0;
    top: 0;
  }
  100% {
    opacity: 1;
    top: -12 * @rem;
  }
}
@keyframes tipShowLast {
  0% {
    opacity: 0;
    top: 0;
  }
  100% {
    opacity: 1;
    top: -30 * @rem;
  }
}
</style>
