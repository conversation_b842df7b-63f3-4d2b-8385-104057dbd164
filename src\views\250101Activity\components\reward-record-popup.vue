<template>
  <div>
    <van-popup
      v-model="popup"
      position="bottom"
      class="reward-record-popup"
      :lock-scroll="false"
    >
      <div class="title-container">
        <img
          class="title-pic"
          src="@/assets/images/250101/reward-record-popup-title.png"
          alt=""
        />
        <div class="close-btn" @click="closePopup"></div>
      </div>
      <div class="banner" v-if="!isPaySvip" @click="goToRecharge">
        <img src="@/assets/images/250101/double-svip-ad.png" alt="" />
      </div>
      <div class="record-container">
        <content-empty
          style="height: 60%"
          v-if="!recordList.length"
          tips="暂无数据"
        ></content-empty>
        <template v-else>
          <div class="record-title">
            <div class="title">获得时间</div>
            <div class="title">奖品名称</div>
          </div>
          <div class="record-list">
            <div
              class="record-item"
              v-for="(item, index) in recordList"
              :key="index"
            >
              <div class="time">{{ item.date }}</div>
              <div class="content">
                {{ item.desc }}<span v-if="item.tip">{{ item.tip }}</span>
              </div>
            </div>
          </div>
        </template>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { ApiDoingsYuandanTaskLog } from '@/api/views/250101.js';
export default {
  name: 'rewardRecordPopup',
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    isPaySvip: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      recordList: [],
    };
  },
  computed: {
    popup: {
      get() {
        return this.show;
      },
      set(value) {
        this.$emit('update:show', value);
      },
    },
  },
  watch: {
    async popup(val) {
      if (val) {
        await this.getRecordList();
      }
    },
  },
  methods: {
    closePopup() {
      this.popup = false;
    },
    async getRecordList() {
      const res = await ApiDoingsYuandanTaskLog();
      this.recordList = res.data;
    },
    goToRecharge() {
      this.closePopup();
      this.$nextTick(() => {
        const oB = document.querySelector('.svip-container');
        window.scrollTo({
          top: oB.scrollHeight,
          behavior: 'smooth',
        });
      });
    },
  },
};
</script>

<style lang="less" scoped>
.reward-record-popup {
  box-sizing: border-box;
  padding: 20 * @rem 0 0;
  border-radius: 20 * @rem 20 * @rem 0 0;
  height: 522 * @rem;
  display: flex;
  background: rgba(250, 249, 245, 1);
  flex-direction: column;
  .title-container {
    position: relative;
    height: 24 * @rem;
    .title-pic {
      width: 268 * @rem;
      height: 24 * @rem;
      margin: 0 auto;
    }
    .close-btn {
      width: 16 * @rem;
      height: 16 * @rem;
      background: url('~@/assets/images/250101/250101_popup_close.png') center
        center no-repeat;
      background-size: 16 * @rem 16 * @rem;
      position: absolute;
      top: 0;
      right: 18 * @rem;
    }
  }
  .banner {
    margin-top: 20 * @rem;
    width: 100%;
    height: 74 * @rem;
  }
  .record-container {
    flex: 1;
    min-height: 0;
    margin-top: 20 * @rem;
    display: flex;
    flex-direction: column;

    .record-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 40 * @rem;
      .title {
        font-size: 16 * @rem;
        color: rgba(197, 102, 57, 1);
        font-weight: bold;
        line-height: 22 * @rem;
      }
    }
    .record-list {
      flex: 1;
      flex-shrink: 0;
      overflow-y: auto;
      margin-top: 10 * @rem;
      padding-bottom: 20 * @rem;
      padding-top: 5 * @rem;
      .record-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 12 * @rem;
        line-height: 19 * @rem;
        padding: 0 18 * @rem;
        .time {
          font-size: 14 * @rem;
          color: rgba(197, 102, 57, 1);
          width: 40%;
        }
        .content {
          font-size: 14 * @rem;
          color: rgba(197, 102, 57, 1);
          margin-left: 10 * @rem;
          flex: 1;
          min-width: 0;
          text-align: right;
          span {
            border-radius: 4 * @rem;
            border: 0.5 * @rem solid rgba(197, 102, 57, 1);
            font-size: 10 * @rem;
            padding: 1 * @rem 2 * @rem;
            color: rgba(197, 102, 57, 1);
            margin-left: 4 * @rem;
          }
        }
      }
    }
  }
}
</style>
